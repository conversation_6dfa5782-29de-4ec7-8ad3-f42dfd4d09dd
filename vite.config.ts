import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import { compression } from 'vite-plugin-compression2';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd());

  return {
    plugins: [
      react(),
      // 生产环境启用打包分析和压缩
      mode === 'production' &&
        visualizer({
          open: false,
          gzipSize: true,
          brotliSize: true,
          filename: 'dist/stats.html',
        }),
      mode === 'production' &&
        compression({
          algorithm: 'gzip',
          ext: '.gz',
        }),
    ],
    // 依赖优化配置
    optimizeDeps: {
      include: ['react', 'react-dom', 'antd', '@ant-design/icons', 'dayjs', 'lodash-es'],
      force: true, // 强制重新预构建依赖
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    server: {
      port: 5173,
      host: 'localhost',
      proxy: {
        '/api': {
          target: 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    build: {
      // 生产环境构建配置
      outDir: 'dist',
      assetsDir: 'assets',
      // 小于此阈值的导入或引用资源将内联为 base64 编码
      assetsInlineLimit: 4096,
      // 启用/禁用 CSS 代码拆分
      cssCodeSplit: true,
      // 构建后是否生成 source map 文件
      sourcemap: false,
      // 强制缓存破坏
      assetsInclude: ['**/*.html'],
      // 自定义底层的 Rollup 打包配置
      rollupOptions: {
        output: {
          // 静态资源分类打包
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          // 最小化拆分包
          manualChunks: {
            react: ['react', 'react-dom', 'react-router-dom'],
            antd: ['antd', '@ant-design/icons'],
            // 其他大型依赖可以在这里添加
          },
        },
      },
      // 设置为 false 可以禁用最小化混淆
      minify: 'terser',
      // terser 配置
      terserOptions: {
        compress: {
          // 生产环境去除 console
          drop_console: true,
          drop_debugger: true,
        },
      },
    },
    // 预览配置
    preview: {
      port: 5000,
      host: '0.0.0.0',
      strictPort: true,
    },
  };
});
