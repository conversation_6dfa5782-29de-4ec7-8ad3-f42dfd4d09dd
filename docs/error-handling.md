# 错误处理指南

本项目提供了统一的错误处理机制，优先使用后端返回的错误信息，而不是前端自定义的错误信息。

## 核心原则

1. **优先使用后端返回的错误信息**
2. **统一的错误处理格式**
3. **详细的错误日志记录**
4. **用户友好的错误提示**

## 错误信息优先级

错误处理工具会按以下优先级提取错误信息：

1. `error.response.data.message` - Axios 错误响应格式
2. `error.data.message` - 直接的 API 响应格式
3. `error.message` - 通用错误信息
4. `error` (字符串) - 字符串错误
5. 默认错误信息 - 兜底方案

## 使用方法

### 1. 基础错误处理

```typescript
import { getErrorMessage, logError } from '@/utils/errorHandler';

try {
  const response = await someApiCall();
  // 处理成功逻辑
} catch (error: any) {
  logError('操作名称', error);
  message.error(getErrorMessage(error, '默认错误信息'));
}
```

### 2. API 响应处理

```typescript
import { handleApiResponse } from '@/utils/errorHandler';

const response = await someApiCall();
const result = handleApiResponse(response, '操作成功', '操作失败');

if (result.success) {
  message.success(result.message); // 使用后端返回的成功信息
} else {
  message.error(result.message); // 使用后端返回的错误信息
}
```

### 3. 统一的 API 调用包装器

```typescript
import { callApi } from '@/utils/apiHelper';

const handleSaveUser = async (userData: any) => {
  const result = await callApi(
    () => createUser(userData),
    {
      successMessage: '用户创建成功', // 默认成功信息
      errorMessage: '用户创建失败',   // 默认错误信息
      showSuccessMessage: true,      // 是否显示成功提示
      showErrorMessage: true,        // 是否显示错误提示
      logOperation: '创建用户',       // 日志操作名称
    }
  );
  
  if (result.success) {
    // 处理成功逻辑
    refreshUserList();
  }
  // 错误已经自动处理和显示
};
```

### 4. 简化的 API 调用

```typescript
import { callApiSimple } from '@/utils/apiHelper';

const handleGetUsers = async () => {
  try {
    const users = await callApiSimple(
      () => getUserList(),
      '获取用户列表失败'
    );
    setUsers(users);
  } catch (error) {
    // 错误信息已经从后端提取，这里可以做额外处理
    console.error('获取用户失败:', error.message);
  }
};
```

### 5. 带加载状态的 API 调用

```typescript
import { callApiWithLoading } from '@/utils/apiHelper';

const handleDeleteUser = async (userCode: string) => {
  const result = await callApiWithLoading(
    () => deleteUser(userCode),
    setLoading, // 加载状态设置函数
    {
      successMessage: '用户删除成功',
      showSuccessMessage: true,
      logOperation: '删除用户',
    }
  );
  
  if (result.success) {
    refreshUserList();
  }
};
```

## 后端 API 响应格式

### 成功响应
```json
{
  "code": 200,
  "data": { /* 响应数据 */ },
  "message": "操作成功" // 后端返回的成功信息
}
```

### 错误响应
```json
{
  "code": 400,
  "data": null,
  "message": "用户名已存在" // 后端返回的错误信息
}
```

### 网络错误
```json
{
  "response": {
    "status": 500,
    "data": {
      "message": "服务器内部错误" // 后端返回的错误信息
    }
  }
}
```

## 错误类型处理

### 1. 业务错误
- 使用 `response.message` 中的后端错误信息
- 例如：用户名已存在、权限不足等

### 2. 网络错误
- 自动识别网络状态和 HTTP 状态码
- 提供用户友好的错误提示

### 3. 表单验证错误
- 前端表单验证错误仍使用前端定义的错误信息
- 后端验证错误使用后端返回的错误信息

## 最佳实践

1. **始终使用错误处理工具**：不要直接使用 `message.error('硬编码错误')`

2. **提供有意义的默认错误信息**：作为后端错误信息的兜底

3. **记录详细的错误日志**：使用 `logError` 函数记录错误上下文

4. **区分用户错误和系统错误**：
   - 用户错误：显示后端返回的具体错误信息
   - 系统错误：显示通用的用户友好错误信息

5. **错误信息国际化**：后端返回的错误信息应该已经是用户当前语言

## 示例：用户管理页面

```typescript
// ✅ 正确的做法
const handleSave = async () => {
  try {
    const values = await form.validateFields();
    const response = await updateUser(userCode, values);
    const result = handleApiResponse(response, '更新成功', '更新失败');
    
    if (result.success) {
      message.success(result.message); // 使用后端返回的成功信息
      handleRefresh();
    } else {
      message.error(result.message); // 使用后端返回的错误信息
    }
  } catch (error: any) {
    logError('更新用户', error);
    message.error(getErrorMessage(error, '更新失败')); // 优先使用后端错误信息
  }
};

// ❌ 错误的做法
const handleSave = async () => {
  try {
    const response = await updateUser(userCode, values);
    if (response.code === 200) {
      message.success('更新成功'); // 硬编码的成功信息
    } else {
      message.error('更新失败'); // 忽略了后端返回的具体错误信息
    }
  } catch (error) {
    message.error('网络错误'); // 过于简单的错误信息
  }
};
```

通过使用这套错误处理机制，您可以确保用户看到的都是后端返回的准确、具体的错误信息，而不是前端硬编码的通用错误信息。
