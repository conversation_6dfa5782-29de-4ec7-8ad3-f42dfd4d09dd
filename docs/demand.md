# 销售订单系统接口文档

## 接口概览

销售订单系统提供5个主要接口，所有接口都需要JWT认证，统一返回格式为：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": null | object
}
```

**基础URL**: `/sales-orders`  
**认证方式**: JWT Bearer Token

---

## 1. 创建销售订单

**接口地址**: `POST /sales-orders`

### 请求参数

#### 主订单信息（必填）

| 参数名          | 类型   | 必填 | 说明                  | 示例           |
| --------------- | ------ | ---- | --------------------- | -------------- |
| salesPersonCode | string | ✓    | 销售人员编码          | "user001"      |
| orderDate       | string | ✓    | 订单日期 (YYYY-MM-DD) | "2025-01-19"   |
| customerCode    | string | ✓    | 主客户编码            | "CUS001"       |
| details         | array  | ✓    | 销售订单明细列表      | 见下方明细结构 |

#### 主订单信息（可选）

| 参数名               | 类型    | 必填 | 说明                      | 示例               | 默认值    |
| -------------------- | ------- | ---- | ------------------------- | ------------------ | --------- |
| expectedDeliveryDate | string  | -    | 期望交货日期 (YYYY-MM-DD) | "2025-01-25"       | null      |
| priority             | enum    | -    | 订单优先级                | "normal"           | "normal"  |
| isDropShipping       | boolean | -    | 是否代发                  | false              | false     |
| isReleased           | boolean | -    | 是否放单                  | false              | false     |
| shippingMethod       | enum    | -    | 物流方式                  | "collect"          | "collect" |
| shippingFee          | number  | -    | 运费金额 (≥0)             | 15.0               | 0         |
| shippingCompany      | string  | -    | 物流公司名称              | "顺丰快递"         | null      |
| shipperUserCode      | string  | -    | 发货人编码                | "user003"          | null      |
| dropShipCustomerCode | string  | -    | 代发客户编码              | "CUS002"           | null      |
| remark               | string  | -    | 订单备注                  | "客户要求加急处理" | null      |
| customers            | array   | -    | 客户关联列表              | 见下方客户结构     | null      |

#### 订单明细结构 (details)

| 参数名           | 类型   | 必填 | 说明              | 示例                                   |
| ---------------- | ------ | ---- | ----------------- | -------------------------------------- |
| inventoryId      | string | ✓    | 库存记录ID (UUID) | "a1b2c3d4-e5f6-7890-abcd-ef1234567890" |
| quantity         | number | ✓    | 销售数量 (≥1)     | 10                                     |
| priceType        | enum   | ✓    | 价格类型          | "retail"                               |
| unitPrice        | number | ✓    | 单价 (≥0)         | 50.0                                   |
| discountAmount   | number | -    | 折扣金额 (≥0)     | 0.0                                    |
| expectedShipDate | string | -    | 期望发货日期      | "2025-01-20"                           |
| remark           | string | -    | 明细备注          | "客户指定颜色"                         |

#### 客户关联结构 (customers)

| 参数名               | 类型    | 必填 | 说明       | 示例                       |
| -------------------- | ------- | ---- | ---------- | -------------------------- |
| customerCode         | string  | ✓    | 客户编码   | "CUS001"                   |
| allocatedQuantity    | number  | -    | 分配总数量 | 50                         |
| allocatedAmount      | number  | -    | 分配总金额 | 2500.0                     |
| allocatedShippingFee | number  | -    | 分配运费   | 15.0                       |
| receiverName         | string  | -    | 收货人姓名 | "李四"                     |
| receiverPhone        | string  | -    | 收货人电话 | "13800138000"              |
| shippingAddress      | string  | -    | 收货地址   | "北京市朝阳区xxx街道xxx号" |
| provinceCode         | number  | -    | 省份代码   | 11                         |
| city                 | string  | -    | 城市       | "北京市"                   |
| district             | string  | -    | 区县       | "朝阳区"                   |
| priority             | number  | -    | 客户优先级 | 0                          |
| isUrgent             | boolean | -    | 是否加急   | false                      |
| specialRequirements  | string  | -    | 特殊要求   | "需要特殊包装"             |
| remark               | string  | -    | 客户备注   | "客户要求分批发货"         |

### 业务规则

1. 系统自动生成订单编号，格式：SO + YYYYMMDD + 4位序号
2. 验证销售人员、主客户、代发客户、发货人是否存在
3. 验证库存是否充足，库存为0不允许开单
4. 自动预留库存（扣减库存数量）
5. 自动计算成本、毛利润等财务数据
6. 如果是寄付模式且订单完成，自动创建财务支出记录

### 响应示例

```json
{
  "code": 200,
  "data": null,
  "message": "销售订单创建成功"
}
```

---

## 2. 分页查询销售订单列表

**接口地址**: `GET /sales-orders`

### 查询参数

#### 分页参数

| 参数名    | 类型   | 必填 | 说明             | 示例        | 默认值      |
| --------- | ------ | ---- | ---------------- | ----------- | ----------- |
| page      | number | -    | 页码 (≥1)        | 1           | 1           |
| pageSize  | number | -    | 每页数量 (1-100) | 10          | 10          |
| sortField | enum   | -    | 排序字段         | "createdAt" | "createdAt" |
| sortOrder | enum   | -    | 排序方向         | "DESC"      | "DESC"      |

#### 过滤参数（全部可选）

| 参数名          | 类型    | 说明                 | 示例                       |
| --------------- | ------- | -------------------- | -------------------------- |
| orderNumber     | string  | 订单编号（模糊搜索） | "SO202501"                 |
| salesPersonCode | string  | 销售人员编码         | "user001"                  |
| customerCode    | string  | 主客户编码           | "CUS001"                   |
| status          | enum    | 订单状态             | "draft"                    |
| priority        | enum    | 订单优先级           | "normal"                   |
| shippingMethod  | enum    | 物流方式             | "collect"                  |
| isDropShipping  | boolean | 是否代发             | false                      |
| isReleased      | boolean | 是否放单             | false                      |
| orderDateStart  | string  | 订单日期开始         | "2025-01-01"               |
| orderDateEnd    | string  | 订单日期结束         | "2025-01-31"               |
| createdAtStart  | string  | 创建时间开始         | "2025-01-01T00:00:00.000Z" |
| createdAtEnd    | string  | 创建时间结束         | "2025-01-31T23:59:59.999Z" |
| totalAmountMin  | number  | 总金额最小值         | 1000.00                    |
| totalAmountMax  | number  | 总金额最大值         | 10000.00                   |
| shippingCompany | string  | 物流公司（模糊搜索） | "顺丰"                     |
| trackingNumber  | string  | 物流单号（模糊搜索） | "SF123"                    |

#### 关联数据参数

| 参数名           | 类型    | 说明                 | 默认值 |
| ---------------- | ------- | -------------------- | ------ |
| includeDetails   | boolean | 是否包含明细信息     | false  |
| includeCustomers | boolean | 是否包含客户关联信息 | false  |

### 响应数据结构

```json
{
  "code": 200,
  "data": {
    "data": [订单列表],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  },
  "message": "获取销售订单列表成功"
}
```

---

## 3. 查询销售订单详情

**接口地址**: `GET /sales-orders/{id}`

### 路径参数

| 参数名 | 类型   | 必填 | 说明              | 示例                                   |
| ------ | ------ | ---- | ----------------- | -------------------------------------- |
| id     | string | ✓    | 销售订单ID (UUID) | "a1b2c3d4-e5f6-7890-abcd-ef1234567890" |

### 响应数据

返回完整的订单信息，包括：

- 订单基本信息
- 销售人员信息（编码、昵称）
- 客户信息（主客户、代发客户）
- 发货人信息
- 订单明细列表（包含库存、价格、成本、毛利等详细信息）
- 客户关联列表（如果有多客户分配）

---

## 4. 更新销售订单基本信息

**接口地址**: `PATCH /sales-orders/{id}`

### 路径参数

| 参数名 | 类型   | 必填 | 说明              |
| ------ | ------ | ---- | ----------------- |
| id     | string | ✓    | 销售订单ID (UUID) |

### 请求参数（全部可选）

| 参数名               | 类型    | 说明         | 示例           |
| -------------------- | ------- | ------------ | -------------- |
| expectedDeliveryDate | string  | 期望交货日期 | "2025-01-25"   |
| priority             | enum    | 订单优先级   | "high"         |
| isDropShipping       | boolean | 是否代发     | true           |
| isReleased           | boolean | 是否放单     | true           |
| shippingMethod       | enum    | 物流方式     | "prepaid"      |
| shippingFee          | number  | 运费金额     | 20.0           |
| shippingCompany      | string  | 物流公司名称 | "圆通快递"     |
| trackingNumber       | string  | 物流单号     | "YT1234567890" |
| shipperUserCode      | string  | 发货人编码   | "user004"      |
| dropShipCustomerCode | string  | 代发客户编码 | "CUS003"       |
| remark               | string  | 订单备注     | "更新备注信息" |

### 业务规则

1. **只有草稿状态的订单才能编辑基本信息**
2. 如果运费发生变化，系统自动重新计算订单总金额
3. 验证代发客户和发货人是否存在

### 响应示例

```json
{
  "code": 200,
  "data": null,
  "message": "销售订单更新成功"
}
```

---

## 5. 更新销售订单状态

**接口地址**: `PATCH /sales-orders/{id}/status`

### 路径参数

| 参数名 | 类型   | 必填 | 说明              |
| ------ | ------ | ---- | ----------------- |
| id     | string | ✓    | 销售订单ID (UUID) |

### 请求参数

| 参数名              | 类型   | 必填     | 说明                                 | 示例                   |
| ------------------- | ------ | -------- | ------------------------------------ | ---------------------- |
| status              | enum   | ✓        | 订单状态                             | "pending_shipment"     |
| confirmedByUserCode | string | 条件必填 | 确认人编码（状态变更为待发货时必填） | "user002"              |
| remark              | string | -        | 状态变更备注                         | "订单已确认，准备发货" |

### 状态转换规则

| 当前状态                  | 可转换状态                                    |
| ------------------------- | --------------------------------------------- |
| draft (草稿)              | pending_shipment (待发货)、cancelled (已取消) |
| pending_shipment (待发货) | completed (已完成)、cancelled (已取消)        |
| completed (已完成)        | 无法转换（终态）                              |
| cancelled (已取消)        | 无法转换（终态）                              |

### 特殊业务逻辑

1. 状态变更为待发货时，必须提供确认人编码，系统记录确认时间
2. 订单完成且物流方式为寄付时，自动创建财务支出记录

### 响应示例

```json
{
  "code": 200,
  "data": null,
  "message": "销售订单状态更新成功"
}
```

---

## 6. 删除销售订单

**接口地址**: `DELETE /sales-orders/{id}`

### 路径参数

| 参数名 | 类型   | 必填 | 说明              |
| ------ | ------ | ---- | ----------------- |
| id     | string | ✓    | 销售订单ID (UUID) |

### 业务规则

1. 软删除，不物理删除数据
2. **已完成的订单不能删除**
3. 删除时记录删除时间

### 响应示例

```json
{
  "code": 200,
  "data": null,
  "message": "销售订单删除成功"
}
```

---

## 枚举值说明

### 订单状态 (SalesOrderStatus)

| 值               | 说明             | 中文名称 |
| ---------------- | ---------------- | -------- |
| draft            | 草稿状态，可编辑 | 草稿     |
| pending_shipment | 已确认，等待发货 | 待发货   |
| completed        | 订单已完成       | 已完成   |
| cancelled        | 订单已取消       | 已取消   |

### 订单优先级 (SalesOrderPriority)

| 值     | 说明               | 中文名称 |
| ------ | ------------------ | -------- |
| urgent | 最高优先级         | 紧急     |
| high   | 高优先级           | 高       |
| normal | 普通优先级（默认） | 普通     |
| low    | 低优先级           | 低       |

### 物流方式 (ShippingMethod)

| 值      | 说明                   | 中文名称 |
| ------- | ---------------------- | -------- |
| collect | 货到付款，客户承担运费 | 到付     |
| prepaid | 预付运费，商家承担运费 | 寄付     |

### 价格类型 (PriceType)

| 值        | 说明     | 中文名称 |
| --------- | -------- | -------- |
| retail    | 零售价格 | 零售     |
| pre_order | 预订价格 | 预订     |
| restock   | 补货价格 | 补货     |
| spot      | 现货价格 | 现货     |

### 客户订单状态 (CustomerOrderStatus)

| 值        | 说明     | 中文名称 |
| --------- | -------- | -------- |
| pending   | 等待处理 | 待处理   |
| confirmed | 已确认   | 已确认   |
| shipped   | 已发货   | 已发货   |
| delivered | 已送达   | 已送达   |
| completed | 已完成   | 已完成   |

### 排序字段 (SortField)

| 值          | 说明             |
| ----------- | ---------------- |
| orderDate   | 按订单日期排序   |
| createdAt   | 按创建时间排序   |
| totalAmount | 按商品总金额排序 |
| grandTotal  | 按订单总金额排序 |
| orderNumber | 按订单编号排序   |

### 排序方向 (SortOrder)

| 值   | 说明 |
| ---- | ---- |
| ASC  | 升序 |
| DESC | 降序 |

---

## 注意事项

### 认证与权限

- **认证要求**: 所有接口都需要JWT Bearer Token认证
- **权限控制**: 基于JWT token进行用户身份验证和权限控制

### 数据格式规范

- **日期格式**: 使用YYYY-MM-DD格式（如：2025-01-19）
- **时间格式**: 使用ISO 8601格式（如：2025-01-19T08:00:00.000Z）
- **UUID格式**: 所有ID字段都使用UUID v4格式
- **数字精度**: 金额字段支持小数点后2位

### 业务逻辑要点

1. **库存管理**:

   - 创建订单时自动预留库存（扣减库存数量）
   - 删除订单需要手动处理库存释放
   - 库存为0时不允许创建销售订单

2. **财务集成**:

   - 寄付订单完成时自动创建财务支出记录
   - 系统自动计算成本价、毛利润等财务数据

3. **状态管理**:

   - 严格按照状态转换规则，不允许非法状态转换
   - 只有草稿状态的订单才能编辑基本信息
   - 已完成的订单不能删除

4. **订单编号**:
   - 系统自动生成，格式：SO + YYYYMMDD + 4位序号
   - 例如：SO202501190001

### 错误处理

- **400 Bad Request**: 请求参数错误、业务规则验证失败
- **401 Unauthorized**: 未提供认证信息或认证失败
- **404 Not Found**: 资源不存在（订单、客户、用户等）
- **500 Internal Server Error**: 服务器内部错误

### 性能建议

1. **分页查询**: 建议每页数量不超过50条，避免一次性查询过多数据
2. **关联数据**: 只在需要时设置`includeDetails`和`includeCustomers`为true
3. **日期范围**: 查询时建议限制日期范围，避免全表扫描

### 开发提示

1. **测试环境**: 建议先在测试环境验证接口功能
2. **数据备份**: 重要操作前建议备份相关数据
3. **日志记录**: 系统会记录关键操作日志，便于问题排查
4. **并发控制**: 涉及库存操作时注意并发控制，避免超卖

---

## 联系方式

如有接口使用问题，请联系开发团队。
