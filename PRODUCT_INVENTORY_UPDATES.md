# 📋 Product表和库存表接口总结

## 🎯 核心设计理念

**每种颜色都必须配置：**

1. ✅ **对应的辅料** - 不同颜色可以使用不同的辅料
2. ✅ **对应的价格** - 不同颜色可以有不同的成本和售价
3. ✅ **对应的图片** - 每个颜色至少要有一张专属图片

---

## 📊 Product表接口总结

### 1. 数据库表结构

**核心字段：**

```sql
CREATE TABLE products (
  id UUID PRIMARY KEY,
  code VARCHAR(100) UNIQUE NOT NULL,
  name VARCHAR(200) NOT NULL,
  brandCode VARCHAR(50) NOT NULL,
  supplierCode VARCHAR(50) NOT NULL,

  -- 基础价格（所有颜色的基准价格）
  clothingCost DECIMAL(10,2) NOT NULL,
  accessoryCost DECIMAL(10,2) NOT NULL,
  retailPrice DECIMAL(10,2) NOT NULL,
  preOrderPrice DECIMAL(10,2) NOT NULL,
  restockPrice DECIMAL(10,2) NOT NULL,
  spotPrice DECIMAL(10,2) NOT NULL,

  -- 颜色级别配置（JSONB格式）
  colorSizeCombinations JSONB NOT NULL,

  -- 通用辅料（适用于所有颜色）
  accessories JSONB,

  -- 主图
  mainImages TEXT[],

  isDeleted BOOLEAN DEFAULT false,
  createdAt TIMESTAMP DEFAULT NOW(),
  updatedAt TIMESTAMP DEFAULT NOW()
);
```

### 2. colorSizeCombinations字段结构

**完整的颜色配置：**

```typescript
interface ColorSizeCombination {
  colorCode: string; // 颜色编码（必填）
  sizes: string[]; // 尺寸数组（必填）

  // 🔴 以下三个字段现在都是必填的
  images: string[]; // 颜色专属图片（必填，至少1张）
  accessories: AccessoryQuantity[]; // 颜色专属辅料（必填）
  priceAdjustments: PriceAdjustment; // 颜色价格调整（必填）

  remark?: string; // 颜色备注（可选）
}

interface AccessoryQuantity {
  accessoryId: string; // 辅料ID
  quantity: number; // 数量
}

interface PriceAdjustment {
  clothingCostAdjustment?: number; // 服装成本调整
  accessoryCostAdjustment?: number; // 辅料成本调整
  retailPriceAdjustment?: number; // 零售价调整
  preOrderPriceAdjustment?: number; // 预订价调整
  restockPriceAdjustment?: number; // 补货价调整
  spotPriceAdjustment?: number; // 现货价调整
}
```

### 3. Product API接口

#### 3.1 创建商品（强制颜色配置）

**POST /products**

```json
{
  "code": "SHIRT001",
  "name": "高级商务衬衫",
  "brandCode": "BRAND001",
  "supplierCode": "SUP001",
  "clothingCost": 80.0,
  "accessoryCost": 15.0,
  "retailPrice": 200.0,
  "preOrderPrice": 180.0,
  "restockPrice": 190.0,
  "spotPrice": 195.0,
  "colorSizeCombinations": [
    {
      "colorCode": "WHITE",
      "sizes": ["S", "M", "L", "XL"],
      "images": ["white-shirt-1.jpg", "white-shirt-2.jpg"],
      "accessories": [
        { "accessoryId": "acc-button-white", "quantity": 8 },
        { "accessoryId": "acc-thread-white", "quantity": 1 }
      ],
      "priceAdjustments": {
        "clothingCostAdjustment": 0,
        "accessoryCostAdjustment": 0,
        "retailPriceAdjustment": 0
      },
      "remark": "经典白色款"
    },
    {
      "colorCode": "BLUE",
      "sizes": ["M", "L", "XL"],
      "images": ["blue-shirt-1.jpg", "blue-shirt-2.jpg", "blue-shirt-detail.jpg"],
      "accessories": [
        { "accessoryId": "acc-button-blue", "quantity": 8 },
        { "accessoryId": "acc-thread-blue", "quantity": 1 },
        { "accessoryId": "acc-special-label", "quantity": 1 }
      ],
      "priceAdjustments": {
        "clothingCostAdjustment": 5.0,
        "accessoryCostAdjustment": 3.0,
        "retailPriceAdjustment": 20.0
      },
      "remark": "蓝色款使用特殊面料和标签"
    }
  ],
  "mainImages": ["shirt-main-1.jpg", "shirt-main-2.jpg"]
}
```

**验证规则：**

- ✅ 每个颜色必须至少有1张图片
- ✅ 每个颜色必须指定辅料配置（可以为空数组）
- ✅ 每个颜色必须指定价格调整（可以全为0）

#### 3.2 查询商品详情

**GET /products/{id}**

**响应示例：**

```json
{
  "code": 200,
  "data": {
    "id": "uuid",
    "code": "SHIRT001",
    "name": "高级商务衬衫",
    "brand": { "code": "BRAND001", "name": "知名品牌" },
    "supplier": { "code": "SUP001", "name": "优质供应商" },
    "clothingCost": 80.0,
    "accessoryCost": 15.0,
    "retailPrice": 200.0,
    "colorSizeCombinations": [
      {
        "colorCode": "WHITE",
        "sizes": ["S", "M", "L", "XL"],
        "images": ["white-shirt-1.jpg", "white-shirt-2.jpg"],
        "accessories": [{ "accessoryId": "acc-button-white", "quantity": 8 }],
        "priceAdjustments": {
          "retailPriceAdjustment": 0
        },
        "calculatedPrices": {
          "clothingCost": 80.0,
          "accessoryCost": 15.0,
          "retailPrice": 200.0
        }
      },
      {
        "colorCode": "BLUE",
        "sizes": ["M", "L", "XL"],
        "images": ["blue-shirt-1.jpg", "blue-shirt-2.jpg"],
        "accessories": [
          { "accessoryId": "acc-button-blue", "quantity": 8 },
          { "accessoryId": "acc-special-label", "quantity": 1 }
        ],
        "priceAdjustments": {
          "clothingCostAdjustment": 5.0,
          "accessoryCostAdjustment": 3.0,
          "retailPriceAdjustment": 20.0
        },
        "calculatedPrices": {
          "clothingCost": 85.0,
          "accessoryCost": 18.0,
          "retailPrice": 220.0
        }
      }
    ]
  }
}
```

---

## 📦 库存表接口总结

### 1. 数据库表结构

**inventory_details表：**

```sql
CREATE TABLE inventory_details (
  id UUID PRIMARY KEY,
  productCode VARCHAR(100) NOT NULL,
  colorCode VARCHAR(50) NOT NULL,
  size VARCHAR(10) NOT NULL,

  -- 库存数量
  quantity INTEGER NOT NULL DEFAULT 0,
  reservedQuantity INTEGER NOT NULL DEFAULT 0,
  availableQuantity INTEGER NOT NULL DEFAULT 0,
  safetyStock INTEGER NOT NULL DEFAULT 0,

  -- 成本和位置
  latestCost DECIMAL(10,2),
  warehouseLocation VARCHAR(100),

  -- 🔴 新增：颜色信息字段
  colorImages JSONB,                    -- 颜色对应图片
  colorPriceInfo JSONB,                 -- 颜色价格信息
  colorAccessories JSONB,               -- 颜色对应辅料

  remark TEXT,
  isDeleted BOOLEAN DEFAULT false,
  createdAt TIMESTAMP DEFAULT NOW(),
  updatedAt TIMESTAMP DEFAULT NOW(),

  -- 唯一约束
  UNIQUE(productCode, colorCode, size)
);
```

### 2. 库存API接口

#### 2.1 获取商品库存（按颜色分组）

**GET /inventory/products/{productCode}**

**响应结构：**

```json
{
  "code": 200,
  "data": {
    "productInfo": {
      "code": "SHIRT001",
      "name": "高级商务衬衫",
      "brandName": "知名品牌",
      "supplierName": "优质供应商",
      "mainImages": ["shirt-main-1.jpg"]
    },
    "colorInventories": [
      {
        "colorCode": "WHITE",
        "colorName": "白色",
        "colorHex": "#FFFFFF",
        "totalQuantity": 150,
        "totalAvailableQuantity": 140,
        "images": ["white-shirt-1.jpg", "white-shirt-2.jpg"],
        "priceInfo": {
          "clothingCost": 80.0,
          "accessoryCost": 15.0,
          "retailPrice": 200.0,
          "preOrderPrice": 180.0,
          "restockPrice": 190.0,
          "spotPrice": 195.0
        },
        "accessories": [
          { "accessoryId": "acc-button-white", "quantity": 8 },
          { "accessoryId": "acc-thread-white", "quantity": 1 }
        ],
        "sizeDetails": [
          {
            "inventoryId": "uuid-1",
            "size": "S",
            "quantity": 30,
            "availableQuantity": 28,
            "reservedQuantity": 2,
            "safetyStock": 10,
            "latestCost": 80.0,
            "warehouseLocation": "A区-01-01",
            "isLowStock": false
          },
          {
            "inventoryId": "uuid-2",
            "size": "M",
            "quantity": 60,
            "availableQuantity": 55,
            "reservedQuantity": 5,
            "safetyStock": 15,
            "latestCost": 80.0,
            "warehouseLocation": "A区-01-02",
            "isLowStock": false
          },
          {
            "inventoryId": "uuid-3",
            "size": "L",
            "quantity": 40,
            "availableQuantity": 37,
            "reservedQuantity": 3,
            "safetyStock": 20,
            "latestCost": 80.0,
            "warehouseLocation": "A区-01-03",
            "isLowStock": true
          },
          {
            "inventoryId": "uuid-4",
            "size": "XL",
            "quantity": 20,
            "availableQuantity": 20,
            "reservedQuantity": 0,
            "safetyStock": 8,
            "latestCost": 80.0,
            "warehouseLocation": "A区-01-04",
            "isLowStock": false
          }
        ],
        "remark": "经典白色款"
      },
      {
        "colorCode": "BLUE",
        "colorName": "蓝色",
        "colorHex": "#0000FF",
        "totalQuantity": 90,
        "totalAvailableQuantity": 85,
        "images": ["blue-shirt-1.jpg", "blue-shirt-2.jpg"],
        "priceInfo": {
          "clothingCost": 85.0,
          "accessoryCost": 18.0,
          "retailPrice": 220.0,
          "preOrderPrice": 180.0,
          "restockPrice": 190.0,
          "spotPrice": 195.0
        },
        "accessories": [
          { "accessoryId": "acc-button-blue", "quantity": 8 },
          { "accessoryId": "acc-special-label", "quantity": 1 }
        ],
        "sizeDetails": [
          {
            "inventoryId": "uuid-5",
            "size": "M",
            "quantity": 35,
            "availableQuantity": 32,
            "reservedQuantity": 3,
            "safetyStock": 12,
            "latestCost": 85.0,
            "warehouseLocation": "A区-02-01",
            "isLowStock": false
          },
          {
            "inventoryId": "uuid-6",
            "size": "L",
            "quantity": 40,
            "availableQuantity": 38,
            "reservedQuantity": 2,
            "safetyStock": 15,
            "latestCost": 85.0,
            "warehouseLocation": "A区-02-02",
            "isLowStock": false
          },
          {
            "inventoryId": "uuid-7",
            "size": "XL",
            "quantity": 15,
            "availableQuantity": 15,
            "reservedQuantity": 0,
            "safetyStock": 8,
            "latestCost": 85.0,
            "warehouseLocation": "A区-02-03",
            "isLowStock": false
          }
        ],
        "remark": "蓝色款使用特殊面料"
      }
    ],
    "totalQuantity": 240,
    "totalAvailableQuantity": 225
  }
}
```

#### 2.2 库存明细列表（支持排序）

**GET /inventory**

**查询参数：**

- `page`, `pageSize`: 分页参数
- `sortBy`: 排序字段 (`quantity`, `availableQuantity`, `latestCost`, `createdAt`, `productCode`)
- `sortOrder`: 排序方向 (`ASC`, `DESC`)
- `productCode`, `colorCode`, `size`: 筛选参数
- `lowStockOnly`, `hasStockOnly`: 状态筛选

**示例：**

```bash
# 按库存数量降序
GET /inventory?page=1&pageSize=10&sortBy=quantity&sortOrder=DESC

# 按价格升序
GET /inventory?page=1&pageSize=10&sortBy=latestCost&sortOrder=ASC

# 筛选特定商品的低库存
GET /inventory?productCode=SHIRT001&lowStockOnly=true
```

**响应结构：**

```json
{
  "code": 200,
  "data": {
    "inventoryDetails": [
      {
        "id": "uuid",
        "productCode": "SHIRT001",
        "productName": "高级商务衬衫",
        "brandName": "知名品牌",
        "colorCode": "WHITE",
        "colorName": "白色",
        "size": "M",
        "quantity": 60,
        "availableQuantity": 55,
        "reservedQuantity": 5,
        "safetyStock": 15,
        "latestCost": 80.0,
        "warehouseLocation": "A区-01-02",
        "colorImages": ["white-shirt-1.jpg", "white-shirt-2.jpg"],
        "colorPriceInfo": {
          "retailPrice": 200.0,
          "clothingCost": 80.0
        },
        "isLowStock": false,
        "createdAt": "2025-01-01T00:00:00.000Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

#### 2.3 库存调整

**POST /inventory/adjust**

```json
{
  "productCode": "SHIRT001",
  "colorCode": "WHITE",
  "size": "M",
  "adjustType": "ADD",
  "quantity": 50,
  "reason": "采购入库",
  "referenceNumber": "PO202501001",
  "operatorCode": "admin"
}
```

**调整类型：**

- `SET`: 设置为指定数量
- `ADD`: 增加指定数量
- `SUBTRACT`: 减少指定数量

---

## 🔄 数据流程

### 1. 商品创建流程

```
创建商品请求 → 验证颜色配置完整性 → 保存商品 → 自动生成库存记录 → 计算颜色价格 → 保存库存
```

### 2. 库存查询流程

```
查询商品库存 → 获取库存数据 → 按颜色分组 → 聚合尺寸明细 → 返回分组结构
```

### 3. 价格计算逻辑

```
最终价格 = 基础价格 + 颜色调整价格

例如：
- 基础零售价：¥200
- 蓝色调整：+¥20
- 蓝色最终价格：¥220
```

---

## 📋 数据库迁移清单

**需要执行的迁移（按顺序）：**

1. **UpdateProductColorSupport1704067100000** - 更新Product表支持完整颜色配置
2. **CreateInventoryTables1704067200000** - 创建库存相关表
3. **MigrateProductsToInventory1704067300000** - 迁移商品数据到库存表
4. **AddColorInfoToInventory1704067400000** - 添加颜色信息到库存表

**执行命令：**

```bash
npm run migration:run
```

---

## ✅ 验证检查

### 1. Product表验证

```sql
-- 检查颜色配置完整性
SELECT
  p.code,
  p.name,
  combo->>'colorCode' as color_code,
  CASE WHEN combo->'images' IS NOT NULL AND jsonb_array_length(combo->'images') > 0
       THEN '✅' ELSE '❌' END as has_images,
  CASE WHEN combo->'accessories' IS NOT NULL
       THEN '✅' ELSE '❌' END as has_accessories,
  CASE WHEN combo->'priceAdjustments' IS NOT NULL
       THEN '✅' ELSE '❌' END as has_price_adjustments
FROM products p,
jsonb_array_elements(p."colorSizeCombinations") AS combo
WHERE p."isDeleted" = false
ORDER BY p.code, combo->>'colorCode';
```

### 2. 库存表验证

```sql
-- 检查库存记录的颜色信息
SELECT
  "productCode",
  "colorCode",
  size,
  CASE WHEN "colorImages" IS NOT NULL THEN '✅' ELSE '❌' END as has_images,
  CASE WHEN "colorPriceInfo" IS NOT NULL THEN '✅' ELSE '❌' END as has_price,
  CASE WHEN "colorAccessories" IS NOT NULL THEN '✅' ELSE '❌' END as has_accessories
FROM inventory_details
WHERE "isDeleted" = false
ORDER BY "productCode", "colorCode", size;
```

### 3. API功能验证

```bash
# 1. 创建支持颜色差异的商品
curl -X POST http://localhost:3000/products -d '{...}'

# 2. 查看按颜色分组的库存
curl http://localhost:3000/inventory/products/SHIRT001

# 3. 测试库存排序
curl "http://localhost:3000/inventory?sortBy=quantity&sortOrder=DESC"

# 4. 测试库存调整
curl -X POST http://localhost:3000/inventory/adjust -d '{...}'
```

---

## 🎯 核心特性总结

### Product表特性

- ✅ **强制颜色配置** - 每个颜色必须有图片、辅料、价格
- ✅ **颜色级别差异** - 支持不同颜色的复杂配置
- ✅ **价格计算** - 基础价格 + 颜色调整 = 最终价格
- ✅ **数据验证** - 创建时强制验证配置完整性

### 库存表特性

- ✅ **按颜色分组** - 一个颜色对应多个尺码
- ✅ **颜色信息存储** - 库存表包含完整颜色信息
- ✅ **排序功能** - 支持按价格和数量排序
- ✅ **自动同步** - 商品变更自动同步库存

### 前端展示优势

- ✅ **清晰的颜色分组** - 便于用户理解和操作
- ✅ **完整的价格信息** - 每个颜色显示实际价格
- ✅ **丰富的图片展示** - 每个颜色有专属图片
- ✅ **精确的库存管理** - 精确到颜色+尺寸级别

**现在您的系统完全支持服装行业的复杂颜色管理需求！** 🎉
