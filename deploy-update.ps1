# Updated Deployment Script for CentOS Nginx
# This script deploys to the correct Nginx directory

param(
    [string]$User = "root",
    [switch]$Help
)

$SERVER_IP = "*************"
$NGINX_ROOT = "/usr/share/nginx/html"

function Write-Success { param([string]$Text) Write-Host $Text -ForegroundColor Green }
function Write-Warning { param([string]$Text) Write-Host $Text -ForegroundColor Yellow }
function Write-Error { param([string]$Text) Write-Host $Text -ForegroundColor Red }
function Write-Info { param([string]$Text) Write-Host $Text -ForegroundColor Cyan }

if ($Help) {
    Write-Host @"
Updated Deployment Script for CentOS Nginx

Usage:
    .\deploy-update.ps1                # Use root user (default)
    .\deploy-update.ps1 -User husky    # Use husky user
    .\deploy-update.ps1 -Help          # Show help

This script will:
    1. Build frontend project with cache busting
    2. Upload files to server
    3. Deploy to correct Nginx directory
    4. Restart Nginx service
    5. Clear browser cache headers
"@
    exit 0
}

Write-Success "===================================="
Write-Success "   Updated Frontend Deployment   "
Write-Success "===================================="
Write-Info "Server: $User@$SERVER_IP"
Write-Info "Nginx Root: $NGINX_ROOT"
Write-Success "===================================="

# Step 1: Clean build
Write-Warning "Step 1: Clean building frontend..."

# Clear local cache
if (Test-Path "node_modules/.cache") {
    Remove-Item "node_modules/.cache" -Recurse -Force -ErrorAction SilentlyContinue
    Write-Info "Cleared build cache"
}

if (Test-Path "dist") {
    Remove-Item "dist" -Recurse -Force
    Write-Info "Cleared dist directory"
}

# Build with timestamp
$buildTime = Get-Date -Format "yyyyMMddHHmmss"
$env:VITE_BUILD_TIMESTAMP = $buildTime

Write-Info "Building with timestamp: $buildTime"
pnpm build

if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed"
    exit 1
}

Write-Success "Build completed successfully"

# Step 2: Add build info to HTML
Write-Warning "Step 2: Adding build information..."
$indexPath = "dist/index.html"
if (Test-Path $indexPath) {
    $content = Get-Content $indexPath -Raw
    $buildInfo = "<!-- Build Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') -->"
    $content = $content -replace '<head>', "<head>`n    $buildInfo"
    $content | Set-Content $indexPath -Encoding UTF8
    Write-Info "Added build timestamp to index.html"
}

# Step 3: Upload files
Write-Warning "Step 3: Uploading files to server..."
if (Test-Path "dist.tar.gz") {
    Remove-Item "dist.tar.gz" -Force
}

tar -czf dist.tar.gz -C dist .
scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null dist.tar.gz ${User}@${SERVER_IP}:/tmp/

if ($LASTEXITCODE -ne 0) {
    Write-Error "Upload failed"
    exit 1
}

Write-Success "Files uploaded successfully"

# Step 4: Deploy on server
Write-Warning "Step 4: Deploying on server..."

ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP @"
echo 'Creating backup of current files...'
BACKUP_DIR="/tmp/nginx_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p \$BACKUP_DIR
cp -r $NGINX_ROOT/* \$BACKUP_DIR/ 2>/dev/null || true

echo 'Clearing current web files...'
rm -rf $NGINX_ROOT/*

echo 'Extracting new files...'
cd $NGINX_ROOT
tar -xzf /tmp/dist.tar.gz

echo 'Setting proper permissions...'
chown -R nginx:nginx $NGINX_ROOT
chmod -R 755 $NGINX_ROOT

echo 'Updating Nginx configuration for no-cache...'
# Create a simple configuration to prevent caching
cat > /etc/nginx/conf.d/no-cache.conf << 'EOF'
# Prevent caching for HTML files
location ~* \.html$ {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}

# Add cache busting for static assets
location ~* \.(js|css)$ {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}
EOF

echo 'Testing Nginx configuration...'
nginx -t

if [ \$? -eq 0 ]; then
    echo 'Restarting Nginx...'
    systemctl restart nginx
    echo 'Nginx restarted successfully'
else
    echo 'Nginx configuration test failed'
    exit 1
fi

echo 'Cleaning up...'
rm /tmp/dist.tar.gz

echo 'Deployment completed successfully'
"@

if ($LASTEXITCODE -ne 0) {
    Write-Error "Server deployment failed"
    exit 1
}

Write-Success "Server deployment completed"

# Step 5: Verify deployment
Write-Warning "Step 5: Verifying deployment..."
Start-Sleep -Seconds 3

# Check Nginx status
$nginxStatus = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "systemctl is-active nginx"
Write-Info "Nginx status: $nginxStatus"

# Check file timestamp
$fileTime = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "stat -c '%y' $NGINX_ROOT/index.html 2>/dev/null || echo 'File check failed'"
Write-Info "File modification time: $fileTime"

# Test HTTP response
$httpTest = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "curl -s -o /dev/null -w '%{http_code}' http://localhost:80"
Write-Info "HTTP response code: $httpTest"

# Check for build timestamp in HTML
$buildCheck = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "grep 'Build Time:' $NGINX_ROOT/index.html || echo 'Build timestamp not found'"
Write-Info "Build timestamp check: $buildCheck"

if ($httpTest -eq "200") {
    Write-Success "HTTP test passed!"
} else {
    Write-Warning "HTTP test returned: $httpTest"
}

# Clean up local files
Remove-Item "dist.tar.gz" -Force -ErrorAction SilentlyContinue

Write-Success "===================================="
Write-Success "   🎉 Deployment Successful! 🎉   "
Write-Success "===================================="
Write-Info "Frontend URL: http://$SERVER_IP"
Write-Info "Build Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Info "Nginx Root: $NGINX_ROOT"
Write-Success "===================================="
Write-Warning "To see the latest changes:"
Write-Host "  1. Open http://$SERVER_IP in browser"
Write-Host "  2. Hard refresh: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)"
Write-Host "  3. Or open in incognito/private mode"
Write-Host "  4. Check browser dev tools (F12) for build timestamp"
Write-Success "===================================="
Write-Info "Next deployment: .\deploy-update.ps1"
Write-Success "===================================="
