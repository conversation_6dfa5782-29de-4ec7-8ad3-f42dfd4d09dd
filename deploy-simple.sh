#!/bin/bash

# VASA前端快速部署脚本 (非Docker版本)
# 适用于直接部署到Nginx服务器
# Usage: ./deploy-simple.sh [--help] [--check] [--dry-run]

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置信息
SERVER_USER="root"
SERVER_IP="*************"
WEB_ROOT="/usr/share/nginx/html"
BACKUP_DIR="/usr/share/nginx/html/backup"

# 默认选项
DRY_RUN=false
CHECK_ONLY=false

# 帮助函数
show_help() {
    echo -e "${GREEN}====================================${NC}"
    echo -e "${GREEN}   VASA前端快速部署脚本   ${NC}"
    echo -e "${GREEN}====================================${NC}"
    echo -e "${BLUE}直接部署到Nginx，无需Docker${NC}"
    echo -e "${GREEN}====================================${NC}"
    echo ""
    echo -e "${YELLOW}用法:${NC}"
    echo "  ./deploy-simple.sh [选项]"
    echo ""
    echo -e "${YELLOW}选项:${NC}"
    echo "  --help      显示此帮助信息"
    echo "  --check     仅检查服务器状态"
    echo "  --dry-run   模拟运行，不实际部署"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  ./deploy-simple.sh           # 正常部署"
    echo "  ./deploy-simple.sh --check   # 检查服务器状态"
    echo "  ./deploy-simple.sh --dry-run # 模拟部署过程"
    echo ""
    exit 0
}

# 检查服务器状态
check_server() {
    echo -e "${BLUE}检查服务器状态...${NC}"
    echo -e "${YELLOW}服务器: ${SERVER_USER}@${SERVER_IP}${NC}"

    # 检查SSH连接
    if ssh -o ConnectTimeout=10 "$SERVER_USER@$SERVER_IP" "echo 'SSH连接正常'" 2>/dev/null; then
        echo -e "${GREEN}✅ SSH连接正常${NC}"
    else
        echo -e "${RED}❌ SSH连接失败${NC}"
        exit 1
    fi

    # 检查Nginx状态
    echo -e "${BLUE}检查Nginx状态...${NC}"
    ssh "$SERVER_USER@$SERVER_IP" "systemctl status nginx --no-pager -l"

    # 检查网站访问
    echo -e "${BLUE}检查网站访问...${NC}"
    if curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_IP" | grep -q "200"; then
        echo -e "${GREEN}✅ 网站访问正常${NC}"
    else
        echo -e "${YELLOW}⚠️  网站访问异常或需要时间启动${NC}"
    fi

    exit 0
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            ;;
        --check|-c)
            CHECK_ONLY=true
            ;;
        --dry-run|-d)
            DRY_RUN=true
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
    shift
done

# 如果只是检查服务器状态
if [[ "$CHECK_ONLY" == true ]]; then
    check_server
fi

# 显示标题
echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   VASA前端快速部署脚本   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}直接部署到Nginx，无需Docker${NC}"
if [[ "$DRY_RUN" == true ]]; then
    echo -e "${YELLOW}🔍 模拟运行模式 (不会实际部署)${NC}"
fi
echo -e "${GREEN}====================================${NC}"

# 检查是否在正确的目录
if [ ! -f "package.json" ] || [ ! -f "vite.config.ts" ]; then
    echo -e "${RED}错误: 请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 检查必要工具
echo -e "${YELLOW}[1/6] 检查必要工具...${NC}"

if ! command -v pnpm &> /dev/null; then
    echo -e "${RED}错误: pnpm 未安装，请先安装 pnpm${NC}"
    exit 1
fi

if ! command -v ssh &> /dev/null; then
    echo -e "${RED}错误: ssh 未安装${NC}"
    exit 1
fi

if ! command -v scp &> /dev/null; then
    echo -e "${RED}错误: scp 未安装${NC}"
    exit 1
fi

echo -e "${GREEN}工具检查完成!${NC}"

# 构建项目
echo -e "${YELLOW}[2/6] 构建项目...${NC}"

if [[ "$DRY_RUN" == true ]]; then
    echo -e "${BLUE}🔍 [模拟] 清理旧的构建文件...${NC}"
    echo -e "${BLUE}🔍 [模拟] 开始构建...${NC}"
    echo -e "${GREEN}🔍 [模拟] 构建成功!${NC}"
else
    echo -e "${BLUE}清理旧的构建文件...${NC}"
    rm -rf dist

    echo -e "${BLUE}开始构建...${NC}"
    NODE_ENV=production pnpm build

    if [ $? -ne 0 ]; then
        echo -e "${RED}构建失败，请检查错误信息${NC}"
        exit 1
    fi

    # 检查构建结果
    if [ ! -d "dist" ] || [ ! "$(ls -A dist)" ]; then
        echo -e "${RED}构建产物不存在或为空${NC}"
        exit 1
    fi

    echo -e "${GREEN}构建成功!${NC}"
    echo -e "${BLUE}构建文件大小: $(du -sh dist | cut -f1)${NC}"
fi

# 压缩构建文件
echo -e "${YELLOW}[3/6] 压缩构建文件...${NC}"

if [[ "$DRY_RUN" == true ]]; then
    echo -e "${BLUE}🔍 [模拟] 压缩构建文件...${NC}"
    echo -e "${GREEN}🔍 [模拟] 压缩成功!${NC}"
else
    tar -czf dist.tar.gz -C dist .

    if [ $? -ne 0 ]; then
        echo -e "${RED}压缩失败${NC}"
        exit 1
    fi

    echo -e "${GREEN}压缩成功!${NC}"
fi

# 上传文件到服务器
echo -e "${YELLOW}[4/6] 上传文件到服务器...${NC}"
echo -e "${BLUE}服务器: ${SERVER_USER}@${SERVER_IP}${NC}"

if [[ "$DRY_RUN" == true ]]; then
    echo -e "${BLUE}🔍 [模拟] 上传压缩包到服务器...${NC}"
    echo -e "${BLUE}🔍 [模拟] 上传nginx配置到服务器...${NC}"
    echo -e "${GREEN}🔍 [模拟] 上传成功!${NC}"
else
    # 上传压缩包和nginx配置
    scp dist.tar.gz ${SERVER_USER}@${SERVER_IP}:/tmp/
    scp nginx.conf ${SERVER_USER}@${SERVER_IP}:/tmp/

    if [ $? -ne 0 ]; then
        echo -e "${RED}上传失败${NC}"
        exit 1
    fi

    echo -e "${GREEN}上传成功!${NC}"
fi

# 在服务器上部署
echo -e "${YELLOW}[5/6] 在服务器上部署...${NC}"

if [[ "$DRY_RUN" == true ]]; then
    echo -e "${BLUE}🔍 [模拟] 连接到服务器...${NC}"
    echo -e "${BLUE}🔍 [模拟] 检查并安装Nginx...${NC}"
    echo -e "${BLUE}🔍 [模拟] 备份当前文件...${NC}"
    echo -e "${BLUE}🔍 [模拟] 清理旧文件...${NC}"
    echo -e "${BLUE}🔍 [模拟] 部署新文件...${NC}"
    echo -e "${BLUE}🔍 [模拟] 设置权限...${NC}"
    echo -e "${BLUE}🔍 [模拟] 更新Nginx配置...${NC}"
    echo -e "${BLUE}🔍 [模拟] 清理临时文件...${NC}"
    echo -e "${GREEN}🔍 [模拟] 部署完成${NC}"
else
    ssh ${SERVER_USER}@${SERVER_IP} << 'EOF'
echo '=== 开始部署 ==='

# 检查并安装Nginx
if ! command -v nginx &> /dev/null; then
    echo 'Installing Nginx...'
    apt update
    apt install nginx -y
    systemctl start nginx
    systemctl enable nginx
fi

# 备份当前文件
echo '=== 备份当前文件 ==='
if [ -d "/usr/share/nginx/html" ] && [ "$(ls -A /usr/share/nginx/html)" ]; then
    BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "/usr/share/nginx/html/backups"
    cp -r /usr/share/nginx/html/* "/usr/share/nginx/html/backups/$BACKUP_NAME/" 2>/dev/null || true
    echo "已备份到: /usr/share/nginx/html/backups/$BACKUP_NAME"
fi

# 清理旧文件（保留backups目录）
echo '=== 清理旧文件 ==='
find /usr/share/nginx/html -mindepth 1 -maxdepth 1 ! -name 'backups' -exec rm -rf {} \;

# 解压新文件
echo '=== 部署新文件 ==='
cd /usr/share/nginx/html
tar -xzf /tmp/dist.tar.gz

# 设置权限
echo '=== 设置权限 ==='
chown -R nginx:nginx /usr/share/nginx/html 2>/dev/null || chown -R www-data:www-data /usr/share/nginx/html 2>/dev/null || true
chmod -R 755 /usr/share/nginx/html

# 更新Nginx配置
echo '=== 更新Nginx配置 ==='
cp /tmp/nginx.conf /etc/nginx/sites-available/default
nginx -t

if [ $? -eq 0 ]; then
    systemctl reload nginx
    echo 'Nginx配置更新成功'
else
    echo 'Nginx配置测试失败，保持原配置'
fi

# 清理临时文件
rm -f /tmp/dist.tar.gz /tmp/nginx.conf

echo '=== 部署完成 ==='
echo "网站文件数量: $(find /usr/share/nginx/html -type f | wc -l)"
echo "网站目录大小: $(du -sh /usr/share/nginx/html | cut -f1)"

EOF

    if [ $? -ne 0 ]; then
        echo -e "${RED}服务器部署失败${NC}"
        exit 1
    fi
fi

# 验证部署
echo -e "${YELLOW}[6/6] 验证部署...${NC}"

if [[ "$DRY_RUN" == true ]]; then
    echo -e "${BLUE}🔍 [模拟] 检查Nginx状态...${NC}"
    echo -e "${BLUE}🔍 [模拟] 检查网站访问...${NC}"
    echo -e "${GREEN}🔍 [模拟] 网站访问正常${NC}"
    echo -e "${BLUE}🔍 [模拟] 清理本地文件...${NC}"
else
    # 检查服务器状态
    echo -e "${BLUE}检查Nginx状态...${NC}"
    ssh ${SERVER_USER}@${SERVER_IP} "systemctl status nginx --no-pager -l"

    echo -e "${BLUE}检查网站访问...${NC}"
    if curl -s -o /dev/null -w "%{http_code}" http://${SERVER_IP} | grep -q "200"; then
        echo -e "${GREEN}✅ 网站访问正常${NC}"
    else
        echo -e "${YELLOW}⚠️  网站可能需要几秒钟启动${NC}"
    fi

    # 清理本地文件
    rm -f dist.tar.gz
fi

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   部署完成!   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}前端访问地址: http://${SERVER_IP}${NC}"
echo -e "${BLUE}后端API地址: http://${SERVER_IP}/api${NC}"
echo -e "${GREEN}====================================${NC}"
