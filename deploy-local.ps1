# Local Docker Build and Deploy Script
# This script builds the Docker image locally and uploads it to the server

param(
    [string]$User = "root",
    [switch]$Help
)

$SERVER_IP = "*************"
$CONTAINER_NAME = "vasa-frontend"
$IMAGE_NAME = "vasa-frontend:latest"

function Write-Success { param([string]$Text) Write-Host $Text -ForegroundColor Green }
function Write-Warning { param([string]$Text) Write-Host $Text -ForegroundColor Yellow }
function Write-Error { param([string]$Text) Write-Host $Text -ForegroundColor Red }
function Write-Info { param([string]$Text) Write-Host $Text -ForegroundColor Cyan }

if ($Help) {
    Write-Host @"
Local Docker Build and Deploy Script

Usage:
    .\deploy-local.ps1                # Use root user (default)
    .\deploy-local.ps1 -User husky    # Use husky user
    .\deploy-local.ps1 -Help          # Show help

This script will:
    1. Build Docker image locally
    2. Save image to tar file
    3. Upload to server
    4. Load and run on server
"@
    exit 0
}

Write-Success "===================================="
Write-Success "   Local Docker Build & Deploy   "
Write-Success "===================================="
Write-Info "Building locally and uploading to: $User@$SERVER_IP"
Write-Success "===================================="

# Step 1: Check Docker locally
Write-Warning "Step 1: Checking local Docker..."
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Error "Docker not found locally. Please install Docker Desktop"
    exit 1
}

$dockerStatus = docker version --format "{{.Server.Version}}" 2>$null
if (!$dockerStatus) {
    Write-Error "Docker daemon not running. Please start Docker Desktop"
    exit 1
}

Write-Success "Local Docker is running (version: $dockerStatus)"

# Step 2: Build project
Write-Warning "Step 2: Building frontend project..."
if (!(Test-Path "dist")) {
    Write-Info "Building project..."
    pnpm build
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Frontend build failed"
        exit 1
    }
} else {
    Write-Success "Dist directory already exists"
}

# Step 3: Build Docker image locally
Write-Warning "Step 3: Building Docker image locally..."
docker build -t $IMAGE_NAME .

if ($LASTEXITCODE -ne 0) {
    Write-Error "Docker build failed locally"
    exit 1
}

Write-Success "Docker image built successfully"

# Step 4: Save Docker image to tar file
Write-Warning "Step 4: Saving Docker image to file..."
docker save -o vasa-frontend.tar $IMAGE_NAME

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to save Docker image"
    exit 1
}

Write-Success "Docker image saved to vasa-frontend.tar"

# Step 5: Upload image to server
Write-Warning "Step 5: Uploading Docker image to server..."
Write-Info "This may take a few minutes depending on your network speed..."

scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null vasa-frontend.tar ${User}@${SERVER_IP}:/tmp/

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to upload Docker image"
    exit 1
}

Write-Success "Docker image uploaded successfully"

# Step 6: Stop conflicting services on server
Write-Warning "Step 6: Preparing server environment..."
ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP @"
# Stop nginx if running
systemctl stop nginx 2>/dev/null || true
systemctl disable nginx 2>/dev/null || true

# Stop and remove old container
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# Remove old image
docker rmi $IMAGE_NAME 2>/dev/null || true

echo 'Server environment prepared'
"@

# Step 7: Load and run Docker image on server
Write-Warning "Step 7: Loading and running Docker image on server..."
ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP @"
echo 'Loading Docker image...'
docker load -i /tmp/vasa-frontend.tar

if [ \$? -eq 0 ]; then
    echo 'Starting container...'
    docker run -d --name $CONTAINER_NAME --restart always -p 80:80 $IMAGE_NAME
    
    if [ \$? -eq 0 ]; then
        echo 'Container started successfully'
        # Clean up uploaded tar file
        rm /tmp/vasa-frontend.tar
    else
        echo 'Failed to start container'
        exit 1
    fi
else
    echo 'Failed to load Docker image'
    exit 1
fi
"@

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to deploy on server"
    exit 1
}

# Step 8: Verify deployment
Write-Warning "Step 8: Verifying deployment..."
Start-Sleep -Seconds 5

$containerStatus = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "docker ps --filter name=$CONTAINER_NAME --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'"

if ($containerStatus -like "*$CONTAINER_NAME*") {
    Write-Success "Container is running successfully!"
    Write-Info "Container details:"
    Write-Host $containerStatus
    
    # Test HTTP response
    Write-Info "Testing HTTP response..."
    Start-Sleep -Seconds 3
    $httpTest = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "curl -s -o /dev/null -w '%{http_code}' http://localhost:80 2>/dev/null || echo 'HTTP test failed'"
    Write-Info "HTTP response code: $httpTest"
    
    if ($httpTest -eq "200") {
        Write-Success "HTTP test passed! Website is accessible"
    } else {
        Write-Warning "HTTP test returned: $httpTest"
    }
    
} else {
    Write-Error "Container failed to start"
    Write-Info "Checking container logs..."
    ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "docker logs $CONTAINER_NAME 2>&1 || echo 'No logs available'"
    exit 1
}

# Clean up local files
Remove-Item "vasa-frontend.tar" -Force -ErrorAction SilentlyContinue

Write-Success "===================================="
Write-Success "   🎉 Local Build & Deploy Successful! 🎉   "
Write-Success "===================================="
Write-Info "Frontend URL: http://$SERVER_IP"
Write-Info "Container Name: $CONTAINER_NAME"
Write-Info "Image: $IMAGE_NAME"
Write-Success "===================================="
Write-Warning "Next time you can use:"
Write-Host "  .\deploy.ps1 -Quick    # For quick updates"
Write-Host "  .\deploy-local.ps1     # For full rebuilds"
Write-Success "===================================="
