{"name": "react-jsherp", "version": "0.1.0", "private": true, "dependencies": {"@amap/amap-react": "^0.1.5", "@ant-design/icons": "^5.2.6", "@ant-design/plots": "^2.3.3", "@ant-design/pro-components": "^2.6.43", "ahooks": "^3.7.8", "antd": "^5.11.5", "antd-style": "^3.7.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "formik": "^2.4.5", "i18next": "^23.7.7", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "nanoid": "^5.1.5", "numeral": "^2.0.6", "pinyin-pro": "^3.26.0", "query-string": "^8.1.0", "react": "^18.2.0", "react-bmapgl": "^1.0.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-router-dom": "^6.20.1", "react-sortable-hoc": "^2.0.0", "reactflow": "^11.11.4", "styled-components": "^6.1.1", "swr": "^2.2.4", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "yup": "^1.3.2", "zustand": "^4.4.7"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^22.15.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "rollup-plugin-visualizer": "^5.14.0", "terser": "^5.39.0", "typescript": "^5.8.3", "vite": "^5.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-compression2": "^1.3.3"}, "scripts": {"dev": "vite", "start": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:react-hooks/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["react", "@typescript-eslint", "react-hooks", "prettier"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"]}}