#!/bin/bash

# VASA Deploy Script for macOS/Linux
# Direct port of deploy-simple.ps1 to Bash

SERVER_USER="root"
SERVER_IP="*************"

# Color output functions
print_success() { echo -e "\033[32m$1\033[0m"; }
print_warning() { echo -e "\033[33m$1\033[0m"; }
print_error() { echo -e "\033[31m$1\033[0m"; }
print_info() { echo -e "\033[34m$1\033[0m"; }

# Help function
show_help() {
    print_success "VASA Deploy Script"
    print_info "Usage: ./deploy-macos.sh [--help] [--check]"
    exit 0
}

# Check server status
check_server() {
    print_info "Checking server status..."
    ssh "$SERVER_USER@$SERVER_IP" "systemctl status nginx"
    exit 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --help|-h)
            show_help
            ;;
        --check|-c)
            check_server
            ;;
        *)
            print_error "Unknown option: $1"
            print_info "Use --help for usage information"
            exit 1
            ;;
    esac
    shift
done

print_success "Starting deployment..."

# Check if running from project root
if [[ ! -f "package.json" ]]; then
    print_error "Run from project root"
    exit 1
fi

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    print_error "pnpm not found"
    exit 1
fi

print_warning "Building..."

# Clean previous build and cache
if [[ -d "dist" ]]; then
    rm -rf dist
    print_info "Cleaned previous dist directory"
fi

# Clean pnpm cache and node_modules cache
print_info "Cleaning build cache..."
pnpm store prune
rm -rf node_modules/.vite

# Build the project
print_info "Starting fresh build..."
pnpm build
if [[ $? -ne 0 ]]; then
    print_error "Build failed"
    exit 1
fi

# Verify build output
if [[ ! -d "dist" ]] || [[ ! -f "dist/index.html" ]]; then
    print_error "Build output verification failed"
    exit 1
fi

print_success "Build completed successfully"

print_warning "Compressing..."

# Clean previous archive
if [[ -f "dist.tar.gz" ]]; then
    rm -f dist.tar.gz
fi

# Create compressed archive
tar -czf dist.tar.gz -C dist .

print_warning "Uploading..."

# Upload files
scp dist.tar.gz "$SERVER_USER@$SERVER_IP:/tmp/"
scp nginx.conf "$SERVER_USER@$SERVER_IP:/tmp/"

print_warning "Deploying..."

# Deploy on server with enhanced logging
print_info "Creating backup directory..."
ssh "$SERVER_USER@$SERVER_IP" "mkdir -p /usr/share/nginx/html/backups"

print_info "Backing up current deployment..."
ssh "$SERVER_USER@$SERVER_IP" "if [ -f /usr/share/nginx/html/index.html ]; then tar -czf /usr/share/nginx/html/backups/backup-\$(date +%Y%m%d-%H%M%S).tar.gz -C /usr/share/nginx/html --exclude=backups .; fi"

print_info "Clearing old files..."
ssh "$SERVER_USER@$SERVER_IP" "find /usr/share/nginx/html -mindepth 1 -maxdepth 1 ! -name backups -exec rm -rf {} \;"

print_info "Extracting new files..."
ssh "$SERVER_USER@$SERVER_IP" "cd /usr/share/nginx/html && tar -xzf /tmp/dist.tar.gz"

print_info "Setting permissions..."
ssh "$SERVER_USER@$SERVER_IP" "chmod -R 755 /usr/share/nginx/html"

print_info "Updating nginx configuration..."
ssh "$SERVER_USER@$SERVER_IP" "cp /tmp/nginx.conf /etc/nginx/sites-available/default"

print_info "Testing nginx configuration..."
ssh "$SERVER_USER@$SERVER_IP" "nginx -t"
if [[ $? -ne 0 ]]; then
    print_error "Nginx configuration test failed"
    exit 1
fi

print_info "Reloading nginx..."
ssh "$SERVER_USER@$SERVER_IP" "systemctl reload nginx"

print_info "Verifying deployment..."
ssh "$SERVER_USER@$SERVER_IP" "ls -la /usr/share/nginx/html/ | head -10"

print_info "Cleaning up temporary files..."
ssh "$SERVER_USER@$SERVER_IP" "rm -f /tmp/dist.tar.gz /tmp/nginx.conf"

print_success "Deployment completed!"
print_info "Frontend: http://$SERVER_IP"
print_info "API: http://$SERVER_IP/api"
print_warning "If you still see old content, try:"
print_info "1. Hard refresh browser (Ctrl+F5 or Cmd+Shift+R)"
print_info "2. Clear browser cache"
print_info "3. Open in incognito/private mode"

# Clean up local files
if [[ -f "dist.tar.gz" ]]; then
    rm -f dist.tar.gz
fi
