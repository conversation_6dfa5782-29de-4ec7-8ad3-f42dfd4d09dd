#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置信息
SERVER_USER="husky"
SERVER_IP="*************"
SERVER_PASSWORD='54188yang.'
CONTAINER_NAME="vasa-frontend"

# 显示标题
echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   VASA管理系统快速部署脚本   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}仅更新前端代码，不重新构建Docker镜像${NC}"
echo -e "${GREEN}====================================${NC}"

# 检查pnpm是否安装
if ! command -v pnpm &> /dev/null; then
    echo -e "${RED}pnpm 未安装，请先安装 pnpm${NC}"
    exit 1
fi

# 检查sshpass是否安装
if ! command -v sshpass &> /dev/null; then
    echo -e "${RED}sshpass 未安装，请先运行完整部署脚本${NC}"
    exit 1
fi

# 构建项目
echo -e "${YELLOW}[1/4] 正在构建项目...${NC}"
pnpm build

if [ $? -ne 0 ]; then
    echo -e "${RED}构建失败，请检查错误信息${NC}"
    exit 1
fi

echo -e "${GREEN}构建成功!${NC}"

# 压缩dist目录
echo -e "${YELLOW}[2/4] 正在压缩dist目录...${NC}"
tar -czf dist.tar.gz -C dist .

if [ $? -ne 0 ]; then
    echo -e "${RED}压缩失败${NC}"
    exit 1
fi

echo -e "${GREEN}压缩成功!${NC}"

# 上传并更新容器内容
echo -e "${YELLOW}[3/4] 正在更新容器内容...${NC}"

# 上传新的dist文件
sshpass -p "${SERVER_PASSWORD}" scp dist.tar.gz ${SERVER_USER}@${SERVER_IP}:/tmp/

# 更新容器内的文件
sshpass -p "${SERVER_PASSWORD}" ssh ${SERVER_USER}@${SERVER_IP} "
    # 将新文件复制到容器中
    docker cp /tmp/dist.tar.gz ${CONTAINER_NAME}:/tmp/
    
    # 在容器内更新文件
    docker exec ${CONTAINER_NAME} sh -c '
        cd /usr/share/nginx/html
        rm -rf *
        tar -xzf /tmp/dist.tar.gz
        rm /tmp/dist.tar.gz
        nginx -s reload
    '
    
    # 清理临时文件
    rm /tmp/dist.tar.gz
    
    echo '容器内容更新完成'
"

if [ $? -ne 0 ]; then
    echo -e "${RED}更新失败${NC}"
    exit 1
fi

echo -e "${GREEN}更新成功!${NC}"

# 验证部署
echo -e "${YELLOW}[4/4] 正在验证部署...${NC}"

sleep 2  # 等待nginx重载

# 检查容器状态
CONTAINER_STATUS=$(sshpass -p "${SERVER_PASSWORD}" ssh ${SERVER_USER}@${SERVER_IP} "docker ps --filter name=${CONTAINER_NAME} --format 'table {{.Status}}' | tail -n +2")

if [[ $CONTAINER_STATUS == *"Up"* ]]; then
    echo -e "${GREEN}容器运行正常!${NC}"
else
    echo -e "${RED}容器状态异常${NC}"
    exit 1
fi

# 清理本地文件
rm dist.tar.gz

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   🚀 快速部署成功! 🚀   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}前端访问地址: http://${SERVER_IP}${NC}"
echo -e "${GREEN}====================================${NC}"
