#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置信息
SERVER_USER="root"
SERVER_IP="*************"
WEB_ROOT="/var/www/vasa"
DOMAIN="vasa.work"

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   VASA快速更新脚本(Nginx)   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}仅更新前端文件，不重新配置Nginx${NC}"
echo -e "${GREEN}====================================${NC}"

# 检查是否在正确的目录
if [ ! -f "package.json" ] || [ ! -f "vite.config.ts" ]; then
    echo -e "${RED}请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 检查pnpm
if ! command -v pnpm &> /dev/null; then
    echo -e "${RED}pnpm 未安装，请先安装 pnpm${NC}"
    exit 1
fi

# 本地构建
echo -e "${YELLOW}[1/4] 本地构建项目...${NC}"
echo -e "${BLUE}使用生产环境配置构建...${NC}"

# 清理旧的构建文件
rm -rf dist

# 设置环境变量并构建
NODE_ENV=production pnpm build

if [ $? -ne 0 ]; then
    echo -e "${RED}本地构建失败，请检查错误信息${NC}"
    exit 1
fi

# 检查构建结果
if [ ! -d "dist" ] || [ ! "$(ls -A dist)" ]; then
    echo -e "${RED}构建产物不存在或为空${NC}"
    exit 1
fi

echo -e "${GREEN}本地构建成功!${NC}"
echo -e "${BLUE}构建文件大小: $(du -sh dist | cut -f1)${NC}"

# 压缩构建文件
echo -e "${YELLOW}[2/4] 压缩构建文件...${NC}"
tar -czf dist.tar.gz -C dist .

if [ $? -ne 0 ]; then
    echo -e "${RED}压缩失败${NC}"
    exit 1
fi

echo -e "${GREEN}压缩成功! 压缩包大小: $(du -sh dist.tar.gz | cut -f1)${NC}"

# 上传并更新文件
echo -e "${YELLOW}[3/4] 上传并更新文件...${NC}"

# 上传构建文件
scp dist.tar.gz ${SERVER_USER}@${SERVER_IP}:/tmp/

if [ $? -ne 0 ]; then
    echo -e "${RED}文件上传失败${NC}"
    exit 1
fi

# 在服务器上更新文件
ssh ${SERVER_USER}@${SERVER_IP} "
    # 备份当前文件
    echo '备份当前文件...'
    if [ -d '${WEB_ROOT}/backup' ]; then
        rm -rf ${WEB_ROOT}/backup
    fi
    if [ -d '${WEB_ROOT}/current' ]; then
        cp -r ${WEB_ROOT}/current ${WEB_ROOT}/backup
        echo '已备份当前版本'
    fi
    
    # 清空当前目录并解压新文件
    echo '更新文件...'
    rm -rf ${WEB_ROOT}/current/*
    tar -xzf /tmp/dist.tar.gz -C ${WEB_ROOT}/current/
    rm /tmp/dist.tar.gz
    
    # 设置文件权限
    chown -R www-data:www-data ${WEB_ROOT}
    chmod -R 755 ${WEB_ROOT}
    
    echo '文件更新完成'
"

if [ $? -ne 0 ]; then
    echo -e "${RED}服务器更新失败${NC}"
    exit 1
fi

echo -e "${GREEN}服务器更新成功!${NC}"

# 验证更新
echo -e "${YELLOW}[4/4] 验证更新状态...${NC}"

sleep 2  # 等待文件更新

# 测试网站访问
echo -e "${BLUE}测试域名访问...${NC}"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 http://${DOMAIN}/ 2>/dev/null)
if [ "$HTTP_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 域名访问正常! (http://${DOMAIN})${NC}"
else
    echo -e "${YELLOW}⚠️  域名返回状态码: $HTTP_STATUS，测试IP访问...${NC}"
    HTTP_STATUS_IP=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 http://${SERVER_IP}/ 2>/dev/null)
    if [ "$HTTP_STATUS_IP" = "200" ]; then
        echo -e "${GREEN}✅ IP访问正常! (http://${SERVER_IP})${NC}"
        echo -e "${YELLOW}⚠️  请检查域名DNS解析配置${NC}"
    else
        echo -e "${YELLOW}⚠️  IP返回状态码: $HTTP_STATUS_IP${NC}"
    fi
fi

# 清理本地临时文件
rm dist.tar.gz

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   🎉 快速更新完成! 🎉   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}前端访问地址: http://${DOMAIN}${NC}"
echo -e "${BLUE}后端API地址: http://${DOMAIN}:3000${NC}"
echo -e "${BLUE}备用IP访问: http://${SERVER_IP}${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${YELLOW}更新信息:${NC}"
echo -e "  更新方式: 直接替换文件"
echo -e "  网站根目录: ${WEB_ROOT}/current"
echo -e "  域名: ${DOMAIN}"
echo -e "${GREEN}====================================${NC}"
