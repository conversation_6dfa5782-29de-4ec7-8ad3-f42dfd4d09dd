# Direct Nginx Deployment Script (No Docker)
param(
    [string]$User = "root",
    [switch]$Help
)

$SERVER_IP = "*************"
$WEB_ROOT = "/usr/share/nginx/html"
$NGINX_CONFIG = "/etc/nginx/sites-available/default"

function Write-Success { param([string]$Text) Write-Host $Text -ForegroundColor Green }
function Write-Warning { param([string]$Text) Write-Host $Text -ForegroundColor Yellow }
function Write-Error { param([string]$Text) Write-Host $Text -ForegroundColor Red }
function Write-Info { param([string]$Text) Write-Host $Text -ForegroundColor Cyan }

if ($Help) {
    Write-Host @"
Direct Nginx Deployment Script

Usage:
    .\deploy-nginx.ps1                # Use root user (default)
    .\deploy-nginx.ps1 -User husky    # Use husky user
    .\deploy-nginx.ps1 -Help          # Show help

This script will:
    1. Build frontend project
    2. Upload files to server
    3. Configure Nginx directly
    4. Start Nginx service

No Docker required!
"@
    exit 0
}

Write-Success "===================================="
Write-Success "   Direct Nginx Deployment   "
Write-Success "===================================="
Write-Info "Deploying to: $User@$SERVER_IP"
Write-Info "Web root: $WEB_ROOT"
Write-Success "===================================="

# Step 1: Build project
Write-Warning "Step 1: Building frontend project..."
if (!(Get-Command pnpm -ErrorAction SilentlyContinue)) {
    Write-Error "pnpm not found. Please install pnpm first"
    exit 1
}

pnpm build

if ($LASTEXITCODE -ne 0) {
    Write-Error "Frontend build failed"
    exit 1
}

Write-Success "Frontend build successful"

# Step 2: Compress and upload files
Write-Warning "Step 2: Uploading files to server..."
if (Test-Path "dist.tar.gz") {
    Remove-Item "dist.tar.gz" -Force
}

tar -czf dist.tar.gz -C dist .
scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null dist.tar.gz ${User}@${SERVER_IP}:/tmp/
scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null nginx.conf ${User}@${SERVER_IP}:/tmp/

if ($LASTEXITCODE -ne 0) {
    Write-Error "File upload failed"
    exit 1
}

Write-Success "Files uploaded successfully"

# Step 3: Setup Nginx on server
Write-Warning "Step 3: Setting up Nginx on server..."

ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP @"
# Stop any Docker containers using port 80
docker stop vasa-frontend 2>/dev/null || true
docker rm vasa-frontend 2>/dev/null || true

# Install nginx if not installed
if ! command -v nginx &> /dev/null; then
    echo 'Installing Nginx...'
    apt-get update && apt-get install -y nginx
fi

# Create backup of current files
if [ -d "$WEB_ROOT" ] && [ "\$(ls -A $WEB_ROOT)" ]; then
    BACKUP_NAME="backup_\$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$WEB_ROOT/backups"
    cp -r $WEB_ROOT/* "$WEB_ROOT/backups/\$BACKUP_NAME/" 2>/dev/null || true
    echo "已备份到: $WEB_ROOT/backups/\$BACKUP_NAME"
fi

# Create web root directory and deploy files
mkdir -p $WEB_ROOT
cd $WEB_ROOT
# Clear current files (except backups)
find $WEB_ROOT -mindepth 1 -maxdepth 1 ! -name 'backups' -exec rm -rf {} \;
tar -xzf /tmp/dist.tar.gz
chown -R nginx:nginx $WEB_ROOT 2>/dev/null || chown -R www-data:www-data $WEB_ROOT 2>/dev/null || true
chmod -R 755 $WEB_ROOT

# Use project's nginx configuration
cp /tmp/nginx.conf $NGINX_CONFIG

# Enable the site (remove default if exists)
rm -f /etc/nginx/sites-enabled/default

# Test and start Nginx
nginx -t
if [ \$? -ne 0 ]; then
    echo 'Nginx configuration test failed'
    exit 1
fi

systemctl enable nginx
systemctl restart nginx

if [ \$? -ne 0 ]; then
    echo 'Failed to start Nginx'
    exit 1
fi

rm /tmp/dist.tar.gz /tmp/nginx.conf
echo 'Nginx deployment completed successfully'
"@

if ($LASTEXITCODE -ne 0) {
    Write-Error "Nginx setup failed"
    exit 1
}

Write-Success "Nginx setup completed"

# Step 4: Verify deployment
Write-Warning "Step 4: Verifying deployment..."
Start-Sleep -Seconds 3

$nginxStatus = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "systemctl is-active nginx"
Write-Info "Nginx status: $nginxStatus"

if ($nginxStatus -eq "active") {
    Write-Success "Nginx is running!"

    $httpTest = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "curl -s -o /dev/null -w '%{http_code}' http://localhost:80 2>/dev/null || echo 'HTTP test failed'"
    Write-Info "HTTP response code: $httpTest"

    if ($httpTest -eq "200") {
        Write-Success "HTTP test passed! Website is accessible"
    } else {
        Write-Warning "HTTP test returned: $httpTest"
    }

    $fileCheck = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "ls -la $WEB_ROOT/index.html 2>/dev/null && echo 'Files deployed' || echo 'Files missing'"
    Write-Info "File check: $fileCheck"

} else {
    Write-Error "Nginx failed to start"
    exit 1
}

Remove-Item "dist.tar.gz" -Force -ErrorAction SilentlyContinue

Write-Success "===================================="
Write-Success "   🎉 Nginx Deployment Successful! 🎉   "
Write-Success "===================================="
Write-Info "Frontend URL: http://$SERVER_IP"
Write-Info "Web Root: $WEB_ROOT"
Write-Success "===================================="
