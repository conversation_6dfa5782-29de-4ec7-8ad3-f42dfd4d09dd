#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置信息
SERVER_USER="root"
SERVER_IP="*************"
SERVER_PATH="/root/manager-web"

# 显示标题
echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   无Docker部署方案   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}使用Nginx直接部署静态文件${NC}"
echo -e "${GREEN}====================================${NC}"

# 检查本地构建
if [ ! -d "dist" ] || [ ! "$(ls -A dist)" ]; then
    echo -e "${YELLOW}dist目录不存在，开始本地构建...${NC}"
    pnpm build
    if [ $? -ne 0 ]; then
        echo -e "${RED}本地构建失败${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}发现dist目录，继续部署...${NC}"

# 压缩构建文件
echo -e "${YELLOW}[1/4] 压缩构建文件...${NC}"
tar -czf dist.tar.gz -C dist .

# 上传文件
echo -e "${YELLOW}[2/4] 上传文件到服务器...${NC}"
scp dist.tar.gz ${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/
scp nginx.conf ${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/

# 在服务器上安装和配置Nginx
echo -e "${YELLOW}[3/4] 在服务器上配置Nginx...${NC}"

ssh ${SERVER_USER}@${SERVER_IP} "
    cd ${SERVER_PATH}

    # 安装Nginx（如果未安装）
    if ! command -v nginx &> /dev/null; then
        echo '安装Nginx...'
        yum install -y epel-release
        yum install -y nginx
    fi

    # 停止可能运行的Docker容器
    docker stop vasa-frontend 2>/dev/null || true
    docker rm vasa-frontend 2>/dev/null || true

    # 创建网站目录
    mkdir -p /var/www/vasa-frontend

    # 解压网站文件
    tar -xzf dist.tar.gz -C /var/www/vasa-frontend/
    rm dist.tar.gz

    # 修改Nginx配置文件
    cat > /etc/nginx/conf.d/vasa-frontend.conf << 'EOF'
server {
    listen 80;
    server_name vasa.work www.vasa.work *************;

    root /var/www/vasa-frontend;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;

    # 缓存静态资源
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
        expires 7d;
        add_header Cache-Control \"public, max-age=604800, immutable\";
    }

    # 处理前端路由
    location / {
        try_files \$uri \$uri/ /index.html;
        add_header Cache-Control \"no-cache, no-store, must-revalidate\";
        add_header Pragma \"no-cache\";
        add_header Expires \"0\";
    }

    # 错误页面
    error_page 404 /index.html;
}
EOF

    # 删除默认配置（如果存在）
    rm -f /etc/nginx/conf.d/default.conf

    # 测试Nginx配置
    nginx -t
    if [ \$? -ne 0 ]; then
        echo 'Nginx配置测试失败'
        exit 1
    fi

    # 启动并启用Nginx
    systemctl enable nginx
    systemctl restart nginx

    # 检查Nginx状态
    systemctl status nginx --no-pager

    echo 'Nginx配置完成'
"

if [ $? -ne 0 ]; then
    echo -e "${RED}服务器配置失败${NC}"
    exit 1
fi

# 验证部署
echo -e "${YELLOW}[4/4] 验证部署...${NC}"

sleep 3

# 检查Nginx状态
NGINX_STATUS=$(ssh ${SERVER_USER}@${SERVER_IP} "systemctl is-active nginx")
if [ "$NGINX_STATUS" = "active" ]; then
    echo -e "${GREEN}✅ Nginx运行正常${NC}"
else
    echo -e "${RED}❌ Nginx未运行${NC}"
    exit 1
fi

# 测试网站访问
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 http://${SERVER_IP}/ 2>/dev/null)
if [ "$HTTP_STATUS" = "200" ]; then
    echo -e "${GREEN}✅ 网站访问正常!${NC}"
elif [ "$HTTP_STATUS" = "000" ]; then
    echo -e "${RED}✗ 网站无法访问 (连接超时)${NC}"
else
    echo -e "${YELLOW}⚠ 网站返回状态码: $HTTP_STATUS${NC}"
fi

# 清理本地文件
rm dist.tar.gz

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   🎉 部署完成! 🎉   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}前端访问地址: http://${SERVER_IP}${NC}"
echo -e "${BLUE}后端API地址: http://${SERVER_IP}:3000${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${YELLOW}部署信息:${NC}"
echo -e "  服务器: Nginx"
echo -e "  网站目录: /var/www/vasa-frontend"
echo -e "  配置文件: /etc/nginx/conf.d/vasa-frontend.conf"
echo -e "${GREEN}====================================${NC}"
echo -e "${YELLOW}常用命令:${NC}"
echo -e "  重启Nginx: ssh ${SERVER_USER}@${SERVER_IP} 'systemctl restart nginx'"
echo -e "  查看日志: ssh ${SERVER_USER}@${SERVER_IP} 'tail -f /var/log/nginx/error.log'"
echo -e "  查看状态: ssh ${SERVER_USER}@${SERVER_IP} 'systemctl status nginx'"
