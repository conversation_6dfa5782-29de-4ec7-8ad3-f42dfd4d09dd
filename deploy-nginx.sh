#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置信息
SERVER_USER="root"
SERVER_IP="*************"
WEB_ROOT="/var/www/vasa"
NGINX_CONFIG="/etc/nginx/sites-available/vasa"
DOMAIN="vasa.work"

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   VASA系统Nginx部署脚本   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}域名: http://${DOMAIN}${NC}"
echo -e "${BLUE}部署路径: ${WEB_ROOT}${NC}"
echo -e "${GREEN}====================================${NC}"

# 检查是否在正确的目录
if [ ! -f "package.json" ] || [ ! -f "vite.config.ts" ]; then
    echo -e "${RED}请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 检查pnpm
if ! command -v pnpm &> /dev/null; then
    echo -e "${RED}pnpm 未安装，请先安装 pnpm${NC}"
    exit 1
fi

# 本地构建
echo -e "${YELLOW}[1/5] 本地构建项目...${NC}"
echo -e "${BLUE}使用生产环境配置构建...${NC}"

# 清理旧的构建文件
rm -rf dist

# 设置环境变量并构建
NODE_ENV=production pnpm build

if [ $? -ne 0 ]; then
    echo -e "${RED}本地构建失败，请检查错误信息${NC}"
    exit 1
fi

# 检查构建结果
if [ ! -d "dist" ] || [ ! "$(ls -A dist)" ]; then
    echo -e "${RED}构建产物不存在或为空${NC}"
    exit 1
fi

echo -e "${GREEN}本地构建成功!${NC}"
echo -e "${BLUE}构建文件大小: $(du -sh dist | cut -f1)${NC}"

# 压缩构建文件
echo -e "${YELLOW}[2/5] 压缩构建文件...${NC}"
tar -czf dist.tar.gz -C dist .

if [ $? -ne 0 ]; then
    echo -e "${RED}压缩失败${NC}"
    exit 1
fi

echo -e "${GREEN}压缩成功! 压缩包大小: $(du -sh dist.tar.gz | cut -f1)${NC}"

# 上传文件到服务器
echo -e "${YELLOW}[3/5] 上传文件到服务器...${NC}"
echo -e "${BLUE}目标服务器: ${SERVER_USER}@${SERVER_IP}:${WEB_ROOT}${NC}"

# 上传构建文件
scp dist.tar.gz ${SERVER_USER}@${SERVER_IP}:/tmp/

if [ $? -ne 0 ]; then
    echo -e "${RED}文件上传失败${NC}"
    exit 1
fi

echo -e "${GREEN}文件上传成功!${NC}"

# 在服务器上部署
echo -e "${YELLOW}[4/5] 在服务器上部署...${NC}"

ssh ${SERVER_USER}@${SERVER_IP} "
    # 创建网站目录
    echo '创建网站目录...'
    mkdir -p ${WEB_ROOT}
    
    # 备份旧文件（如果存在）
    if [ -d '${WEB_ROOT}/backup' ]; then
        rm -rf ${WEB_ROOT}/backup
    fi
    if [ -d '${WEB_ROOT}/current' ]; then
        mv ${WEB_ROOT}/current ${WEB_ROOT}/backup
        echo '已备份旧版本文件'
    fi
    
    # 解压新文件
    echo '解压新文件...'
    mkdir -p ${WEB_ROOT}/current
    tar -xzf /tmp/dist.tar.gz -C ${WEB_ROOT}/current/
    rm /tmp/dist.tar.gz
    
    # 设置文件权限
    chown -R www-data:www-data ${WEB_ROOT}
    chmod -R 755 ${WEB_ROOT}
    
    # 创建Nginx配置文件
    echo '配置Nginx...'
    cat > ${NGINX_CONFIG} << 'EOF'
server {
    listen 80;
    server_name ${DOMAIN} www.${DOMAIN} ${SERVER_IP};
    
    root ${WEB_ROOT}/current;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control \"public, immutable\";
        try_files \$uri =404;
    }
    
    # SPA路由支持
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # API代理（如果需要）
    location /api/ {
        proxy_pass http://localhost:3000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 安全头
    add_header X-Frame-Options \"SAMEORIGIN\" always;
    add_header X-Content-Type-Options \"nosniff\" always;
    add_header X-XSS-Protection \"1; mode=block\" always;
    
    # 隐藏Nginx版本
    server_tokens off;
}
EOF
    
    # 替换配置文件中的变量
    sed -i 's/\${DOMAIN}/${DOMAIN}/g' ${NGINX_CONFIG}
    sed -i 's/\${WEB_ROOT}/$(echo ${WEB_ROOT} | sed 's/\//\\\//g')/g' ${NGINX_CONFIG}
    sed -i 's/\${SERVER_IP}/${SERVER_IP}/g' ${NGINX_CONFIG}
    
    # 启用站点
    ln -sf ${NGINX_CONFIG} /etc/nginx/sites-enabled/vasa
    
    # 删除默认站点（如果存在）
    rm -f /etc/nginx/sites-enabled/default
    
    # 测试Nginx配置
    echo '测试Nginx配置...'
    nginx -t
    
    if [ \$? -ne 0 ]; then
        echo 'Nginx配置测试失败'
        exit 1
    fi
    
    # 重载Nginx
    echo '重载Nginx...'
    systemctl reload nginx
    
    if [ \$? -ne 0 ]; then
        echo 'Nginx重载失败'
        exit 1
    fi
    
    echo '部署完成'
"

if [ $? -ne 0 ]; then
    echo -e "${RED}服务器部署失败${NC}"
    exit 1
fi

echo -e "${GREEN}服务器部署成功!${NC}"

# 验证部署
echo -e "${YELLOW}[5/5] 验证部署状态...${NC}"

sleep 3  # 等待Nginx重载

# 检查Nginx状态
NGINX_STATUS=$(ssh ${SERVER_USER}@${SERVER_IP} "systemctl is-active nginx")

if [ "$NGINX_STATUS" = "active" ]; then
    echo -e "${GREEN}✅ Nginx服务运行正常!${NC}"
    
    # 测试网站访问
    echo -e "${BLUE}测试域名访问...${NC}"
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 http://${DOMAIN}/ 2>/dev/null)
    if [ "$HTTP_STATUS" = "200" ]; then
        echo -e "${GREEN}✅ 域名访问正常! (http://${DOMAIN})${NC}"
    else
        echo -e "${YELLOW}⚠️  域名返回状态码: $HTTP_STATUS，测试IP访问...${NC}"
        HTTP_STATUS_IP=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 http://${SERVER_IP}/ 2>/dev/null)
        if [ "$HTTP_STATUS_IP" = "200" ]; then
            echo -e "${GREEN}✅ IP访问正常! (http://${SERVER_IP})${NC}"
            echo -e "${YELLOW}⚠️  请检查域名DNS解析配置${NC}"
        else
            echo -e "${YELLOW}⚠️  IP返回状态码: $HTTP_STATUS_IP${NC}"
        fi
    fi
else
    echo -e "${RED}❌ Nginx服务未运行${NC}"
    echo -e "${YELLOW}查看Nginx状态:${NC}"
    ssh ${SERVER_USER}@${SERVER_IP} "systemctl status nginx"
    exit 1
fi

# 清理本地临时文件
rm dist.tar.gz

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   🎉 部署完成! 🎉   ${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${BLUE}前端访问地址: http://${DOMAIN}${NC}"
echo -e "${BLUE}后端API地址: http://${DOMAIN}:3000${NC}"
echo -e "${BLUE}备用IP访问: http://${SERVER_IP}${NC}"
echo -e "${GREEN}====================================${NC}"
echo -e "${YELLOW}部署信息:${NC}"
echo -e "  网站根目录: ${WEB_ROOT}/current"
echo -e "  Nginx配置: ${NGINX_CONFIG}"
echo -e "  域名: ${DOMAIN}"
echo -e "${GREEN}====================================${NC}"
echo -e "${YELLOW}常用命令:${NC}"
echo -e "  查看Nginx状态: ssh ${SERVER_USER}@${SERVER_IP} 'systemctl status nginx'"
echo -e "  重载Nginx: ssh ${SERVER_USER}@${SERVER_IP} 'systemctl reload nginx'"
echo -e "  查看网站文件: ssh ${SERVER_USER}@${SERVER_IP} 'ls -la ${WEB_ROOT}/current'"
echo -e "${GREEN}====================================${NC}"
