# VASA Deploy Script
param([switch]$Help, [switch]$Check)

$SERVER_USER = "root"
$SERVER_IP = "*************"

function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Blue }

if ($Help) {
    Write-Success "VASA Deploy Script"
    Write-Info "Usage: .\deploy-simple.ps1 [-Help] [-Check]"
    exit 0
}

if ($Check) {
    Write-Info "Checking server status..."
    & ssh $SERVER_USER@$SERVER_IP "systemctl status nginx"
    exit 0
}

Write-Success "Starting deployment..."

if (!(Test-Path "package.json")) {
    Write-Error "Run from project root"
    exit 1
}

if (!(Get-Command pnpm -ErrorAction SilentlyContinue)) {
    Write-Error "pnpm not found"
    exit 1
}

Write-Warning "Building..."
if (Test-Path "dist") { 
    Remove-Item "dist" -Recurse -Force 
}
& pnpm build
if ($LASTEXITCODE -ne 0) { 
    Write-Error "Build failed"
    exit 1 
}

Write-Warning "Compressing..."
if (Test-Path "dist.tar.gz") { 
    Remove-Item "dist.tar.gz" -Force 
}
& tar -czf dist.tar.gz -C dist .

Write-Warning "Uploading..."
& scp dist.tar.gz "${SERVER_USER}@${SERVER_IP}:/tmp/"
& scp nginx.conf "${SERVER_USER}@${SERVER_IP}:/tmp/"

Write-Warning "Deploying..."
& ssh $SERVER_USER@$SERVER_IP "mkdir -p /usr/share/nginx/html/backups"
& ssh $SERVER_USER@$SERVER_IP "find /usr/share/nginx/html -mindepth 1 -maxdepth 1 ! -name backups -exec rm -rf {} \;"
& ssh $SERVER_USER@$SERVER_IP "cd /usr/share/nginx/html && tar -xzf /tmp/dist.tar.gz"
& ssh $SERVER_USER@$SERVER_IP "chmod -R 755 /usr/share/nginx/html"
& ssh $SERVER_USER@$SERVER_IP "cp /tmp/nginx.conf /etc/nginx/sites-available/default"
& ssh $SERVER_USER@$SERVER_IP "nginx -t"
& ssh $SERVER_USER@$SERVER_IP "systemctl reload nginx"
& ssh $SERVER_USER@$SERVER_IP "rm -f /tmp/dist.tar.gz /tmp/nginx.conf"

Write-Success "Deployment completed!"
Write-Info "Frontend: http://$SERVER_IP"
Write-Info "API: http://$SERVER_IP/api"

if (Test-Path "dist.tar.gz") { 
    Remove-Item "dist.tar.gz" -Force 
}
