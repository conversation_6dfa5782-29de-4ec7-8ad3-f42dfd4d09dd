#!/bin/bash

# Enhanced Deploy Script with Aggressive Cache Clearing
# Use this if regular deploy doesn't update the frontend

SERVER_USER="root"
SERVER_IP="*************"

# Color output functions
print_success() { echo -e "\033[32m$1\033[0m"; }
print_warning() { echo -e "\033[33m$1\033[0m"; }
print_error() { echo -e "\033[31m$1\033[0m"; }
print_info() { echo -e "\033[34m$1\033[0m"; }

print_success "Starting AGGRESSIVE cache-clearing deployment..."

# Check if running from project root
if [[ ! -f "package.json" ]]; then
    print_error "Run from project root"
    exit 1
fi

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    print_error "pnpm not found"
    exit 1
fi

print_warning "Aggressive cache clearing..."

# Clean ALL caches
rm -rf dist
rm -rf node_modules/.vite
rm -rf node_modules/.cache
pnpm store prune

print_warning "Fresh build with cache busting..."

# Add timestamp to force cache invalidation
TIMESTAMP=$(date +%s)
export VITE_BUILD_TIMESTAMP=$TIMESTAMP

# Build the project
pnpm build
if [[ $? -ne 0 ]]; then
    print_error "Build failed"
    exit 1
fi

print_warning "Compressing..."

# Clean previous archive
if [[ -f "dist.tar.gz" ]]; then
    rm -f dist.tar.gz
fi

# Create compressed archive
tar -czf dist.tar.gz -C dist .

print_warning "Uploading..."

# Upload files
scp dist.tar.gz "$SERVER_USER@$SERVER_IP:/tmp/"
scp nginx.conf "$SERVER_USER@$SERVER_IP:/tmp/"

print_warning "Server-side cache clearing..."

# Clear server caches and deploy
ssh "$SERVER_USER@$SERVER_IP" "
    # Stop nginx temporarily
    systemctl stop nginx
    
    # Clear nginx cache
    rm -rf /var/cache/nginx/*
    
    # Backup current deployment
    mkdir -p /usr/share/nginx/html/backups
    if [ -f /usr/share/nginx/html/index.html ]; then
        tar -czf /usr/share/nginx/html/backups/backup-\$(date +%Y%m%d-%H%M%S).tar.gz -C /usr/share/nginx/html --exclude=backups .
    fi
    
    # Completely remove old files
    find /usr/share/nginx/html -mindepth 1 -maxdepth 1 ! -name backups -exec rm -rf {} \;
    
    # Extract new files
    cd /usr/share/nginx/html && tar -xzf /tmp/dist.tar.gz
    
    # Set permissions
    chmod -R 755 /usr/share/nginx/html
    chown -R www-data:www-data /usr/share/nginx/html
    
    # Update nginx config
    cp /tmp/nginx.conf /etc/nginx/sites-available/default
    
    # Test and start nginx
    nginx -t && systemctl start nginx
    
    # Clean up
    rm -f /tmp/dist.tar.gz /tmp/nginx.conf
"

if [[ $? -ne 0 ]]; then
    print_error "Server deployment failed"
    exit 1
fi

print_success "Aggressive deployment completed!"
print_info "Frontend: http://$SERVER_IP"
print_info "API: http://$SERVER_IP/api"
print_warning "Cache clearing actions taken:"
print_info "✓ Cleared local build cache"
print_info "✓ Cleared pnpm store cache"
print_info "✓ Added build timestamp: $TIMESTAMP"
print_info "✓ Stopped/started nginx"
print_info "✓ Cleared nginx cache"
print_info "✓ Set proper file ownership"

print_warning "If still seeing old content:"
print_info "1. Hard refresh: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)"
print_info "2. Clear browser cache completely"
print_info "3. Try incognito/private browsing mode"
print_info "4. Try different browser"

# Clean up local files
if [[ -f "dist.tar.gz" ]]; then
    rm -f dist.tar.gz
fi
