/**
 * 拼音码生成工具
 * 使用 pinyin-pro 库来生成准确的中文拼音首字母
 */

import { pinyin } from 'pinyin-pro';

/**
 * 获取单个字符的拼音首字母
 * @param char 字符
 * @returns 拼音首字母，如果不是中文字符则返回原字符的大写
 */
export const getCharPinyin = (char: string): string => {
  // 如果是中文字符，使用 pinyin-pro 库获取拼音首字母
  if (/[\u4e00-\u9fff]/.test(char)) {
    try {
      const pinyinResult = pinyin(char, {
        pattern: 'first', // 只获取首字母
        toneType: 'none', // 不要声调
        type: 'string', // 返回字符串
      });
      return pinyinResult.toUpperCase();
    } catch (error) {
      // 如果拼音转换失败，返回默认值
      console.warn('拼音转换失败:', char, error);
      return 'Z';
    }
  }

  // 如果是英文字符，返回大写
  if (/[a-zA-Z]/.test(char)) {
    return char.toUpperCase();
  }

  // 如果是数字，返回原字符
  if (/[0-9]/.test(char)) {
    return char;
  }

  // 其他字符返回空字符串
  return '';
};

/**
 * 生成中文字符串的拼音码
 * @param text 中文字符串
 * @returns 拼音码字符串
 */
export const generatePinyinCode = (text: string): string => {
  if (!text || typeof text !== 'string') {
    return '';
  }

  return text
    .split('')
    .map((char) => getCharPinyin(char))
    .filter((pinyin) => pinyin !== '') // 过滤掉空字符串
    .join('');
};

/**
 * 自动生成客户名称的拼音码
 * @param customerName 客户名称
 * @returns 拼音码
 */
export const generateCustomerPinyinCode = (customerName: string): string => {
  return generatePinyinCode(customerName);
};

export default {
  getCharPinyin,
  generatePinyinCode,
  generateCustomerPinyinCode,
};
