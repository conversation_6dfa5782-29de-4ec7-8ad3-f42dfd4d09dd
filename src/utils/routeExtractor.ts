import { RouteObject } from 'react-router-dom';

export interface RouteInfo {
  path: string;
  displayName: string;
  category: string;
  description?: string;
}

// 路由分类映射
const routeCategories: { [key: string]: string } = {
  system: '系统管理',
  company: '公司管理',
  customer: '客户管理',
  product: '产品管理',
  purchase: '采购管理',
  statistics: '统计分析',
  supplier: '供应商管理',
  financial: '财务管理',
};

// 路由显示名称映射 - 只包含一级功能路由
const routeDisplayNames: { [key: string]: string } = {
  '/': '首页',
  'system/users': '用户管理',
  'company/management': '公司管理',
  'company/supplier': '供应管理',
  'company/supplier-archives': '供应商档案',
  'company/customer': '客户管理',
  'customer/profiles': '客户档案',
  'product/brand': '品牌管理',
  'product/category': '类别管理',
  'product/color': '颜色管理',
  'product/auxiliary': '辅料管理',
  'product/archive': '商品档案',
  'product/inventory': '库存明细',
  'purchase/purchase-contracts': '辅料采购',
  'purchase/product-orders': '商品采购',
  'sales/demand-orders': '需求订单',
  'sales/orders': '销售订单',
  'sales/returns': '销售退货',
  'statistics/demand-purchase': '综合统计',
  'supplier/purchase-overview': '供应商采购概览',
  'financial/fixed-assets': '固定资产',
  'financial/operating-assets': '运营资产',
  'financial/rental-assets': '租赁资产',
  'financial/rd-costs': '研发成本',
  'financial/income-assets': '收入资产',
  'financial/expense-assets': '支出资产',
  'admin/memos': '备忘录管理',
};

/**
 * 从路由对象中提取路径 - 只提取一级功能路由
 */
function extractPathsFromRoutes(routes: RouteObject[], parentPath = ''): string[] {
  const paths: string[] = [];

  routes.forEach((route) => {
    if (route.path) {
      // 正确处理路径拼接，避免双斜杠
      let fullPath: string;
      if (!parentPath || parentPath === '/') {
        fullPath = route.path.startsWith('/') ? route.path : `/${route.path}`;
      } else {
        const cleanParentPath = parentPath.endsWith('/') ? parentPath.slice(0, -1) : parentPath;
        const cleanRoutePath = route.path.startsWith('/') ? route.path.slice(1) : route.path;
        fullPath = `${cleanParentPath}/${cleanRoutePath}`;
      }

      // 跳过不需要权限控制的路由
      if (!shouldSkipRoute(fullPath) && isMainFunctionalRoute(fullPath)) {
        paths.push(fullPath);
      }
    }

    // 处理子路由
    if (route.children) {
      // 确定当前路由作为父路径
      let currentParentPath: string;
      if (!parentPath || parentPath === '/') {
        currentParentPath = route.path || '/';
      } else {
        const cleanParentPath = parentPath.endsWith('/') ? parentPath.slice(0, -1) : parentPath;
        const cleanRoutePath = route.path?.startsWith('/') ? route.path.slice(1) : route.path;
        currentParentPath = cleanRoutePath
          ? `${cleanParentPath}/${cleanRoutePath}`
          : cleanParentPath;
      }

      const childPaths = extractPathsFromRoutes(route.children, currentParentPath);
      paths.push(...childPaths);
    }
  });

  return paths;
}

/**
 * 判断是否应该跳过某个路由（不需要权限控制）
 */
function shouldSkipRoute(path: string): boolean {
  const skipPatterns = ['/login', '/404', '/403', '/500', '*'];

  return skipPatterns.some((pattern) => path === pattern || path.includes(pattern));
}

/**
 * 判断是否为一级功能路由（需要权限配置的主要功能模块）
 */
function isMainFunctionalRoute(path: string): boolean {
  // 首页
  if (path === '/') return true;

  // 移除开头的斜杠
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;

  // 二级操作路由模式（需要过滤掉）
  const operationPatterns = ['add', 'create', 'edit', 'view', 'detail'];

  // 将路径分割成段
  const pathSegments = cleanPath.split('/');

  // 如果路径段中包含操作模式，则不是一级功能路由
  if (operationPatterns.some((pattern) => pathSegments.includes(pattern))) {
    return false;
  }

  // 参数路由（包含冒号）通常是二级路由
  if (cleanPath.includes(':')) {
    return false;
  }

  // 其他情况认为是一级功能路由
  return true;
}

/**
 * 获取路由的分类
 */
function getRouteCategory(path: string): string {
  const segments = path.split('/').filter(Boolean);
  const firstSegment = segments[0];

  return routeCategories[firstSegment] || '其他';
}

/**
 * 获取路由的显示名称
 */
function getRouteDisplayName(path: string): string {
  // 移除开头的斜杠
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;

  // 首先尝试精确匹配
  if (routeDisplayNames[cleanPath]) {
    return routeDisplayNames[cleanPath];
  }

  // 如果是参数路由，尝试匹配模式
  for (const [pattern, name] of Object.entries(routeDisplayNames)) {
    if (cleanPath.includes(':') && pattern.includes(':')) {
      const cleanPattern = pattern.replace(/:\w+/g, '');
      const cleanCurrentPath = cleanPath.replace(/:\w+/g, '');
      if (cleanCurrentPath === cleanPattern) {
        return name;
      }
    }
  }

  // 如果没有找到映射，生成默认名称
  return generateDefaultDisplayName(cleanPath);
}

/**
 * 生成默认的显示名称
 */
function generateDefaultDisplayName(path: string): string {
  const segments = path.split('/').filter(Boolean);
  const lastSegment = segments[segments.length - 1];

  // 处理参数路由
  if (lastSegment?.includes(':')) {
    const action = segments[segments.length - 2];
    switch (action) {
      case 'add':
      case 'create':
        return '新增';
      case 'edit':
        return '编辑';
      case 'view':
        return '查看';
      case 'detail':
        return '详情';
      default:
        return lastSegment;
    }
  }

  return lastSegment || path;
}

/**
 * 从路由配置中提取所有可用的路由信息
 */
export function extractAvailableRoutes(routes: RouteObject[]): RouteInfo[] {
  const paths = extractPathsFromRoutes(routes);

  return paths
    .map((path) => ({
      path,
      displayName: getRouteDisplayName(path),
      category: getRouteCategory(path),
    }))
    .sort((a, b) => {
      // 先按分类排序，再按显示名称排序
      if (a.category !== b.category) {
        return a.category.localeCompare(b.category);
      }
      return a.displayName.localeCompare(b.displayName);
    });
}

/**
 * 按分类分组路由
 */
export function groupRoutesByCategory(routes: RouteInfo[]): { [category: string]: RouteInfo[] } {
  return routes.reduce(
    (groups, route) => {
      const category = route.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(route);
      return groups;
    },
    {} as { [category: string]: RouteInfo[] },
  );
}
