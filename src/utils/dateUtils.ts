import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';

// 扩展dayjs以支持自定义解析格式和日期比较
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

/**
 * 安全地将任何日期值转换为dayjs对象
 * @param dateValue 日期值（可以是字符串、Date对象、dayjs对象、null或undefined）
 * @returns 有效的dayjs对象或undefined
 */
export const safelyParseDateToDayjs = (dateValue: any): dayjs.Dayjs | undefined => {
  // 如果值为空，返回undefined
  if (dateValue === null || dateValue === undefined || dateValue === '') {
    return undefined;
  }

  // 如果已经是dayjs对象，检查其有效性
  if (dayjs.isDayjs(dateValue)) {
    return dateValue.isValid() ? dateValue : undefined;
  }

  // 如果是Date对象，转换为dayjs
  if (dateValue instanceof Date) {
    const dayjsObj = dayjs(dateValue);
    return dayjsObj.isValid() ? dayjsObj : undefined;
  }

  // 如果是字符串，尝试解析
  if (typeof dateValue === 'string') {
    // 尝试使用不同的格式解析日期
    const formats = [
      'YYYY-MM-DD',
      'YYYY/MM/DD',
      'MM/DD/YYYY',
      'DD/MM/YYYY',
      'YYYY-MM-DDTHH:mm:ss.SSSZ',
    ];

    for (const format of formats) {
      const dayjsObj = dayjs(dateValue, format, true); // 严格模式
      if (dayjsObj.isValid()) {
        return dayjsObj;
      }
    }

    // 如果严格模式失败，尝试非严格模式
    const dayjsObj = dayjs(dateValue);
    if (dayjsObj.isValid()) {
      return dayjsObj;
    }
  }

  // 所有尝试都失败，返回undefined
  console.warn('无法解析日期值:', dateValue);
  return undefined;
};

/**
 * 格式化日期为字符串
 * @param dateValue 日期值
 * @param format 格式化字符串，默认为YYYY-MM-DD
 * @returns 格式化的日期字符串或undefined
 */
export const formatDateToString = (
  dateValue: any,
  format: string = 'YYYY-MM-DD',
): string | undefined => {
  const dayjsObj = safelyParseDateToDayjs(dateValue);
  return dayjsObj ? dayjsObj.format(format) : undefined;
};

/**
 * 安全地比较两个日期
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 如果date1小于date2，返回-1；如果date1等于date2，返回0；如果date1大于date2，返回1；如果任一日期无效，返回undefined
 */
export const safelyCompareDates = (date1: any, date2: any): number | undefined => {
  const dayjs1 = safelyParseDateToDayjs(date1);
  const dayjs2 = safelyParseDateToDayjs(date2);

  if (!dayjs1 || !dayjs2) {
    return undefined;
  }

  if (dayjs1.isBefore(dayjs2)) {
    return -1;
  } else if (dayjs1.isSame(dayjs2)) {
    return 0;
  } else {
    return 1;
  }
};

/**
 * 检查日期是否在指定范围内
 * @param date 要检查的日期
 * @param startDate 范围开始日期
 * @param endDate 范围结束日期
 * @returns 如果日期在范围内，返回true；否则返回false；如果任一日期无效，返回false
 */
export const isDateInRange = (date: any, startDate: any, endDate: any): boolean => {
  const dayjsDate = safelyParseDateToDayjs(date);
  const dayjsStartDate = safelyParseDateToDayjs(startDate);
  const dayjsEndDate = safelyParseDateToDayjs(endDate);

  if (!dayjsDate || !dayjsStartDate || !dayjsEndDate) {
    return false;
  }

  return dayjsDate.isSameOrAfter(dayjsStartDate) && dayjsDate.isSameOrBefore(dayjsEndDate);
};

/**
 * 格式化日期时间
 * @param dateString ISO格式的日期字符串
 * @returns 格式化后的日期时间字符串，格式为：YYYY-MM-DD HH:mm:ss
 */
export const formatDateTime = (dateString: string): string => {
  if (!dateString) return '-';

  const dayjsObj = safelyParseDateToDayjs(dateString);
  return dayjsObj ? dayjsObj.format('YYYY-MM-DD HH:mm:ss') : '-';
};

/**
 * 格式化日期（不含时间）
 * @param dateString ISO格式的日期字符串
 * @returns 格式化后的日期字符串，格式为：YYYY-MM-DD
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return '-';

  const dayjsObj = safelyParseDateToDayjs(dateString);
  return dayjsObj ? dayjsObj.format('YYYY-MM-DD') : '-';
};

export default {
  safelyParseDateToDayjs,
  formatDateToString,
  safelyCompareDates,
  isDateInRange,
  formatDateTime,
  formatDate,
};
