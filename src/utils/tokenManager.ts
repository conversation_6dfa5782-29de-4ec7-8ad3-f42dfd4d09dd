/**
 * Token 管理工具
 * 用于处理 token 的存储、获取、删除和有效期管理
 */

// Token 存储键名
const TOKEN_KEY = 'token';
// Token 过期时间键名
const TOKEN_EXPIRY_KEY = 'token_expiry';
// 用户信息键名
const USER_KEY = 'user';
// Token 默认有效期（7天，单位：毫秒）
const TOKEN_EXPIRY_TIME = 7 * 24 * 60 * 60 * 1000;

/**
 * 保存 token 到本地存储
 * @param token JWT token
 */
export const saveToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
  
  // 设置过期时间（当前时间 + 7天）
  const expiryTime = new Date().getTime() + TOKEN_EXPIRY_TIME;
  localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());
};

/**
 * 保存用户信息到本地存储
 * @param user 用户信息对象
 */
export const saveUser = (user: any): void => {
  localStorage.setItem(USER_KEY, JSON.stringify(user));
};

/**
 * 获取 token
 * @returns token 或 null（如果不存在或已过期）
 */
export const getToken = (): string | null => {
  const token = localStorage.getItem(TOKEN_KEY);
  
  // 如果没有 token，直接返回 null
  if (!token) {
    return null;
  }
  
  // 检查 token 是否过期
  const expiryTime = localStorage.getItem(TOKEN_EXPIRY_KEY);
  if (expiryTime) {
    const expiryTimeNum = parseInt(expiryTime, 10);
    const now = new Date().getTime();
    
    // 如果当前时间超过过期时间，清除 token 并返回 null
    if (now > expiryTimeNum) {
      clearToken();
      return null;
    }
  }
  
  return token;
};

/**
 * 获取用户信息
 * @returns 用户信息对象或 null
 */
export const getUser = (): any | null => {
  const userStr = localStorage.getItem(USER_KEY);
  if (!userStr) {
    return null;
  }
  
  try {
    return JSON.parse(userStr);
  } catch (error) {
    console.error('解析用户信息失败:', error);
    return null;
  }
};

/**
 * 清除 token 和用户信息
 */
export const clearToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(TOKEN_EXPIRY_KEY);
  localStorage.removeItem(USER_KEY);
};

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export const isAuthenticated = (): boolean => {
  return !!getToken();
};

/**
 * 刷新 token 有效期
 * 在用户活跃时调用，延长 token 的有效期
 */
export const refreshTokenExpiry = (): void => {
  const token = localStorage.getItem(TOKEN_KEY);
  if (token) {
    const expiryTime = new Date().getTime() + TOKEN_EXPIRY_TIME;
    localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());
  }
};
