/**
 * API 调用辅助工具
 * 提供统一的API调用方式，自动处理错误信息
 */

import { message } from 'antd';
import { getErrorMessage, handleApiResponse, logError, handleNetworkError } from './errorHandler';

/**
 * 统一的API调用包装器
 * @param apiCall API调用函数
 * @param options 配置选项
 * @returns Promise<{ success: boolean, data?: any, message: string }>
 */
export const callApi = async <T = any>(
  apiCall: () => Promise<any>,
  options: {
    successMessage?: string;
    errorMessage?: string;
    showSuccessMessage?: boolean;
    showErrorMessage?: boolean;
    logOperation?: string;
  } = {}
): Promise<{ success: boolean; data?: T; message: string }> => {
  const {
    successMessage = '操作成功',
    errorMessage = '操作失败',
    showSuccessMessage = false,
    showErrorMessage = true,
    logOperation = '操作',
  } = options;

  try {
    const response = await apiCall();
    const result = handleApiResponse(response, successMessage, errorMessage);

    if (result.success) {
      if (showSuccessMessage) {
        message.success(result.message);
      }
      return {
        success: true,
        data: response.data,
        message: result.message,
      };
    } else {
      if (showErrorMessage) {
        message.error(result.message);
      }
      return {
        success: false,
        message: result.message,
      };
    }
  } catch (error: any) {
    logError(logOperation, error);
    const errorMsg = handleNetworkError(error);
    
    if (showErrorMessage) {
      message.error(errorMsg);
    }
    
    return {
      success: false,
      message: errorMsg,
    };
  }
};

/**
 * 简化的API调用，只返回数据或抛出错误
 * @param apiCall API调用函数
 * @param errorMessage 错误信息
 * @returns Promise<T>
 */
export const callApiSimple = async <T = any>(
  apiCall: () => Promise<any>,
  errorMessage = '操作失败'
): Promise<T> => {
  try {
    const response = await apiCall();
    
    if (response.code === 200) {
      return response.data;
    } else {
      throw new Error(response.message || errorMessage);
    }
  } catch (error: any) {
    throw new Error(getErrorMessage(error, errorMessage));
  }
};

/**
 * 带加载状态的API调用
 * @param apiCall API调用函数
 * @param setLoading 设置加载状态的函数
 * @param options 配置选项
 * @returns Promise<{ success: boolean, data?: any, message: string }>
 */
export const callApiWithLoading = async <T = any>(
  apiCall: () => Promise<any>,
  setLoading: (loading: boolean) => void,
  options: {
    successMessage?: string;
    errorMessage?: string;
    showSuccessMessage?: boolean;
    showErrorMessage?: boolean;
    logOperation?: string;
  } = {}
): Promise<{ success: boolean; data?: T; message: string }> => {
  setLoading(true);
  try {
    return await callApi<T>(apiCall, options);
  } finally {
    setLoading(false);
  }
};

// 使用示例：
/*
// 1. 基本用法
const handleSaveUser = async (userData: any) => {
  const result = await callApi(
    () => createUser(userData),
    {
      successMessage: '用户创建成功',
      errorMessage: '用户创建失败',
      showSuccessMessage: true,
      logOperation: '创建用户',
    }
  );
  
  if (result.success) {
    // 处理成功逻辑
    refreshUserList();
  }
};

// 2. 简化用法
const handleGetUsers = async () => {
  try {
    const users = await callApiSimple(() => getUserList(), '获取用户列表失败');
    setUsers(users);
  } catch (error) {
    // 错误已经被处理，这里可以做额外的错误处理
  }
};

// 3. 带加载状态
const handleDeleteUser = async (userCode: string) => {
  const result = await callApiWithLoading(
    () => deleteUser(userCode),
    setLoading,
    {
      successMessage: '用户删除成功',
      showSuccessMessage: true,
      logOperation: '删除用户',
    }
  );
  
  if (result.success) {
    refreshUserList();
  }
};
*/
