// 供应商档案模拟数据
import { SupplierArchiveType, type ListDetail, type Detail } from '@/types/supplierArchives';

// 模拟汇总数据
export const mockSummaryData: ListDetail[] = [
  {
    supplierCode: 'SUP001',
    supplierName: '优质供应商A',
    totalPurchaseAmount: 1250000,
    totalPurchaseQuantity: 5000,
    totalArrivalQuantity: 4950,
    totalDefectQuantity: 50,
    totalRepairQuantity: 30,
    totalRepairedQuantity: 28,
    totalPaymentAmount: 1200000,
    defectRate: 0.01,
    repairRate: 0.006,
    repairSuccessRate: 0.933,
    orderCompletionRate: 0.99,
    averageUnitPrice: 250,
    cooperationStartDate: '2022-01-15',
    lastTransactionDate: '2024-12-01',
    creditLevel: 'AAA',
    paymentCycle: 30,
    deliveryCycle: 7,
    qualityScore: 95,
    returnCount: 5,
    returnAmount: 12500,
    returnRate: 0.001,
    unsettledAmount: 50000,
    prepaymentBalance: 100000,
    averagePaymentPeriod: 28,
    onTimeDeliveryRate: 0.98,
    responseSpeedScore: 92,
    serviceAttitudeScore: 96,
    overallScore: 94,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-12-01T00:00:00Z',
  },
  {
    supplierCode: 'SUP002',
    supplierName: '标准供应商B',
    totalPurchaseAmount: 800000,
    totalPurchaseQuantity: 4000,
    totalArrivalQuantity: 3900,
    totalDefectQuantity: 100,
    totalRepairQuantity: 60,
    totalRepairedQuantity: 55,
    totalPaymentAmount: 780000,
    defectRate: 0.025,
    repairRate: 0.015,
    repairSuccessRate: 0.917,
    orderCompletionRate: 0.975,
    averageUnitPrice: 200,
    cooperationStartDate: '2022-06-01',
    lastTransactionDate: '2024-11-28',
    creditLevel: 'AA',
    paymentCycle: 45,
    deliveryCycle: 10,
    qualityScore: 85,
    returnCount: 12,
    returnAmount: 24000,
    returnRate: 0.003,
    unsettledAmount: 20000,
    prepaymentBalance: 50000,
    averagePaymentPeriod: 42,
    onTimeDeliveryRate: 0.92,
    responseSpeedScore: 88,
    serviceAttitudeScore: 90,
    overallScore: 87,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-11-28T00:00:00Z',
  },
  {
    supplierCode: 'SUP003',
    supplierName: '经济供应商C',
    totalPurchaseAmount: 450000,
    totalPurchaseQuantity: 3000,
    totalArrivalQuantity: 2850,
    totalDefectQuantity: 150,
    totalRepairQuantity: 90,
    totalRepairedQuantity: 80,
    totalPaymentAmount: 430000,
    defectRate: 0.05,
    repairRate: 0.03,
    repairSuccessRate: 0.889,
    orderCompletionRate: 0.95,
    averageUnitPrice: 150,
    cooperationStartDate: '2023-03-01',
    lastTransactionDate: '2024-11-25',
    creditLevel: 'BBB',
    paymentCycle: 60,
    deliveryCycle: 14,
    qualityScore: 75,
    returnCount: 20,
    returnAmount: 30000,
    returnRate: 0.007,
    unsettledAmount: 15000,
    prepaymentBalance: 25000,
    averagePaymentPeriod: 55,
    onTimeDeliveryRate: 0.85,
    responseSpeedScore: 78,
    serviceAttitudeScore: 82,
    overallScore: 78,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-11-25T00:00:00Z',
  },
];

// 模拟详情数据
export const mockDetailData: Detail[] = [
  {
    id: '1',
    supplierCode: 'SUP001',
    supplierName: '优质供应商A',
    type: SupplierArchiveType.PURCHASE,
    totalQuantity: 1000,
    totalAmount: 250000,
    purchaseOrderId: 'PO20241201001',
    remark: '年度大批量采购',
    createdAt: '2024-12-01T10:00:00Z',
    updatedAt: '2024-12-01T10:00:00Z',
  },
  {
    id: '2',
    supplierCode: 'SUP001',
    supplierName: '优质供应商A',
    type: SupplierArchiveType.ARRIVAL,
    actualArrivalQuantity: 990,
    defectQuantity: 10,
    logisticsOrderId: 'LO20241201001',
    remark: '到货质量良好',
    createdAt: '2024-12-01T14:00:00Z',
    updatedAt: '2024-12-01T14:00:00Z',
  },
  {
    id: '3',
    supplierCode: 'SUP001',
    supplierName: '优质供应商A',
    type: SupplierArchiveType.REPAIR_SEND,
    repairQuantity: 10,
    logisticsOrderId: 'LO20241201002',
    remark: '轻微瑕疵返厂修复',
    createdAt: '2024-12-02T09:00:00Z',
    updatedAt: '2024-12-02T09:00:00Z',
  },
  {
    id: '4',
    supplierCode: 'SUP001',
    supplierName: '优质供应商A',
    type: SupplierArchiveType.REPAIR_ARRIVAL,
    actualRepairedQuantity: 9,
    totalArrivalQuantity: 10,
    logisticsOrderId: 'LO20241205001',
    remark: '修复完成，质量合格',
    createdAt: '2024-12-05T16:00:00Z',
    updatedAt: '2024-12-05T16:00:00Z',
  },
];

// 根据供应商编码获取详情数据
export const getDetailsBySupplierCode = (supplierCode: string): Detail[] => {
  return mockDetailData.filter(item => item.supplierCode === supplierCode);
};

// 根据供应商编码获取汇总数据
export const getSummaryBySupplierCode = (supplierCode: string): ListDetail | null => {
  return mockSummaryData.find(item => item.supplierCode === supplierCode) || null;
};
