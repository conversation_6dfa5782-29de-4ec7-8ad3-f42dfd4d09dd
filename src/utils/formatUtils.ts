/**
 * 格式化工具函数
 * 包含金额、数字等格式化功能
 */

/**
 * 格式化金额，添加千分位分隔符
 * @param amount 金额数值
 * @param precision 小数位数，默认为2
 * @param currency 货币符号，默认为¥
 * @returns 格式化后的金额字符串
 */
export const formatAmount = (
  amount: number | string | null | undefined,
  precision: number = 2,
  currency: string = '¥',
): string => {
  // 处理空值
  if (amount === null || amount === undefined || amount === '') {
    return `${currency}0.00`;
  }

  // 转换为数字
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // 检查是否为有效数字
  if (isNaN(numAmount)) {
    return `${currency}0.00`;
  }

  // 格式化数字，添加千分位分隔符
  const formattedNumber = numAmount.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  });

  return `${currency}${formattedNumber}`;
};

/**
 * 格式化数字，添加千分位分隔符（不带货币符号）
 * @param number 数值
 * @param precision 小数位数，默认为2
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (
  number: number | string | null | undefined,
  precision: number = 2,
): string => {
  // 处理空值
  if (number === null || number === undefined || number === '') {
    return '0.00';
  }

  // 转换为数字
  const numValue = typeof number === 'string' ? parseFloat(number) : number;

  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return '0.00';
  }

  // 格式化数字，添加千分位分隔符
  return numValue.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  });
};

/**
 * 格式化整数，添加千分位分隔符
 * @param number 数值
 * @returns 格式化后的整数字符串
 */
export const formatInteger = (number: number | string | null | undefined): string => {
  return formatNumber(number, 0);
};

/**
 * 格式化百分比
 * @param value 数值（0-1之间的小数或0-100之间的整数）
 * @param precision 小数位数，默认为2
 * @param isDecimal 是否为小数形式（0-1），默认为false（0-100）
 * @returns 格式化后的百分比字符串
 */
export const formatPercentage = (
  value: number | string | null | undefined,
  precision: number = 2,
  isDecimal: boolean = false,
): string => {
  // 处理空值
  if (value === null || value === undefined || value === '') {
    return '0.00%';
  }

  // 转换为数字
  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return '0.00%';
  }

  // 如果是小数形式，需要乘以100
  const percentage = isDecimal ? numValue * 100 : numValue;

  return `${percentage.toFixed(precision)}%`;
};

/**
 * 简化的金额格式化（用于表格显示）
 * @param amount 金额数值
 * @param showCurrency 是否显示货币符号，默认为true
 * @returns 格式化后的金额字符串
 */
export const formatAmountSimple = (
  amount: number | string | null | undefined,
  showCurrency: boolean = true,
): string => {
  const currency = showCurrency ? '¥' : '';
  return formatAmount(amount, 2, currency);
};

export default {
  formatAmount,
  formatNumber,
  formatInteger,
  formatPercentage,
  formatAmountSimple,
};
