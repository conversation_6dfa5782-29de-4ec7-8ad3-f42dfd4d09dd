/**
 * 错误处理工具函数
 * 用于统一处理API错误信息，优先使用后端返回的错误信息
 */

/**
 * 从错误对象中提取错误信息
 * @param error 错误对象
 * @param defaultMessage 默认错误信息
 * @returns 错误信息字符串
 */
export const getErrorMessage = (error: any, defaultMessage = '操作失败'): string => {
  // 优先级：
  // 1. Axios 错误响应格式 (error.response.data.message)
  // 2. 直接的 API 响应格式 (error.data.message)
  // 3. 通用错误信息 (error.message)
  // 4. 字符串错误
  // 5. 默认错误信息

  if (error?.response?.data?.message) {
    // Axios 错误响应格式
    return error.response.data.message;
  }
  
  if (error?.data?.message) {
    // 直接的 API 响应格式
    return error.data.message;
  }
  
  if (error?.message) {
    // 通用错误信息
    return error.message;
  }
  
  if (typeof error === 'string') {
    // 字符串错误
    return error;
  }
  
  // 默认错误信息
  return defaultMessage;
};

/**
 * 处理API响应，提取成功或错误信息
 * @param response API响应对象
 * @param defaultSuccessMessage 默认成功信息
 * @param defaultErrorMessage 默认错误信息
 * @returns { success: boolean, message: string }
 */
export const handleApiResponse = (
  response: any,
  defaultSuccessMessage = '操作成功',
  defaultErrorMessage = '操作失败'
): { success: boolean; message: string } => {
  if (response.code === 200) {
    return {
      success: true,
      message: response.message || defaultSuccessMessage,
    };
  } else {
    return {
      success: false,
      message: response.message || defaultErrorMessage,
    };
  }
};

/**
 * 统一的错误日志记录
 * @param operation 操作名称
 * @param error 错误对象
 */
export const logError = (operation: string, error: any): void => {
  console.error(`${operation}失败:`, error);
  
  // 可以在这里添加更多的错误日志记录逻辑
  // 比如发送到错误监控服务
  if (process.env.NODE_ENV === 'production') {
    // 生产环境可以发送到错误监控服务
    // 例如：Sentry, LogRocket 等
  }
};

/**
 * 网络错误处理
 * @param error 错误对象
 * @returns 用户友好的错误信息
 */
export const handleNetworkError = (error: any): string => {
  if (!navigator.onLine) {
    return '网络连接已断开，请检查网络设置';
  }
  
  if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
    return '网络连接失败，请稍后重试';
  }
  
  if (error?.response?.status) {
    const status = error.response.status;
    switch (status) {
      case 400:
        return error.response.data?.message || '请求参数错误';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '权限不足，无法执行此操作';
      case 404:
        return '请求的资源不存在';
      case 500:
        return '服务器内部错误，请稍后重试';
      case 502:
        return '服务器网关错误，请稍后重试';
      case 503:
        return '服务暂时不可用，请稍后重试';
      default:
        return error.response.data?.message || `请求失败 (${status})`;
    }
  }
  
  return getErrorMessage(error, '网络请求失败');
};
