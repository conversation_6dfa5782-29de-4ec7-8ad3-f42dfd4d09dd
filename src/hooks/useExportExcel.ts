import { useState } from 'react';
import { message } from 'antd';
import { logError, getErrorMessage } from '@/utils/errorHandler';

export interface ExportExcelOptions {
  exportFunction: (params: any) => Promise<Blob>;
  fileName: string;
  successMessage?: string;
  errorMessage?: string;
}

export const useExportExcel = () => {
  const [isExporting, setIsExporting] = useState(false);

  const exportExcel = async (options: ExportExcelOptions, params: any) => {
    setIsExporting(true);
    try {
      const blob = await options.exportFunction(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = options.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success(options.successMessage || '导出成功');
    } catch (error: any) {
      logError('导出Excel', error);
      message.error(getErrorMessage(error, options.errorMessage || '导出失败'));
    } finally {
      setIsExporting(false);
    }
  };

  return {
    isExporting,
    exportExcel,
  };
};
