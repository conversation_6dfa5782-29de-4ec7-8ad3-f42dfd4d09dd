import { request } from './api';
import type {
  GetStatisticsParams,
  StatisticsResponse,
  GetDemandOrdersParams,
  DemandOrdersResponse,
  GetPurchaseOrdersParams,
  PurchaseOrdersResponse,
  GetProductDemandParams,
  ProductDemandResponse,
} from '@/types/demandPurchase';

/**
 * 获取综合仪表板数据
 */
export const getDashboardStatistics = async (
  params?: GetStatisticsParams,
): Promise<StatisticsResponse> => {
  return request({
    url: '/demand-purchase-statistics/dashboard',
    method: 'GET',
    params,
  });
};

/**
 * 获取需求订单统计
 */
export const getDemandOrderStatistics = async (
  params?: GetDemandOrdersParams,
): Promise<DemandOrdersResponse> => {
  return request({
    url: '/demand-purchase-statistics/demand-orders',
    method: 'GET',
    params,
  });
};

/**
 * 获取采购订单统计
 */
export const getPurchaseOrderStatistics = async (
  params?: GetPurchaseOrdersParams,
): Promise<PurchaseOrdersResponse> => {
  return request({
    url: '/demand-purchase-statistics/purchase-orders',
    method: 'GET',
    params,
  });
};

/**
 * 获取商品需求统计
 */
export const getProductDemandStatistics = async (
  params?: GetProductDemandParams,
): Promise<ProductDemandResponse> => {
  return request({
    url: '/demand-purchase-statistics/product-demand',
    method: 'GET',
    params,
  });
};
