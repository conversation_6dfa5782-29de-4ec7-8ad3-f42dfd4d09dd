import { request } from './api';
import {
  AddRentalAssetsParams,
  RentalAssetsListParams,
  RentalAssetsListResponse,
  ExportRentalAssetsParams,
  ApiResponse,
} from '@/types/rentalAssets';

// 创建租赁资产详情 - POST /rental-assets/details
export const createRentalAssetDetail = async (
  assetData: AddRentalAssetsParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/rental-assets/details',
    method: 'POST',
    data: assetData,
  });
};

// 获取租赁资产详情列表 - GET /rental-assets/details
export const getRentalAssetsList = async (
  params: RentalAssetsListParams,
): Promise<ApiResponse<RentalAssetsListResponse['data']>> => {
  return request({
    url: '/rental-assets/details',
    method: 'GET',
    params: {
      ...params,
      // 确保排序参数被正确传递
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    },
  });
};

// 获取租赁资产详情 - GET /rental-assets/details/{id}
export const getRentalAssetDetail = async (id: string): Promise<ApiResponse<any>> => {
  return request({
    url: `/rental-assets/details/${id}`,
    method: 'GET',
  });
};

// 更新租赁资产详情 - PATCH /rental-assets/details/{id}
export const updateRentalAssetDetail = async (
  id: string,
  assetData: AddRentalAssetsParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/rental-assets/details/${id}`,
    method: 'PATCH',
    data: assetData,
  });
};

// 删除租赁资产详情 - DELETE /rental-assets/details/{id}
export const deleteRentalAssetDetail = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/rental-assets/details/${id}`,
    method: 'DELETE',
  });
};

// 导出租赁资产Excel - GET /rental-assets/export/excel
export const exportRentalAssetsExcel = async (params: ExportRentalAssetsParams): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  // startTime和endTime现在是必填参数
  queryParams.append('startTime', params.startTime);
  queryParams.append('endTime', params.endTime);
  if (params.detailIds && params.detailIds.length > 0) {
    // 将UUID数组转换为逗号分隔的字符串，并进行URL编码
    const detailIdsString = params.detailIds.join(',');
    queryParams.append('detailIds', encodeURIComponent(detailIdsString));
  }
  if (params.type) {
    queryParams.append('type', params.type);
  }
  if (params.search) {
    queryParams.append('search', encodeURIComponent(params.search));
  }
  if (params.companyCode) {
    queryParams.append('companyCode', params.companyCode);
  }

  const url = `${baseURL}/rental-assets/export/excel?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
