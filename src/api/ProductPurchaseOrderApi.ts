import { request } from './api';
import type {
  MergeDemandOrdersParams,
  MergeDemandOrdersResponse,
  GetProductPurchaseOrdersParams,
  GetProductPurchaseOrdersResponse,
  GetProductPurchaseOrderDetailResponse,
  ConfirmPurchaseOrderParams,
  ConfirmPurchaseOrderResponse,
  ReceiveOrderParams,
  ReceiveOrderResponse,
  CancelPurchaseOrderResponse,
} from '@/types/productPurchaseOrder';

/**
 * 合并需求订单为采购订单
 */
export const mergeDemandOrders = async (
  params: MergeDemandOrdersParams,
): Promise<MergeDemandOrdersResponse> => {
  return request({
    url: '/product-purchase-orders/merge-demands',
    method: 'POST',
    data: params,
  });
};

/**
 * 查询采购订单列表
 */
export const getProductPurchaseOrderList = async (
  params: GetProductPurchaseOrdersParams,
): Promise<GetProductPurchaseOrdersResponse> => {
  return request({
    url: '/product-purchase-orders',
    method: 'GET',
    params,
  });
};

/**
 * 获取采购订单详情
 */
export const getProductPurchaseOrderDetail = async (
  id: string,
): Promise<GetProductPurchaseOrderDetailResponse> => {
  return request({
    url: `/product-purchase-orders/${id}`,
    method: 'GET',
  });
};

/**
 * 确认采购订单
 */
export const confirmPurchaseOrder = async (
  id: string,
  params: ConfirmPurchaseOrderParams,
): Promise<ConfirmPurchaseOrderResponse> => {
  return request({
    url: `/product-purchase-orders/${id}/confirm`,
    method: 'POST',
    data: params,
  });
};

/**
 * 收货确认
 */
export const receiveOrder = async (
  id: string,
  params: ReceiveOrderParams,
): Promise<ReceiveOrderResponse> => {
  return request({
    url: `/product-purchase-orders/${id}/receive`,
    method: 'POST',
    data: params,
  });
};

/**
 * 取消采购订单
 */
export const cancelPurchaseOrder = async (id: string): Promise<CancelPurchaseOrderResponse> => {
  return request({
    url: `/product-purchase-orders/${id}/cancel`,
    method: 'POST',
  });
};
