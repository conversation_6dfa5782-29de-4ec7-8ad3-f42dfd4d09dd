import { request } from './api';
import {
  CustomerDetailResponse,
  CustomerListResponse,
  CustomerListParams,
  GetCustomerListParams,
  AddCustomerParams,
  UpdateCustomerParams,
  ApiResponse,
  CustomerSearchItem,
} from '@/types/customers';

// 创建客户
export const createCustomer = async (
  customerData: AddCustomerParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/customers',
    method: 'POST',
    data: customerData,
  });
};

// 获取客户列表 - 支持新的搜索参数
export const getCustomerList = async (
  params: GetCustomerListParams | CustomerListParams,
): Promise<ApiResponse<CustomerListResponse['data']>> => {
  return request({
    url: '/customers',
    method: 'GET',
    params,
  });
};

// 获取客户详情
export const getCustomerDetail = async (code: string): Promise<CustomerDetailResponse> => {
  return request({
    url: `/customers/${code}`,
    method: 'GET',
  });
};

// 搜索客户 - 新接口
export const searchCustomers = async (
  keyword: string,
): Promise<ApiResponse<CustomerSearchItem[]>> => {
  return request({
    url: '/customers/search',
    method: 'GET',
    params: { keyword },
  });
};

// 更新客户
export const updateCustomer = async (
  code: string,
  customerData: UpdateCustomerParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/customers/${code}`,
    method: 'PATCH',
    data: customerData,
  });
};

// 删除客户
export const deleteCustomer = async (code: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/customers/${code}`,
    method: 'DELETE',
  });
};

// 导出客户Excel
export const exportCustomerExcel = async (): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const response = await fetch(`${baseURL}/customers/export/excel`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出失败');
  }

  return response.blob();
};

// 下载导入模板
export const downloadCustomerImportTemplate = async (): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const response = await fetch(`${baseURL}/customers/import/template`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('下载模板失败');
  }

  return response.blob();
};

// 导入客户Excel
export const importCustomerExcel = async (file: File): Promise<ApiResponse<any>> => {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: '/customers/import/excel',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
