import axios, { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// 定义接口
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

interface PlatformConfigParams {
  platformKey: string;
}

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      // 使用 Bearer 认证方案
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data;
  },
  (error) => {
    // 处理 401 未授权错误（token 过期或无效）
    if (error.response && error.response.status === 401) {
      // 清除本地存储的 token 和用户信息
      localStorage.removeItem('token');
      localStorage.removeItem('token_expiry');
      localStorage.removeItem('user');

      // 跳转到登录页
      window.location.href = '/login';

      return Promise.reject(new Error('登录已过期，请重新登录'));
    }

    return Promise.reject(error);
  },
);

// 添加错误处理的通用请求方法
export const request = async <T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> => {
  try {
    // 这里的response已经是ApiResponse类型，因为我们在响应拦截器中处理了
    const response = (await api(config)) as unknown as ApiResponse<T>;

    // 检查响应状态码，如果不是成功状态码，抛出异常
    if (response.code !== 200) {
      throw new Error(response.message || '请求失败');
    }

    return response;
  } catch (error) {
    // 统一错误处理
    console.error('API请求错误:', error);
    throw error; // 重新抛出错误，让调用者处理
  }
};

export default api;
