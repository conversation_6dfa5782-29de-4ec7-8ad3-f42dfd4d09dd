import { request } from './api';
import {
  AddIncomeParams,
  IncomeListParams,
  IncomeListResponse,
  ExportIncomeParams,
  ApiResponse,
} from '@/types/Income';

// 创建收入详情 - POST /incomes/details
export const createIncomeDetail = async (
  incomeData: AddIncomeParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/incomes/details',
    method: 'POST',
    data: incomeData,
  });
};

// 获取收入详情列表 - GET /incomes/details
export const getIncomeList = async (
  params: IncomeListParams,
): Promise<ApiResponse<IncomeListResponse['data']>> => {
  return request({
    url: '/incomes/details',
    method: 'GET',
    params: {
      ...params,
      // 确保排序参数被正确传递
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    },
  });
};

// 获取收入详情 - GET /incomes/details/{id}
export const getIncomeDetail = async (id: string): Promise<ApiResponse<any>> => {
  return request({
    url: `/incomes/details/${id}`,
    method: 'GET',
  });
};

// 更新收入详情 - PATCH /incomes/details/{id}
export const updateIncomeDetail = async (
  id: string,
  incomeData: AddIncomeParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/incomes/details/${id}`,
    method: 'PATCH',
    data: incomeData,
  });
};

// 删除收入详情 - DELETE /incomes/details/{id}
export const deleteIncomeDetail = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/incomes/details/${id}`,
    method: 'DELETE',
  });
};

// 导出收入Excel - GET /incomes/export
export const exportIncomeExcel = async (params: ExportIncomeParams): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  // startTime和endTime现在是必填参数
  queryParams.append('startTime', params.startTime);
  queryParams.append('endTime', params.endTime);
  if (params.detailIds && params.detailIds.length > 0) {
    // 将UUID数组转换为逗号分隔的字符串，并进行URL编码
    const detailIdsString = params.detailIds.join(',');
    queryParams.append('detailIds', encodeURIComponent(detailIdsString));
  }
  if (params.customerSearch) {
    queryParams.append('customerSearch', encodeURIComponent(params.customerSearch));
  }
  if (params.responsibleUserSearch) {
    queryParams.append('responsibleUserSearch', encodeURIComponent(params.responsibleUserSearch));
  }
  if (params.remarkSearch) {
    queryParams.append('remarkSearch', encodeURIComponent(params.remarkSearch));
  }

  const url = `${baseURL}/incomes/export?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
