import { request } from './api';
import {
  AccessoryListProps,
  AccessorListResponse,
  AccessorListParams,
  AddAccessorParams,
  UpdateAccessoryParams,
  ApiResponse,
} from '@/types/accessories';

// 创建辅料
export const createAccessory = async (
  accessoryData: AddAccessorParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/accessories',
    method: 'POST',
    data: accessoryData,
  });
};

// 获取辅料列表
export const getAccessoryList = async (
  params: AccessorListParams,
): Promise<ApiResponse<AccessorListResponse['data']>> => {
  return request({
    url: '/accessories',
    method: 'GET',
    params,
  });
};

// 获取辅料详情
export const getAccessoryDetail = async (id: string): Promise<ApiResponse<AccessoryListProps>> => {
  return request({
    url: `/accessories/${id}`,
    method: 'GET',
  });
};

// 更新辅料
export const updateAccessory = async (
  id: string,
  accessoryData: UpdateAccessoryParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/accessories/${id}`,
    method: 'PATCH',
    data: accessoryData,
  });
};

// 删除辅料
export const deleteAccessory = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/accessories/${id}`,
    method: 'DELETE',
  });
};

// 辅料搜索接口（用于下拉选择器）
export const searchAccessories = async (params: {
  page?: number;
  pageSize?: number;
  nameSearch?: string;
}): Promise<ApiResponse<AccessorListResponse['data']>> => {
  return request({
    url: '/accessories/search',
    method: 'GET',
    params,
  });
};
