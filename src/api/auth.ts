import { request } from './api';
import type { LoginParams, LoginResponse, ApiResponse } from '@/types/auth';

// 用户登录
export const login = async (params: LoginParams): Promise<ApiResponse<LoginResponse>> => {
  return request({
    url: '/auth/login',
    method: 'POST',
    data: params,
  });
};

// 用户登出
export const logout = async (): Promise<ApiResponse<null>> => {
  return request({
    url: '/auth/logout',
    method: 'POST',
  });
};

// 刷新token
export const refreshToken = async (): Promise<ApiResponse<{ accessToken: string }>> => {
  return request({
    url: '/auth/refresh',
    method: 'POST',
  });
};
