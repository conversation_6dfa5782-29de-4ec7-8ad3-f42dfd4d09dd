import { request } from './api';
import type {
  CreateSalesDemandOrderParams,
  CreateSalesDemandOrderResponse,
  GetSalesDemandOrderListParams,
  GetSalesDemandOrderListResponse,
  GetMergeableDemandOrdersParams,
  GetMergeableDemandOrdersResponse,
  GetSalesDemandOrderDetailResponse,
  UpdateSalesDemandOrderParams,
  UpdateSalesDemandOrderResponse,
  SubmitSalesDemandOrderParams,
  SubmitSalesDemandOrderResponse,
  ApproveSalesDemandOrderParams,
  ApproveSalesDemandOrderResponse,
  CancelSalesDemandOrderResponse,
  DeleteSalesDemandOrderResponse,
  ExportSalesDemandOrdersParams,
} from '@/types/salesDemand';

/**
 * 创建销售需求订单
 */
export const createSalesDemandOrder = async (
  params: CreateSalesDemandOrderParams,
): Promise<CreateSalesDemandOrderResponse> => {
  return request({
    url: '/sales-demand-orders',
    method: 'POST',
    data: params,
  });
};

/**
 * 查询销售需求订单列表
 */
export const getSalesDemandOrderList = async (
  params: GetSalesDemandOrderListParams,
): Promise<GetSalesDemandOrderListResponse> => {
  return request({
    url: '/sales-demand-orders',
    method: 'GET',
    params,
  });
};

/**
 * 获取可合并的需求订单
 */
export const getMergeableDemandOrders = async (
  params?: GetMergeableDemandOrdersParams,
): Promise<GetMergeableDemandOrdersResponse> => {
  return request({
    url: '/sales-demand-orders/mergeable',
    method: 'GET',
    params,
  });
};

/**
 * 获取销售需求订单详情
 */
export const getSalesDemandOrderDetail = async (
  id: string,
): Promise<GetSalesDemandOrderDetailResponse> => {
  return request({
    url: `/sales-demand-orders/${id}`,
    method: 'GET',
  });
};

/**
 * 更新销售需求订单
 */
export const updateSalesDemandOrder = async (
  id: string,
  params: UpdateSalesDemandOrderParams,
): Promise<UpdateSalesDemandOrderResponse> => {
  return request({
    url: `/sales-demand-orders/${id}`,
    method: 'PATCH',
    data: params,
  });
};

/**
 * 提交审核
 */
export const submitSalesDemandOrder = async (
  id: string,
  params?: SubmitSalesDemandOrderParams,
): Promise<SubmitSalesDemandOrderResponse> => {
  return request({
    url: `/sales-demand-orders/${id}/submit`,
    method: 'POST',
    data: params,
  });
};

/**
 * 审核通过
 */
export const approveSalesDemandOrder = async (
  id: string,
  params: ApproveSalesDemandOrderParams,
): Promise<ApproveSalesDemandOrderResponse> => {
  return request({
    url: `/sales-demand-orders/${id}/approve`,
    method: 'POST',
    data: params,
  });
};

/**
 * 取消订单
 */
export const cancelSalesDemandOrder = async (id: string): Promise<CancelSalesDemandOrderResponse> => {
  return request({
    url: `/sales-demand-orders/${id}/cancel`,
    method: 'POST',
  });
};

/**
 * 删除订单
 */
export const deleteSalesDemandOrder = async (id: string): Promise<DeleteSalesDemandOrderResponse> => {
  return request({
    url: `/sales-demand-orders/${id}`,
    method: 'DELETE',
  });
};

/**
 * 导出Excel
 */
export const exportSalesDemandOrdersExcel = async (
  params?: ExportSalesDemandOrdersParams,
): Promise<Blob> => {
  return request({
    url: '/sales-demand-orders/export/excel',
    method: 'GET',
    params,
    responseType: 'blob',
  });
};
