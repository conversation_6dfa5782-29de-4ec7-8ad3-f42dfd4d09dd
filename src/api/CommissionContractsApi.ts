import { request } from './api';
import {
  AddCommissionContractsParams,
  CommissionContractsListParams,
  CommissionContractsListResponse,
  CommissionContractsDetailResponse,
  UpdateContractsParams,
  UpdateContractsStatusParams,
  UpdateContractsPaymentParams,
} from '@/types/commissionContracts';
import type { ApiResponse } from '@/api/api';

// 创建佣金发货合同 - POST /commission-contracts
export const createCommissionContract = async (
  contractData: AddCommissionContractsParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/commission-contracts',
    method: 'POST',
    data: contractData,
  });
};

// 获取佣金发货合同列表 - GET /commission-contracts
export const getCommissionContractsList = async (
  params: CommissionContractsListParams,
): Promise<ApiResponse<CommissionContractsListResponse['data']>> => {
  return request({
    url: '/commission-contracts',
    method: 'GET',
    params,
  });
};

// 获取佣金发货合同详情 - GET /commission-contracts/{id}
export const getCommissionContractDetail = async (
  id: string,
): Promise<ApiResponse<CommissionContractsDetailResponse['data']>> => {
  return request({
    url: `/commission-contracts/${id}`,
    method: 'GET',
  });
};

// 更新佣金发货合同 - PATCH /commission-contracts/{id}
export const updateCommissionContract = async (
  id: string,
  contractData: UpdateContractsParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/commission-contracts/${id}`,
    method: 'PATCH',
    data: contractData,
  });
};

// 删除佣金发货合同 - DELETE /commission-contracts/{id}
export const deleteCommissionContract = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/commission-contracts/${id}`,
    method: 'DELETE',
  });
};

// 更新合同状态 - PATCH /commission-contracts/status
export const updateCommissionContractStatus = async (
  id: string,
  statusData: UpdateContractsStatusParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/commission-contracts/${id}/status`,
    method: 'PATCH',
    data: statusData,
  });
};

// 更新支付状态 - PATCH /commission-contracts/payment
export const updateCommissionContractPayment = async (
  id: string,
  paymentData: UpdateContractsPaymentParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/commission-contracts/${id}/payment`,
    method: 'PATCH',
    data: paymentData,
  });
};

// 导出PDF - GET /commission-contracts/{id}/pdf
export const exportCommissionContractPdf = async (id: string): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const url = `${baseURL}/commission-contracts/${id}/pdf`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出PDF失败');
  }

  return response.blob();
};
