import { request } from './api';

// 定义上传文件夹常量
export const UPLOAD_FOLDERS = {
  PRODUCT: 'products',
  BRAND: 'brands',
  AVATAR: 'avatars',
  DOCUMENT: 'documents',
  SUPPLIER: 'suppliers',
  MEMO: 'memos',
};

/**
 * 上传单个图片
 * @param file 图片文件
 * @param folder 存储文件夹，默认为'images'
 * @returns Promise<{code: number, data: {url: string}, message: string}>
 */
export const uploadImage = async (file: File, folder: string = 'images'): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('folder', folder);

  return request({
    url: '/file-upload/image',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 上传单个文件（通用文件上传）
 * @param file 文件
 * @param folder 存储文件夹，默认为'documents'
 * @returns Promise<{code: number, data: {url: string}, message: string}>
 */
export const uploadFile = async (file: File, folder: string = 'documents'): Promise<any> => {
  const formData = new FormData();
  formData.append('files', file);
  formData.append('folder', folder);

  return request({
    url: '/file-upload/files',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 上传多个文件
 * @param files 文件数组
 * @param folder 存储文件夹，默认为'documents'
 * @returns Promise<{code: number, data: {urls: string[]}, message: string}>
 */
export const uploadMultipleFiles = async (
  files: File[],
  folder: string = 'documents',
): Promise<any> => {
  const formData = new FormData();

  files.forEach((file) => {
    formData.append('files', file);
  });

  formData.append('folder', folder);

  return request({
    url: '/file-upload/files',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 上传多个图片
 * @param files 图片文件数组
 * @param folder 存储文件夹，默认为'images'
 * @returns Promise<{code: number, data: {urls: string[]}, message: string}>
 */
export const uploadImages = async (files: File[], folder: string = 'images'): Promise<any> => {
  const formData = new FormData();

  files.forEach((file) => {
    formData.append('files', file);
  });

  formData.append('folder', folder);

  return request({
    url: '/file-upload/images',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 处理Ant Design Upload组件的自定义上传
 * @param options Upload组件的customRequest参数
 * @param folder 存储文件夹
 */
export const customUploadRequest = async (
  options: any,
  folder: string = UPLOAD_FOLDERS.PRODUCT,
) => {
  const { file, onSuccess, onError, onProgress } = options;

  try {
    // 如果需要显示上传进度
    if (onProgress) {
      onProgress({ percent: 50 });
    }

    const response = await uploadImage(file, folder);

    if (response.code === 200) {
      // 上传成功
      if (onSuccess) {
        // 返回格式与Ant Design Upload组件期望的格式一致
        onSuccess(response.data);
      }
      return response.data;
    } else {
      // 上传失败
      if (onError) {
        onError(new Error(response.message || '上传失败'));
      }
      return null;
    }
  } catch (error) {
    // 处理错误
    if (onError) {
      onError(error);
    }
    return null;
  }
};

/**
 * 处理Ant Design Upload组件的多文件自定义上传
 * @param options Upload组件的customRequest参数
 * @param folder 存储文件夹
 */
export const customMultipleUploadRequest = async (
  options: any,
  folder: string = UPLOAD_FOLDERS.PRODUCT,
) => {
  const { fileList, onSuccess, onError, onProgress } = options;

  try {
    // 如果需要显示上传进度
    if (onProgress) {
      onProgress({ percent: 50 });
    }

    const files = fileList.map((fileItem: any) => fileItem.originFileObj);
    const response = await uploadImages(files, folder);

    if (response.code === 200) {
      // 上传成功
      if (onSuccess) {
        // 返回格式与Ant Design Upload组件期望的格式一致
        onSuccess(response.data);
      }
      return response.data;
    } else {
      // 上传失败
      if (onError) {
        onError(new Error(response.message || '上传失败'));
      }
      return null;
    }
  } catch (error) {
    // 处理错误
    if (onError) {
      onError(error);
    }
    return null;
  }
};
