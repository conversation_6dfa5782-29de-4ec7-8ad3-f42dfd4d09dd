import { request } from './api';
import {
  ProductCategoryListProps,
  ProductCategoryListResponse,
  ProductCategoryListParams,
  AddProductCategoryParams,
  UpdateProductCategoryParams,
  ApiResponse,
} from '@/types/productCategory';

// 创建商品分类
export const createProductCategory = async (
  categoryData: AddProductCategoryParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/product-categories',
    method: 'POST',
    data: categoryData,
  });
};

// 获取商品分类列表
export const getProductCategoryList = async (
  params: ProductCategoryListParams,
): Promise<ApiResponse<ProductCategoryListResponse['data']>> => {
  return request({
    url: '/product-categories',
    method: 'GET',
    params,
  });
};

// 获取商品分类详情
export const getProductCategoryDetail = async (
  id: string,
): Promise<ApiResponse<ProductCategoryListProps>> => {
  return request({
    url: `/product-categories/${id}`,
    method: 'GET',
  });
};

// 更新商品分类
export const updateProductCategory = async (
  id: string,
  categoryData: UpdateProductCategoryParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/product-categories/${id}`,
    method: 'PATCH',
    data: categoryData,
  });
};

// 删除商品分类
export const deleteProductCategory = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/product-categories/${id}`,
    method: 'DELETE',
  });
};
