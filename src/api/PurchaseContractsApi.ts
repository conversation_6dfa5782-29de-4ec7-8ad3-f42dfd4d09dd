import { request } from './api';
import {
  AddPurchaseContractParams,
  GetPurchaseListParams,
  PurchaseContractListReponse,
  PurchaseContractDetailResponse,
  UpdatePurchaseContractsParams,
  UpdateDraftPricesParams,
  ExportPurchaseContractsParams,
  ApiResponse,
} from '@/types/purchaseContracts';

// 创建采购合同 - POST /purchase-contracts
export const createPurchaseContract = async (
  contractData: AddPurchaseContractParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/purchase-contracts',
    method: 'POST',
    data: contractData,
  });
};

// 获取采购合同列表 - GET /purchase-contracts
export const getPurchaseContractsList = async (
  params: GetPurchaseListParams,
): Promise<ApiResponse<PurchaseContractListReponse['data']>> => {
  return request({
    url: '/purchase-contracts',
    method: 'GET',
    params,
  });
};

// 获取采购合同详情 - GET /purchase-contracts/{id}
export const getPurchaseContractDetail = async (
  id: string,
): Promise<ApiResponse<PurchaseContractDetailResponse['data']>> => {
  return request({
    url: `/purchase-contracts/${id}`,
    method: 'GET',
  });
};

// 更新采购合同 - PATCH /purchase-contracts/{id}
export const updatePurchaseContract = async (
  id: string,
  contractData: UpdatePurchaseContractsParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/purchase-contracts/${id}`,
    method: 'PATCH',
    data: contractData,
  });
};

// 删除采购合同 - DELETE /purchase-contracts/{id}
export const deletePurchaseContract = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/purchase-contracts/${id}`,
    method: 'DELETE',
  });
};

// 更新草稿价格 - PATCH /purchase-contracts/update-draft-prices
export const updateDraftPrices = async (
  params: UpdateDraftPricesParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/purchase-contracts/update-draft-prices',
    method: 'PATCH',
    data: params,
  });
};

// 导出采购合同Excel - GET /purchase-contracts/export/excel
export const exportPurchaseContractsExcel = async (
  params: ExportPurchaseContractsParams,
): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params.startTime) queryParams.append('startTime', params.startTime);
  if (params.endTime) queryParams.append('endTime', params.endTime);
  if (params.supplierCode) queryParams.append('supplierCode', params.supplierCode);
  if (params.status) queryParams.append('status', params.status);
  if (params.contractIds && params.contractIds.length > 0) {
    // 将UUID数组转换为逗号分隔的字符串，并进行URL编码
    const contractIdsString = params.contractIds.join(',');
    queryParams.append('contractIds', encodeURIComponent(contractIdsString));
  }

  const url = `${baseURL}/purchase-contracts/export/excel${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
