import { request } from './api';
import type {
  CreateSalesReturnParams,
  CreateSalesReturnResponse,
  UpdateSalesReturnParams,
  UpdateSalesReturnResponse,
  GetSalesReturnListParams,
  GetSalesReturnListResponse,
  GetSalesReturnDetailResponse,
  DeleteSalesReturnResponse,
  SubmitSalesReturnParams,
  SubmitSalesReturnResponse,
  ApproveSalesReturnParams,
  ApproveSalesReturnResponse,
  ProcessSalesReturnParams,
  ProcessSalesReturnResponse,
  CompleteSalesReturnParams,
  CompleteSalesReturnResponse,
  CancelSalesReturnResponse,
} from '@/types/salesReturn';

/**
 * 创建销售退单
 */
export const createSalesReturn = async (
  params: CreateSalesReturnParams,
): Promise<CreateSalesReturnResponse> => {
  return request({
    url: '/sales-returns',
    method: 'POST',
    data: params,
  });
};

/**
 * 查询销售退单列表
 */
export const getSalesReturnList = async (
  params: GetSalesReturnListParams,
): Promise<GetSalesReturnListResponse> => {
  return request({
    url: '/sales-returns',
    method: 'GET',
    params,
  });
};

/**
 * 获取销售退单详情
 */
export const getSalesReturnDetail = async (
  id: string,
): Promise<GetSalesReturnDetailResponse> => {
  return request({
    url: `/sales-returns/${id}`,
    method: 'GET',
  });
};

/**
 * 更新销售退单
 */
export const updateSalesReturn = async (
  id: string,
  params: UpdateSalesReturnParams,
): Promise<UpdateSalesReturnResponse> => {
  return request({
    url: `/sales-returns/${id}`,
    method: 'PUT',
    data: params,
  });
};

/**
 * 删除销售退单
 */
export const deleteSalesReturn = async (id: string): Promise<DeleteSalesReturnResponse> => {
  return request({
    url: `/sales-returns/${id}`,
    method: 'DELETE',
  });
};

/**
 * 提交销售退单
 */
export const submitSalesReturn = async (
  id: string,
  params: SubmitSalesReturnParams,
): Promise<SubmitSalesReturnResponse> => {
  return request({
    url: `/sales-returns/${id}/submit`,
    method: 'POST',
    data: params,
  });
};

/**
 * 审核销售退单
 */
export const approveSalesReturn = async (
  id: string,
  params: ApproveSalesReturnParams,
): Promise<ApproveSalesReturnResponse> => {
  return request({
    url: `/sales-returns/${id}/approve`,
    method: 'POST',
    data: params,
  });
};

/**
 * 处理销售退单
 */
export const processSalesReturn = async (
  id: string,
  params: ProcessSalesReturnParams,
): Promise<ProcessSalesReturnResponse> => {
  return request({
    url: `/sales-returns/${id}/process`,
    method: 'POST',
    data: params,
  });
};

/**
 * 完成销售退单
 */
export const completeSalesReturn = async (
  id: string,
  params: CompleteSalesReturnParams,
): Promise<CompleteSalesReturnResponse> => {
  return request({
    url: `/sales-returns/${id}/complete`,
    method: 'POST',
    data: params,
  });
};

/**
 * 取消销售退单
 */
export const cancelSalesReturn = async (id: string): Promise<CancelSalesReturnResponse> => {
  return request({
    url: `/sales-returns/${id}/cancel`,
    method: 'POST',
  });
};

/**
 * 导出销售退单Excel
 */
export const exportSalesReturnsExcel = async (params: {
  customerCode?: string;
  applicantCode?: string;
  returnType?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  returnIds?: string[];
}): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params.customerCode) queryParams.append('customerCode', params.customerCode);
  if (params.applicantCode) queryParams.append('applicantCode', params.applicantCode);
  if (params.returnType) queryParams.append('returnType', params.returnType);
  if (params.status) queryParams.append('status', params.status);
  if (params.startDate) queryParams.append('startDate', params.startDate);
  if (params.endDate) queryParams.append('endDate', params.endDate);
  if (params.returnIds && params.returnIds.length > 0) {
    const returnIdsString = params.returnIds.join(',');
    queryParams.append('returnIds', encodeURIComponent(returnIdsString));
  }

  const url = `${baseURL}/sales-returns/export/excel${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
