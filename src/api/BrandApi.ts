import { request } from './api';
import {
  BrandListProps,
  BrandListResponse,
  BrandListParams,
  AddBrandParams,
  UpdateBrandParams,
  ApiResponse,
} from '@/types/brands';

// 创建品牌
export const createBrand = async (brandData: AddBrandParams): Promise<ApiResponse<null>> => {
  return request({
    url: '/brands',
    method: 'POST',
    data: brandData,
  });
};

// 获取品牌列表
export const getBrandList = async (
  params: BrandListParams,
): Promise<ApiResponse<BrandListResponse['data']>> => {
  return request({
    url: '/brands',
    method: 'GET',
    params,
  });
};

// 获取品牌详情
export const getBrandDetail = async (code: string): Promise<ApiResponse<BrandListProps>> => {
  return request({
    url: `/brands/${code}`,
    method: 'GET',
  });
};

// 更新品牌
export const updateBrand = async (
  code: string,
  brandData: UpdateBrandParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/brands/${code}`,
    method: 'PATCH',
    data: brandData,
  });
};

// 删除品牌
export const deleteBrand = async (code: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/brands/${code}`,
    method: 'DELETE',
  });
};
