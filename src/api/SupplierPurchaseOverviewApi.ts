import { request } from './api';
import type {
  GetSupplierOverviewParams,
  SupplierOverviewResponse,
  GetSupplierRankingParams,
  SupplierRankingResponse,
  GetSupplierPerformanceParams,
  SupplierPerformanceResponse,
  GetSupplierDetailParams,
  SupplierDetailResponse,
} from '@/types/supplierPurchase';

/**
 * 获取所有供应商采购概览
 */
export const getSupplierOverview = async (
  params?: GetSupplierOverviewParams,
): Promise<SupplierOverviewResponse> => {
  return request({
    url: '/supplier-purchase-overview',
    method: 'GET',
    params,
  });
};

/**
 * 获取供应商采购排行榜
 */
export const getSupplierRanking = async (
  params?: GetSupplierRankingParams,
): Promise<SupplierRankingResponse> => {
  return request({
    url: '/supplier-purchase-overview/ranking',
    method: 'GET',
    params,
  });
};

/**
 * 获取供应商绩效对比
 */
export const getSupplierPerformanceComparison = async (
  params: GetSupplierPerformanceParams,
): Promise<SupplierPerformanceResponse> => {
  return request({
    url: '/supplier-purchase-overview/performance-comparison',
    method: 'GET',
    params,
  });
};

/**
 * 获取单个供应商采购详情
 */
export const getSupplierPurchaseDetail = async (
  supplierCode: string,
  params?: Omit<GetSupplierDetailParams, 'supplierCode'>,
): Promise<SupplierDetailResponse> => {
  return request({
    url: `/supplier-purchase-overview/${supplierCode}`,
    method: 'GET',
    params,
  });
};
