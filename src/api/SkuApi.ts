import { request } from './api';
import {
  Sku,
  PaginatedListResponse,
  PaginatedParams,
  AddSkuParams,
  UpdateSkuParams,
} from '@/types/skus';
import { ApiResponse } from '@/types/common';

// 创建SKU
export const createSku = async (skuData: AddSkuParams): Promise<ApiResponse<null>> => {
  return request({
    url: '/skus',
    method: 'POST',
    data: skuData,
  });
};

// 获取SKU分页列表
export const getSkuList = async (
  params: PaginatedParams,
): Promise<ApiResponse<PaginatedListResponse['data']>> => {
  return request({
    url: '/skus/paginated',
    method: 'GET',
    params,
  });
};

// 获取SKU详情
export const getSkuDetail = async (id: string): Promise<ApiResponse<Sku>> => {
  return request({
    url: `/skus/${id}`,
    method: 'GET',
  });
};

// 更新SKU
export const updateSku = async (
  id: string,
  skuData: UpdateSkuParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/skus/${id}`,
    method: 'PATCH',
    data: skuData,
  });
};

// 删除SKU
export const deleteSku = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/skus/${id}`,
    method: 'DELETE',
  });
};

// 导出SKU Excel
export const exportSkuExcel = async (params?: {
  skuIds?: string[];
  nameSearch?: string;
  codeSearch?: string;
  brandCodeSearch?: string;
  supplierCodeSearch?: string;
  categoryCodeSearch?: string;
  colorCodeSearch?: string;
}): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params?.skuIds && params.skuIds.length > 0) {
    queryParams.append('skuIds', params.skuIds.join(','));
  }
  if (params?.nameSearch) {
    queryParams.append('nameSearch', params.nameSearch);
  }
  if (params?.codeSearch) {
    queryParams.append('codeSearch', params.codeSearch);
  }
  if (params?.brandCodeSearch) {
    queryParams.append('brandCodeSearch', params.brandCodeSearch);
  }
  if (params?.supplierCodeSearch) {
    queryParams.append('supplierCodeSearch', params.supplierCodeSearch);
  }
  if (params?.categoryCodeSearch) {
    queryParams.append('categoryCodeSearch', params.categoryCodeSearch);
  }
  if (params?.colorCodeSearch) {
    queryParams.append('colorCodeSearch', params.colorCodeSearch);
  }

  const url = `${baseURL}/skus/export/excel?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};

// 下载导入模板
export const downloadSkuImportTemplate = async (): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const response = await fetch(`${baseURL}/skus/import/template`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('下载模板失败');
  }

  return response.blob();
};

// 导入SKU Excel
export const importSkuExcel = async (file: File): Promise<ApiResponse<null>> => {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: '/skus/import/excel',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
