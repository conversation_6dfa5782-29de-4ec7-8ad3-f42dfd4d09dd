import { request } from './api';
import {
  Product,
  ProductListItem,
  ProductListResponse,
  ProductListParams,
  ProductDetailResponse,
  AddProductParams,
  UpdateProductDetailParams,
  ExportProductsParams,
  ProductOrderInfoResponse,
  ApiResponse,
} from '@/types/product';

// 创建商品
export const createProduct = async (productData: AddProductParams): Promise<ApiResponse<null>> => {
  return request({
    url: '/products',
    method: 'POST',
    data: productData,
  });
};

// 获取商品列表
export const getProductList = async (
  params: ProductListParams,
): Promise<ApiResponse<ProductListResponse['data']>> => {
  return request({
    url: '/products',
    method: 'GET',
    params,
  });
};

// 获取商品详情
export const getProductDetail = async (id: string): Promise<ApiResponse<Product>> => {
  return request({
    url: `/products/${id}`,
    method: 'GET',
  });
};

// 更新商品
export const updateProduct = async (
  id: string,
  productData: UpdateProductDetailParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/products/${id}`,
    method: 'PUT',
    data: { ...productData },
  });
};

// 删除商品
export const deleteProduct = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/products/${id}`,
    method: 'DELETE',
  });
};

// 导出商品Excel
export const exportProductsExcel = async (params: ExportProductsParams): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params.brandCode) queryParams.append('brandCode', params.brandCode);
  if (params.supplierCode) queryParams.append('supplierCode', params.supplierCode);
  if (params.productIds && params.productIds.length > 0) {
    // 将商品ID数组转换为逗号分隔的字符串，并进行URL编码
    const productIdsString = params.productIds.join(',');
    queryParams.append('productIds', encodeURIComponent(productIdsString));
  }

  const url = `${baseURL}/products/export/excel${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};

// 下载导入模板
export const downloadImportTemplate = async (): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const url = `${baseURL}/products/import/template`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('下载模板失败');
  }

  return response.blob();
};

// 导入商品Excel
export const importProductsExcel = async (file: File): Promise<ApiResponse<any>> => {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: '/products/import/excel',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 根据商品编码获取订单信息（包含库存和价格）
 */
export const getProductOrderInfo = async (
  productCode: string,
): Promise<ProductOrderInfoResponse> => {
  return request({
    url: `/products/code/${productCode}/order-info`,
    method: 'GET',
  });
};
