import { request } from './api';
import {
  AddExpenseParams,
  ExpensesListParams,
  ExpensesListResponse,
  ExportExpenseParams,
  ApiResponse,
} from '@/types/Expense';

// 创建支出详情 - POST /expenses/details
export const createExpenseDetail = async (
  expenseData: AddExpenseParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/expenses/details',
    method: 'POST',
    data: expenseData,
  });
};

// 获取支出详情列表 - GET /expenses/details
export const getExpenseList = async (
  params: ExpensesListParams,
): Promise<ApiResponse<ExpensesListResponse['data']>> => {
  return request({
    url: '/expenses/details',
    method: 'GET',
    params: {
      ...params,
      // 确保排序参数被正确传递
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    },
  });
};

// 获取支出详情 - GET /expenses/details/{id}
export const getExpenseDetail = async (id: string): Promise<ApiResponse<any>> => {
  return request({
    url: `/expenses/details/${id}`,
    method: 'GET',
  });
};

// 更新支出详情 - PATCH /expenses/details/{id}
export const updateExpenseDetail = async (
  id: string,
  expenseData: AddExpenseParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/expenses/details/${id}`,
    method: 'PATCH',
    data: expenseData,
  });
};

// 删除支出详情 - DELETE /expenses/details/{id}
export const deleteExpenseDetail = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/expenses/details/${id}`,
    method: 'DELETE',
  });
};

// 导出支出Excel - GET /expenses/export
export const exportExpenseExcel = async (params: ExportExpenseParams): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  // startTime和endTime现在是必填参数
  queryParams.append('startTime', params.startTime);
  queryParams.append('endTime', params.endTime);
  if (params.detailIds && params.detailIds.length > 0) {
    // 将UUID数组转换为逗号分隔的字符串，并进行URL编码
    const detailIdsString = params.detailIds.join(',');
    queryParams.append('detailIds', encodeURIComponent(detailIdsString));
  }
  if (params.supplierSearch) {
    queryParams.append('supplierSearch', encodeURIComponent(params.supplierSearch));
  }
  if (params.remarkSearch) {
    queryParams.append('remarkSearch', encodeURIComponent(params.remarkSearch));
  }

  const url = `${baseURL}/expenses/export?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};

// 下载导入模板 - GET /expenses/import/template
export const downloadExpenseImportTemplate = async (): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const response = await fetch(`${baseURL}/expenses/import/template`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('下载模板失败');
  }

  return response.blob();
};

// 导入支出Excel - POST /expenses/import/excel
export const importExpenseExcel = async (file: File): Promise<ApiResponse<any>> => {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: '/expenses/import/excel',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
