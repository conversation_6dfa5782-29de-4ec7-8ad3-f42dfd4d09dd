import { request } from './api';
import type {
  AddMemoParams,
  AddMemoParamsReponse,
  MemosListParams,
  MemoeListReponse,
  UpdateMemoParams,
  ApiResponse,
} from '@/types/memo';

// 获取备忘录列表
export const getMemoList = async (params: MemosListParams): Promise<MemoeListReponse> => {
  return request({
    url: '/memos',
    method: 'GET',
    params,
  });
};

// 获取备忘录详情
export const getMemoDetail = async (
  id: string,
): Promise<
  ApiResponse<{
    id: string;
    title: string;
    details: string | null;
    image: string | null;
    file: string | null;
    createdAt: string;
    updatedAt: string;
  }>
> => {
  return request({
    url: `/memos/${id}`,
    method: 'GET',
  });
};

// 创建备忘录
export const createMemo = async (params: AddMemoParams): Promise<AddMemoParamsReponse> => {
  return request({
    url: '/memos',
    method: 'POST',
    data: params,
  });
};

// 更新备忘录
export const updateMemo = async (
  id: string,
  params: UpdateMemoParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/memos/${id}`,
    method: 'PATCH',
    data: params,
  });
};

// 删除备忘录
export const deleteMemo = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/memos/${id}`,
    method: 'DELETE',
  });
};
