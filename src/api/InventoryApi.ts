import { request } from './api';
import {
  ProductInventoryResponse,
  InventoryListParams,
  InventoryListResponse,
  BatchInventorySummaryParams,
  BatchInventorySummaryResponse,
  InventoryAdjustParams,
  ExportInventoryParams,
  CreateInventoryParams,
  BatchCreateInventoryParams,
  UpdateInventoryParams,
  ApiResponse,
} from '@/types/inventory';

// 获取商品库存信息（按颜色分组）
export const getProductInventory = async (
  productCode: string,
): Promise<ApiResponse<ProductInventoryResponse>> => {
  return request({
    url: `/inventory/products/${productCode}`,
    method: 'GET',
  });
};

// 批量获取商品库存汇总
export const getBatchInventorySummary = async (
  params: BatchInventorySummaryParams,
): Promise<ApiResponse<BatchInventorySummaryResponse['data']>> => {
  return request({
    url: '/inventory/products/batch',
    method: 'POST',
    data: params,
  });
};

// 库存明细列表查询
export const getInventoryList = async (
  params: InventoryListParams,
): Promise<ApiResponse<InventoryListResponse['data']>> => {
  return request({
    url: '/inventory',
    method: 'GET',
    params,
  });
};

// 创建库存明细
export const createInventory = async (
  params: CreateInventoryParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/inventory',
    method: 'POST',
    data: params,
  });
};

// 批量创建库存明细
export const batchCreateInventory = async (
  params: BatchCreateInventoryParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/inventory/batch',
    method: 'POST',
    data: params,
  });
};

// 更新库存明细
export const updateInventory = async (
  id: string,
  params: UpdateInventoryParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/inventory/${id}`,
    method: 'PATCH',
    data: params,
  });
};

// 调整库存数量
export const adjustInventory = async (
  params: InventoryAdjustParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/inventory/adjust',
    method: 'POST',
    data: params,
  });
};

// 删除库存明细
export const deleteInventory = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/inventory/${id}`,
    method: 'DELETE',
  });
};

// 导出库存Excel
export const exportInventoryExcel = async (params: ExportInventoryParams): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params.productCode) queryParams.append('productCode', params.productCode);
  if (params.colorCode) queryParams.append('colorCode', params.colorCode);
  if (params.brandCode) queryParams.append('brandCode', params.brandCode);
  if (params.supplierCode) queryParams.append('supplierCode', params.supplierCode);
  if (params.lowStockOnly !== undefined)
    queryParams.append('lowStockOnly', params.lowStockOnly.toString());
  if (params.hasStockOnly !== undefined)
    queryParams.append('hasStockOnly', params.hasStockOnly.toString());
  if (params.inventoryIds && params.inventoryIds.length > 0) {
    // 将库存ID数组转换为逗号分隔的字符串，并进行URL编码
    const inventoryIdsString = params.inventoryIds.join(',');
    queryParams.append('inventoryIds', encodeURIComponent(inventoryIdsString));
  }

  const url = `${baseURL}/inventory/export/excel${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
