import { request } from './api';
import {
  CompanyListData,
  CompanyListParams,
  ApiResponse,
  CompanyDetailProps,
} from '@/types/company';

export const createCompany = async (companyData: {
  code: string;
  name: string;
  address?: string;
  socialCreditCode?: string;
  businessLicenseUrl?: string;
  managerCode?: string;
  employeeCodes?: string[];
}): Promise<ApiResponse<null>> => {
  return request({
    url: '/companies',
    method: 'POST',
    data: companyData,
  });
};

// 获取用户列表
export const getCompanyList = async (
  params: CompanyListParams = {},
): Promise<ApiResponse<CompanyListData>> => {
  return request({
    url: '/companies',
    method: 'GET',
    params,
  });
};

export const updateCompany = async (
  code: string,
  companyData: {
    name: string;
    address?: string;
    socialCreditCode?: string;
    businessLicenseUrl?: string;
    managerCode?: string;
    employeeCodes?: string[];
  },
): Promise<ApiResponse<null>> => {
  return request({
    url: `/companies/${code}`,
    method: 'PATCH',
    data: companyData,
  });
};

export const deleteCompany = async (code: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/companies/${code}`,
    method: 'DELETE',
  });
};

export const getCompanyDetail = async (code: string): Promise<ApiResponse<CompanyDetailProps>> => {
  return request({
    url: `/companies/${code}`,
    method: 'GET',
  });
};
