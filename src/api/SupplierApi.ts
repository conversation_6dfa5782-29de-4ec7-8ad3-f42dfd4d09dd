import { request } from './api';
import {
  SupplierListProps,
  SupplierListData,
  SupplierListParams,
  AddSupplierParams,
  UpdateSupplierParams,
  ApiResponse,
} from '@/types/supplier';

// 创建供应商
export const createSupplier = async (
  supplierData: AddSupplierParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/suppliers',
    method: 'POST',
    data: supplierData,
  });
};

// 获取供应商列表
export const getSupplierList = async (
  params: SupplierListParams,
): Promise<ApiResponse<SupplierListData['data']>> => {
  return request({
    url: '/suppliers',
    method: 'GET',
    params,
  });
};

// 获取供应商详情
export const getSupplierDetail = async (code: string): Promise<ApiResponse<SupplierListProps>> => {
  return request({
    url: `/suppliers/${code}`,
    method: 'GET',
  });
};

// 更新供应商
export const updateSupplier = async (
  code: string,
  supplierData: UpdateSupplierParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/suppliers/${code}`,
    method: 'PATCH',
    data: supplierData,
  });
};

// 删除供应商
export const deleteSupplier = async (code: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/suppliers/${code}`,
    method: 'DELETE',
  });
};

// 导出供应商Excel
export const exportSupplierExcel = async (): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const response = await fetch(`${baseURL}/suppliers/export/excel`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出失败');
  }

  return response.blob();
};

// 下载导入模板
export const downloadImportTemplate = async (): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const response = await fetch(`${baseURL}/suppliers/import/template`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('下载模板失败');
  }

  return response.blob();
};

// 导入供应商Excel
export const importSupplierExcel = async (file: File): Promise<ApiResponse<any>> => {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: '/suppliers/import/excel',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
