import { request } from './api';
import type {
  CreateSalesOrderParams,
  CreateSalesOrderResponse,
  UpdateSalesOrderParams,
  UpdateSalesOrderResponse,
  GetSalesOrderListParams,
  GetSalesOrderListResponse,
  GetSalesOrderDetailResponse,
  DeleteSalesOrderResponse,
  ConfirmSalesOrderParams,
  ConfirmSalesOrderResponse,
  ShipSalesOrderParams,
  ShipSalesOrderResponse,
  CompleteSalesOrderParams,
  CompleteSalesOrderResponse,
  CancelSalesOrderResponse,
} from '@/types/salesOrder';

/**
 * 创建销售订单
 */
export const createSalesOrder = async (
  params: CreateSalesOrderParams,
): Promise<CreateSalesOrderResponse> => {
  return request({
    url: '/sales-orders',
    method: 'POST',
    data: params,
  });
};

/**
 * 查询销售订单列表
 */
export const getSalesOrderList = async (
  params: GetSalesOrderListParams,
): Promise<GetSalesOrderListResponse> => {
  return request({
    url: '/sales-orders',
    method: 'GET',
    params,
  });
};

/**
 * 获取销售订单详情
 */
export const getSalesOrderDetail = async (
  id: string,
): Promise<GetSalesOrderDetailResponse> => {
  return request({
    url: `/sales-orders/${id}`,
    method: 'GET',
  });
};

/**
 * 更新销售订单
 */
export const updateSalesOrder = async (
  id: string,
  params: UpdateSalesOrderParams,
): Promise<UpdateSalesOrderResponse> => {
  return request({
    url: `/sales-orders/${id}`,
    method: 'PUT',
    data: params,
  });
};

/**
 * 删除销售订单
 */
export const deleteSalesOrder = async (id: string): Promise<DeleteSalesOrderResponse> => {
  return request({
    url: `/sales-orders/${id}`,
    method: 'DELETE',
  });
};

/**
 * 确认销售订单
 */
export const confirmSalesOrder = async (
  id: string,
  params: ConfirmSalesOrderParams,
): Promise<ConfirmSalesOrderResponse> => {
  return request({
    url: `/sales-orders/${id}/confirm`,
    method: 'POST',
    data: params,
  });
};

/**
 * 发货
 */
export const shipSalesOrder = async (
  id: string,
  params: ShipSalesOrderParams,
): Promise<ShipSalesOrderResponse> => {
  return request({
    url: `/sales-orders/${id}/ship`,
    method: 'POST',
    data: params,
  });
};

/**
 * 完成订单
 */
export const completeSalesOrder = async (
  id: string,
  params: CompleteSalesOrderParams,
): Promise<CompleteSalesOrderResponse> => {
  return request({
    url: `/sales-orders/${id}/complete`,
    method: 'POST',
    data: params,
  });
};

/**
 * 取消销售订单
 */
export const cancelSalesOrder = async (id: string): Promise<CancelSalesOrderResponse> => {
  return request({
    url: `/sales-orders/${id}/cancel`,
    method: 'POST',
  });
};

/**
 * 导出销售订单Excel
 */
export const exportSalesOrdersExcel = async (params: {
  customerCode?: string;
  salesPersonCode?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  orderIds?: string[];
}): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params.customerCode) queryParams.append('customerCode', params.customerCode);
  if (params.salesPersonCode) queryParams.append('salesPersonCode', params.salesPersonCode);
  if (params.status) queryParams.append('status', params.status);
  if (params.startDate) queryParams.append('startDate', params.startDate);
  if (params.endDate) queryParams.append('endDate', params.endDate);
  if (params.orderIds && params.orderIds.length > 0) {
    const orderIdsString = params.orderIds.join(',');
    queryParams.append('orderIds', encodeURIComponent(orderIdsString));
  }

  const url = `${baseURL}/sales-orders/export/excel${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
