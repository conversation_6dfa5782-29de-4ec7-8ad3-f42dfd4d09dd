import { request } from './api';
import {
  SkusInventoryData,
  SkusInventoryListResponse,
  GetSkusInventoryListParams,
  AddSkusInventory,
  UpdateSkusInventory,
  AdjustSkusInventoryStock,
  CountSkusInventory,
  ExportSkusInventoryParams,
} from '@/types/skusInventory';
import { ApiResponse } from '@/types/common';

// 创建SKU库存明细
export const createSkusInventory = async (
  inventoryData: AddSkusInventory,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/skus-inventory',
    method: 'POST',
    data: inventoryData,
  });
};

// 获取SKU库存明细分页列表
export const getSkusInventoryList = async (
  params: GetSkusInventoryListParams,
): Promise<ApiResponse<SkusInventoryListResponse['data']>> => {
  return request({
    url: '/skus-inventory',
    method: 'GET',
    params,
  });
};

// 根据SKU ID获取库存明细
export const getSkusInventoryBySku = async (
  skuId: string,
): Promise<ApiResponse<SkusInventoryData[]>> => {
  return request({
    url: `/skus-inventory/sku/${skuId}`,
    method: 'GET',
  });
};

// 获取SKU库存明细详情
export const getSkusInventoryDetail = async (
  id: string,
): Promise<ApiResponse<SkusInventoryData>> => {
  return request({
    url: `/skus-inventory/${id}`,
    method: 'GET',
  });
};

// 更新SKU库存明细
export const updateSkusInventory = async (
  id: string,
  inventoryData: UpdateSkusInventory,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/skus-inventory/${id}`,
    method: 'PATCH',
    data: inventoryData,
  });
};

// 删除SKU库存明细
export const deleteSkusInventory = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/skus-inventory/${id}`,
    method: 'DELETE',
  });
};

// 调整库存数量
export const adjustSkusInventoryStock = async (
  id: string,
  adjustData: AdjustSkusInventoryStock,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/skus-inventory/${id}/stock`,
    method: 'PATCH',
    data: adjustData,
  });
};

// 库存盘点
export const countSkusInventory = async (
  id: string,
  countData: CountSkusInventory,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/skus-inventory/${id}/count`,
    method: 'PATCH',
    data: countData,
  });
};

// 导出SKU库存明细Excel
export const exportSkusInventoryExcel = async (
  params?: ExportSkusInventoryParams,
): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params?.skusInventoryIds && params.skusInventoryIds.length > 0) {
    queryParams.append('skusInventoryIds', params.skusInventoryIds.join(','));
  }
  if (params?.search) {
    queryParams.append('search', params.search);
  }
  if (params?.skuCode) {
    queryParams.append('skuCode', params.skuCode);
  }
  if (params?.brandCode) {
    queryParams.append('brandCode', params.brandCode);
  }
  if (params?.colorCode) {
    queryParams.append('colorCode', params.colorCode);
  }
  if (params?.size) {
    queryParams.append('size', params.size);
  }
  if (params?.stockStatus) {
    queryParams.append('stockStatus', params.stockStatus);
  }

  const url = `${baseURL}/skus-inventory/export/excel?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};

// 下载导入模板
export const downloadSkusInventoryImportTemplate = async (): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const response = await fetch(`${baseURL}/skus-inventory/import/template`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('下载模板失败');
  }

  return response.blob();
};

// 导入SKU库存明细Excel
export const importSkusInventoryExcel = async (file: File): Promise<ApiResponse<null>> => {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: '/skus-inventory/import/excel',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
