import { request } from './api';
import {
  ColorListProps,
  ColorListResponse,
  ColorListParams,
  AddColorParams,
  UpdateColorParams,
  ApiResponse,
} from '@/types/color';

// 创建颜色
export const createColor = async (colorData: AddColorParams): Promise<ApiResponse<null>> => {
  return request({
    url: '/colors',
    method: 'POST',
    data: colorData,
  });
};

// 获取颜色列表
export const getColorList = async (
  params: ColorListParams,
): Promise<ApiResponse<ColorListResponse['data']>> => {
  return request({
    url: '/colors',
    method: 'GET',
    params,
  });
};

// 获取颜色详情
export const getColorDetail = async (id: string): Promise<ApiResponse<ColorListProps>> => {
  return request({
    url: `/colors/${id}`,
    method: 'GET',
  });
};

// 更新颜色
export const updateColor = async (
  id: string,
  colorData: UpdateColorParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/colors/${id}`,
    method: 'PATCH',
    data: colorData,
  });
};

// 删除颜色
export const deleteColor = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/colors/${id}`,
    method: 'DELETE',
  });
};
