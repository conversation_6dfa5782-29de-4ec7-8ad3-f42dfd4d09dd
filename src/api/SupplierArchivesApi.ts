import { request } from './api';
import {
  AddSupplierArchivesParams,
  SupplierArchivesListParams,
  SupplierArchivesListResponse,
  UpdateSupplierArchivesParams,
  SupplierArchiveDetailResponse,
  ExportSupplierArchivesParams,
  SupplierArchivesSummaryListParams,
  SupplierArchivesSummaryListResponse,
  SupplierArchivesSummaryDetailResponse,
  UpdateSupplierArchivesSummaryParams,
  ExportSupplierArchivesSummaryParams,
  ApiResponse,
} from '@/types/supplierArchives';

// 创建供应商档案详情 - POST /supplier-archives/details
export const createSupplierArchiveDetail = async (
  archiveData: AddSupplierArchivesParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/supplier-archives/details',
    method: 'POST',
    data: archiveData,
  });
};

// 获取供应商档案详情列表 - GET /supplier-archives/details
export const getSupplierArchivesList = async (
  params: SupplierArchivesListParams,
): Promise<ApiResponse<SupplierArchivesListResponse['data']>> => {
  return request({
    url: '/supplier-archives/details',
    method: 'GET',
    params,
  });
};

// 获取供应商档案详情 - GET /supplier-archives/details/{id}
export const getSupplierArchiveDetail = async (
  id: string,
): Promise<SupplierArchiveDetailResponse> => {
  return request({
    url: `/supplier-archives/details/${id}`,
    method: 'GET',
  });
};

// 更新供应商档案详情 - PATCH /supplier-archives/details/{id}
export const updateSupplierArchiveDetail = async (
  id: string,
  data: UpdateSupplierArchivesParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/supplier-archives/details/${id}`,
    method: 'PATCH',
    data,
  });
};

// 删除供应商档案详情 - DELETE /supplier-archives/details/{id}
export const deleteSupplierArchiveDetail = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/supplier-archives/details/${id}`,
    method: 'DELETE',
  });
};

// 导出供应商档案Excel - GET /supplier-archives/export
export const exportSupplierArchivesExcel = async (
  params: ExportSupplierArchivesParams,
): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  queryParams.append('supplierCode', params.supplierCode);
  if (params.startTime) queryParams.append('startTime', params.startTime);
  if (params.endTime) queryParams.append('endTime', params.endTime);
  if (params.type) queryParams.append('type', params.type);
  if (params.detailIds && params.detailIds.length > 0) {
    // 将UUID数组转换为逗号分隔的字符串，并进行URL编码
    const detailIdsString = params.detailIds.join(',');
    queryParams.append('detailIds', encodeURIComponent(detailIdsString));
  }

  const url = `${baseURL}/supplier-archives/export${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};

// 下载导入模板 - GET /supplier-archives/import-template/{type}
export const downloadSupplierArchiveImportTemplate = async (type: string): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
  const response = await fetch(`${baseURL}/supplier-archives/import-template/${type}`, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('下载模板失败');
  }

  return response.blob();
};

// 导入供应商档案Excel - POST /supplier-archives/import/{type}
export const importSupplierArchiveExcel = async (
  type: string,
  file: File,
): Promise<ApiResponse<any>> => {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: `/supplier-archives/import/${type}`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// ==================== 供应商档案汇总相关API ====================

// 获取供应商档案汇总列表 - GET /supplier-archives/archives
export const getSupplierArchivesSummaryList = async (
  params: SupplierArchivesSummaryListParams,
): Promise<ApiResponse<SupplierArchivesSummaryListResponse['data']>> => {
  return request({
    url: '/supplier-archives/archives',
    method: 'GET',
    params,
  });
};

// 获取供应商档案汇总详情 - GET /supplier-archives/archives/{supplierCode}
export const getSupplierArchivesSummaryDetail = async (
  supplierCode: string,
): Promise<SupplierArchivesSummaryDetailResponse> => {
  return request({
    url: `/supplier-archives/archives/${supplierCode}`,
    method: 'GET',
  });
};

// 更新供应商档案汇总(超级管理员权限) - PATCH /supplier-archives/archives/{supplierCode}/admin
export const updateSupplierArchivesSummary = async (
  supplierCode: string,
  data: UpdateSupplierArchivesSummaryParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/supplier-archives/archives/${supplierCode}/admin`,
    method: 'PATCH',
    data,
  });
};

// 导出供应商档案汇总Excel - GET /supplier-archives/archives/export/summary
export const exportSupplierArchivesSummaryExcel = async (
  params: ExportSupplierArchivesSummaryParams,
): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params.search) queryParams.append('search', params.search);
  if (params.creditLevel) queryParams.append('creditLevel', params.creditLevel);
  if (params.sortBy) queryParams.append('sortBy', params.sortBy);
  if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

  const url = `${baseURL}/supplier-archives/archives/export/summary${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
