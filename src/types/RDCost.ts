export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// rd-costs/details POST
export interface AddRDCostParams {
  companyCode: string;
  amount: number;
  screenshot: string;
  remark?: string;
  createDate: string;
}
// rd-costs/details GET - 使用通用的分页参数接口
export type { RDCostListParams } from './commonFinancial';

export interface Detail {
  id: string;
  rdCostId: string;
  companyCode: string;
  companyName?: string;
  amount: number;
  screenshot: string;
  remark?: string;
  createDate: string;
  isDeleted?: boolean;
}

export interface RDCostListResponse {
  code: number;
  data: {
    details: Detail[];
    total: number;
    page: number;
    pageSize: number;
    totalAmount: number;
    systemTotalAmount: number;
  };
  message: string;
}

// /rd-costs/export/excel - 使用通用的导出参数接口
export type { ExportRDCostParams } from './commonFinancial';
