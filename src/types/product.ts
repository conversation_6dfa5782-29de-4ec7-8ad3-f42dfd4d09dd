import type { AccessorySelectOption } from './accessories';

export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// 辅料选择接口 (用于表单)
export interface AccessorySelection {
  id: string; // 辅料ID
  code: string; // 辅料编码
  name: string; // 辅料名称
  price: number; // 辅料单价
  quantity: number; // 数量
}

// 辅料数量接口 (用于API)
export interface AccessoryQuantity {
  accessoryId: string;
  quantity: number;
}

// 价格调整接口
export interface PriceAdjustment {
  clothingCostAdjustment?: number;
  accessoryCostAdjustment?: number;
  retailPriceAdjustment?: number;
  preOrderPriceAdjustment?: number;
  restockPriceAdjustment?: number;
  spotPriceAdjustment?: number;
}

// 选择的颜色配置接口
export interface SelectedColor {
  colorCode: string;
  images: string[];
  priceAdjustments?: PriceAdjustment;
  accessories?: AccessorySelectOption[];
  remark?: string;
}

// 创建产品参数接口
export interface AddProductParams {
  code: string;
  name: string;
  manufacturerCode?: string;
  brandCode: string;
  supplierCode: string;
  categoryCode?: string;
  craftDescription?: string;
  clothingCost: number;
  accessoryCost: number;
  retailPrice: number;
  preOrderPrice: number;
  restockPrice: number;
  spotPrice: number;
  accessories?: AccessoryQuantity[];
  selectedColors: SelectedColor[];
}

// 品牌信息接口
export interface BrandInfo {
  code: string;
  name: string;
  orderPrice: number;
  restockPrice: number;
  spotPrice: number;
  isActive: boolean;
}

// 供应商信息接口
export interface SupplierInfo {
  code: string;
  name: string;
  address?: string;
  contactName?: string;
  contactPhone?: string;
}

// 分类信息接口
export interface CategoryInfo {
  id: string;
  code: string;
  name: string;
  sizes?: string[];
}

// 颜色信息接口
export interface ColorInfo {
  id: string;
  code: string;
  name: string;
}

// 辅料详情接口
export interface AccessoryDetail {
  id: string;
  code: string;
  name: string;
  unit: string;
  unitPrice: number;
  supplierCode: string;
  supplierName: string;
}

// 产品辅料接口
export interface ProductAccessory {
  accessoryId: string;
  accessoryCode: string;
  accessoryName: string;
  quantity: number;
  unit: string;
  unitPrice: number;
}

// 颜色尺寸组合接口
export interface ColorSizeCombination {
  colorCode: string;
  colorName: string;
  colorHex?: string;
  images: string[];
  priceAdjustments?: PriceAdjustment;
  accessories: ProductAccessory[];
  remark?: string;
  sizes: string[];
}

// 产品列表项接口
export interface ProductListItem {
  id: string;
  code: string;
  name: string;
  brandCode: string;
  brandName: string;
  supplierCode: string;
  supplierName: string;
  categoryCode?: string;
  categoryName?: string;
  manufacturerCode?: string;
  craftDescription?: string;
  clothingCost: number;
  accessoryCost: number;
  retailPrice: number;
  preOrderPrice: number;
  restockPrice: number;
  spotPrice: number;
  colorSizeCombinations: ColorSizeCombination[];
  createdAt: string;
  updatedAt: string;
}

// 完整产品信息接口
export interface Product extends ProductListItem {
  globalAccessories: ProductAccessory[];
}

// 产品列表查询参数
export interface ProductListParams {
  page: number;
  pageSize: number;
  search?: string;
  codeSearch?: string;
  brandCode?: string;
  supplierCode?: string;
  categoryCode?: string;
}

// 产品列表响应
export interface ProductListResponse {
  code: number;
  message: string;
  data: {
    products: ProductListItem[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

// 产品详情响应
export interface ProductDetailResponse {
  code: number;
  data: Product;
  message: string;
}

// 更新产品参数
export interface UpdateProductDetailParams {
  name: string;
  manufacturerCode?: string;
  brandCode: string;
  supplierCode: string;
  categoryCode?: string;
  craftDescription?: string;
  clothingCost: number;
  accessoryCost: number;
  retailPrice: number;
  preOrderPrice: number;
  restockPrice: number;
  spotPrice: number;
  accessories?: AccessoryQuantity[];
  selectedColors: SelectedColor[];
}

// 导出产品参数
export interface ExportProductsParams {
  brandCode?: string;
  supplierCode?: string;
  categoryCode?: string;
  productIds?: string[];
}

// SKU库存信息
export interface SkuStock {
  skuCode: string;
  colorCode: string;
  colorName: string;
  sizeCode: string;
  actualStock: number;
  reservedStock: number;
  totalStock: number;
}

// 颜色组合信息（用于订单）
export interface ProductColorCombination {
  colorCode: string;
  colorName: string;
  sizes: string[];
  skuStocks: SkuStock[];
}

// 商品订单信息
export interface ProductOrderInfo {
  code: string;
  name: string;
  brandCode: string;
  brandName: string;
  supplierCode: string;
  supplierName: string;
  retailPrice: number;
  preOrderPrice: number;
  restockPrice: number;
  spotPrice: number;
  colorCombinations: ProductColorCombination[];
  totalAvailableStock: number;
  totalReservedStock: number;
}

// 商品订单信息响应
export interface ProductOrderInfoResponse {
  code: number;
  data: ProductOrderInfo;
  message: string;
}
