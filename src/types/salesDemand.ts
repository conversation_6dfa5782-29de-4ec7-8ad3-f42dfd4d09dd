import type { CustomersListProps } from './customers';
import type { UserProps } from './user';
import type { Product } from './product';
import type { ColorListProps } from './color';

// 销售需求订单状态
export enum SalesDemandOrderStatus {
  DRAFT = 'draft', // 草稿
  SUBMITTED = 'submitted', // 已提交
  APPROVED = 'approved', // 已审核
  MERGED = 'merged', // 已合并
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
}

// 优先级
export enum PriorityLevel {
  URGENT = 'urgent', // 紧急
  HIGH = 'high', // 高
  NORMAL = 'normal', // 普通
  LOW = 'low', // 低
}

// 需求订单明细
export interface SalesDemandOrderDetail {
  id?: string;
  productCode: string;
  colorCode: string;
  sizeCode: string;
  skuCode?: string;
  demandQuantity: number;
  unitPrice?: number;
  totalAmount?: number;
  currentStock?: number;
  shortageQuantity?: number;
  remark?: string;
  product?: Product;
  color?: ColorListProps;
}

// 销售需求订单
export interface SalesDemandOrder {
  id: string;
  orderNumber: string;
  customerCode?: string;
  salesPersonCode: string;
  demandDate: string;
  expectedDeliveryDate?: string;
  priorityLevel: PriorityLevel;
  status: SalesDemandOrderStatus;
  totalQuantity: number;
  estimatedTotalAmount: number;
  remark?: string;
  createdByUserCode?: string;
  approvedByUserCode?: string;
  approvedAt?: string;
  mergedToPurchaseOrderId?: string;
  mergedAt?: string;
  createdAt: string;
  updatedAt: string;
  customer?: CustomersListProps;
  salesPerson?: UserProps;
  createdByUser?: UserProps;
  approvedByUser?: UserProps;
  details?: SalesDemandOrderDetail[];
}

// 创建销售需求订单参数
export interface CreateSalesDemandOrderParams {
  customerCode?: string;
  salesPersonCode: string;
  demandDate: string;
  expectedDeliveryDate?: string;
  priorityLevel?: PriorityLevel;
  remark?: string;
  createdByUserCode?: string;
  details: SalesDemandOrderDetail[];
}

// 创建销售需求订单响应
export interface CreateSalesDemandOrderResponse {
  code: number;
  message: string;
  data: null;
}
// 查询销售需求订单列表参数
export interface GetSalesDemandOrderListParams {
  page: number;
  pageSize: number;
  orderNumber?: string;
  customerCode?: string;
  salesPersonCode?: string;
  status?: SalesDemandOrderStatus;
  priorityLevel?: PriorityLevel;
  demandDateStart?: string;
  demandDateEnd?: string;
  createdAtStart?: string;
  createdAtEnd?: string;
  productCode?: string;
  sortBy?: 'createdAt' | 'demandDate' | 'totalQuantity' | 'estimatedTotalAmount';
  sortOrder?: 'ASC' | 'DESC';
}

// 查询销售需求订单列表响应
export interface GetSalesDemandOrderListResponse {
  code: number;
  message: string;
  data: {
    data: SalesDemandOrder[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

// 获取可合并的需求订单参数
export interface GetMergeableDemandOrdersParams {
  supplierCode?: string;
}

// 获取可合并的需求订单响应
export interface GetMergeableDemandOrdersResponse {
  code: number;
  message: string;
  data: SalesDemandOrder[];
}

// 获取销售需求订单详情响应
export interface GetSalesDemandOrderDetailResponse {
  code: number;
  message: string;
  data: SalesDemandOrder;
}

// 更新销售需求订单参数
export interface UpdateSalesDemandOrderParams {
  customerCode?: string;
  demandDate?: string;
  expectedDeliveryDate?: string;
  priorityLevel?: PriorityLevel;
  remark?: string;
  details?: SalesDemandOrderDetail[];
}

// 更新销售需求订单响应
export interface UpdateSalesDemandOrderResponse {
  code: number;
  message: string;
  data: null;
}

// 提交审核参数
export interface SubmitSalesDemandOrderParams {
  userCode?: string;
}

// 提交审核响应
export interface SubmitSalesDemandOrderResponse {
  code: number;
  message: string;
  data: null;
}

// 审核通过参数
export interface ApproveSalesDemandOrderParams {
  approvedByUserCode: string;
}

// 审核通过响应
export interface ApproveSalesDemandOrderResponse {
  code: number;
  message: string;
  data: null;
}

// 取消订单响应
export interface CancelSalesDemandOrderResponse {
  code: number;
  message: string;
  data: null;
}

// 删除订单响应
export interface DeleteSalesDemandOrderResponse {
  code: number;
  message: string;
  data: null;
}

// 导出Excel参数
export interface ExportSalesDemandOrdersParams
  extends Omit<GetSalesDemandOrderListParams, 'page' | 'pageSize'> {}

// 状态颜色映射
export const SALES_DEMAND_ORDER_STATUS_COLORS = {
  [SalesDemandOrderStatus.DRAFT]: 'default',
  [SalesDemandOrderStatus.SUBMITTED]: 'processing',
  [SalesDemandOrderStatus.APPROVED]: 'success',
  [SalesDemandOrderStatus.MERGED]: 'warning',
  [SalesDemandOrderStatus.COMPLETED]: 'success',
  [SalesDemandOrderStatus.CANCELLED]: 'error',
} as const;

// 优先级颜色映射
export const PRIORITY_LEVEL_COLORS = {
  [PriorityLevel.URGENT]: 'error',
  [PriorityLevel.HIGH]: 'warning',
  [PriorityLevel.NORMAL]: 'default',
  [PriorityLevel.LOW]: 'default',
} as const;

// 状态文本映射
export const SALES_DEMAND_ORDER_STATUS_TEXT = {
  [SalesDemandOrderStatus.DRAFT]: '草稿',
  [SalesDemandOrderStatus.SUBMITTED]: '已提交',
  [SalesDemandOrderStatus.APPROVED]: '已审核',
  [SalesDemandOrderStatus.MERGED]: '已合并',
  [SalesDemandOrderStatus.COMPLETED]: '已完成',
  [SalesDemandOrderStatus.CANCELLED]: '已取消',
} as const;

// 优先级文本映射
export const PRIORITY_LEVEL_TEXT = {
  [PriorityLevel.URGENT]: '紧急',
  [PriorityLevel.HIGH]: '高',
  [PriorityLevel.NORMAL]: '普通',
  [PriorityLevel.LOW]: '低',
} as const;
