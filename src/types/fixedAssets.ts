export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// fixed-assets/details POST
export interface AddFixedAssetsParams {
  companyCode: string;
  amount: number;
  screenshot: string;
  remark?: string;
  createDate: string;
}

export interface Detail {
  id: string;
  fixedAssetId: string;
  companyCode: string;
  companyName?: string;
  amount: number;
  screenshot: string;
  remark: string;
  createDate: string;
  isDeleted?: boolean;
}

// 使用通用的分页参数接口
export type { FixedAssetsListParams } from './commonFinancial';

// fixed-assets/details GET
export interface FixedAssetsListResponse {
  code: number;
  data: {
    details: Detail[];
    total: number;
    page: number;
    pageSize: number;
    totalAmount: number;
    systemTotalAmount: number;
  };
  message: string;
}

// /fixed-assets/details/${id} GET
export interface DeleteFixedAssetsParams {
  id: string;
}
// /fixed-assets/details/${id} PATCH
export interface UpdateFixedAssetsParams {
  id: string;
  data: AddFixedAssetsParams;
}

export interface DeleteFixedAssetsParams {
  id: string;
}

// /fixed-assets/export/excel - 使用通用的导出参数接口
export type { ExportFixedAssetsParams } from './commonFinancial';
