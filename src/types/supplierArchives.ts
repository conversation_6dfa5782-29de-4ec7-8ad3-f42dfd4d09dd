export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

export enum SupplierArchiveType {
  PURCHASE = 'purchase', // 采购
  ARRIVAL = 'arrival', // 到货
  REPAIR_SEND = 'repair_send', // 返厂修复
  REPAIR_ARRIVAL = 'repair_arrival', // 修复到货
}

// supplier-archives/details POST
export interface AddSupplierArchivesParams {
  supplierCode: string;
  type: SupplierArchiveType;
  // 采购类型必填字段
  totalQuantity?: number;
  totalAmount?: number;
  purchaseOrderId?: string;
  // 到货类型必填字段
  actualArrivalQuantity?: number;
  defectQuantity?: number;
  logisticsOrderId?: string;
  // 返厂修复类型必填字段
  repairQuantity?: number;
  // 修复到货类型必填字段
  actualRepairedQuantity?: number;
  totalArrivalQuantity?: number;
  remark?: string;
}

// supplier-archives/details GET
export interface SupplierArchivesListParams {
  page: number;
  pageSize: number;
  supplierCode: string;
  startTime: string;
  endTime: string;
  search?: string;
  type?: SupplierArchiveType;
}

export interface Detail {
  id: string; //uuid
  supplierCode: string;
  supplierName: string;
  type: SupplierArchiveType;
  remark?: string;
  createdAt: string;
  updatedAt: string;
  // 采购类型字段
  totalQuantity?: number;
  totalAmount?: number;
  purchaseOrderId?: string;
  // 到货类型字段
  actualArrivalQuantity?: number;
  defectQuantity?: number;
  logisticsOrderId?: string;
  // 返厂修复类型字段
  repairQuantity?: number;
  // 修复到货类型字段
  actualRepairedQuantity?: number;
  totalArrivalQuantity?: number;
}

// supplier-archives/details GET
export interface SupplierArchivesListResponse {
  code: 200;
  data: {
    details: Detail[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  message: string;
}

// /supplier-archives/details/{id} 获取详情 GET
export interface SupplierArchiveDetailResponse {
  code: number;
  data: Detail;
  message: string;
}

// /supplier-archives/details/{id} PATCH
export interface UpdateSupplierArchivesParams {
  type: SupplierArchiveType;
  // 采购类型字段
  totalQuantity?: number;
  totalAmount?: number;
  purchaseOrderId?: string;
  // 到货类型字段
  actualArrivalQuantity?: number;
  defectQuantity?: number;
  logisticsOrderId?: string;
  // 返厂修复类型字段
  repairQuantity?: number;
  // 修复到货类型字段
  actualRepairedQuantity?: number;
  totalArrivalQuantity?: number;
  remark?: string;
}

// /supplier-archives/export 导出参数
export interface ExportSupplierArchivesParams {
  supplierCode: string;
  startTime?: string;
  endTime?: string;
  type?: SupplierArchiveType;
  detailIds?: string[];
}

// 档案类型标签映射
export const SupplierArchiveTypeLabels = {
  [SupplierArchiveType.PURCHASE]: '采购',
  [SupplierArchiveType.ARRIVAL]: '到货',
  [SupplierArchiveType.REPAIR_SEND]: '返厂修复',
  [SupplierArchiveType.REPAIR_ARRIVAL]: '修复到货',
};

// supplier-archives/archives GET 分页查询供应商汇总列表
export interface SupplierArchivesSummaryListParams {
  page: number;
  pageSize: number;
  search?: string; //模糊搜索供应商编码和名称
  creditLevel?: string; // 信用等级
  sortBy?: string; // 排序字段
  sortOrder?: string; //排序方向
}

export interface ListDetail {
  supplierCode: string;
  supplierName: string;
  totalPurchaseAmount: number;
  totalPurchaseQuantity: number;
  totalArrivalQuantity: number;
  totalDefectQuantity: number;
  totalRepairQuantity: number;
  totalRepairedQuantity: number;
  totalPaymentAmount: number;
  defectRate: number;
  repairRate: number;
  repairSuccessRate: number;
  orderCompletionRate: number;
  averageUnitPrice: number;
  cooperationStartDate?: string;
  lastTransactionDate?: string;
  creditLevel: string;
  paymentCycle: number;
  deliveryCycle: number;
  qualityScore: number;
  returnCount: number;
  returnAmount: number;
  returnRate: number;
  unsettledAmount: number;
  prepaymentBalance: number;
  averagePaymentPeriod: number;
  onTimeDeliveryRate: number;
  responseSpeedScore: number;
  serviceAttitudeScore: number;
  overallScore: number;
  createdAt: string;
  updatedAt: string;
}

export interface SupplierArchivesSummaryListResponse {
  code: number;
  data: {
    archives: ListDetail[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  message: string;
}

// supplier-archives/archives/{supplierCode} GET 获取供应商档案汇总详情
export interface SupplierArchivesSummaryDetailResponse {
  code: number;
  data: ListDetail;
  message: string;
}

// supplier-archives/archives/{supplierCode}/admin PATCH 只有超级管理员可以更新供应商档案(超级管理员权限)
export interface UpdateSupplierArchivesSummaryParams {
  totalPurchaseAmount: number;
  defectRate: number;
  repairRate: number;
  repairSuccessRate: number;
  creditLevel: string;
  qualityScore: number;
  returnAmount: number;
  returnRate: number;
  unsettledAmount: number;
  prepaymentBalance: number;
  onTimeDeliveryRate: number;
  responseSpeedScore: number;
  serviceAttitudeScore: number;
  overallScore: number;
}

// supplier-archives/archives/export/summary GET导出供应商档案汇总Excel
export interface ExportSupplierArchivesSummaryParams {
  search?: string;
  creditLevel?: string;
  sortBy?: string;
  sortOrder?: string;
}

// 信用等级选项
export const CreditLevelOptions = [
  { value: 'A', label: 'A级', color: '#95de64' },
  { value: 'B', label: 'B级', color: '#ffd666' },
  { value: 'C', label: 'C级', color: '#ffbb96' },
  { value: 'D', label: 'D级', color: '#d9d9d9' },
];

// 获取信用等级颜色
export const getCreditLevelColor = (level: string): string => {
  const option = CreditLevelOptions.find((opt) => opt.value === level);
  return option?.color || '#d9d9d9';
};
