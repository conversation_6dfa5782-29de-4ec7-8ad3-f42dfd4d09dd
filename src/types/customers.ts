export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

export interface AddCustomerParams {
  name: string;
  phone?: string;
  address?: string;
  managerCode?: string;
  provinceCode: number;
  pinyinCode: string;
  remarks: string | null;
}

export interface UpdateCustomerParams {
  name: string;
  phone?: string;
  address?: string;
  managerCode?: string;
  provinceCode: number;
  remarks?: string | null;
}

export interface CustomersListProps {
  name: string;
  code: string;
  pinyinCode: string;
  phone: string | null;
  address: string | null;
  provinceCode: number | null;
  provinceName: string | null;
  managerCode: string | null;
  managerName: string | null;
  remarks: string | null;
  updatedAt: string;
}

export interface CustomerDetailResponse {
  code: number;
  data: {
    code: string;
    name: string;
    pinyinCode: string;
    phone: string;
    address: string;
    provinceCode: number;
    provinceName: string;
    managerCode: string;
    managerName: string;
    remarks: string;
    updatedAt: string;
  };
  message?: string;
}

export interface CustomerListResponse {
  code: number;
  message: string;
  data: {
    customers: CustomersListProps[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

export interface CustomerListParams {
  page: number;
  pageSize: number;
  search?: string;
}

export interface GetCustomerListParams {
  provinceCode: string | null;
  page: number;
  pageSize: number;
  search: string;
  pinyinCode: string | null;
  managerCode: string | null;
}

// 客户搜索结果项
export interface CustomerSearchItem {
  code: string;
  name: string;
  phone: string;
  managerCode: string;
  managerName: string;
}
