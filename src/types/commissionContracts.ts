interface Detail {
  brandName: string;
  quantity: number;
  amount: number;
}

// API响应基础接口
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
}

export enum SortField {
  CREATED_AT = 'createdAt',
  APPLICATION_DATE = 'applicationDate',
  REPAYMENT_DATE = 'repaymentDate',
  ACTUAL_SHIPMENT_AMOUNT = 'actualShipmentAmount',
  REQUESTED_DEBT_AMOUNT = 'requestedDebtAmount',
  TOTAL_ACCUMULATED_DEBT = 'totalAccumulatedDebt',
  OVERDUE_DAYS = 'overdueDays',
  REMAINING_DEBT_AMOUNT = 'remainingDebtAmount',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum CommissionContractStatus {
  DRAFT = 'draft', // 草稿
  SUBMITTED = 'submitted', // 已提交
  APPROVED = 'approved', // 已审批
  REJECTED = 'rejected', // 已拒绝
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
}

export enum PaymentStatus {
  UNPAID = 'unpaid', // 未还款
  PARTIAL_PAID = 'partial_paid', // 部分还款
  FULLY_PAID = 'fully_paid', // 已还款
  OVERDUE = 'overdue', // 已逾期
}

// POST commission-contracts 新增用尽发货申请书参数
export interface AddCommissionContractsParams {
  companyName: string;
  partyBName: string;
  partyBIdCard: string;
  applicationDate: string;
  brandDetails: {
    brandName: string;
    quantity: number;
    amount: number;
  }[];
  actualShipmentAmount: number;
  requestedDebtAmount: number;
  repaymentDate: string;
  totalAccumulatedDebt: number;
}

interface Contracts {
  id: string;
  contractNumber: string;
  companyName: string;
  partyBName: string;
  partyBIdCard: string;
  applicationDate: string;
  applicationDateDisplay: string;
  actualShipmentAmount: number;
  requestedDebtAmount: number;
  totalAccumulatedDebt: number;
  paidAmount: number;
  remainingDebtAmount: number;
  repaymentDate: string;
  repaymentDateDisplay: string;
  overdueDays: number;
  overdueDaysText: string;
  status: string;
  paymentStatus: string;
  paymentStatusText: string;
  lastPaymentDate: string | null;
  createdAt: string;
  updatedAt: string;
}
// GET commission-contracts 分页查询佣金发货书列表
export interface CommissionContractsListParams {
  page: number;
  pageSize: number;
  contractNumber?: string;
  companyName?: string;
  partyBName?: string;
  status?: string;
  paymentStatus?: string;
  applicationStartDate?: string; //YYYY-MM-DD
  applicationEndDate?: string; //YYYY-MM-DD
  repaymentStartDate?: string;
  repaymentEndDate?: string;
  search?: string; // 申请书编号或者乙方姓名或者身份证号
  minActualShipmentAmount?: number;
  maxActualShipmentAmount?: number;
  minRequestedDebtAmount?: number;
  maxRequestedDebtAmount?: number;
  minTotalAccumulatedDebt?: number;
  maxTotalAccumulatedDebt?: number;
  onlyOverDue?: boolean;
  minOverDueDays?: number;
  maxOverDueDays?: number;
  sortField?: string; //createdAt applicationDate repaymentDate actualShipmentAmount requestedDebtAmount totalAccumulatedDebt overdueDays remainingDebtAmount
  sortOrder?: 'ASC' | 'DESC';
}

export interface CommissionContractsListResponse {
  code: number;
  data: {
    contracts: Contracts[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  message: string;
}

interface BrandDetails {
  id: string;
  brandName: string;
  quantity: number;
  amount: number;
  sortOrder: number;
}
// GET commission-contracts/{id} 获取佣金发货书详情 id使用uuid
export interface CommissionContractsDetailResponse {
  code: number;
  data: {
    id: string;
    contractNumber: string;
    companyName: string;
    partyBName: string;
    partyBIdCard: string;
    applicationYear: number;
    applicationMonth: number;
    applicationDay: number;
    actualShipmentAmount: number;
    actualShipmentAmountInWords: string;
    requestedDebtAmount: number;
    requestedDebtAmountInWords: string;
    repaymentYear: number;
    repaymentMonth: number;
    repaymentDay: number;
    totalAccumulatedDebt: number;
    totalAccumulatedDebtInWords: string;
    status: string;
    remarks: string | null;
    createdByUserCode: string | null;
    approvedByUserCode: string | null;
    approvedAt: string | null;
    createdAt: string;
    updatedAt: string;
    brandDetails: BrandDetails[];
  };
  message: string;
}
// PATCH commission-contracts/{id} 更新佣金发货书
//使用body进行更新
export interface UpdateContractsParams {
  companyName: string;
  partyBName: string;
  partyBIdCard: string;
  applicationYear: number;
  applicationMonth: number;
  applicationDay: number;
  actualShipmentAmount: number;
  requestedDebtAmount: number;
  repaymentYear: number;
  repaymentMonth: number;
  repaymentDay: number;
  totalAccumulatedDebt: number;
  remarks: string;
  brandDetails: BrandDetails[];
}
// DELETE commission-contracts/{id} 删除佣金发货书
// PATCH commission-contracts/status
export interface UpdateContractsStatusParams {
  status: string;
  approvedByUserCode: string;
  remarks: string;
}
// PATCH commission-contracts/payment
export interface UpdateContractsPaymentParams {
  paymentStatus: string;
  paidAmount: number;
  lastPaymentDate: string;
  remarks: string;
}
// GET commission-contracts/{id}/pdf
