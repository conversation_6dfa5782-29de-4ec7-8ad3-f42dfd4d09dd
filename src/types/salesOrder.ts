// 销售订单相关类型定义

import type { ApiResponse } from './auth';

// 订单状态枚举
export type SalesOrderStatus =
  | 'draft'
  | 'confirmed'
  | 'shipped'
  | 'delivered'
  | 'completed'
  | 'cancelled';

// 物流方式枚举
export type ShippingMethod = 'collect' | 'prepaid';

// 订单优先级枚举
export type OrderPriority = 'urgent' | 'high' | 'normal' | 'low';

// 价格类型枚举
export type PriceType = 'retail' | 'pre_order' | 'restock' | 'spot';

// 订单状态配置
export const SALES_ORDER_STATUS_CONFIG: Record<SalesOrderStatus, { text: string; color: string }> =
  {
    draft: { text: '草稿', color: 'gray' },
    confirmed: { text: '已确认', color: 'blue' },
    shipped: { text: '已发货', color: 'orange' },
    delivered: { text: '已送达', color: 'green' },
    completed: { text: '已完成', color: 'dark-green' },
    cancelled: { text: '已取消', color: 'red' },
  };

// 物流方式配置
export const SHIPPING_METHOD_CONFIG: Record<ShippingMethod, string> = {
  collect: '到付',
  prepaid: '寄付',
};

// 订单优先级配置
export const ORDER_PRIORITY_CONFIG: Record<OrderPriority, { text: string; color: string }> = {
  urgent: { text: '紧急', color: 'red' },
  high: { text: '高', color: 'orange' },
  normal: { text: '普通', color: 'blue' },
  low: { text: '低', color: 'gray' },
};

// 价格类型配置
export const PRICE_TYPE_CONFIG: Record<PriceType, string> = {
  retail: '零售价',
  pre_order: '预订价',
  restock: '补货价',
  spot: '现货价',
};

// 销售订单明细
export interface SalesOrderDetail {
  id?: string;
  productCode: string;
  productName?: string;
  colorCode: string;
  colorName?: string;
  sizeCode: string;
  sizeName?: string;
  skuCode: string;
  quantity: number;
  priceType: PriceType;
  unitPrice: number;
  discountAmount?: number;
  totalAmount?: number;
  expectedShipDate?: string;
  remark?: string;
}

// 销售订单主表
export interface SalesOrder {
  id?: string;
  orderNumber?: string;
  salesPersonCode: string;
  salesPersonName?: string;
  customerCode: string;
  customerName?: string;
  orderDate: string;
  expectedDeliveryDate?: string;
  priority?: OrderPriority;
  status?: SalesOrderStatus;
  isDropShipping?: boolean;
  isReleased?: boolean;
  shippingMethod?: ShippingMethod;
  shippingFee?: number;
  shippingCompany?: string;
  // 代发信息
  dropShipReceiverName?: string;
  dropShipReceiverPhone?: string;
  dropShipAddress?: string;
  dropShipProvinceCode?: number;
  dropShipCity?: string;
  dropShipDistrict?: string;
  dropShipSpecialRequirements?: string;
  // 其他字段
  createdByUserCode?: string;
  remark?: string;
  totalAmount?: number;
  totalQuantity?: number;
  createdAt?: string;
  updatedAt?: string;
  details: SalesOrderDetail[];
}

// 创建销售订单参数
export interface CreateSalesOrderParams {
  salesPersonCode: string;
  customerCode: string;
  orderDate: string;
  expectedDeliveryDate?: string;
  priority?: OrderPriority;
  isDropShipping?: boolean;
  isReleased?: boolean;
  shippingMethod?: ShippingMethod;
  shippingFee?: number;
  shippingCompany?: string;
  // 代发信息
  dropShipReceiverName?: string;
  dropShipReceiverPhone?: string;
  dropShipAddress?: string;
  dropShipProvinceCode?: number;
  dropShipCity?: string;
  dropShipDistrict?: string;
  dropShipSpecialRequirements?: string;
  // 其他字段
  createdByUserCode?: string;
  remark?: string;
  details: Omit<
    SalesOrderDetail,
    'id' | 'productName' | 'colorName' | 'sizeName' | 'totalAmount'
  >[];
}

// 更新销售订单参数
export interface UpdateSalesOrderParams extends CreateSalesOrderParams {
  id: string;
}

// 销售订单列表查询参数
export interface GetSalesOrderListParams {
  page: number;
  pageSize: number;
  search?: string;
  customerCode?: string;
  salesPersonCode?: string;
  status?: SalesOrderStatus;
  priority?: OrderPriority;
  companyCode?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 销售订单列表响应
export interface SalesOrderListItem {
  id: string;
  orderNumber: string;
  customerCode: string;
  customerName: string;
  salesPersonCode: string;
  salesPersonName: string;
  orderDate: string;
  expectedDeliveryDate?: string;
  status: SalesOrderStatus;
  priority?: OrderPriority;
  totalAmount: number;
  totalQuantity: number;
  isDropShipping: boolean;
  isReleased: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SalesOrderListResponse {
  orders: SalesOrderListItem[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// API响应类型
export type CreateSalesOrderResponse = ApiResponse<{ id: string; orderNumber: string }>;
export type UpdateSalesOrderResponse = ApiResponse<null>;
export type GetSalesOrderListResponse = ApiResponse<SalesOrderListResponse>;
export type GetSalesOrderDetailResponse = ApiResponse<SalesOrder>;
export type DeleteSalesOrderResponse = ApiResponse<null>;

// 订单操作参数
export interface ConfirmSalesOrderParams {
  expectedDeliveryDate?: string;
  remark?: string;
}

export interface ShipSalesOrderParams {
  shippingCompany?: string;
  trackingNumber?: string;
  shippingDate?: string;
  remark?: string;
}

export interface CompleteSalesOrderParams {
  completedDate?: string;
  remark?: string;
}

// 订单操作响应
export type ConfirmSalesOrderResponse = ApiResponse<null>;
export type ShipSalesOrderResponse = ApiResponse<null>;
export type CompleteSalesOrderResponse = ApiResponse<null>;
export type CancelSalesOrderResponse = ApiResponse<null>;
