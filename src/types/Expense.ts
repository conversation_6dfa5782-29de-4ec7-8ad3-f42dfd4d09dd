export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// expoenses/details GET
export interface AddExpenseParams {
  companyCode: string;
  supplierCode: string;
  amount: string;
  screenshot?: string;
  createDate: string;
  remark?: string;
}

export interface Detail {
  id: string;
  expenseId: string;
  companyCode: string;
  companyName?: string;
  supplierCode: string;
  supplierName: string;
  amount: number;
  screenshot: string;
  remark: string;
  isDeleted: boolean;
  createDate: string;
}

// expenses/details GET - 使用通用的分页参数接口
export type { ExpensesListParams } from './commonFinancial';

export interface ExpensesListResponse {
  code: number;
  data: {
    details: Detail[];
    total: number;
    page: number;
    pageSize: number;
    totalAmount: number; //查询条件总金额
    systemTotalAmount: number; //系统总金额
  };
  message: string;
}

// expenses/details/{id} GET
export interface ExpensesDetailResponse {
  code: number;
  data: Detail;
  message: string;
}

// expenses/details/{id} PATCH 更新支出明细
// expenses/details/{id} DELETE 删除支出明细

//expenses/export GET - 使用通用的导出参数接口
export type { ExportExpenseParams } from './commonFinancial';
