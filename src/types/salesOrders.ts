enum PriceType {
  RETAIL = 'retail', // 零售价
  PRE_ORDER = 'pre_order', // 预订价
  RESTOCK = 'restock', // 补货价
  SPOT = 'spot', // 现货价
}

// 销售订单状态枚举
export enum SalesOrderStatus {
  DRAFT = 'draft', // 草稿
  CONFIRMED = 'confirmed', // 已确认
  SHIPPED = 'shipped', // 已发货
  DELIVERED = 'delivered', // 已送达
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
}

// 物流方式枚举
export enum ShippingMethod {
  COLLECT = 'collect', // 到付
  PREPAID = 'prepaid', // 寄付
}

// 优先级枚举
export enum OrderPriority {
  URGENT = 'urgent', // 紧急
  HIGH = 'high', // 高
  NORMAL = 'normal', // 普通
  LOW = 'low', // 低
}

//POST sales-orders
export interface AddSalesOrdersParams {
  salesPersonCode: string;
  customerCode: string;
  orderDate: string;
  expectedDeliveryDate: string;
  priority: string;
  status: SalesOrderStatus;
  isDropShipping: boolean;
  isReleased: true;
  shippingMethod: ShippingMethod;
  shippingFee: number;
  shippingCompany: string;
  dropShipReceiverName: string;
  dropShipReceiverPhone: string;
  dropShipAddress: string;
  dropShipProvinceCode: number;
  dropShipCity: string;
  dropShipDistrict: string;
  dropShipSpecialRequirements: string;
  createdByUserCode: string;
  remark: string;
  details: [
    {
      productCode: string;
      colorCode: string;
      sizeCode: string;
      skuCode: string;
      quantity: number;
      priceType: string;
      unitPrice: number;
      discountAmount: number;
      expectedShipDate: string;
      remark: string;
    },
  ];
}

//GET sales-orders
export interface GetSalesOrdersParam {
  page: number;
  pageSize: number;
  orderNumber?: string;
  customerCode?: string;
  salesPersonCode?: string;
  status?: string;
  priority?: OrderPriority;
  shippingMethod?: ShippingMethod;
  isDropShipping?: boolean;
  isReleased?: boolean;
  startDate?: string;
  endDate?: string;
  expectedDeliveryStartDate?: string;
  expectedDeliveryEndDate?: string;
  minAmount?: number;
  maxAmount?: number;
  productCode?: string;
  brandCode?: string;
  supplierCode?: string;
  createdByUserCode?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

