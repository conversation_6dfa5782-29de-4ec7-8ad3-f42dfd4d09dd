export interface CompanyProps {
  code: string;
  name: string;
  address: string;
  socialCreditCode: string;
  businessLicenseUrl: string;
  managerName: string | null;
  managerCode: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface CompanyDetailProps {
  code: string;
  name: string;
  address: string;
  socialCreditCode: string;
  businessLicenseUrl: string;
  managerCode: string | null;
  managerName: string | null;
  employees: {
    code: string;
    name: string;
    isCompanyAdmin: boolean;
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface CompanyListData {
  companies: CompanyProps[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 定义API响应类型
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// 定义用户列表查询参数
export interface CompanyListParams {
  page?: number;
  pageSize?: number;
  search?: string;
}
