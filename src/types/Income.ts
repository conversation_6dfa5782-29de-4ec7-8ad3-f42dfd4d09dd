export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// incomes/details GET
export interface AddIncomeParams {
  companyCode: string;
  customerCode: string;
  responsibleUserCode: string;
  createDate: string;
  amount: number;
  screenshot?: string;
  remark?: string;
}

export interface Detail {
  id: string;
  incomeId?: string;
  companyCode: string;
  companyName?: string;
  customerCode: string;
  customerName: string;
  responsibleUserCode: string;
  responsibleUserName: string;
  amount: number;
  screenshot: string;
  remark?: string;
  isDeleted: boolean;
  createDate: string;
}

// incomes/details GET - 使用通用的分页参数接口
export type { IncomeListParams } from './commonFinancial';

export interface IncomeListResponse {
  code: number;
  data: {
    details: Detail[];
    total: number;
    page: number;
    pageSize: number;
    totalAmount: number; //查询条件总金额
    systemTotalAmount: number; //系统总金额
  };
  message: string;
}

// incomes/details/{id} GET
export interface IncomeDetailResponse {
  code: number;
  data: Detail;
  message: string;
}

// incomes/details/{id} PATCH 更新收入明细
// incomes/details/{id} DELETE 删除收入明细

//incomes/export GET - 使用通用的导出参数接口
export type { ExportIncomeParams } from './commonFinancial';
