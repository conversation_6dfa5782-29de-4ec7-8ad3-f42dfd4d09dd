// 商品采购订单管理相关类型定义

import type { ApiResponse } from './common';

// 采购订单状态
export type ProductPurchaseOrderStatus =
  | 'draft' // 草稿
  | 'confirmed' // 已确认
  | 'partial_received' // 部分收货
  | 'producing' // 生产中
  | 'shipped' // 已发货
  | 'received' // 已收货
  | 'completed' // 已完成
  | 'cancelled'; // 已取消

// 需求订单优先级
export type DemandOrderPriority =
  | 'low' // 低优先级
  | 'medium' // 中优先级
  | 'high' // 高优先级
  | 'urgent'; // 紧急

// 采购订单详情
export interface ProductPurchaseOrderDetail {
  id: string;
  productCode: string;
  productName?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  receivedQuantity?: number;
  createdAt: string;
  updatedAt: string;
}

// 采购订单基本信息
export interface ProductPurchaseOrder {
  id: string;
  orderNumber: string;
  supplierCode: string;
  orderDate: string;
  expectedDeliveryDate?: string;
  totalAmount: number;
  totalQuantity: number;
  status: ProductPurchaseOrderStatus;
  createdByUserCode?: string;
  mergedDemandOrderIds?: string;
  confirmedByUserCode?: string;
  confirmedAt?: string;
  receivedByUserCode?: string;
  receivedAt?: string;
  createdAt: string;
  updatedAt: string;
  supplier?: {
    code: string;
    name: string;
    contactName?: string;
    contactPhone?: string;
  };
  createdByUser?: {
    code: string;
    nickname: string;
    name?: string;
  };
  details?: ProductPurchaseOrderDetail[];
}

// 需求来源信息
export interface DemandSource {
  demandOrderId: string;
  demandOrderNumber: string;
  salesPersonCode: string;
  demandDate: string;
  totalQuantity: number;
  totalAmount: number;
}

// POST /product-purchase-orders/merge-demands 合并需求订单为采购订单
export interface MergeDemandOrdersParams {
  demandOrderIds: string[];
  supplierCode: string;
  expectedDeliveryDate?: string;
  createdByUserCode: string;
}

export type MergeDemandOrdersResponse = ApiResponse<null>;

// GET /product-purchase-orders 查询采购订单列表
export interface GetProductPurchaseOrdersParams {
  page: number;
  pageSize: number;
  orderNumber?: string;
  supplierCode?: string;
  status?: ProductPurchaseOrderStatus;
  orderDateStart?: string;
  orderDateEnd?: string;
  createdAtStart?: string;
  createdAtEnd?: string;
  productCode?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface GetProductPurchaseOrdersData {
  data: ProductPurchaseOrder[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export type GetProductPurchaseOrdersResponse = ApiResponse<GetProductPurchaseOrdersData>;

// GET /product-purchase-orders/{id} 获取采购订单详情
export interface GetProductPurchaseOrderDetailData extends ProductPurchaseOrder {
  demandSources?: DemandSource[];
}

export type GetProductPurchaseOrderDetailResponse = ApiResponse<GetProductPurchaseOrderDetailData>;

// POST /product-purchase-orders/{id}/confirm 确认采购订单
export interface ConfirmPurchaseOrderParams {
  confirmedByUserCode: string;
}

export type ConfirmPurchaseOrderResponse = ApiResponse<null>;

// POST /product-purchase-orders/{id}/receive 收货确认
export interface ReceiveOrderParams {
  receivedByUserCode: string;
  receiveDetails: Array<{
    detailId: string;
    receivedQuantity: number;
  }>;
}

export type ReceiveOrderResponse = ApiResponse<null>;

// POST /product-purchase-orders/{id}/cancel 取消采购订单
export type CancelPurchaseOrderResponse = ApiResponse<null>;

// 状态显示配置
export const PURCHASE_ORDER_STATUS_CONFIG = {
  draft: { text: '草稿', color: '#d9d9d9' },
  confirmed: { text: '已确认', color: '#1890ff' },
  partial_received: { text: '部分收货', color: '#722ed1' },
  producing: { text: '生产中', color: '#faad14' },
  shipped: { text: '已发货', color: '#722ed1' },
  received: { text: '已收货', color: '#52c41a' },
  completed: { text: '已完成', color: '#52c41a' },
  cancelled: { text: '已取消', color: '#ff4d4f' },
} as const;

// 优先级显示配置
export const DEMAND_PRIORITY_CONFIG = {
  low: { text: '低优先级', color: '#d9d9d9' },
  medium: { text: '中优先级', color: '#faad14' },
  high: { text: '高优先级', color: '#ff7a45' },
  urgent: { text: '紧急', color: '#ff4d4f' },
} as const;
