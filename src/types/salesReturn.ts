// 销售退单相关类型定义

import type { ApiResponse } from './auth';

// 退单类型枚举
export type ReturnType = 'return' | 'exchange' | 'return_exchange';

// 退单状态枚举
export type ReturnStatus = 'draft' | 'submitted' | 'approved' | 'processing' | 'completed' | 'rejected' | 'cancelled';

// 退单原因枚举
export type ReturnReason = 'quality_issue' | 'size_issue' | 'color_issue' | 'customer_change' | 'delivery_delay' | 'wrong_product' | 'damaged' | 'other';

// 退单类型配置
export const RETURN_TYPE_CONFIG: Record<ReturnType, string> = {
  return: '退货',
  exchange: '换货',
  return_exchange: '退换货',
};

// 退单状态配置
export const RETURN_STATUS_CONFIG: Record<ReturnStatus, { text: string; color: string }> = {
  draft: { text: '草稿', color: 'gray' },
  submitted: { text: '已提交', color: 'blue' },
  approved: { text: '已审核', color: 'green' },
  processing: { text: '处理中', color: 'orange' },
  completed: { text: '已完成', color: 'dark-green' },
  rejected: { text: '已拒绝', color: 'red' },
  cancelled: { text: '已取消', color: 'gray' },
};

// 退单原因配置
export const RETURN_REASON_CONFIG: Record<ReturnReason, string> = {
  quality_issue: '质量问题',
  size_issue: '尺寸问题',
  color_issue: '颜色问题',
  customer_change: '客户变更',
  delivery_delay: '交货延迟',
  wrong_product: '发错商品',
  damaged: '商品损坏',
  other: '其他原因',
};

// 销售退单明细
export interface SalesReturnDetail {
  id?: string;
  originalDetailId?: string;
  productCode: string;
  productName?: string;
  colorCode: string;
  colorName?: string;
  sizeCode: string;
  sizeName?: string;
  skuCode: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  discountRate?: number;
  discountAmount?: number;
  actualAmount: number;
  // 换货信息
  exchangeProductCode?: string;
  exchangeProductName?: string;
  exchangeColorCode?: string;
  exchangeColorName?: string;
  exchangeSizeCode?: string;
  exchangeSizeName?: string;
  exchangeSkuCode?: string;
  exchangeQuantity?: number;
  exchangeUnitPrice?: number;
  exchangeTotalAmount?: number;
  remark?: string;
}

// 销售退单主表
export interface SalesReturn {
  id?: string;
  returnNumber?: string;
  originalOrderId?: string;
  originalOrderNumber?: string;
  customerCode: string;
  customerName?: string;
  applicantCode: string;
  applicantName?: string;
  returnDate: string;
  returnType: ReturnType;
  status?: ReturnStatus;
  reason: ReturnReason;
  reasonDetail?: string;
  handlingFee?: number;
  returnShippingCompany?: string;
  returnTrackingNumber: string;
  totalReturnAmount?: number;
  totalExchangeAmount?: number;
  totalQuantity?: number;
  createdAt?: string;
  updatedAt?: string;
  remark?: string;
  details: SalesReturnDetail[];
}

// 创建销售退单参数
export interface CreateSalesReturnParams {
  originalOrderId?: string;
  customerCode: string;
  applicantCode: string;
  returnDate: string;
  returnType: ReturnType;
  reason: ReturnReason;
  reasonDetail?: string;
  handlingFee?: number;
  returnShippingCompany?: string;
  returnTrackingNumber: string;
  remark?: string;
  details: Omit<SalesReturnDetail, 'id' | 'productName' | 'colorName' | 'sizeName' | 'exchangeProductName' | 'exchangeColorName' | 'exchangeSizeName' | 'totalAmount' | 'actualAmount' | 'exchangeTotalAmount'>[];
}

// 更新销售退单参数
export interface UpdateSalesReturnParams extends CreateSalesReturnParams {
  id: string;
}

// 销售退单列表查询参数
export interface GetSalesReturnListParams {
  page: number;
  pageSize: number;
  search?: string;
  customerCode?: string;
  applicantCode?: string;
  returnType?: ReturnType;
  status?: ReturnStatus;
  reason?: ReturnReason;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 销售退单列表响应
export interface SalesReturnListItem {
  id: string;
  returnNumber: string;
  originalOrderNumber?: string;
  customerCode: string;
  customerName: string;
  applicantCode: string;
  applicantName: string;
  returnDate: string;
  returnType: ReturnType;
  status: ReturnStatus;
  reason: ReturnReason;
  totalReturnAmount: number;
  totalExchangeAmount: number;
  totalQuantity: number;
  returnTrackingNumber: string;
  createdAt: string;
  updatedAt: string;
}

export interface SalesReturnListResponse {
  returns: SalesReturnListItem[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// API响应类型
export type CreateSalesReturnResponse = ApiResponse<{ id: string; returnNumber: string }>;
export type UpdateSalesReturnResponse = ApiResponse<null>;
export type GetSalesReturnListResponse = ApiResponse<SalesReturnListResponse>;
export type GetSalesReturnDetailResponse = ApiResponse<SalesReturn>;
export type DeleteSalesReturnResponse = ApiResponse<null>;

// 退单操作参数
export interface SubmitSalesReturnParams {
  remark?: string;
}

export interface ApproveSalesReturnParams {
  approved: boolean;
  remark?: string;
}

export interface ProcessSalesReturnParams {
  processedDate?: string;
  remark?: string;
}

export interface CompleteSalesReturnParams {
  completedDate?: string;
  remark?: string;
}

// 退单操作响应
export type SubmitSalesReturnResponse = ApiResponse<null>;
export type ApproveSalesReturnResponse = ApiResponse<null>;
export type ProcessSalesReturnResponse = ApiResponse<null>;
export type CompleteSalesReturnResponse = ApiResponse<null>;
export type CancelSalesReturnResponse = ApiResponse<null>;
