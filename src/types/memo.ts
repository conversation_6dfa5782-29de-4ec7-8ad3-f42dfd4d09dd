export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// memos POST
export interface AddMemoParams {
  title: string;
  details: string | null;
  image: string | null;
  file: string | null;
}

export interface AddMemoParamsReponse {
  code: number;
  data: null;
  message?: string;
}

// memos GET
export interface MemosListParams {
  page: number;
  pageSize: number;
  title: string | null;
  startDate: string; //YYYY-MM-DD
  endDate: string; //YYYY-MM-DD
  sortOrder: 'ASC' | 'DESC' | '';
}

interface Item {
  id: string; //uuid
  title: string;
  details: string | null;
  image: string | null;
  file: string | null;
  createdAt: string; //'2025-06-13T11:49:08.016Z';
  updatedAt: string; //'2025-06-13T11:55:44.143Z';
}

export interface MemoeListReponse {
  data: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
    items: Item[];
  };
  code: number;
  message?: string;
}

// GET memos/{id} 获取备忘录详情
// PATCH memos/{id} 更新备忘录
export interface UpdateMemoParams {
  title?: string;
  details?: string;
  image?: string;
  file?: string;
}

//DELETE memos/{id}
