export interface SkuAccessory {
  accessoryCode: string;
  quantity: number;
}
// POST /skus
export interface AddSkuParams {
  name: string;
  manufacturerCode: string;
  brandCode: string;
  supplierCode: string;
  categoryCode: string;
  colorCode: string;
  craftDescription: string;
  clothingCost: number;
  retailPrice: number;
  preOrderPrice: number;
  restockPrice: number;
  spotPrice: number;
  accessories: SkuAccessory[];
  images: string[];
}

//GET /skus/paginated
export interface PaginatedParams {
  page: number;
  pageSize: number;
  nameSearch?: string;
  codeSearch?: string;
  brandCodeSearch?: string;
  supplierCodeSearch?: string;
  categoryCodeSearch?: string;
  colorCodeSearch?: string;
}

export interface Sku {
  id: string;
  code: string;
  name: string;
  manufacturerCode: string;
  brandCode: string;
  brandName: string;
  supplierCode: string;
  supplierName: string;
  categoryCode: string;
  categoryName: string;
  colorCode: string;
  colorName: string;
  craftDescription: string;
  clothingCost: number;
  retailPrice: number;
  preOrderPrice: number;
  restockPrice: number;
  spotPrice: number;
  accessoryCost: number;
  totalCost: number;
  accessories: SkuAccessory[];
  images: string[];
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedListResponse {
  code: number;
  message: string;
  data: {
    skus: Sku[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

//PATCH /skus/{id}
export interface UpdateSkuParams {
  name: string;
  manufacturerCode: string;
  brandCode: string;
  supplierCode: string;
  categoryCode: string;
  colorCode: string;
  craftDescription: string;
  clothingCost: number;
  retailPrice: number;
  preOrderPrice: number;
  restockPrice: number;
  spotPrice: number;
  accessories: SkuAccessory[];
  images: string[];
}

//DELETE /skus/{id}
//GET /skus/export/excel
export interface ExportSkuParams {
  skuIds: string[]; // uuid
  brandCodeSearch?: string;
  supplierCodeSearch?: string;
}

//GET /skus/import/template
//POST /skus/import/excel
