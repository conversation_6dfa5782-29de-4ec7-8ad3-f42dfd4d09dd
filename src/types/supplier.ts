export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

export interface AddSupplierParams {
  code: string;
  name: string;
  address?: string;
  contactName?: string;
  contactPhone?: string;
}

export interface SupplierListProps {
  code: string;
  name: string;
  address?: string;
  contactName?: string;
  contactPhone?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface SupplierListData {
  code: number;
  message: string;
  data: {
    suppliers: SupplierListProps[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

export interface SupplierDetailProps {
  code: number;
  data: SupplierListProps;
  message: string;
}

// 供应商列表查询参数
export interface SupplierListParams {
  page: number;
  pageSize: number;
  search?: string;
}

// 更新供应商参数
export interface UpdateSupplierParams {
  name: string;
  address?: string;
  contactName?: string;
  contactPhone?: string;
}
