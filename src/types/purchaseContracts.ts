export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

export enum PurchaseContractStatus {
  DRAFT = 'draft', // 草稿
  CONFIRMED = 'confirmed', // 已确认
  IN_PRODUCTION = 'in_production', // 生产中
  SHIPPED = 'shipped', // 已发货
  RECEIVED = 'received', // 已收货
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
}

// POST /purchase-contracts
export interface AddPurchaseContractParams {
  supplierCode: string;
  orderDate: string;
  status: PurchaseContractStatus;
  expectedDeliveryDate: string;
  remark: string;
  createdByUserCode: string;
  details: {
    accessoryId: string;
    quantity: number;
    unitPrice: number;
    deliveryDate: string;
    remark: string;
  }[];
}

// GET /purchase-contracts
export interface GetPurchaseListParams {
  page: number;
  pageSize: number;
  orderNumber?: number;
  supplierCode?: string;
  supplierName?: string;
  status?: PurchaseContractStatus;
  startDate?: string;
  endDate?: string;
  search?: string;
  createdByUserCode?: string;
}

export interface PurchaseContractListResponse {
  code: number;
  data: {
    details: GetPurchaseListParams[];
    total: number;
    page: number;
    pageSize: number;
  };
  message: string;
}

interface ContractListDetail {
  id: string;
  orderNumber: string;
  supplierCode: string;
  orderDate: string;
  totalAmount: number;
  totalQuantity: number;
  status: PurchaseContractStatus;
  expectedDeliveryDate: string;
  remark: string;
  createdByUserCode: string;
  createdAt: string;
  updatedAt: string;
  supplierName: string;
  supplierAddress: string | null;
  supplierContactName: string | null;
  supplierContactPhone: string | null;
}

// GET /purchase-contracts
export interface PurchaseContractListReponse {
  code: number;
  data: {
    contracts: ContractListDetail[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  message: string;
}

// GET /purchase-contracts/{id} uuid
export interface PurchaseContractDetailResponse {
  code: number;
  data: {
    id: string;
    orderNumber: string;
    supplierCode: string;
    orderDate: string;
    totalAmount: number;
    totalQuantity: number;
    status: PurchaseContractStatus;
    expectedDeliveryDate: string;
    remark: string;
    createdByUserCode: string;
    createdAt: string;
    updatedAt: string;
    supplierName: string;
    supplierAddress: string | null;
    supplierContactName: string | null;
    supplierContactPhone: string | null;
    details: [
      {
        id: string;
        accessoryId: string | null;
        quantity: number;
        unitPrice: number;
        totalAmount: number;
        deliveryDate: string;
        remark: string;
        createdAt: string | null;
        updatedAt: string | null;
        accessory: string | null;
      },
    ];
  };
  message: string;
}

export interface ContractDetailItem {
  accessoryId: string;
  quantity: number;
  unitPrice: number;
  deliveryDate: string;
  remark: string;
}

// PATCH purchase-contracts/{id} uuid
export interface UpdatePurchaseContractsParams {
  orderNumber: string;
  supplierCode: string;
  orderDate: string;
  status: PurchaseContractStatus;
  expectedDeliveryDate: string;
  remark: string;
  createdByUserCode: string;
  details: {
    accessoryId: string;
    quantity: number;
    unitPrice: number;
    deliveryDate: string;
    remark: string;
  }[];
}

// DELETE purchase-contracts/{id}

// GET purchase-contracts/export/excel
export interface ExportPurchaseContractsParams {
  startTime?: string;
  endTime?: string;
  contractIds?: string[]; // UUID数组
  supplierCode?: string;
  status?: PurchaseContractStatus;
}

// PATCH purchase-contracts/update-draft-prices
export interface UpdateDraftPricesParams {
  accessoryId: string;
  newUnitPrice?: number; //如果不提供的话就是用辅料默认的单价
}
