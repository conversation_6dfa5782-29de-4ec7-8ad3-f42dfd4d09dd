export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}



export interface AddCustomerProfileParams {
  customerCode: string;
  responsibleUserCode: string;
  amount: number;
  screenshot?: string;
  remark?: string;
}

// 客户档案明细类型枚举
export enum CustomerProfileDetailType {
  EXCHANGE = 'exchange', // 换货
  REFUND = 'refund', // 退货
  ORDER = 'order', // 订货
  RESTOCK = 'restock', // 补货
  DROP_SHIPPING = 'drop_shipping', // 代发
}

//customer-profiles/details POST
export interface UpdateCustomerProfileParams {
  customerCode: string,
  type: CustomerProfileDetailType,
  returnQuantity?: number,
  totalGoodsAmount?: number,
  exchangeOrderString?: string,
  deposit?: number,
  purchaseOrderId?: string,
  reservedQuantity?: number,
  restockDealAmount?: number,
  restockQuantity?: number,
  restockOrderId?: string,
  dropShippingAmount?: number,
  dropShippingQuantity?: number,
  dropShippingCustomerCount?: number,
  logisticsAmount?: number,
  imageUrl?: string,
  remark?: string,
  responsibleUserCode?: string
}

export interface CustomerProfileDetail {
        id: string,
        customerCode: string,
        type: CustomerProfileDetailType,
        returnQuantity: number,
        totalGoodsAmount: number,
        exchangeOrderString: string,
        deposit: number,
        purchaseOrderId: string,
        reservedQuantity: number,
        restockDealAmount: number,
        restockQuantity: number,
        restockOrderId: string,
        dropShippingAmount: number,
        dropShippingQuantity: number,
        dropShippingCustomerCount: number,
        logisticsAmount: number,
        imageUrl: string,
        remark: string,
        responsibleUserCode: string,
        createdAt: string,
        updatedAt: string,
        customerName: string,
        customerPhone: string | null ,
        customerAddress: string | null,
        responsibleUserName: string
      }

//customer-profiles/details GET
export interface CustomerProfileDetailResponse {
code: number,
  data: {
    details: CustomerProfileDetail[],
    total: number,
    page: number,
    pageSize: number,
    totalPages: number,
    customerProfile: {
      customerCode: string,
      totalDealAmount: number,
      totalExchangeAmount: number,
      totalRefundAmount: number,
      exchangeRate: number,
      refundRate: number,
      maxDebtLimit: number,
      allowDropShipping: boolean,
      currentDebt: number,
      currentPayment: number
    }
  },
  message?: string
}
//customer-profiles/details/{id} GET 使用uuid获取详情
export interface CustomerProfilesDetailResponse {
  code: number,
  data: {
    id: string,
    customerCode : string,
    type : CustomerProfileDetailType,
    returnQuantity : number,
    totalGoodsAmount : number,
    exchangeOrderString : string,
    deposit : number,
    purchaseOrderId : string,
    reservedQuantity : number,
    restockDealAmount : number,
    restockQuantity : number,
    restockOrderId : string,
    dropShippingAmount : number,
    dropShippingQuantity : number,
    dropShippingCustomerCount : number,
    logisticsAmount : number,
    imageUrl : string,
    remark : string,
    responsibleUserCode : string,
    createdAt : string,
    updatedAt : string,
    customerName : string,
    customerPhone : string | null,
    customerAddress : string | null,
    responsibleUserName : string
  },
  message?: string
}


//customer-profiles/details/{id} PATCH uuid更新客户档案

//customer-profiles/details/{id} DELETE uuid删除客户档案

//customer-profiles/export GET 导出客户档案Excel
