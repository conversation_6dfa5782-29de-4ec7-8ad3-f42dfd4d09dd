// 登录参数类型
export interface LoginParams {
  userCode: string;
  password: string;
}

// 登录响应数据类型
export interface LoginResponse {
  accessToken: string;
  user: {
    code: string;
    nickname: string;
    isSuperAdmin: boolean;
    isActive: boolean;
    routePermissions?: {
      [key: string]: {
        read: boolean;
        create: boolean;
        delete: boolean;
        export: boolean;
        import: boolean;
        update: boolean;
      };
    };
  };
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}
