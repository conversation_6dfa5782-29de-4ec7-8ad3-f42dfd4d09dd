export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

export interface AddBrandParams {
  code: string;
  name: string;
  orderPrice: number;
  restockPrice: number;
  spotPrice: number;
  isActive: boolean;
}

export interface UpdateBrandParams {
  name: string;
  orderPrice: number;
  restockPrice: number;
  spotPrice: number;
  isActive: boolean;
}

export interface BrandListProps {
  code: string;
  name: string;
  orderPrice: number;
  restockPrice: number;
  spotPrice: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface BrandListResponse {
  code: number;
  message: string;
  data: {
    brands: BrandListProps[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

export interface BrandListParams {
  page: number;
  pageSize: number;
  search?: string;
}
