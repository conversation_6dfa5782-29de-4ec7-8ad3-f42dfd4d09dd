export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// 价格信息
export interface PriceInfo {
  clothingCost: number;
  accessoryCost: number;
  retailPrice: number;
  preOrderPrice: number;
  restockPrice: number;
  spotPrice: number;
}

// 商品基本信息
export interface ProductInfo {
  code: string;
  name: string;
  brandName: string;
  supplierName: string;
}

// 尺寸库存
export interface SizeInventory {
  inventoryId: string; // 库存明细ID
  size: string;
  quantity: number;
  availableQuantity: number;
  reservedQuantity: number;
  safetyStock: number;
  latestCost?: number;
  warehouseLocation?: string;
  isLowStock: boolean;
  remark?: string;
  createdAt: string;
  updatedAt: string;
}

// 辅料数量
export interface AccessoryQuantity {
  accessoryId: string;
  quantity: number;
  accessoryName?: string;
  accessoryCode?: string;
}

// 颜色库存
export interface ColorInventory {
  colorCode: string;
  colorName: string;
  colorHex?: string;
  totalQuantity: number;
  totalAvailableQuantity: number;
  sizeDetails: SizeInventory[];
  images: string[];
  priceInfo: PriceInfo;
  accessories: AccessoryQuantity[];
  remark?: string;
}

// 商品库存响应
export interface ProductInventoryResponse {
  productInfo: ProductInfo;
  colorInventories: ColorInventory[];
  totalQuantity: number;
  totalAvailableQuantity: number;
}

// 库存明细
export interface InventoryDetail {
  id: string;
  productCode: string;
  productName: string;
  brandName: string;
  supplierName: string;
  colorCode: string;
  colorName: string;
  size: string;
  quantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  safetyStock: number;
  latestCost?: number;
  warehouseLocation?: string;
  remark?: string;
  isLowStock: boolean;
  createdAt: string;
  updatedAt: string;
}

// 库存明细列表参数
export interface InventoryListParams {
  page: number;
  pageSize: number;
  productCode?: string;
  productName?: string;
  colorCode?: string;
  colorName?: string;
  size?: string;
  brandCode?: string;
  supplierCode?: string;
  warehouseLocation?: string;
  lowStockOnly?: boolean;
  hasStockOnly?: boolean;
  search?: string;
  sortBy?: 'quantity' | 'availableQuantity' | 'latestCost' | 'createdAt' | 'productCode';
  sortOrder?: 'ASC' | 'DESC';
}

// 库存明细列表响应（按颜色分组）
export interface InventoryListResponse {
  code: number;
  data: {
    inventoryDetails: InventoryGroupByColor[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  message: string;
}

// 按颜色分组的库存明细
export interface InventoryGroupByColor {
  productCode: string;
  productName: string;
  brandName: string;
  supplierName: string;
  colorCode: string;
  colorName: string;
  colorImages: string[];
  totalQuantity: number;
  totalAvailableQuantity: number;
  sizeDetails: SizeInventory[];
}

// 批量获取商品库存汇总参数
export interface BatchInventorySummaryParams {
  productCodes: string[];
}

// 商品库存汇总
export interface ProductInventorySummary {
  productName: string;
  totalQuantity: number;
  totalAvailableQuantity: number;
  colorCount: number;
  lowStockColors: string[];
}

// 批量库存汇总响应
export interface BatchInventorySummaryResponse {
  code: number;
  data: Record<string, ProductInventorySummary | { error: string }>;
  message: string;
}

// 库存调整参数
export interface InventoryAdjustParams {
  productCode: string;
  colorCode: string;
  size: string;
  adjustType: 'SET' | 'ADD' | 'SUBTRACT';
  quantity: number;
  reason: string;
  referenceNumber?: string;
  operatorCode: string;
}

// 创建库存明细参数
export interface CreateInventoryParams {
  productCode: string;
  colorCode: string;
  size: string;
  quantity: number;
  reservedQuantity?: number;
  safetyStock?: number;
  latestCost?: number;
  warehouseLocation?: string;
  remark?: string;
}

// 批量创建库存明细参数
export interface BatchCreateInventoryParams {
  inventoryDetails: CreateInventoryParams[];
}

// 更新库存明细参数
export interface UpdateInventoryParams {
  quantity?: number;
  reservedQuantity?: number;
  safetyStock?: number;
  latestCost?: number;
  warehouseLocation?: string;
  remark?: string;
}

// 导出库存Excel参数
export interface ExportInventoryParams {
  productCode?: string;
  colorCode?: string;
  brandCode?: string;
  supplierCode?: string;
  lowStockOnly?: boolean;
  hasStockOnly?: boolean;
  inventoryIds?: string[];
}
