export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// accessories POST
export interface AddAccessorParams {
  articleNumber: string; // 货号
  name: string; //名称
  brandName?: string; //供应商名称, 用户自己选填
  supplierStyleNumber?: string; //供应商款号 可以不填
  color: string; //颜色, 用户自己输入
  size: string; //尺码, 用户自己输入
  costPrice: number; //成本价, 小数点后两位
  supplierCode: string; //供应商编码, 列表下拉单选
  imageUrl?: string; //图片, 可以不传入
}

// accessories GET
export interface AccessorListParams {
  page: number;
  pageSize: number;
  nameSearch?: string; //名称搜索
  articleNumberSearch?: string; //货号搜索
  supplierCodeSearch?: string; //供应商编码搜索
}

// 辅料列表项
export interface AccessoryListProps {
  id: string;
  articleNumber: string;
  name: string;
  brandName: string;
  supplierStyleNumber: string;
  color: string;
  size: string;
  costPrice: number;
  supplierCode: string; // 关联供应商编码
  supplierName: string;
  imageUrl: string;
  createdAt: string;
  updatedAt: string;
}

// 更新辅料参数
export interface UpdateAccessoryParams {
  articleNumber: string;
  name: string;
  brandName?: string;
  supplierStyleNumber?: string;
  color: string;
  size: string;
  costPrice: number;
  supplierCode: string;
  imageUrl?: string;
}

export interface AccessorListResponse {
  code: number;
  data: {
    accessories: AccessoryListProps[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  message: string;
}

// 辅料详情响应
export interface AccessoryDetailResponse {
  code: number;
  data: AccessoryListProps;
  message: string;
}

// 辅料搜索选择器返回的数据
export interface AccessorySelectOption {
  id: string; // 辅料UUID
  code: string; // 辅料编码
  name: string; // 辅料名称
  price: number; // 辅料价格
  quantity?: number; // 数量（用于产品表单）
}
