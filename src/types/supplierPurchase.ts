import type { ApiResponse } from './common';

// GET supplier-purchase-overview 获取所有供应商采购概览
export interface GetSupplierOverviewParams {
  startDate?: string;
  endDate?: string;
}

export interface SupplierOverviewItem {
  supplierCode: string;
  supplierName: string;
  totalOrders: number;
  totalAmount: number;
  totalQuantity: number;
  avgOrderAmount: number;
  activeContracts: number;
  pendingOrders: number;
  completedOrders: number;
  onTimeDeliveryRate: number;
  lastOrderDate: string;
  topProducts: Array<{
    productCode: string;
    productName: string;
    totalQuantity: number;
    totalAmount: number;
  }>;
}

export type SupplierOverviewResponse = ApiResponse<SupplierOverviewItem[]>;

// GET supplier-purchase-overview/ranking 获取供应商采购排行榜
export interface GetSupplierRankingParams {
  startDate?: string;
  endDate?: string;
  limit?: number;
}

export interface SupplierRankingItem {
  rank: number;
  supplierCode: string;
  supplierName: string;
  totalAmount: number;
  totalOrders: number;
  totalQuantity: number;
  avgOrderAmount: number;
  onTimeDeliveryRate: number;
  growthRate: number;
}

export type SupplierRankingResponse = ApiResponse<SupplierRankingItem[]>;

// GET supplier-purchase-overview/performance-comparison 获取供应商绩效对比
export interface GetSupplierPerformanceParams {
  supplierCodes: string; // 逗号分隔的供应商编码列表
}

export interface SupplierPerformanceItem {
  supplierCode: string;
  supplierName: string;
  totalOrders: number;
  totalAmount: number;
  totalQuantity: number;
  avgOrderAmount: number;
  avgDeliveryDays: number;
  onTimeDeliveryRate: number;
  qualityScore: number;
  cooperationMonths: number;
}

export type SupplierPerformanceResponse = ApiResponse<SupplierPerformanceItem[]>;

// GET supplier-purchase-overview/{supplierCode} 获取单个供应商采购详情
export interface GetSupplierDetailParams {
  supplierCode: string;
  startDate?: string;
  endDate?: string;
}

export interface SupplierDetailData {
  supplier: {
    code: string;
    name: string;
    address: string;
    contactName: string;
    contactPhone: string;
    createdAt: string;
    updatedAt: string;
  };
  summary: {
    totalOrders: number;
    totalAmount: number;
    totalQuantity: number;
    avgOrderAmount: number;
    avgDeliveryDays: number;
    onTimeDeliveryRate: number;
  };
  ordersByStatus: {
    [status: string]: number;
  };
  monthlyTrend: Array<{
    month: string;
    orderCount: number;
    totalAmount: number;
    avgDeliveryDays: number;
  }>;
  topProducts: Array<{
    productCode: string;
    productName: string;
    brandCode: string;
    categoryCode: string;
    totalQuantity: number;
    totalAmount: number;
    avgPrice: number;
    orderCount: number;
  }>;
  demandSources: Array<{
    salesPersonCode: string;
    salesPersonName: string;
    demandOrderCount: number;
    totalDemandQuantity: number;
    contributionRate: number;
  }>;
  recentOrders: Array<{
    id: string;
    orderNumber: string;
    orderDate: string;
    totalAmount: number;
    totalQuantity: number;
    status: string;
    createdByUserCode: string;
  }>;
}

export type SupplierDetailResponse = ApiResponse<SupplierDetailData>;
