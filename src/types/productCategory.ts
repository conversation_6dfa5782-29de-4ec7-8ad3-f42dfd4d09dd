export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// 新增商品分类参数
export interface AddProductCategoryParams {
  code: string;
  name: string;
  sizes?: string[];
}

// 更新商品分类参数
export interface UpdateProductCategoryParams {
  name: string;
  sizes?: string[];
}

// 商品分类列表项
export interface ProductCategoryListProps {
  id: string;
  code: string;
  name: string;
  sizes?: string[];
  createdAt: string;
  updatedAt: string;
}

// 商品分类列表响应
export interface ProductCategoryListResponse {
  code: number;
  message: string;
  data: {
    categories: ProductCategoryListProps[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

// 商品分类详情响应
export interface ProductCategoryDetailResponse {
  code: number;
  data: ProductCategoryListProps;
  message: string;
}

// 商品分类列表查询参数
export interface ProductCategoryListParams {
  page: number;
  pageSize: number;
  search?: string;
}

// 更新商品分类参数（用于API调用）
export interface UpdateProductCategoryParams {
  name: string;
  sizes?: string[];
}
