import type { ApiResponse } from './common';

//GET demand-purchase-statistics/dashboard 获取综合仪表板数据
export interface GetStatisticsParams {
  categoryCode?: string;
  brandCode?: string;
  supplierCode?: string;
  salesPersonCode?: string;
  endDate?: string;
  startDate?: string;
}

export interface StatisticsData {
  summary: {
    totalDemandOrders: number;
    totalPurchaseOrders: number;
    totalDemandQuantity: number;
    totalPurchaseQuantity: number;
    totalDemandAmount: number;
    totalPurchaseAmount: number;
    conversionRate: number;
    fulfillmentRate: number;
  };
  demandOrderStats: object;
  purchaseOrderStats: object;
  productDemandStats: object;
}

export type StatisticsResponse = ApiResponse<StatisticsData>;

//GET demand-purchase-statistics/demand-orders 获取需求订单统计
export interface GetDemandOrdersParams {
  salesPersonCode?: string;
  endDate?: string;
  startDate?: string;
}

export interface DemandOrdersData {
  totalOrders: number;
  totalQuantity: number;
  totalAmount: number;
  avgAmount: number;
  statusBreakdown: {
    [status: string]: number;
  };
  priorityBreakdown: {
    [priority: string]: number;
  };
  salesPersonBreakdown: Array<{
    salesPersonCode: string;
    orderCount: number;
    totalQuantity: number;
    totalAmount: number;
  }>;
  monthlyTrend: Array<{
    month: string;
    orderCount: number;
    totalQuantity: number;
    totalAmount: number;
  }>;
}

export type DemandOrdersResponse = ApiResponse<DemandOrdersData>;

//GET demand-purchase-statistics/purchase-orders 获取采购订单统计
export interface GetPurchaseOrdersParams {
  supplierCode?: string;
  endDate?: string;
  startDate?: string;
}

export interface PurchaseOrdersData {
  totalOrders: number;
  totalQuantity: number;
  totalAmount: number;
  avgAmount: number;
  statusBreakdown: {
    [status: string]: number;
  };
  supplierBreakdown: Array<{
    supplierCode: string;
    orderCount: number;
    totalQuantity: number;
    totalAmount: number;
    avgDeliveryDays: number;
  }>;
  monthlyTrend: Array<{
    month: string;
    orderCount: number;
    totalQuantity: number;
    totalAmount: number;
  }>;
}

export type PurchaseOrdersResponse = ApiResponse<PurchaseOrdersData>;

// GET demand-purchase-statistics/product-demand 获取商品需求统计
export interface GetProductDemandParams {
  categoryCode?: string;
  brandCode?: string;
  endDate?: string;
  startDate?: string;
}

export interface ProductDemandData {
  topDemandProducts: Array<{
    productCode: string;
    totalDemand: number;
    totalShortage: number;
    demandOrderCount: number;
    avgUnitPrice: number;
  }>;
  brandAnalysis: Array<{
    brandCode: string;
    totalDemand: number;
    totalShortage: number;
    productCount: number;
  }>;
  supplierAnalysis: Array<{
    supplierCode: string;
    totalDemand: number;
    totalShortage: number;
    productCount: number;
  }>;
}

export type ProductDemandResponse = ApiResponse<ProductDemandData>;
