//POST skus-inventory
export interface AddSkusInventory {
  skuId: string;
  size: string;
  currentStock: number;
  purchasingStock: number;
  needRestockStock: number;
  remarks: string;
}

// GET skus-inventory
export interface GetSkusInventoryListParams {
  page: number;
  pageSize: number;
  search?: string;
  skuCode?: string;
  brandCode?: string;
  colorCode?: string;
  size?: string;
  stockStatus?: 'normal' | 'out_of_stock';
}

export interface SkusInventoryData {
  id: string;
  skuId: string;
  skuCode: string;
  skuName: string;
  brandCode: string;
  brandName: string;
  skuImages: string[];
  colorCode: string;
  colorName: string;
  size: string;
  currentStock: number;
  purchasingStock: number;
  needRestockStock: number;
  totalStock: number;
  availableStock: number;
  isOutOfStock: boolean;
  stockStatus: string;
  brandIsActive: boolean;
  remarks: string;
  lastInventoryDate: string | null;
  lastInventoryCount: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface SkusInventoryListResponse {
  code: number;
  message: string;
  data: {
    data: SkusInventoryData[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

// 更新库存明细
export interface UpdateSkusInventory {
  currentStock?: number;
  purchasingStock?: number;
  needRestockStock?: number;
  remarks?: string;
}

// 库存调整参数
export interface AdjustSkusInventoryStock {
  adjustmentType: 'increase' | 'decrease' | 'set';
  quantity: number;
  reason: string;
}

// 库存盘点参数
export interface CountSkusInventory {
  actualStock: number;
  remarks?: string;
}

// 导出参数
export interface ExportSkusInventoryParams {
  skusInventoryIds?: string[];
  search?: string;
  skuCode?: string;
  brandCode?: string;
  colorCode?: string;
  size?: string;
  stockStatus?: 'normal' | 'out_of_stock';
}
