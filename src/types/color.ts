export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// 新增颜色参数
export interface AddColorParams {
  code: string;
  name: string;
}

// 更新颜色参数
export interface UpdateColorParams {
  code: string;
  name: string;
}

// 颜色列表项
export interface ColorListProps {
  id: string;
  code: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

// 颜色列表查询参数
export interface ColorListParams {
  page: number;
  pageSize: number;
  search?: string;
}

// 颜色列表响应
export interface ColorListResponse {
  code: number;
  message: string;
  data: {
    colors: ColorListProps[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

// 颜色详情响应
export interface ColorDetailResponse {
  code: number;
  data: ColorListProps;
  message: string;
}
