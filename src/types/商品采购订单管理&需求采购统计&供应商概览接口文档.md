# 商品采购订单管理 & 需求采购统计 & 供应商概览接口文档

## 目录

1. [商品采购订单管理接口](#商品采购订单管理接口)
2. [需求采购统计分析接口](#需求采购统计分析接口)
3. [供应商采购概览接口](#供应商采购概览接口)

---

## 商品采购订单管理接口

### 模块概述

商品采购订单管理模块负责处理从需求订单到采购订单的完整流程，包括需求订单合并、采购订单创建、状态管理等功能。

### 基础路径

```
/product-purchase-orders
```

### 认证要求

所有接口都需要 JWT Token 认证，请在请求头中添加：

```
Authorization: Bearer <your-jwt-token>
```

### 1. 合并需求订单为采购订单

**接口地址：** `POST /product-purchase-orders/merge-demands`

**用途：** 将多个需求订单合并为一个采购订单，用于批量采购管理

**请求参数：**

```typescript
{
  demandOrderIds: string[];        // 需求订单ID列表（必填）
  supplierCode: string;           // 供应商编码（必填）
  expectedDeliveryDate?: string;  // 预计交货日期（可选，格式：YYYY-MM-DD）
  createdByUserCode: string;      // 创建人编码（必填）
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "采购订单创建成功",
  data: null
}
```

### 2. 查询采购订单列表

**接口地址：** `GET /product-purchase-orders`

**用途：** 分页查询采购订单列表，支持多种筛选条件

**查询参数：**

- `page` (必填): 页码，从1开始
- `pageSize` (必填): 每页数量，1-100
- `orderNumber` (可选): 订单编号，支持模糊查询
- `supplierCode` (可选): 供应商编码
- `status` (可选): 订单状态
  - `draft`: 草稿
  - `confirmed`: 已确认
  - `producing`: 生产中
  - `shipped`: 已发货
  - `received`: 已收货
  - `completed`: 已完成
  - `cancelled`: 已取消
- `orderDateStart` (可选): 订单日期开始，格式：YYYY-MM-DD
- `orderDateEnd` (可选): 订单日期结束，格式：YYYY-MM-DD
- `createdAtStart` (可选): 创建时间开始，格式：YYYY-MM-DD
- `createdAtEnd` (可选): 创建时间结束，格式：YYYY-MM-DD
- `productCode` (可选): 商品编码，查询包含指定商品的订单
- `sortBy` (可选): 排序字段，默认 createdAt
- `sortOrder` (可选): 排序方向，ASC/DESC，默认 DESC

**响应格式：**

```typescript
{
  code: 200,
  message: "查询成功",
  data: {
    items: ProductPurchaseOrder[],  // 订单列表
    total: number,                  // 总数量
    page: number,                   // 当前页码
    pageSize: number,               // 每页数量
    totalPages: number              // 总页数
  }
}
```

**订单对象结构：**

```typescript
{
  id: string,                           // 采购订单ID
  orderNumber: string,                  // 采购订单编号
  supplierCode: string,                 // 供应商编码
  orderDate: string,                    // 采购订单日期
  expectedDeliveryDate?: string,        // 预计交货日期
  totalAmount: number,                  // 合计金额
  totalQuantity: number,                // 合计数量
  status: string,                       // 订单状态
  createdByUserCode?: string,           // 创建人编码
  mergedDemandOrderIds?: string,        // 合并的需求订单ID列表
  confirmedByUserCode?: string,         // 确认人编码
  confirmedAt?: string,                 // 确认时间
  receivedByUserCode?: string,          // 收货人编码
  receivedAt?: string,                  // 收货时间
  createdAt: string,                    // 创建时间
  updatedAt: string,                    // 更新时间
  supplier?: Supplier,                  // 供应商信息
  createdByUser?: User,                 // 创建人信息
  details?: ProductPurchaseOrderDetail[] // 订单详情
}
```

### 3. 获取采购订单详情

**接口地址：** `GET /product-purchase-orders/{id}`

**用途：** 获取单个采购订单的详细信息，包括订单详情和需求来源

**路径参数：**

- `id`: 采购订单ID

**响应格式：**

```typescript
{
  code: 200,
  message: "查询成功",
  data: {
    ...ProductPurchaseOrder,        // 订单基本信息
    demandSources?: Array<{         // 需求来源信息
      demandOrderId: string,
      demandOrderNumber: string,
      salesPersonCode: string,
      demandDate: string,
      totalQuantity: number,
      totalAmount: number
    }>
  }
}
```

### 4. 确认采购订单

**接口地址：** `POST /product-purchase-orders/{id}/confirm`

**用途：** 确认采购订单，将状态从草稿变更为已确认

**路径参数：**

- `id`: 采购订单ID

**请求参数：**

```typescript
{
  confirmedByUserCode: string; // 确认人编码（必填）
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "订单确认成功",
  data: null
}
```

### 5. 收货确认

**接口地址：** `POST /product-purchase-orders/{id}/receive`

**用途：** 确认收货，更新库存并变更订单状态

**路径参数：**

- `id`: 采购订单ID

**请求参数：**

```typescript
{
  receivedByUserCode: string; // 收货人编码（必填）
  receiveDetails: Array<{
    // 收货详情（必填）
    detailId: string; // 订单详情ID
    receivedQuantity: number; // 实际收货数量
  }>;
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "收货确认成功",
  data: null
}
```

### 6. 取消采购订单

**接口地址：** `POST /product-purchase-orders/{id}/cancel`

**用途：** 取消采购订单，只有草稿状态的订单可以取消

**路径参数：**

- `id`: 采购订单ID

**响应格式：**

```typescript
{
  code: 200,
  message: "订单取消成功",
  data: null
}
```

---

## 需求采购统计分析接口

### 模块概述

需求采购统计分析模块提供全面的数据分析功能，包括需求订单统计、采购订单统计、商品需求分析和综合仪表板数据。

### 基础路径

```
/demand-purchase-statistics
```

### 1. 综合仪表板数据

**接口地址：** `GET /demand-purchase-statistics/dashboard`

**用途：** 获取综合仪表板数据，包含需求采购转换率、满足率等关键指标

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `salesPersonCode` (可选): 销售人员编码
- `supplierCode` (可选): 供应商编码
- `brandCode` (可选): 品牌编码
- `categoryCode` (可选): 分类编码

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    summary: {
      totalDemandOrders: number,      // 总需求订单数
      totalPurchaseOrders: number,    // 总采购订单数
      totalDemandQuantity: number,    // 总需求数量
      totalPurchaseQuantity: number,  // 总采购数量
      totalDemandAmount: number,      // 总需求金额
      totalPurchaseAmount: number,    // 总采购金额
      conversionRate: number,         // 转换率(%)
      fulfillmentRate: number         // 满足率(%)
    },
    demandOrderStats: object,         // 需求订单统计
    purchaseOrderStats: object,       // 采购订单统计
    productDemandStats: object        // 商品需求统计
  }
}
```

### 2. 需求订单统计

**接口地址：** `GET /demand-purchase-statistics/demand-orders`

**用途：** 获取需求订单的详细统计分析，包括状态分布、销售人员分布、月度趋势等

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `salesPersonCode` (可选): 销售人员编码

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    totalOrders: number,              // 总订单数
    totalQuantity: number,            // 总数量
    totalAmount: number,              // 总金额
    avgAmount: number,                // 平均金额
    statusBreakdown: {                // 状态分布
      [status: string]: number
    },
    priorityBreakdown: {              // 优先级分布
      [priority: string]: number
    },
    salesPersonBreakdown: Array<{     // 销售人员分布
      salesPersonCode: string,
      orderCount: number,
      totalQuantity: number,
      totalAmount: number
    }>,
    monthlyTrend: Array<{             // 月度趋势
      month: string,
      orderCount: number,
      totalQuantity: number,
      totalAmount: number
    }>
  }
}
```

### 3. 采购订单统计

**接口地址：** `GET /demand-purchase-statistics/purchase-orders`

**用途：** 获取采购订单的详细统计分析，包括供应商分布、月度趋势等

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `supplierCode` (可选): 供应商编码

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    totalOrders: number,              // 总订单数
    totalQuantity: number,            // 总数量
    totalAmount: number,              // 总金额
    avgAmount: number,                // 平均金额
    statusBreakdown: {                // 状态分布
      [status: string]: number
    },
    supplierBreakdown: Array<{        // 供应商分布
      supplierCode: string,
      orderCount: number,
      totalQuantity: number,
      totalAmount: number,
      avgDeliveryDays: number
    }>,
    monthlyTrend: Array<{             // 月度趋势
      month: string,
      orderCount: number,
      totalQuantity: number,
      totalAmount: number
    }>
  }
}
```

### 4. 商品需求统计

**接口地址：** `GET /demand-purchase-statistics/product-demand`

**用途：** 获取商品需求的详细分析，包括热门商品、品牌分析、供应商分析

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `brandCode` (可选): 品牌编码
- `categoryCode` (可选): 分类编码

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    topDemandProducts: Array<{        // 热门需求商品（前20名）
      productCode: string,
      totalDemand: number,
      totalShortage: number,
      demandOrderCount: number,
      avgUnitPrice: number
    }>,
    brandAnalysis: Array<{            // 品牌分析
      brandCode: string,
      totalDemand: number,
      totalShortage: number,
      productCount: number
    }>,
    supplierAnalysis: Array<{         // 供应商分析
      supplierCode: string,
      totalDemand: number,
      totalShortage: number,
      productCount: number
    }>
  }
}
```

---

## 供应商采购概览接口

### 模块概述

供应商采购概览模块提供供应商维度的采购数据分析，包括供应商概览、详细信息、排行榜和绩效对比等功能。

### 基础路径

```
/supplier-purchase-overview
```

### 1. 获取所有供应商采购概览

**接口地址：** `GET /supplier-purchase-overview`

**用途：** 获取所有有采购记录的供应商概览信息，用于供应商管理首页展示

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: Array<{
    supplierCode: string,             // 供应商编码
    supplierName: string,             // 供应商名称
    totalOrders: number,              // 总订单数
    totalAmount: number,              // 总金额
    totalQuantity: number,            // 总数量
    avgOrderAmount: number,           // 平均订单金额
    activeContracts: number,          // 有效合同数
    pendingOrders: number,            // 待处理订单数
    completedOrders: number,          // 已完成订单数
    onTimeDeliveryRate: number,       // 按时交货率(%)
    lastOrderDate: string,            // 最后订单日期
    topProducts: Array<{              // 热门商品（前5名）
      productCode: string,
      productName: string,
      totalQuantity: number,
      totalAmount: number
    }>
  }>
}
```

### 2. 获取供应商采购排行榜

**接口地址：** `GET /supplier-purchase-overview/ranking`

**用途：** 获取供应商采购排行榜，按采购金额排序

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `limit` (可选): 返回数量限制，默认10

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: Array<{
    rank: number,                     // 排名
    supplierCode: string,             // 供应商编码
    supplierName: string,             // 供应商名称
    totalAmount: number,              // 总采购金额
    totalOrders: number,              // 总订单数
    totalQuantity: number,            // 总采购数量
    avgOrderAmount: number,           // 平均订单金额
    onTimeDeliveryRate: number,       // 按时交货率(%)
    growthRate: number                // 增长率(%)
  }>
}
```

### 3. 获取供应商绩效对比

**接口地址：** `GET /supplier-purchase-overview/performance-comparison`

**用途：** 对比多个供应商的绩效指标

**查询参数：**

- `supplierCodes` (必填): 供应商编码列表，逗号分隔，如：SUP001,SUP002,SUP003

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: Array<{
    supplierCode: string,             // 供应商编码
    supplierName: string,             // 供应商名称
    totalOrders: number,              // 总订单数
    totalAmount: number,              // 总金额
    totalQuantity: number,            // 总数量
    avgOrderAmount: number,           // 平均订单金额
    avgDeliveryDays: number,          // 平均交货天数
    onTimeDeliveryRate: number,       // 按时交货率(%)
    qualityScore: number,             // 质量评分
    cooperationMonths: number         // 合作月数
  }>
}
```

### 4. 获取单个供应商采购详情

**接口地址：** `GET /supplier-purchase-overview/{supplierCode}`

**用途：** 获取单个供应商的详细采购信息，包括统计数据、商品分析、需求来源等

**路径参数：**

- `supplierCode`: 供应商编码

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    supplier: {                       // 供应商基本信息
      code: string,                   // 供应商编码
      name: string,                   // 供应商名称
      address: string,                // 地址
      contactName: string,            // 联系人
      contactPhone: string,           // 联系电话
      createdAt: string,              // 创建时间
      updatedAt: string               // 更新时间
    },
    summary: {                        // 统计摘要
      totalOrders: number,            // 总订单数
      totalAmount: number,            // 总金额
      totalQuantity: number,          // 总数量
      avgOrderAmount: number,         // 平均订单金额
      avgDeliveryDays: number,        // 平均交货天数
      onTimeDeliveryRate: number      // 按时交货率(%)
    },
    ordersByStatus: {                 // 按状态分布
      [status: string]: number
    },
    monthlyTrend: Array<{             // 月度趋势
      month: string,
      orderCount: number,
      totalAmount: number,
      avgDeliveryDays: number
    }>,
    topProducts: Array<{              // 热门商品（前10名）
      productCode: string,
      productName: string,
      brandCode: string,
      categoryCode: string,
      totalQuantity: number,
      totalAmount: number,
      avgPrice: number,
      orderCount: number
    }>,
    demandSources: Array<{            // 需求来源分析
      salesPersonCode: string,
      salesPersonName: string,
      demandOrderCount: number,
      totalDemandQuantity: number,
      contributionRate: number
    }>,
    recentOrders: Array<{             // 最近订单（前10条）
      id: string,
      orderNumber: string,
      orderDate: string,
      totalAmount: number,
      totalQuantity: number,
      status: string,
      createdByUserCode: string
    }>
  }
}
```

---

## 数据字典

### 采购订单状态 (ProductPurchaseOrderStatus)

- `draft`: 草稿 - 订单刚创建，可以编辑
- `confirmed`: 已确认 - 订单已确认，不可编辑
- `producing`: 生产中 - 供应商正在生产
- `shipped`: 已发货 - 供应商已发货
- `received`: 已收货 - 已收到货物
- `completed`: 已完成 - 订单完成
- `cancelled`: 已取消 - 订单已取消

### 需求订单优先级

- `low`: 低优先级
- `medium`: 中优先级
- `high`: 高优先级
- `urgent`: 紧急

### 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

---

## 使用建议

### 1. 数据流程

1. **需求订单创建**: 销售人员根据客户需求创建需求订单
2. **需求订单合并**: 生产人员将多个需求订单合并为采购订单
3. **采购订单确认**: 确认采购订单并发送给供应商
4. **订单状态跟踪**: 跟踪订单状态直到收货完成
5. **数据分析**: 通过统计接口分析采购效率和供应商绩效

### 2. 前端开发建议

#### 页面布局建议

- **采购订单列表页**: 使用表格展示，支持筛选和排序
- **订单详情页**: 使用标签页分组显示基本信息、订单详情、需求来源
- **统计分析页**: 使用图表展示趋势和分布数据
- **供应商概览页**: 使用卡片布局展示关键指标

#### 交互建议

- 分页查询建议每页显示20-50条记录
- 统计图表建议使用月度趋势数据
- 详情页面建议使用标签页分组显示不同维度的数据
- 支持Excel导出功能（虽然接口文档中未详细说明）

#### 状态管理

- 采购订单状态使用不同颜色标识
- 提供状态变更的操作按钮
- 显示状态变更历史记录

### 3. 性能优化建议

#### 查询优化

- 大数据量查询时建议使用日期范围过滤
- 统计接口建议添加缓存机制
- 列表查询建议只返回必要字段，详情查询返回完整信息

#### 数据展示优化

- 长列表使用虚拟滚动
- 图表数据使用懒加载
- 大数据量导出使用异步处理

### 4. 业务逻辑说明

#### 订单合并逻辑

- 只能合并相同供应商的需求订单
- 合并时会自动计算总数量和总金额
- 合并后的采购订单会记录来源需求订单信息

#### 库存更新逻辑

- 收货确认时会自动更新库存
- 支持部分收货，可多次确认收货
- 收货数量不能超过采购数量

#### 统计计算逻辑

- 转换率 = 采购订单数 / 需求订单数 × 100%
- 满足率 = 采购数量 / 需求数量 × 100%
- 按时交货率 = 按时交货订单数 / 已完成订单数 × 100%

### 5. 注意事项

#### 权限控制

- 所有接口都需要JWT认证
- 不同角色可能有不同的操作权限
- 建议在前端也做相应的权限控制

#### 数据一致性

- 订单状态变更需要验证前置条件
- 库存更新需要考虑并发问题
- 统计数据可能存在延迟，建议添加刷新功能

#### 错误处理

- 网络错误时提供重试机制
- 业务错误时显示具体错误信息
- 长时间操作提供进度提示

---

## 常见问题 FAQ

### Q1: 如何处理需求订单合并失败？

A1: 检查以下条件：

- 需求订单是否属于同一供应商
- 需求订单状态是否允许合并
- 用户是否有合并权限

### Q2: 统计数据不准确怎么办？

A2: 统计数据基于实时计算，可能的原因：

- 数据库事务未提交
- 缓存未更新
- 查询条件设置错误

### Q3: 供应商概览数据为空？

A3: 可能的原因：

- 该供应商没有采购记录
- 查询时间范围内没有数据
- 供应商已被删除

### Q4: 如何提高查询性能？

A4: 建议：

- 使用合适的时间范围过滤
- 避免查询过大的数据集
- 使用分页查询
- 考虑添加索引优化

---

_文档版本: v1.0_
_最后更新: 2025-01-20_
_维护人员: 后端开发团队_
