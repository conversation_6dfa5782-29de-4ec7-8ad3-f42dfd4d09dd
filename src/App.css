* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans',
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease;
}

/* 暗色主题样式 */
body.dark-theme {
  background-color: #141414;
  color: rgba(255, 255, 255, 0.85);
}

#root {
  height: 100%;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色主题滚动条样式 */
body.dark-theme ::-webkit-scrollbar-track {
  background: #1f1f1f;
}

body.dark-theme ::-webkit-scrollbar-thumb {
  background: #434343;
}

body.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: #666666;
}

/* 触发器样式 */
.trigger {
  padding: 0 24px;
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

/* 隐藏在小屏幕上的元素 */
@media screen and (max-width: 768px) {
  .hidden-xs {
    display: none !important;
  }
}
