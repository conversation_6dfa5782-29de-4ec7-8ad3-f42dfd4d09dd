import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  Tag,
  message,
  Popconfirm,
  Modal,
  Row,
  Col,
  Tooltip,
  Dropdown,
  MenuProps,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CopyOutlined,
  DollarOutlined,
  BankOutlined,
  DownOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import { getUserList, deleteUser, updateUser } from '@/api/UserApi';
import type { UserProps } from '@/types/user';
import CompanyAdminManager from '@/components/CompanyAdminManager/CompanyAdminManager';
import { getErrorMessage, handleApiResponse, logError } from '@/utils/errorHandler';
import { getCompanyName, getCompanyOptions } from '@/constants/company';
import useStyles from './UserManagementStyles';

const { Search } = Input;

const UserManagement: React.FC = () => {
  const { styles } = useStyles();
  const navigate = useNavigate();
  const [users, setUsers] = useState<UserProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [adminModalVisible, setAdminModalVisible] = useState(false);
  const [managingUser, setManagingUser] = useState<UserProps | null>(null);

  // 获取用户列表
  const fetchUsers = async (page = 1, pageSize = 10, search = '') => {
    setLoading(true);
    try {
      const response = await getUserList({
        page,
        pageSize,
        search: search.trim() || undefined,
        includeInactive: true,
      });

      if (response.code === 200) {
        setUsers(response.data.users);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取用户列表失败');
      }
    } catch (error: any) {
      logError('获取用户列表', error);
      message.error(getErrorMessage(error, '获取用户列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchUsers();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
    fetchUsers(1, pagination.pageSize, value);
  };

  // 分页处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));
    fetchUsers(current, pageSize, searchText);
  };

  // 刷新列表
  const handleRefresh = () => {
    fetchUsers(pagination.current, pagination.pageSize, searchText);
  };

  // 新增用户
  const handleAdd = () => {
    navigate('/system/users/add');
  };

  // 查看用户
  const handleView = (user: UserProps) => {
    navigate(`/system/users/view/${user.code}`);
  };

  // 编辑用户
  const handleEdit = (user: UserProps) => {
    navigate(`/system/users/edit/${user.code}`);
  };

  // 删除用户
  const handleDelete = async (userCode: string) => {
    try {
      const response = await deleteUser(userCode);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除用户', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 管理公司管理员权限
  const handleManageCompanyAdmin = (user: UserProps) => {
    setManagingUser(user);
    setAdminModalVisible(true);
  };

  // 复制银行卡号码
  const handleCopyBankAccount = async (bankAccountNumber: string) => {
    try {
      await navigator.clipboard.writeText(bankAccountNumber);
      message.success('银行卡号码已复制到剪贴板');
    } catch (error) {
      // 如果 Clipboard API 不可用，使用备用方法
      const textArea = document.createElement('textarea');
      textArea.value = bankAccountNumber;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        message.success('银行卡号码已复制到剪贴板');
      } catch (err) {
        message.error('复制失败，请手动复制');
      } finally {
        document.body.removeChild(textArea);
      }
    }
  };

  // 发薪快捷功能
  const handlePaySalary = (user: UserProps) => {
    // 构建跳转参数
    const params = new URLSearchParams({
      type: 'human_resources',
      employeeInfo: user.nickname,
      humanResourcesAuditStatus: 'approved',
      createDate: new Date().toISOString().split('T')[0], // 今天的日期 YYYY-MM-DD 格式
    });

    // 跳转到运营资产页面
    navigate(`/financial/operating-assets?${params.toString()}`);
  };

  // 快捷设置公司
  const handleSetCompany = async (user: UserProps, companyCode: string) => {
    try {
      // 构建更新数据，保持其他字段不变
      const updateData = {
        nickname: user.nickname,
        bankAccountName: user.bankAccountName || undefined,
        bankAccountNumber: user.bankAccountNumber || undefined,
        isActive: user.isActive,
        routePermissions: user.routePermissions,
        companyCode: companyCode, // 只更新公司编码
      };

      const response = await updateUser(user.code, updateData);

      const result = handleApiResponse(response, '设置公司成功', '设置公司失败');

      if (result.success) {
        message.success(`已将用户 ${user.nickname} 的公司设置为${getCompanyName(companyCode)}`);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('设置公司', error);
      message.error(getErrorMessage(error, '设置公司失败'));
    }
  };

  // 创建设置公司的下拉菜单
  const getCompanyMenuItems = (user: UserProps): MenuProps['items'] =>
    getCompanyOptions().map((option) => ({
      key: option.value,
      label: option.label,
      onClick: () => handleSetCompany(user, option.value),
    }));

  // 表格列定义
  const columns: ColumnsType<UserProps> = [
    {
      title: '用户编码',
      dataIndex: 'code',
      key: 'code',
      width: 90,
      render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span>,
    },
    {
      title: '用户昵称',
      dataIndex: 'nickname',
      key: 'nickname',
      width: 90,
    },
    {
      title: '所属公司',
      key: 'company',
      width: 120,
      render: (_, record) => {
        if (record.company?.name) {
          return record.company.name;
        }
        return '-';
      },
    },
    {
      title: '账户名',
      key: 'bankAccountName',
      dataIndex: 'bankAccountName',
      width: 200,
    },
    {
      title: '账户号码',
      key: 'bankAccountNumber',
      dataIndex: 'bankAccountNumber',
      width: 300,
      render: (text: string) => {
        if (!text) return '-';

        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span style={{ fontFamily: 'monospace', flex: 1, fontSize: '13px' }}>{text}</span>
            <Tooltip title="点击复制银行卡号码">
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => handleCopyBankAccount(text)}
                style={{
                  color: '#1890ff',
                  padding: '4px',
                  height: '24px',
                  width: '24px',
                  minWidth: 'auto',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '4px',
                  transition: 'all 0.2s',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f0f8ff';
                  e.currentTarget.style.color = '#0066cc';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = '#1890ff';
                }}
              />
            </Tooltip>
          </div>
        );
      },
    },
    {
      title: '管理员权限',
      key: 'adminRoles',
      width: 80,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          {record.isSuperAdmin && (
            <Tag color="red" style={{ margin: 0 }}>
              超级管理员
            </Tag>
          )}
          {record.isCompanyAdmin && (
            <Tag color="blue" style={{ margin: 0 }}>
              公司管理员 {record.companyCode && `(${record.companyCode})`}
            </Tag>
          )}
          {!record.isSuperAdmin && !record.isCompanyAdmin && (
            <Tag color="default" style={{ margin: 0 }}>
              普通用户
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      align: 'center',
      render: (isActive) => (
        <Tag color={isActive ? 'success' : 'warning'}>{isActive ? '激活' : '禁用'}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 400,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small" wrap>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<DollarOutlined />}
            onClick={() => handlePaySalary(record)}
            style={{ color: '#52c41a' }}
          >
            发薪
          </Button>
          <Dropdown
            menu={{ items: getCompanyMenuItems(record) }}
            trigger={['click']}
            placement="bottomLeft"
          >
            <Button type="link" size="small" icon={<BankOutlined />} style={{ color: '#1890ff' }}>
              设置公司 <DownOutlined />
            </Button>
          </Dropdown>
          {!record.isSuperAdmin && (
            <Button type="link" size="small" onClick={() => handleManageCompanyAdmin(record)}>
              {record.isCompanyAdmin ? '管理权限' : '设为管理员'}
            </Button>
          )}
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record.code)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <Search
                placeholder="搜索用户编码或昵称"
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                style={{ maxWidth: 400 }}
              />
            </Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增用户
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="code"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1320 }}
        />
      </Card>

      {/* 公司管理员管理模态框 */}
      <Modal
        title="管理公司管理员权限"
        open={adminModalVisible}
        onCancel={() => {
          setAdminModalVisible(false);
          setManagingUser(null);
        }}
        footer={null}
        width={700}
        destroyOnClose
      >
        <CompanyAdminManager
          user={managingUser || undefined}
          onSuccess={() => {
            setAdminModalVisible(false);
            setManagingUser(null);
            handleRefresh();
          }}
        />
      </Modal>
    </div>
  );
};

export default UserManagement;
