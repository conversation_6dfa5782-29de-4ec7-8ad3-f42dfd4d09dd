import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    padding: 24px;
    background: ${token.colorBgContainer};
    min-height: calc(100vh - 64px);
  `,

  header: css`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
  `,

  form: css`
    max-width: 1200px;
    
    .ant-form-item-label > label {
      font-weight: 500;
    }
    
    .ant-form-item-explain-error {
      font-size: 12px;
    }
    
    .ant-form-item-extra {
      font-size: 12px;
      color: ${token.colorTextSecondary};
    }
  `,

  section: css`
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
  `,

  sectionTitle: css`
    font-size: 16px;
    font-weight: 600;
    color: ${token.colorText};
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid ${token.colorBorderSecondary};
  `,

  readOnlyField: css`
    .ant-input,
    .ant-select-selector,
    .ant-switch {
      background-color: ${token.colorBgContainerDisabled};
      border-color: ${token.colorBorder};
      cursor: default;
    }
  `,

  actions: css`
    padding-top: 24px;
    border-top: 1px solid ${token.colorBorderSecondary};
    text-align: center;
  `,
}));

export default useStyles;
