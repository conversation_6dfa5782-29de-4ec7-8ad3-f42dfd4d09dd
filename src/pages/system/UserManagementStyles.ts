import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => ({
  container: {
    padding: '24px',
    minHeight: 'calc(100vh - 112px)',
  },

  header: {
    marginBottom: '24px',
    paddingBottom: '16px',
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
  },

  toolbar: {
    marginBottom: '16px',
    padding: '16px',
    backgroundColor: token.colorFillAlter,
    borderRadius: token.borderRadius,
  },

  searchContainer: {
    display: 'flex',
    gap: '12px',
    alignItems: 'center',
    flexWrap: 'wrap',
  },

  actionButtons: {
    display: 'flex',
    gap: '8px',
    alignItems: 'center',
  },

  tableContainer: {
    '& .ant-table-thead > tr > th': {
      backgroundColor: token.colorFillAlter,
      fontWeight: 600,
    },

    '& .ant-table-tbody > tr:hover > td': {
      backgroundColor: token.colorFillContent,
    },
  },

  statusTag: {
    fontWeight: 500,
  },

  actionButton: {
    padding: '4px 8px',
    height: 'auto',
    fontSize: '12px',
  },

  modalForm: {
    '& .ant-form-item-label > label': {
      fontWeight: 500,
    },
  },

  userCode: {
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
    fontSize: '13px',
    backgroundColor: token.colorFillTertiary,
    padding: '2px 6px',
    borderRadius: '4px',
    border: `1px solid ${token.colorBorder}`,
  },
}));

export default useStyles;
