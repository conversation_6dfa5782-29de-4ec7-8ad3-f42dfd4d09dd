import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Form, Input, Button, Switch, Row, Col, message, Space, Spin } from 'antd';
import { SaveOutlined, ArrowLeftOutlined, EditOutlined } from '@ant-design/icons';
import { getUserDetail, createUser, updateUser } from '@/api/UserApi';
import type { UserProps, UserFormData } from '@/types/user';
import PermissionEditor from '@/components/PermissionEditor';
import { getErrorMessage, handleApiResponse, logError } from '@/utils/errorHandler';
import useStyles from './UserFormStyles';

const UserForm: React.FC = () => {
  const { styles } = useStyles();
  const { code } = useParams<{ code: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm<UserFormData>();

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [, setUser] = useState<UserProps | null>(null);
  const [mode, setMode] = useState<'add' | 'edit' | 'view'>('add');

  // 根据路径确定模式
  useEffect(() => {
    const path = window.location.pathname;
    if (path.includes('/add')) {
      setMode('add');
    } else if (path.includes('/edit')) {
      setMode('edit');
    } else if (path.includes('/view')) {
      setMode('view');
    }
  }, []);

  // 获取用户详情
  const fetchUserDetail = async (userCode: string) => {
    setLoading(true);
    try {
      const response = await getUserDetail(userCode);
      if (response.code === 200) {
        const userData = response.data;
        setUser(userData);
        form.setFieldsValue({
          code: userData.code,
          nickname: userData.nickname,
          bankAccountName: userData.bankAccountName || '',
          bankAccountNumber: userData.bankAccountNumber || '',
          isCompanyAdmin: userData.isCompanyAdmin,
          companyCode: userData.companyCode,
          isActive: userData.isActive,
          routePermissions: userData.routePermissions || {},
        });
      } else {
        message.error(response.message || '获取用户详情失败');
        navigate('/system/users');
      }
    } catch (error: any) {
      logError('获取用户详情', error);
      message.error(getErrorMessage(error, '获取用户详情失败'));
      navigate('/system/users');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    if (code && (mode === 'edit' || mode === 'view')) {
      fetchUserDetail(code);
    } else if (mode === 'add') {
      form.setFieldsValue({
        isCompanyAdmin: false,
        isActive: true,
        routePermissions: {},
      });
    }
  }, [code, mode]);

  // 保存用户
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);

      // 清理空字符串为undefined
      const cleanedValues = {
        ...values,
        bankAccountName: values.bankAccountName?.trim() || undefined,
        bankAccountNumber: values.bankAccountNumber?.trim() || undefined,
      };

      if (mode === 'add') {
        if (!cleanedValues.password) {
          message.error('新增用户时密码不能为空');
          return;
        }
        const response = await createUser(cleanedValues as Required<UserFormData>);
        const result = handleApiResponse(response, '创建成功', '创建失败');

        if (result.success) {
          message.success(result.message);
          navigate('/system/users');
        } else {
          message.error(result.message);
        }
      } else if (mode === 'edit') {
        const { code: userCode, ...updateData } = cleanedValues;
        const response = await updateUser(code!, updateData);
        const result = handleApiResponse(response, '更新成功', '更新失败');

        if (result.success) {
          message.success(result.message);
          navigate('/system/users');
        } else {
          message.error(result.message);
        }
      }
    } catch (error: any) {
      logError(mode === 'add' ? '创建用户' : '更新用户', error);
      message.error(getErrorMessage(error, mode === 'add' ? '创建失败' : '更新失败'));
    } finally {
      setSaving(false);
    }
  };

  // 切换到编辑模式
  const handleEdit = () => {
    navigate(`/system/users/edit/${code}`);
  };

  // 返回列表
  const handleBack = () => {
    navigate('/system/users');
  };

  const isReadOnly = mode === 'view';

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.header}>
          <Space>
            {mode === 'view' && (
              <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
                编辑
              </Button>
            )}
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              返回
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          <Form form={form} layout="vertical" className={styles.form} disabled={isReadOnly}>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="code"
                  label="用户编码"
                  rules={[
                    { required: true, message: '请输入用户编码' },
                    { min: 4, message: '用户编码至少4个字符' },
                    { max: 8, message: '用户编码至多8个字符' },
                    { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和横线' },
                  ]}
                >
                  <Input placeholder="请输入用户编码" disabled={mode === 'edit'} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="nickname"
                  label="用户昵称"
                  rules={[{ required: true, message: '请输入用户昵称' }]}
                >
                  <Input placeholder="请输入用户昵称" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="bankAccountName"
                  label="银行账户姓名"
                  help="可选字段，用于财务相关功能"
                >
                  <Input placeholder="请输入银行账户姓名" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="bankAccountNumber"
                  label="银行账户号码"
                  help="可选字段，用于财务相关功能"
                >
                  <Input placeholder="请输入银行账户号码" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              {mode === 'add' && (
                <Col span={12}>
                  <Form.Item
                    name="password"
                    label="密码"
                    rules={[
                      { required: true, message: '请输入密码' },
                      { min: 6, message: '密码至少6个字符' },
                    ]}
                  >
                    <Input.Password placeholder="请输入密码" />
                  </Form.Item>
                </Col>
              )}
              {mode === 'edit' && (
                <Col span={12}>
                  <Form.Item name="password" label="新密码" help="留空则不修改密码">
                    <Input.Password placeholder="请输入新密码（可选）" />
                  </Form.Item>
                </Col>
              )}
              <Col span={6}>
                <Form.Item name="isActive" label="账户状态" valuePropName="checked">
                  <Switch checkedChildren="激活" unCheckedChildren="禁用" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item name="routePermissions" label="路由权限">
                  <PermissionEditor />
                </Form.Item>
              </Col>
            </Row>

            {!isReadOnly && (
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSave}
                    loading={saving}
                  >
                    保存
                  </Button>
                  <Button onClick={handleBack}>取消</Button>
                </Space>
              </Form.Item>
            )}
          </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default UserForm;
