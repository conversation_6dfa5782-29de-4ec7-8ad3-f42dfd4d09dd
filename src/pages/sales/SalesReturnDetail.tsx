import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  message,
  DatePicker,
  Select,
  InputNumber,
  Table,
  Row,
  Col,
  Divider,
  Modal,
  Tag,
  Descriptions,
  Radio,
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined,
  CheckOutlined,
  SendOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  getSalesReturnDetail,
  createSalesReturn,
  updateSalesReturn,
  submitSalesReturn,
  approveSalesReturn,
  processSalesReturn,
  completeSalesReturn,
  cancelSalesReturn,
} from '@/api/SalesReturnApi';
import type {
  SalesReturn,
  SalesReturnDetail,
  CreateSalesReturnParams,
  UpdateSalesReturnParams,
  ReturnStatus,
  ReturnType,
} from '@/types/salesReturn';
import {
  RETURN_STATUS_CONFIG,
  RETURN_TYPE_CONFIG,
  RETURN_REASON_CONFIG,
} from '@/types/salesReturn';
import {
  CustomerSearchSelector,
  UserSearchSelector,
  ProductCodeInputEnhanced,
  ProductColorSelectorEnhanced,
  ProductSizeSelectorEnhanced,
} from '@/components';
import type { ProductOrderInfo } from '@/types/product';
import { handleApiResponse, logError, getErrorMessage } from '@/utils/errorHandler';
import useAuthStore from '@/store/authStore';

const { Option } = Select;
const { TextArea } = Input;

interface ReturnDetailFormData
  extends Omit<SalesReturnDetail, 'totalAmount' | 'actualAmount' | 'exchangeTotalAmount'> {
  product?: ProductOrderInfo | null;
  exchangeProduct?: ProductOrderInfo | null;
}

const SalesReturnDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user, hasRoutePermission } = useAuthStore();
  const [form] = Form.useForm();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [returnOrder, setReturnOrder] = useState<SalesReturn | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [isView, setIsView] = useState(false);

  // 退货明细相关状态
  const [details, setDetails] = useState<ReturnDetailFormData[]>([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingDetail, setEditingDetail] = useState<ReturnDetailFormData | null>(null);
  const [editingIndex, setEditingIndex] = useState<number>(-1);

  // 原订单选择
  const [, setOrderSelectorVisible] = useState(false);

  // 判断页面模式
  useEffect(() => {
    const path = window.location.pathname;
    if (path.includes('/add')) {
      setIsEdit(false);
      setIsView(false);
    } else if (path.includes('/edit/')) {
      setIsEdit(true);
      setIsView(false);
    } else if (path.includes('/view/')) {
      setIsEdit(false);
      setIsView(true);
    }
  }, []);

  // 获取退单详情
  const fetchReturnDetail = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await getSalesReturnDetail(id);
      const result = handleApiResponse(response, '', '获取退单详情失败');

      if (result.success && response.data) {
        const returnData = response.data;
        setReturnOrder(returnData);
        setDetails(
          returnData.details.map((detail) => ({ ...detail, product: null, exchangeProduct: null })),
        );

        // 填充表单
        form.setFieldsValue({
          ...returnData,
          returnDate: returnData.returnDate ? dayjs(returnData.returnDate) : null,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取退单详情', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (id) {
      fetchReturnDetail();
    } else {
      // 新增模式，设置默认值
      form.setFieldsValue({
        returnDate: dayjs(),
        applicantCode: user?.code,
        returnType: 'return',
        reason: 'quality_issue',
      });
    }
  }, [id, user]);

  // 返回列表
  const handleBack = () => {
    navigate('/sales/returns');
  };

  // 保存退单
  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      if (details.length === 0) {
        message.error('请至少添加一个退货明细');
        return;
      }

      setSaving(true);

      // 构建退单数据
      const returnData: CreateSalesReturnParams | UpdateSalesReturnParams = {
        ...values,
        returnDate: values.returnDate?.format('YYYY-MM-DD'),
        details: details.map((detail) => ({
          originalDetailId: detail.originalDetailId,
          productCode: detail.productCode,
          colorCode: detail.colorCode,
          sizeCode: detail.sizeCode,
          skuCode: `${detail.productCode}-${detail.colorCode}-${detail.sizeCode}`,
          quantity: detail.quantity,
          unitPrice: detail.unitPrice,
          discountRate: detail.discountRate || 0,
          discountAmount: detail.discountAmount || 0,
          // 换货信息
          exchangeProductCode: detail.exchangeProductCode,
          exchangeColorCode: detail.exchangeColorCode,
          exchangeSizeCode: detail.exchangeSizeCode,
          exchangeSkuCode:
            detail.exchangeProductCode && detail.exchangeColorCode && detail.exchangeSizeCode
              ? `${detail.exchangeProductCode}-${detail.exchangeColorCode}-${detail.exchangeSizeCode}`
              : undefined,
          exchangeQuantity: detail.exchangeQuantity || 0,
          exchangeUnitPrice: detail.exchangeUnitPrice || 0,
          remark: detail.remark,
        })),
      };

      let response;
      if (id && isEdit) {
        response = await updateSalesReturn(id, { ...returnData, id } as UpdateSalesReturnParams);
      } else {
        response = await createSalesReturn(returnData);
      }

      const result = handleApiResponse(response, '保存成功', '保存失败');

      if (result.success) {
        message.success(result.message);
        navigate('/sales/returns');
      } else {
        message.error(result.message);
      }
    } catch (error) {
      if (error instanceof Error && error.message !== 'Validation failed') {
        logError('保存退单', error);
        message.error(getErrorMessage(error));
      }
    } finally {
      setSaving(false);
    }
  };

  // 提交退单
  const handleSubmit = async () => {
    if (!id) return;

    Modal.confirm({
      title: '提交退单',
      content: '确定要提交这个退单吗？提交后将无法修改退单信息。',
      onOk: async () => {
        try {
          const response = await submitSalesReturn(id, {});
          const result = handleApiResponse(response, '提交成功', '提交失败');

          if (result.success) {
            message.success(result.message);
            fetchReturnDetail();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('提交退单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 审核退单
  const handleApprove = async (approved: boolean) => {
    if (!id) return;

    Modal.confirm({
      title: approved ? '审核通过' : '审核拒绝',
      content: `确定要${approved ? '通过' : '拒绝'}这个退单吗？`,
      onOk: async () => {
        try {
          const response = await approveSalesReturn(id, { approved });
          const result = handleApiResponse(response, '审核成功', '审核失败');

          if (result.success) {
            message.success(result.message);
            fetchReturnDetail();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('审核退单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 处理退单
  const handleProcess = async () => {
    if (!id) return;

    Modal.confirm({
      title: '处理退单',
      content: '确定要开始处理这个退单吗？',
      onOk: async () => {
        try {
          const response = await processSalesReturn(id, {
            processedDate: dayjs().format('YYYY-MM-DD'),
          });
          const result = handleApiResponse(response, '处理成功', '处理失败');

          if (result.success) {
            message.success(result.message);
            fetchReturnDetail();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('处理退单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 完成退单
  const handleComplete = async () => {
    if (!id) return;

    Modal.confirm({
      title: '完成退单',
      content: '确定要完成这个退单吗？',
      onOk: async () => {
        try {
          const response = await completeSalesReturn(id, {
            completedDate: dayjs().format('YYYY-MM-DD'),
          });
          const result = handleApiResponse(response, '完成成功', '完成失败');

          if (result.success) {
            message.success(result.message);
            fetchReturnDetail();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('完成退单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 取消退单
  const handleCancel = async () => {
    if (!id) return;

    Modal.confirm({
      title: '取消退单',
      content: '确定要取消这个退单吗？取消后无法恢复。',
      onOk: async () => {
        try {
          const response = await cancelSalesReturn(id);
          const result = handleApiResponse(response, '取消成功', '取消失败');

          if (result.success) {
            message.success(result.message);
            fetchReturnDetail();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('取消退单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 添加退货明细
  const handleAddDetail = () => {
    setEditingDetail({
      productCode: '',
      colorCode: '',
      sizeCode: '',
      quantity: 1,
      unitPrice: 0,
      product: null,
      exchangeProduct: null,
    });
    setEditingIndex(-1);
    setDetailModalVisible(true);
  };

  // 编辑退货明细
  const handleEditDetail = (record: ReturnDetailFormData, index: number) => {
    setEditingDetail({ ...record });
    setEditingIndex(index);
    setDetailModalVisible(true);
  };

  // 删除退货明细
  const handleDeleteDetail = (index: number) => {
    const newDetails = [...details];
    newDetails.splice(index, 1);
    setDetails(newDetails);
  };

  // 计算总退货金额
  const calculateTotalReturnAmount = () => {
    return details.reduce((total, detail) => {
      const itemTotal = detail.quantity * detail.unitPrice - (detail.discountAmount || 0);
      return total + itemTotal;
    }, 0);
  };

  // 计算总换货金额
  const calculateTotalExchangeAmount = () => {
    return details.reduce((total, detail) => {
      if (detail.exchangeQuantity && detail.exchangeUnitPrice) {
        return total + detail.exchangeQuantity * detail.exchangeUnitPrice;
      }
      return total;
    }, 0);
  };

  // 计算总数量
  const calculateTotalQuantity = () => {
    return details.reduce((total, detail) => total + detail.quantity, 0);
  };

  // 获取状态配置
  const getStatusConfig = (status?: ReturnStatus) => {
    if (!status) return { text: '草稿', color: 'gray' };
    return RETURN_STATUS_CONFIG[status];
  };

  const statusConfig = getStatusConfig(returnOrder?.status);
  const canEdit = !isView && (!returnOrder || returnOrder.status === 'draft');
  const canSubmit = returnOrder && returnOrder.status === 'draft';
  const canApprove = returnOrder && returnOrder.status === 'submitted';
  const canProcess = returnOrder && returnOrder.status === 'approved';
  const canComplete = returnOrder && returnOrder.status === 'processing';
  const canCancel =
    returnOrder && (returnOrder.status === 'draft' || returnOrder.status === 'submitted');

  return (
    <div style={{ padding: '16px' }}>
      <Card>
        {/* 页面头部 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '24px',
          }}
        >
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              返回列表
            </Button>
            <h2 style={{ margin: 0 }}>
              {!id ? '新增销售退单' : isEdit ? '编辑销售退单' : '查看销售退单'}
            </h2>
            {returnOrder && <Tag color={statusConfig.color}>{statusConfig.text}</Tag>}
          </Space>
          <Space>
            {canEdit && (
              <Button type="primary" icon={<SaveOutlined />} onClick={handleSave} loading={saving}>
                保存
              </Button>
            )}
            {canSubmit && hasRoutePermission('sales/returns', 'update') && (
              <Button type="primary" icon={<SendOutlined />} onClick={handleSubmit}>
                提交审核
              </Button>
            )}
            {canApprove && hasRoutePermission('sales/returns', 'update') && (
              <>
                <Button type="primary" icon={<CheckOutlined />} onClick={() => handleApprove(true)}>
                  审核通过
                </Button>
                <Button danger onClick={() => handleApprove(false)}>
                  审核拒绝
                </Button>
              </>
            )}
            {canProcess && hasRoutePermission('sales/returns', 'update') && (
              <Button type="primary" icon={<CheckOutlined />} onClick={handleProcess}>
                开始处理
              </Button>
            )}
            {canComplete && hasRoutePermission('sales/returns', 'update') && (
              <Button type="primary" icon={<CheckOutlined />} onClick={handleComplete}>
                完成退单
              </Button>
            )}
            {canCancel && hasRoutePermission('sales/returns', 'delete') && (
              <Button danger icon={<CloseOutlined />} onClick={handleCancel}>
                取消退单
              </Button>
            )}
          </Space>
        </div>

        {/* 退单信息 */}
        {returnOrder && isView && (
          <>
            <Descriptions
              title="退单信息"
              bordered
              column={{ xs: 1, sm: 2, md: 3 }}
              style={{ marginBottom: '24px' }}
            >
              <Descriptions.Item label="退单编号">{returnOrder.returnNumber}</Descriptions.Item>
              <Descriptions.Item label="原订单号">
                {returnOrder.originalOrderNumber || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="客户">{returnOrder.customerName}</Descriptions.Item>
              <Descriptions.Item label="申请人">{returnOrder.applicantName}</Descriptions.Item>
              <Descriptions.Item label="退单日期">
                {dayjs(returnOrder.returnDate).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="退单类型">
                <Tag color="blue">{RETURN_TYPE_CONFIG[returnOrder.returnType]}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="退单原因">
                {RETURN_REASON_CONFIG[returnOrder.reason]}
              </Descriptions.Item>
              <Descriptions.Item label="退货金额">
                ¥{returnOrder.totalReturnAmount?.toFixed(2) || '0.00'}
              </Descriptions.Item>
              <Descriptions.Item label="换货金额">
                {returnOrder.totalExchangeAmount && returnOrder.totalExchangeAmount > 0
                  ? `¥${returnOrder.totalExchangeAmount.toFixed(2)}`
                  : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="总数量">{returnOrder.totalQuantity || 0}</Descriptions.Item>
              <Descriptions.Item label="手续费">
                {returnOrder.handlingFee ? `¥${returnOrder.handlingFee.toFixed(2)}` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="物流公司">
                {returnOrder.returnShippingCompany || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="物流单号">
                {returnOrder.returnTrackingNumber}
              </Descriptions.Item>
              {returnOrder.reasonDetail && (
                <Descriptions.Item label="原因详细说明" span={3}>
                  {returnOrder.reasonDetail}
                </Descriptions.Item>
              )}
              {returnOrder.remark && (
                <Descriptions.Item label="备注" span={3}>
                  {returnOrder.remark}
                </Descriptions.Item>
              )}
            </Descriptions>
            <Divider />
          </>
        )}

        {/* 表单 */}
        {canEdit && (
          <Form form={form} layout="vertical" disabled={loading}>
            <Row gutter={16}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="原销售订单" name="originalOrderId">
                  <Input.Search
                    placeholder="点击选择原订单"
                    readOnly
                    onSearch={() => setOrderSelectorVisible(true)}
                    enterButton="选择订单"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  label="客户"
                  name="customerCode"
                  rules={[{ required: true, message: '请选择客户' }]}
                >
                  <CustomerSearchSelector
                    placeholder="请选择客户"
                    onChange={(_, customer) => {
                      // 当选择客户时，如果客户有负责人，自动设置为申请人
                      if (customer && customer.managerCode) {
                        form.setFieldsValue({
                          applicantCode: customer.managerCode,
                        });
                      }
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  label="申请人"
                  name="applicantCode"
                  rules={[{ required: true, message: '请选择申请人' }]}
                >
                  <UserSearchSelector placeholder="请选择申请人" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  label="退单日期"
                  name="returnDate"
                  rules={[{ required: true, message: '请选择退单日期' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  label="退单类型"
                  name="returnType"
                  rules={[{ required: true, message: '请选择退单类型' }]}
                >
                  <Radio.Group>
                    {Object.entries(RETURN_TYPE_CONFIG).map(([key, text]) => (
                      <Radio key={key} value={key}>
                        {text}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  label="退单原因"
                  name="reason"
                  rules={[{ required: true, message: '请选择退单原因' }]}
                >
                  <Select placeholder="请选择退单原因">
                    {Object.entries(RETURN_REASON_CONFIG).map(([key, text]) => (
                      <Option key={key} value={key}>
                        {text}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="手续费" name="handlingFee">
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="请输入手续费"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="退货物流公司" name="returnShippingCompany">
                  <Input placeholder="请输入物流公司" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  label="退货物流单号"
                  name="returnTrackingNumber"
                  rules={[{ required: true, message: '请输入物流单号' }]}
                >
                  <Input placeholder="请输入物流单号" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24}>
                <Form.Item label="原因详细说明" name="reasonDetail">
                  <TextArea rows={3} placeholder="请详细说明退单原因" />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item label="备注" name="remark">
                  <TextArea rows={2} placeholder="请输入备注信息" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        )}

        {/* 退货明细 */}
        <Card title="退货明细" style={{ marginTop: '16px' }}>
          <div style={{ marginBottom: '16px' }}>
            <Space>
              {canEdit && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddDetail}>
                  添加商品
                </Button>
              )}
              <span style={{ color: '#666' }}>
                总数量: {calculateTotalQuantity()} | 退货金额: ¥
                {calculateTotalReturnAmount().toFixed(2)} | 换货金额: ¥
                {calculateTotalExchangeAmount().toFixed(2)}
              </span>
            </Space>
          </div>

          <Table
            columns={getDetailColumns()}
            dataSource={details}
            rowKey={(record, index) =>
              `${record.productCode}-${record.colorCode}-${record.sizeCode}-${index}`
            }
            pagination={false}
            scroll={{ x: 1400 }}
            size="small"
          />
        </Card>

        {/* 退货明细弹窗 */}
        <ReturnDetailModal
          visible={detailModalVisible}
          detail={editingDetail}
          returnType={form.getFieldValue('returnType')}
          onOk={handleDetailModalOk}
          onCancel={() => setDetailModalVisible(false)}
        />
      </Card>
    </div>
  );

  // 获取明细表格列配置
  function getDetailColumns(): ColumnsType<ReturnDetailFormData> {
    const columns: ColumnsType<ReturnDetailFormData> = [
      {
        title: '商品编码',
        dataIndex: 'productCode',
        key: 'productCode',
        width: 120,
      },
      {
        title: '商品名称',
        dataIndex: 'productName',
        key: 'productName',
        width: 150,
        render: (_, record) => record.product?.name || '-',
      },
      {
        title: '颜色',
        dataIndex: 'colorCode',
        key: 'colorCode',
        width: 80,
        render: (colorCode, record) => {
          const color = record.product?.colorCombinations?.find(
            (c: any) => c.colorCode === colorCode,
          );
          return color?.colorName || colorCode;
        },
      },
      {
        title: '尺寸',
        dataIndex: 'sizeCode',
        key: 'sizeCode',
        width: 80,
      },
      {
        title: 'SKU',
        key: 'skuCode',
        width: 150,
        render: (_, record) => `${record.productCode}-${record.colorCode}-${record.sizeCode}`,
      },
      {
        title: '退货数量',
        dataIndex: 'quantity',
        key: 'quantity',
        width: 80,
        align: 'right',
      },
      {
        title: '单价',
        dataIndex: 'unitPrice',
        key: 'unitPrice',
        width: 100,
        align: 'right',
        render: (price: number) => `¥${price.toFixed(2)}`,
      },
      {
        title: '折扣金额',
        dataIndex: 'discountAmount',
        key: 'discountAmount',
        width: 100,
        align: 'right',
        render: (amount?: number) => (amount ? `¥${amount.toFixed(2)}` : '-'),
      },
      {
        title: '退货金额',
        key: 'returnAmount',
        width: 100,
        align: 'right',
        render: (_, record) => {
          const total = record.quantity * record.unitPrice - (record.discountAmount || 0);
          return `¥${total.toFixed(2)}`;
        },
      },
    ];

    // 如果是换货类型，添加换货相关列
    const returnType = form.getFieldValue('returnType');
    if (returnType === 'exchange' || returnType === 'return_exchange') {
      columns.push(
        {
          title: '换货商品',
          dataIndex: 'exchangeProductCode',
          key: 'exchangeProductCode',
          width: 120,
          render: (code?: string) => code || '-',
        },
        {
          title: '换货颜色',
          dataIndex: 'exchangeColorCode',
          key: 'exchangeColorCode',
          width: 80,
          render: (colorCode?: string, record) => {
            if (!colorCode) return '-';
            const color = record.exchangeProduct?.colorCombinations?.find(
              (c) => c.colorCode === colorCode,
            );
            return color?.colorName || colorCode;
          },
        },
        {
          title: '换货尺寸',
          dataIndex: 'exchangeSizeCode',
          key: 'exchangeSizeCode',
          width: 80,
          render: (code?: string) => code || '-',
        },
        {
          title: '换货数量',
          dataIndex: 'exchangeQuantity',
          key: 'exchangeQuantity',
          width: 80,
          align: 'right',
          render: (quantity?: number) => quantity || '-',
        },
        {
          title: '换货单价',
          dataIndex: 'exchangeUnitPrice',
          key: 'exchangeUnitPrice',
          width: 100,
          align: 'right',
          render: (price?: number) => (price ? `¥${price.toFixed(2)}` : '-'),
        },
        {
          title: '换货金额',
          key: 'exchangeAmount',
          width: 100,
          align: 'right',
          render: (_, record) => {
            if (record.exchangeQuantity && record.exchangeUnitPrice) {
              return `¥${(record.exchangeQuantity * record.exchangeUnitPrice).toFixed(2)}`;
            }
            return '-';
          },
        },
      );
    }

    columns.push({
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 120,
      ellipsis: true,
    });

    if (canEdit) {
      columns.push({
        title: '操作',
        key: 'action',
        width: 100,
        fixed: 'right',
        render: (_, record, index) => (
          <Space size="small">
            <Button type="text" size="small" onClick={() => handleEditDetail(record, index)}>
              编辑
            </Button>
            <Button type="text" size="small" danger onClick={() => handleDeleteDetail(index)}>
              删除
            </Button>
          </Space>
        ),
      });
    }

    return columns;
  }

  // 处理明细弹窗确认
  function handleDetailModalOk(detail: ReturnDetailFormData) {
    const newDetails = [...details];
    if (editingIndex >= 0) {
      newDetails[editingIndex] = detail;
    } else {
      newDetails.push(detail);
    }
    setDetails(newDetails);
    setDetailModalVisible(false);
  }
};

// 退货明细弹窗组件
interface ReturnDetailModalProps {
  visible: boolean;
  detail: ReturnDetailFormData | null;
  returnType?: ReturnType;
  onOk: (detail: ReturnDetailFormData) => void;
  onCancel: () => void;
}

const ReturnDetailModal: React.FC<ReturnDetailModalProps> = ({
  visible,
  detail,
  returnType,
  onOk,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [selectedProduct, setSelectedProduct] = useState<ProductOrderInfo | null>(null);
  const [selectedExchangeProduct, setSelectedExchangeProduct] = useState<ProductOrderInfo | null>(
    null,
  );

  const showExchangeFields = returnType === 'exchange' || returnType === 'return_exchange';

  useEffect(() => {
    if (visible && detail) {
      form.setFieldsValue(detail);
      setSelectedProduct(detail.product || null);
      setSelectedExchangeProduct(detail.exchangeProduct || null);
    } else if (visible) {
      form.resetFields();
      setSelectedProduct(null);
      setSelectedExchangeProduct(null);
    }
  }, [visible, detail, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const detailData: ReturnDetailFormData = {
        ...values,
        product: selectedProduct,
        exchangeProduct: selectedExchangeProduct,
        productName: selectedProduct?.name,
        exchangeProductName: selectedExchangeProduct?.name,
      };
      onOk(detailData);
    } catch (error) {
      // 表单验证失败
    }
  };

  const handleProductSelect = (product: ProductOrderInfo | null) => {
    setSelectedProduct(product);
    if (product) {
      form.setFieldsValue({
        unitPrice: product.retailPrice,
        colorCode: undefined,
        sizeCode: undefined,
      });
    }
  };

  const handleExchangeProductSelect = (product: ProductOrderInfo | null) => {
    setSelectedExchangeProduct(product);
    if (product) {
      form.setFieldsValue({
        exchangeUnitPrice: product.retailPrice,
        exchangeColorCode: undefined,
        exchangeSizeCode: undefined,
      });
    }
  };

  return (
    <Modal
      title={detail ? '编辑退货明细' : '添加退货明细'}
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      width={1000}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          quantity: 1,
          unitPrice: 0,
          discountAmount: 0,
          exchangeQuantity: 0,
          exchangeUnitPrice: 0,
        }}
      >
        <Divider orientation="left">退货信息</Divider>
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="商品编码"
              name="productCode"
              rules={[{ required: true, message: '请输入商品编码' }]}
            >
              <ProductCodeInputEnhanced
                placeholder="请输入商品编码"
                onProductSelect={handleProductSelect}
                showStockInfo={true}
                showPriceInfo={true}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="颜色"
              name="colorCode"
              rules={[{ required: true, message: '请选择颜色' }]}
            >
              <ProductColorSelectorEnhanced
                product={selectedProduct}
                onChange={() => form.setFieldsValue({ sizeCode: undefined })}
                showStockInfo={true}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.colorCode !== currentValues.colorCode
              }
            >
              {({ getFieldValue }) => (
                <Form.Item
                  label="尺寸"
                  name="sizeCode"
                  rules={[{ required: true, message: '请选择尺寸' }]}
                >
                  <ProductSizeSelectorEnhanced
                    product={selectedProduct}
                    selectedColorCode={getFieldValue('colorCode')}
                    showStockInfo={true}
                  />
                </Form.Item>
              )}
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="退货数量"
              name="quantity"
              rules={[
                { required: true, message: '请输入数量' },
                { type: 'number', min: 1, message: '数量必须大于0' },
              ]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item
              label="单价"
              name="unitPrice"
              rules={[
                { required: true, message: '请输入单价' },
                { type: 'number', min: 0, message: '单价不能小于0' },
              ]}
            >
              <InputNumber min={0} precision={2} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item label="折扣金额" name="discountAmount">
              <InputNumber min={0} precision={2} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        {showExchangeFields && (
          <>
            <Divider orientation="left">换货信息</Divider>
            <Row gutter={16}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="换货商品编码" name="exchangeProductCode">
                  <ProductCodeInputEnhanced
                    placeholder="请输入换货商品编码"
                    onProductSelect={handleExchangeProductSelect}
                    showStockInfo={true}
                    showPriceInfo={true}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="换货颜色" name="exchangeColorCode">
                  <ProductColorSelectorEnhanced
                    product={selectedExchangeProduct}
                    onChange={() => form.setFieldsValue({ exchangeSizeCode: undefined })}
                    showStockInfo={true}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.exchangeColorCode !== currentValues.exchangeColorCode
                  }
                >
                  {({ getFieldValue }) => (
                    <Form.Item label="换货尺寸" name="exchangeSizeCode">
                      <ProductSizeSelectorEnhanced
                        product={selectedExchangeProduct}
                        selectedColorCode={getFieldValue('exchangeColorCode')}
                        showStockInfo={true}
                      />
                    </Form.Item>
                  )}
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="换货数量" name="exchangeQuantity">
                  <InputNumber min={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="换货单价" name="exchangeUnitPrice">
                  <InputNumber min={0} precision={2} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </>
        )}

        <Row gutter={16}>
          <Col xs={24}>
            <Form.Item label="备注" name="remark">
              <TextArea rows={2} placeholder="请输入备注" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default SalesReturnDetail;
