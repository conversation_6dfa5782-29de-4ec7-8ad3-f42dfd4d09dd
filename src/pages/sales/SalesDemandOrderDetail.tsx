import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Descriptions,
  Table,
  Button,
  Space,
  message,
  Statistic,
  Tabs,
  Tag,
  Timeline,
} from 'antd';
import {
  ArrowLeftOutlined,
  ReloadOutlined,
  EditOutlined,
  CheckOutlined,
  CloseOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  getSalesDemandOrderDetail,
  submitSalesDemandOrder,
  approveSalesDemandOrder,
  cancelSalesDemandOrder,
} from '@/api/SalesDemandOrderApi';
import {
  SalesDemandOrder,
  SalesDemandOrderDetail as OrderDetail,
  SalesDemandOrderStatus,
  SALES_DEMAND_ORDER_STATUS_COLORS,
  SALES_DEMAND_ORDER_STATUS_TEXT,
  PRIORITY_LEVEL_COLORS,
  PRIORITY_LEVEL_TEXT,
} from '@/types/salesDemand';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { getUser } from '@/utils/tokenManager';
import styles from './SalesDemandOrderDetail.module.css';

const { TabPane } = Tabs;

const SalesDemandOrderDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const currentUser = getUser();
  const [loading, setLoading] = useState(false);
  const [order, setOrder] = useState<SalesDemandOrder | null>(null);

  // 获取订单详情
  const fetchOrderDetail = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await getSalesDemandOrderDetail(id);
      const result = handleApiResponse(response, '', '获取订单详情失败');

      if (result.success && response.data) {
        setOrder(response.data);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取订单详情', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchOrderDetail();
  }, [id]);

  // 返回列表
  const handleBack = () => {
    navigate('/sales/demand-orders');
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchOrderDetail();
  };

  // 编辑订单
  const handleEdit = () => {
    navigate(`/sales/demand-orders/edit/${id}`);
  };

  // 提交审核
  const handleSubmit = async () => {
    if (!id) return;
    try {
      await submitSalesDemandOrder(id);
      message.success('提交成功');
      fetchOrderDetail();
    } catch (error) {
      message.error('提交失败');
    }
  };

  // 审核通过
  const handleApprove = async () => {
    if (!id) return;
    if (!currentUser?.code) {
      message.error('无法获取当前用户信息');
      return;
    }
    try {
      await approveSalesDemandOrder(id, { approvedByUserCode: currentUser.code });
      message.success('审核通过');
      fetchOrderDetail();
    } catch (error) {
      message.error('审核失败');
    }
  };

  // 取消订单
  const handleCancel = async () => {
    if (!id) return;
    try {
      await cancelSalesDemandOrder(id);
      message.success('取消成功');
      fetchOrderDetail();
    } catch (error) {
      message.error('取消失败');
    }
  };

  if (!order) {
    return (
      <div className={styles.container}>
        <Card loading={loading}>
          <div className={styles.emptyState}>{loading ? '加载中...' : '订单信息不存在'}</div>
        </Card>
      </div>
    );
  }

  // 订单明细表格列定义
  const detailColumns: ColumnsType<OrderDetail> = [
    {
      title: '商品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
    },
    {
      title: '商品名称',
      key: 'productName',
      width: 200,
      render: (_, record) => record.product?.name || '-',
    },
    {
      title: '颜色',
      key: 'color',
      width: 100,
      render: (_, record) => record.color?.name || record.colorCode,
    },
    {
      title: '尺寸',
      dataIndex: 'sizeCode',
      key: 'sizeCode',
      width: 80,
    },
    {
      title: 'SKU编码',
      dataIndex: 'skuCode',
      key: 'skuCode',
      width: 120,
    },
    {
      title: '需求数量',
      dataIndex: 'demandQuantity',
      key: 'demandQuantity',
      width: 100,
      align: 'right',
    },
    {
      title: '预估单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      align: 'right',
      render: (price: number) => (price ? `¥${price.toFixed(2)}` : '-'),
    },
    {
      title: '小计',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 100,
      align: 'right',
      render: (amount: number) => (amount ? `¥${amount.toFixed(2)}` : '-'),
    },
    {
      title: '当前库存',
      dataIndex: 'currentStock',
      key: 'currentStock',
      width: 100,
      align: 'right',
      render: (stock: number) => stock || 0,
    },
    {
      title: '缺货数量',
      dataIndex: 'shortageQuantity',
      key: 'shortageQuantity',
      width: 100,
      align: 'right',
      render: (shortage: number) => (
        <span style={{ color: shortage > 0 ? '#ff4d4f' : '#52c41a' }}>{shortage || 0}</span>
      ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 150,
      render: (remark: string) => remark || '-',
    },
  ];

  // 状态时间线数据
  const timelineItems = [
    {
      color: 'blue',
      children: (
        <div>
          <div>订单创建</div>
          <div className={styles.timelineTime}>
            {dayjs(order.createdAt).format('YYYY-MM-DD HH:mm:ss')}
          </div>
          {order.createdByUser && (
            <div className={styles.timelineUser}>创建人：{order.createdByUser.nickname}</div>
          )}
        </div>
      ),
    },
  ];

  if (order.status !== SalesDemandOrderStatus.DRAFT) {
    timelineItems.push({
      color: 'green',
      children: (
        <div>
          <div>提交审核</div>
          <div className={styles.timelineTime}>{/* 这里需要后端提供提交时间 */}</div>
        </div>
      ),
    });
  }

  if (order.approvedAt) {
    timelineItems.push({
      color: 'green',
      children: (
        <div>
          <div>审核通过</div>
          <div className={styles.timelineTime}>
            {dayjs(order.approvedAt).format('YYYY-MM-DD HH:mm:ss')}
          </div>
          {order.approvedByUser && (
            <div className={styles.timelineUser}>审核人：{order.approvedByUser.nickname}</div>
          )}
        </div>
      ),
    });
  }

  if (order.mergedAt) {
    timelineItems.push({
      color: 'orange',
      children: (
        <div>
          <div>合并到采购订单</div>
          <div className={styles.timelineTime}>
            {dayjs(order.mergedAt).format('YYYY-MM-DD HH:mm:ss')}
          </div>
          {order.mergedToPurchaseOrderId && (
            <div className={styles.timelineUser}>采购订单ID：{order.mergedToPurchaseOrderId}</div>
          )}
        </div>
      ),
    });
  }

  return (
    <div className={styles.container}>
      {/* 头部操作栏 */}
      <Card className={styles.headerCard}>
        <Row gutter={16} justify="space-between" align="middle">
          <Col>
            <Space size="middle">
              <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
                返回列表
              </Button>
              <div className={styles.titleSection}>
                <FileTextOutlined className={styles.titleIcon} />
                <h2 className={styles.title}>{order.orderNumber}</h2>
                <Tag color={SALES_DEMAND_ORDER_STATUS_COLORS[order.status]}>
                  {SALES_DEMAND_ORDER_STATUS_TEXT[order.status]}
                </Tag>
                <Tag color={PRIORITY_LEVEL_COLORS[order.priorityLevel]}>
                  {PRIORITY_LEVEL_TEXT[order.priorityLevel]}
                </Tag>
              </div>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
                刷新
              </Button>
              {order.status === SalesDemandOrderStatus.DRAFT && (
                <>
                  <Button icon={<EditOutlined />} onClick={handleEdit}>
                    编辑
                  </Button>
                  <Button type="primary" icon={<CheckOutlined />} onClick={handleSubmit}>
                    提交审核
                  </Button>
                </>
              )}
              {order.status === SalesDemandOrderStatus.SUBMITTED && (
                <Button type="primary" icon={<CheckOutlined />} onClick={handleApprove}>
                  审核通过
                </Button>
              )}
              {[
                SalesDemandOrderStatus.DRAFT,
                SalesDemandOrderStatus.SUBMITTED,
                SalesDemandOrderStatus.APPROVED,
              ].includes(order.status) && (
                <Button danger icon={<CloseOutlined />} onClick={handleCancel}>
                  取消订单
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      <Tabs defaultActiveKey="basic" className={styles.tabs}>
        <TabPane tab="基本信息" key="basic">
          <Row gutter={[16, 16]}>
            {/* 订单信息 */}
            <Col xs={24} lg={16}>
              <Card title="订单信息" className={styles.infoCard}>
                <Descriptions column={{ xs: 1, sm: 2 }} bordered>
                  <Descriptions.Item label="订单编号">{order.orderNumber}</Descriptions.Item>
                  <Descriptions.Item label="客户">{order.customer?.name || '-'}</Descriptions.Item>
                  <Descriptions.Item label="销售人员">
                    {order.salesPerson?.nickname || order.salesPersonCode}
                  </Descriptions.Item>
                  <Descriptions.Item label="需求日期">
                    {dayjs(order.demandDate).format('YYYY-MM-DD')}
                  </Descriptions.Item>
                  <Descriptions.Item label="期望交货日期">
                    {order.expectedDeliveryDate
                      ? dayjs(order.expectedDeliveryDate).format('YYYY-MM-DD')
                      : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="优先级">
                    <Tag color={PRIORITY_LEVEL_COLORS[order.priorityLevel]}>
                      {PRIORITY_LEVEL_TEXT[order.priorityLevel]}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={SALES_DEMAND_ORDER_STATUS_COLORS[order.status]}>
                      {SALES_DEMAND_ORDER_STATUS_TEXT[order.status]}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="创建时间">
                    {dayjs(order.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                  <Descriptions.Item label="备注" span={2}>
                    {order.remark || '-'}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>

            {/* 统计信息 */}
            <Col xs={24} lg={8}>
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Card className={styles.statisticCard}>
                    <Statistic
                      title="预估总金额"
                      value={order.estimatedTotalAmount}
                      precision={2}
                      prefix="¥"
                      valueStyle={{ color: '#f5222d', fontSize: '20px' }}
                    />
                  </Card>
                </Col>
                <Col span={24}>
                  <Card className={styles.statisticCard}>
                    <Statistic
                      title="总需求数量"
                      value={order.totalQuantity}
                      valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                    />
                  </Card>
                </Col>
                <Col span={24}>
                  <Card className={styles.statisticCard}>
                    <Statistic
                      title="商品种类"
                      value={order.details?.length || 0}
                      suffix="种"
                      valueStyle={{ color: '#52c41a', fontSize: '18px' }}
                    />
                  </Card>
                </Col>
              </Row>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="订单明细" key="details">
          <Card>
            <Table
              columns={detailColumns}
              dataSource={order.details || []}
              rowKey={(record, index) => index?.toString() || '0'}
              pagination={false}
              scroll={{ x: 1200 }}
              className={styles.table}
              summary={(pageData) => {
                const totalQuantity = pageData.reduce(
                  (sum, record) => sum + record.demandQuantity,
                  0,
                );
                const totalAmount = pageData.reduce(
                  (sum, record) => sum + (record.totalAmount || 0),
                  0,
                );
                const totalShortage = pageData.reduce(
                  (sum, record) => sum + (record.shortageQuantity || 0),
                  0,
                );

                return (
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={5}>
                      <strong>合计</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={1}>
                      <strong>{totalQuantity}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2}></Table.Summary.Cell>
                    <Table.Summary.Cell index={3}>
                      <strong>¥{totalAmount.toFixed(2)}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4}></Table.Summary.Cell>
                    <Table.Summary.Cell index={5}>
                      <strong style={{ color: totalShortage > 0 ? '#ff4d4f' : '#52c41a' }}>
                        {totalShortage}
                      </strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={6}></Table.Summary.Cell>
                  </Table.Summary.Row>
                );
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="状态流转" key="timeline">
          <Card>
            <Timeline items={timelineItems} />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SalesDemandOrderDetail;
