import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  message,
  Tag,
  Row,
  Col,
  Modal,
  Tooltip,
  Tabs,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  FileExcelOutlined,
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import dayjs, { Dayjs } from 'dayjs';
import {
  getSalesReturnList,
  deleteSalesReturn,
  exportSalesReturnsExcel,
} from '@/api/SalesReturnApi';
import type {
  SalesReturnListItem,
  GetSalesReturnListParams,
  ReturnStatus,
  ReturnType,
  ReturnReason,
} from '@/types/salesReturn';
import {
  RETURN_STATUS_CONFIG,
  RETURN_TYPE_CONFIG,
  RETURN_REASON_CONFIG,
} from '@/types/salesReturn';
import { CustomerSearchSelector, UserSearchSelector } from '@/components';
import { handleApiResponse, logError, getErrorMessage } from '@/utils/errorHandler';
import useAuthStore from '@/store/authStore';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;

const SalesReturnManagement: React.FC = () => {
  const navigate = useNavigate();
  const { hasRoutePermission } = useAuthStore();

  // 状态管理
  const [returns, setReturns] = useState<SalesReturnListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 搜索条件
  const [searchText, setSearchText] = useState('');
  const [customerCode, setCustomerCode] = useState<string | undefined>();
  const [applicantCode, setApplicantCode] = useState<string | undefined>();
  const [returnType, setReturnType] = useState<ReturnType | undefined>();
  const [status, setStatus] = useState<ReturnStatus | undefined>();
  const [reason, setReason] = useState<ReturnReason | undefined>();
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);

  // 分页配置
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  });

  // 获取退单列表
  const fetchReturns = async (
    page = 1,
    pageSize = 20,
    search = '',
    customer = '',
    applicant = '',
    type?: ReturnType,
    returnStatus?: ReturnStatus,
    returnReason?: ReturnReason,
    startDate = '',
    endDate = '',
  ) => {
    setLoading(true);
    try {
      const params: GetSalesReturnListParams = {
        page,
        pageSize,
        search: search.trim() || undefined,
        customerCode: customer || undefined,
        applicantCode: applicant || undefined,
        returnType: type,
        status: returnStatus,
        reason: returnReason,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const response = await getSalesReturnList(params);
      const result = handleApiResponse(response, '', '获取退单列表失败');

      if (result.success && response.data) {
        setReturns(response.data.returns);
        setPagination((prev) => ({
          ...prev,
          current: page,
          pageSize,
          total: response.data.total,
        }));
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取退单列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchReturns();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    const [startDate, endDate] = dateRange
      ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')]
      : ['', ''];
    fetchReturns(
      1,
      pagination.pageSize,
      searchText,
      customerCode,
      applicantCode,
      returnType,
      status,
      reason,
      startDate,
      endDate,
    );
  };

  // 重置搜索
  const handleReset = () => {
    setSearchText('');
    setCustomerCode(undefined);
    setApplicantCode(undefined);
    setReturnType(undefined);
    setStatus(undefined);
    setReason(undefined);
    setDateRange(null);
    fetchReturns(1, pagination.pageSize);
  };

  // 刷新数据
  const handleRefresh = () => {
    const [startDate, endDate] = dateRange
      ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')]
      : ['', ''];
    fetchReturns(
      pagination.current,
      pagination.pageSize,
      searchText,
      customerCode,
      applicantCode,
      returnType,
      status,
      reason,
      startDate,
      endDate,
    );
  };

  // 分页变化处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 20 } = paginationConfig;
    const [startDate, endDate] = dateRange
      ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')]
      : ['', ''];
    fetchReturns(
      current,
      pageSize,
      searchText,
      customerCode,
      applicantCode,
      returnType,
      status,
      reason,
      startDate,
      endDate,
    );
  };

  // 新增退单
  const handleAdd = () => {
    navigate('/sales/returns/add');
  };

  // 编辑退单
  const handleEdit = (record: SalesReturnListItem) => {
    navigate(`/sales/returns/edit/${record.id}`);
  };

  // 查看退单
  const handleView = (record: SalesReturnListItem) => {
    navigate(`/sales/returns/view/${record.id}`);
  };

  // 删除退单
  const handleDelete = (record: SalesReturnListItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除退单 ${record.returnNumber} 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await deleteSalesReturn(record.id);
          const result = handleApiResponse(response, '删除成功', '删除失败');

          if (result.success) {
            message.success(result.message);
            handleRefresh();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('删除退单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 导出Excel
  const handleExportExcel = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要导出的退单');
      return;
    }

    setExportLoading(true);
    try {
      const [startDate, endDate] = dateRange
        ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')]
        : ['', ''];

      const blob = await exportSalesReturnsExcel({
        customerCode,
        applicantCode,
        returnType,
        status,
        startDate,
        endDate,
        returnIds: selectedRowKeys as string[],
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `销售退单_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error) {
      logError('导出Excel', error);
      message.error(getErrorMessage(error));
    } finally {
      setExportLoading(false);
    }
  };

  // 表格列配置
  const columns: ColumnsType<SalesReturnListItem> = [
    {
      title: '退单编号',
      dataIndex: 'returnNumber',
      key: 'returnNumber',
      width: 150,
      fixed: 'left',
    },
    {
      title: '原订单号',
      dataIndex: 'originalOrderNumber',
      key: 'originalOrderNumber',
      width: 150,
      render: (orderNumber?: string) => orderNumber || '-',
    },
    {
      title: '客户',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 120,
    },
    {
      title: '申请人',
      dataIndex: 'applicantName',
      key: 'applicantName',
      width: 100,
    },
    {
      title: '退单日期',
      dataIndex: 'returnDate',
      key: 'returnDate',
      width: 100,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '退单类型',
      dataIndex: 'returnType',
      key: 'returnType',
      width: 100,
      render: (type: ReturnType) => <Tag color="blue">{RETURN_TYPE_CONFIG[type]}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: ReturnStatus) => {
        const config = RETURN_STATUS_CONFIG[status];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '退单原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 100,
      render: (reason: ReturnReason) => RETURN_REASON_CONFIG[reason],
    },
    {
      title: '退货金额',
      dataIndex: 'totalReturnAmount',
      key: 'totalReturnAmount',
      width: 100,
      align: 'right',
      render: (amount: number) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '换货金额',
      dataIndex: 'totalExchangeAmount',
      key: 'totalExchangeAmount',
      width: 100,
      align: 'right',
      render: (amount: number) => (amount > 0 ? `¥${amount.toFixed(2)}` : '-'),
    },
    {
      title: '总数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 80,
      align: 'right',
    },
    {
      title: '物流单号',
      dataIndex: 'returnTrackingNumber',
      key: 'returnTrackingNumber',
      width: 150,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          {hasRoutePermission('sales/returns', 'update') && record.status === 'draft' && (
            <Tooltip title="编辑">
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              />
            </Tooltip>
          )}
          {hasRoutePermission('sales/returns', 'delete') && record.status === 'draft' && (
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <div style={{ padding: '16px' }}>
      <Card>
        {/* 搜索区域 */}
        <div style={{ marginBottom: '16px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Search
                placeholder="搜索退单号"
                allowClear
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <CustomerSearchSelector
                value={customerCode}
                onChange={setCustomerCode}
                placeholder="选择客户"
                allowClear
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <UserSearchSelector
                value={applicantCode}
                onChange={setApplicantCode}
                placeholder="选择申请人"
                allowClear
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Select
                value={returnType}
                onChange={setReturnType}
                placeholder="选择退单类型"
                allowClear
                style={{ width: '100%' }}
              >
                {Object.entries(RETURN_TYPE_CONFIG).map(([key, text]) => (
                  <Option key={key} value={key}>
                    {text}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Select
                value={status}
                onChange={setStatus}
                placeholder="选择状态"
                allowClear
                style={{ width: '100%' }}
              >
                {Object.entries(RETURN_STATUS_CONFIG).map(([key, config]) => (
                  <Option key={key} value={key}>
                    {config.text}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Select
                value={reason}
                onChange={setReason}
                placeholder="选择退单原因"
                allowClear
                style={{ width: '100%' }}
              >
                {Object.entries(RETURN_REASON_CONFIG).map(([key, text]) => (
                  <Option key={key} value={key}>
                    {text}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                placeholder={['开始日期', '结束日期']}
                style={{ width: '100%' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 工具栏 */}
        <div style={{ marginBottom: '16px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                {hasRoutePermission('sales/returns', 'create') && (
                  <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                    新增退单
                  </Button>
                )}
                {hasRoutePermission('sales/returns', 'export') && (
                  <Button
                    icon={<FileExcelOutlined />}
                    onClick={handleExportExcel}
                    loading={exportLoading}
                    disabled={selectedRowKeys.length === 0}
                  >
                    导出Excel ({selectedRowKeys.length})
                  </Button>
                )}
                <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
                  刷新
                </Button>
              </Space>
            </Col>
            <Col>
              <span style={{ color: '#666' }}>已选择 {selectedRowKeys.length} 项</span>
            </Col>
          </Row>
        </div>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={returns}
          rowKey="id"
          rowSelection={rowSelection}
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
          scroll={{ x: 1600 }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default SalesReturnManagement;
