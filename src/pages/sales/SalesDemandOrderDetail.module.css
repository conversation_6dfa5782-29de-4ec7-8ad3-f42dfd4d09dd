.container {
  padding: 24px;
}

.headerCard {
  margin-bottom: 16px;
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.titleIcon {
  font-size: 20px;
  color: #1890ff;
}

.title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.emptyState {
  text-align: center;
  padding: 60px 0;
  color: #999;
  font-size: 16px;
}

.tabs {
  background: #fff;
  border-radius: 6px;
}

.infoCard {
  height: 100%;
}

.statisticCard {
  text-align: center;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table {
  margin-top: 16px;
}

.timelineTime {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
}

.timelineUser {
  color: #595959;
  font-size: 12px;
  margin-top: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .titleSection {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .statisticCard {
    height: 80px;
  }
}

@media (max-width: 576px) {
  .headerCard .ant-row {
    flex-direction: column;
    gap: 16px;
  }
  
  .titleSection {
    width: 100%;
  }
}
