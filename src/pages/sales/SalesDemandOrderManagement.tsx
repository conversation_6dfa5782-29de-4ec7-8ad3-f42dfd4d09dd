import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Card,
  message,
  Popconfirm,
  Row,
  Col,
  Tabs,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import type { ColumnsType } from 'antd/es/table';
import {
  getSalesDemandOrderList,
  deleteSalesDemandOrder,
  submitSalesDemandOrder,
  approveSalesDemandOrder,
  cancelSalesDemandOrder,
} from '@/api/SalesDemandOrderApi';
import {
  SalesDemandOrder,
  GetSalesDemandOrderListParams,
  SalesDemandOrderStatus,
  PriorityLevel,
  SALES_DEMAND_ORDER_STATUS_COLORS,
  SALES_DEMAND_ORDER_STATUS_TEXT,
  PRIORITY_LEVEL_COLORS,
  PRIORITY_LEVEL_TEXT,
} from '@/types/salesDemand';
import { CustomerSearchSelector, UserSearchSelector } from '@/components';
import { getUser } from '@/utils/tokenManager';
import styles from './SalesDemandOrderManagement.module.css';

const SalesDemandOrderManagement: React.FC = () => {
  const navigate = useNavigate();
  const currentUser = getUser();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<SalesDemandOrder[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 搜索参数
  const [searchParams, setSearchParams] = useState<Partial<GetSalesDemandOrderListParams>>({});

  // 当前活动的状态标签页
  const [activeStatus, setActiveStatus] = useState<string>('all');

  // 统计数据
  const [statusCounts, setStatusCounts] = useState<Record<string, number>>({});

  // 使用useMemo来稳定searchParams的引用，避免无限循环
  const stableSearchParams = useMemo(
    () => searchParams,
    [
      searchParams.orderNumber,
      searchParams.customerCode,
      searchParams.salesPersonCode,
      searchParams.status,
      searchParams.priorityLevel,
      searchParams.demandDateStart,
      searchParams.demandDateEnd,
      searchParams.createdAtStart,
      searchParams.createdAtEnd,
      searchParams.productCode,
      searchParams.sortBy,
      searchParams.sortOrder,
    ],
  );

  // 获取数据
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const params: GetSalesDemandOrderListParams = {
        page: currentPage,
        pageSize,
        ...stableSearchParams,
        // 根据当前活动状态过滤
        ...(activeStatus !== 'all' && { status: activeStatus as SalesDemandOrderStatus }),
      };
      const response = await getSalesDemandOrderList(params);
      const orderList = response.data.data;
      setData(orderList);
      setTotal(response.data.total);
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, stableSearchParams, activeStatus]);

  // 获取状态统计数据
  const fetchStatusCounts = useCallback(async () => {
    try {
      // 创建一个不包含status的参数对象，避免修改原始searchParams
      const { status, ...baseParams } = stableSearchParams;

      const allResponse = await getSalesDemandOrderList({
        page: 1,
        pageSize: 10, // 获取足够多的数据来统计
        ...baseParams,
      });

      const allOrders = allResponse.data.data;
      const counts: Record<string, number> = {
        all: allOrders.length,
      };

      // 计算各状态数量
      Object.values(SalesDemandOrderStatus).forEach((status) => {
        counts[status] = allOrders.filter((order) => order.status === status).length;
      });

      setStatusCounts(counts);
    } catch (error) {
      console.error('获取状态统计失败:', error);
    }
  }, [stableSearchParams]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchStatusCounts();
  }, [fetchStatusCounts]);

  // 状态标签页切换
  const handleStatusTabChange = useCallback((status: string) => {
    setActiveStatus(status);
    setCurrentPage(1);
  }, []);

  // 搜索
  const handleSearch = useCallback(() => {
    setCurrentPage(1);
    fetchData();
    fetchStatusCounts();
  }, [fetchData, fetchStatusCounts]);

  // 重置搜索
  const handleReset = useCallback(() => {
    setSearchParams({});
    setCurrentPage(1);
    setTimeout(() => {
      fetchData();
      fetchStatusCounts();
    }, 0);
  }, [fetchData, fetchStatusCounts]);

  // 查看我的订单
  const handleMyOrders = useCallback(() => {
    if (currentUser?.code) {
      setSearchParams({ salesPersonCode: currentUser.code });
      setCurrentPage(1);
      setTimeout(() => {
        fetchData();
        fetchStatusCounts();
      }, 0);
    }
  }, [currentUser?.code, fetchData, fetchStatusCounts]);

  // 提交审核
  const handleSubmit = useCallback(
    async (id: string) => {
      try {
        await submitSalesDemandOrder(id);
        message.success('提交成功');
        fetchData();
      } catch (error) {
        message.error('提交失败');
      }
    },
    [fetchData],
  );

  // 审核通过
  const handleApprove = useCallback(
    async (id: string) => {
      if (!currentUser?.code) {
        message.error('无法获取当前用户信息');
        return;
      }
      try {
        await approveSalesDemandOrder(id, { approvedByUserCode: currentUser.code });
        message.success('审核通过');
        fetchData();
      } catch (error) {
        message.error('审核失败');
      }
    },
    [currentUser?.code, fetchData],
  );

  // 取消订单
  const handleCancel = useCallback(
    async (id: string) => {
      try {
        await cancelSalesDemandOrder(id);
        message.success('取消成功');
        fetchData();
      } catch (error) {
        message.error('取消失败');
      }
    },
    [fetchData],
  );

  // 删除订单
  const handleDelete = useCallback(
    async (id: string) => {
      try {
        await deleteSalesDemandOrder(id);
        message.success('删除成功');
        fetchData();
      } catch (error) {
        message.error('删除失败');
      }
    },
    [fetchData],
  );

  // 表格列定义
  const columns: ColumnsType<SalesDemandOrder> = [
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 130,
      fixed: 'left',
      render: (orderNumber: string) => (
        <span style={{ fontWeight: 500, fontSize: '13px' }}>{orderNumber}</span>
      ),
    },
    {
      title: '客户/销售',
      key: 'customerSales',
      width: 110,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, fontSize: '13px' }}>{record.customer?.name || '-'}</div>
          <div style={{ fontSize: '11px', color: '#999' }}>
            {record.salesPerson?.nickname || record.salesPersonCode}
          </div>
        </div>
      ),
    },
    {
      title: '数量/金额',
      key: 'quantityAmount',
      width: 90,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, fontSize: '13px' }}>{record.totalQuantity} 件</div>
          <div style={{ fontSize: '11px', color: '#f5222d' }}>
            ¥{record.estimatedTotalAmount.toFixed(0)}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 70,
      render: (status: SalesDemandOrderStatus) => (
        <Tag color={SALES_DEMAND_ORDER_STATUS_COLORS[status]} style={{ fontSize: '11px' }}>
          {SALES_DEMAND_ORDER_STATUS_TEXT[status]}
        </Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priorityLevel',
      key: 'priorityLevel',
      width: 60,
      render: (level: PriorityLevel) => (
        <Tag color={PRIORITY_LEVEL_COLORS[level]} style={{ fontSize: '11px' }}>
          {PRIORITY_LEVEL_TEXT[level]}
        </Tag>
      ),
    },
    {
      title: '日期',
      key: 'dates',
      width: 90,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '11px' }}>需求: {dayjs(record.demandDate).format('MM-DD')}</div>
          <div style={{ fontSize: '10px', color: '#999' }}>
            创建: {dayjs(record.createdAt).format('MM-DD HH:mm')}
          </div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/sales/demand-orders/view/${record.id}`)}
          />
          {record.status === SalesDemandOrderStatus.DRAFT && (
            <>
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={() => navigate(`/sales/demand-orders/edit/${record.id}`)}
              />
              <Button
                type="text"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleSubmit(record.id)}
                style={{ color: '#52c41a' }}
              />
              <Popconfirm
                title="确定删除？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button type="text" size="small" danger icon={<DeleteOutlined />} />
              </Popconfirm>
            </>
          )}
          {record.status === SalesDemandOrderStatus.SUBMITTED && (
            <Button
              type="text"
              size="small"
              icon={<CheckOutlined />}
              onClick={() => handleApprove(record.id)}
              style={{ color: '#52c41a' }}
            />
          )}
          {[
            SalesDemandOrderStatus.DRAFT,
            SalesDemandOrderStatus.SUBMITTED,
            SalesDemandOrderStatus.APPROVED,
          ].includes(record.status) && (
            <Popconfirm
              title="确定取消？"
              onConfirm={() => handleCancel(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="text" size="small" danger icon={<CloseOutlined />} />
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  // 创建状态标签页数据
  const statusTabs = [
    {
      key: 'all',
      label: (
        <Badge count={statusCounts.all || 0} size="small">
          <span style={{ fontSize: '13px' }}>全部</span>
        </Badge>
      ),
    },
    ...Object.values(SalesDemandOrderStatus).map((status) => ({
      key: status,
      label: (
        <Badge count={statusCounts[status] || 0} size="small">
          <span style={{ fontSize: '13px' }}>{SALES_DEMAND_ORDER_STATUS_TEXT[status]}</span>
        </Badge>
      ),
    })),
  ];

  return (
    <div className={styles.container}>
      {/* 状态标签页 */}
      <Card style={{ marginBottom: 12 }}>
        <Tabs
          activeKey={activeStatus}
          onChange={handleStatusTabChange}
          items={statusTabs}
          size="small"
          style={{ marginBottom: 0 }}
        />
      </Card>

      {/* 搜索和操作栏 */}
      <Card style={{ marginBottom: 12 }}>
        <Row gutter={12} align="middle">
          <Col flex="auto">
            <Space size="small">
              <Input
                placeholder="搜索订单编号"
                value={searchParams.orderNumber}
                onChange={(e) => setSearchParams({ ...searchParams, orderNumber: e.target.value })}
                style={{ width: 140 }}
                size="small"
              />
              <CustomerSearchSelector
                placeholder="客户"
                value={searchParams.customerCode}
                onChange={(value) => setSearchParams({ ...searchParams, customerCode: value })}
                style={{ width: 110 }}
                allowClear
              />
              <UserSearchSelector
                placeholder="销售人员"
                value={searchParams.salesPersonCode}
                onChange={(value) => setSearchParams({ ...searchParams, salesPersonCode: value })}
                style={{ width: 110 }}
                allowClear
              />
            </Space>
          </Col>
          <Col>
            <Space size="small">
              <Button icon={<SearchOutlined />} onClick={handleSearch} size="small">
                搜索
              </Button>
              <Button onClick={handleReset} size="small">
                重置
              </Button>
              <Button icon={<ReloadOutlined />} onClick={fetchData} loading={loading} size="small">
                刷新
              </Button>
              {currentUser?.code && (
                <Button icon={<UserOutlined />} onClick={handleMyOrders} size="small">
                  我的订单
                </Button>
              )}
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/sales/demand-orders/create')}
                size="small"
              >
                新建订单
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 订单表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          scroll={{ x: 900 }}
          size="small"
          pagination={{
            current: currentPage,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} / ${total}`,
            size: 'small',
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            },
          }}
        />
      </Card>
    </div>
  );
};

export default SalesDemandOrderManagement;
