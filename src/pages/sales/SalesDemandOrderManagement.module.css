.container {
  padding: 8px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.searchForm {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.searchForm .ant-space {
  width: 100%;
}

.searchForm .ant-space-item {
  margin-bottom: 8px;
}

/* 紧凑样式 */
.container :global(.ant-card) {
  border-radius: 6px;
}

.container :global(.ant-card-body) {
  padding: 12px;
}

.container :global(.ant-tabs-content-holder) {
  padding-top: 0;
}

.container :global(.ant-tabs-tab) {
  padding: 6px 12px;
  margin: 0 4px;
}

.container :global(.ant-table-small) .ant-table-tbody > tr > td {
  padding: 6px 8px;
}

.container :global(.ant-table-small) .ant-table-thead > tr > th {
  padding: 8px 8px;
  font-size: 12px;
}

@media (max-width: 768px) {
  .container {
    padding: 4px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .searchForm {
    padding: 8px;
  }

  .container :global(.ant-card-body) {
    padding: 8px;
  }
}
