import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Card,
  Table,
  Space,
  message,
  InputNumber,
  Popconfirm,
} from 'antd';
import { PlusOutlined, DeleteOutlined, SaveOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import dayjs from 'dayjs';
import type { ColumnsType } from 'antd/es/table';
import {
  createSalesDemandOrder,
  updateSalesDemandOrder,
  getSalesDemandOrderDetail,
} from '@/api/SalesDemandOrderApi';
import {
  CreateSalesDemandOrderParams,
  UpdateSalesDemandOrderParams,
  SalesDemandOrderDetail,
  PriorityLevel,
  PRIORITY_LEVEL_TEXT,
  SalesDemandOrderStatus,
} from '@/types/salesDemand';
import {
  CustomerSearchSelector,
  UserSearchSelector,
  ProductCodeInput,
  ProductVariantSelector,
} from '@/components';
import type { ProductVariant } from '@/components/ProductVariantSelector/ProductVariantSelector';
import type { ProductListItem } from '@/types/product';
import { getUser } from '@/utils/tokenManager';
import styles from './SalesDemandOrderForm.module.css';

const { Option } = Select;
const { TextArea } = Input;

interface FormData {
  customerCode?: string;
  salesPersonCode: string;
  demandDate: dayjs.Dayjs;
  expectedDeliveryDate?: dayjs.Dayjs;
  priorityLevel: PriorityLevel;
  remark?: string;
}

const SalesDemandOrderForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEdit = !!id;
  const isView = location.pathname.includes('/view/');
  const currentUser = getUser();

  const [form] = Form.useForm<FormData>();
  const [loading, setLoading] = useState(false);
  const [details, setDetails] = useState<SalesDemandOrderDetail[]>([]);
  const [orderData, setOrderData] = useState<any>(null);

  // 新增状态：当前选择的商品和变体
  const [currentProduct, setCurrentProduct] = useState<ProductListItem | null>(null);
  const [currentProductCode, setCurrentProductCode] = useState<string>('');
  const [selectedVariants, setSelectedVariants] = useState<ProductVariant[]>([]);

  // 获取订单详情（编辑/查看模式）
  const fetchOrderDetail = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await getSalesDemandOrderDetail(id);
      const order = response.data;
      setOrderData(order);

      form.setFieldsValue({
        customerCode: order.customerCode,
        salesPersonCode: order.salesPersonCode,
        demandDate: dayjs(order.demandDate),
        expectedDeliveryDate: order.expectedDeliveryDate
          ? dayjs(order.expectedDeliveryDate)
          : undefined,
        priorityLevel: order.priorityLevel,
        remark: order.remark,
      });

      setDetails(order.details || []);
    } catch (error) {
      message.error('获取订单详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isEdit) {
      fetchOrderDetail();
    } else {
      // 创建模式下，默认选择当前用户作为销售人员
      if (currentUser?.code) {
        form.setFieldsValue({
          salesPersonCode: currentUser.code,
        });
      }
    }
  }, [id, isEdit, currentUser, form]);

  // 删除明细行
  const removeDetailRow = (index: number) => {
    const newDetails = details.filter((_, i) => i !== index);
    setDetails(newDetails);
  };

  // 更新明细行（仅用于数量、单价、备注等可编辑字段）
  const updateDetailRow = (index: number, field: keyof SalesDemandOrderDetail, value: any) => {
    const newDetails = [...details];
    newDetails[index] = { ...newDetails[index], [field]: value };
    setDetails(newDetails);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (details.length === 0) {
        message.error('请至少添加一条订单明细');
        return;
      }

      // 验证明细数据
      for (let i = 0; i < details.length; i++) {
        const detail = details[i];
        if (
          !detail.productCode ||
          !detail.colorCode ||
          !detail.sizeCode ||
          !detail.demandQuantity
        ) {
          message.error(`第 ${i + 1} 行明细信息不完整`);
          return;
        }
      }

      setLoading(true);

      const params = {
        customerCode: values.customerCode,
        salesPersonCode: values.salesPersonCode,
        demandDate: values.demandDate.format('YYYY-MM-DD'),
        expectedDeliveryDate: values.expectedDeliveryDate?.format('YYYY-MM-DD'),
        priorityLevel: values.priorityLevel,
        remark: values.remark,
        createdByUserCode: currentUser?.code, // 自动填入创建人
        details: details.map((detail) => ({
          productCode: detail.productCode,
          colorCode: detail.colorCode,
          sizeCode: detail.sizeCode,
          demandQuantity: detail.demandQuantity,
          unitPrice: detail.unitPrice,
          remark: detail.remark,
        })),
      };

      if (isEdit) {
        await updateSalesDemandOrder(id!, params as UpdateSalesDemandOrderParams);
        message.success('更新成功');
      } else {
        await createSalesDemandOrder(params as CreateSalesDemandOrderParams);
        message.success('创建成功');
      }

      navigate('/sales/demand-orders');
    } catch (error) {
      message.error(isEdit ? '更新失败' : '创建失败');
    } finally {
      setLoading(false);
    }
  };

  // 明细表格列定义
  const detailColumns: ColumnsType<SalesDemandOrderDetail> = [
    {
      title: '商品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
      render: (value, record) => (
        <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {value}
          {record.product && (
            <div style={{ fontSize: '11px', color: '#8c8c8c', marginTop: '2px' }}>
              {record.product.name}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '颜色',
      dataIndex: 'colorCode',
      key: 'colorCode',
      width: 100,
      render: (value, record) => {
        // 从商品的颜色配置中找到对应的颜色名称
        const colorInfo = record.product?.colorSizeCombinations?.find(
          (combo) => combo.colorCode === value,
        );
        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            {colorInfo?.colorHex && (
              <span
                style={{
                  display: 'inline-block',
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: colorInfo.colorHex,
                  border: '1px solid #d9d9d9',
                }}
              />
            )}
            <span style={{ fontSize: '12px' }}>{colorInfo?.colorName || value}</span>
          </div>
        );
      },
    },
    {
      title: '尺寸',
      dataIndex: 'sizeCode',
      key: 'sizeCode',
      width: 80,
      render: (value) => <span style={{ fontSize: '12px', fontWeight: '500' }}>{value}</span>,
    },
    {
      title: 'SKU编码',
      dataIndex: 'skuCode',
      key: 'skuCode',
      width: 150,
      render: (value) => (
        <span
          style={{
            fontSize: '12px',
            color: value ? '#52c41a' : '#d9d9d9',
            fontFamily: 'monospace',
          }}
        >
          {value || '请选择商品、颜色、尺寸'}
        </span>
      ),
    },
    {
      title: '需求数量',
      dataIndex: 'demandQuantity',
      key: 'demandQuantity',
      width: 100,
      render: (value, _, index) => (
        <InputNumber
          value={value}
          onChange={(demandQuantity) =>
            updateDetailRow(index, 'demandQuantity', demandQuantity || 0)
          }
          disabled={isView}
          min={1}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '预估单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (value, _, index) => (
        <InputNumber
          value={value}
          onChange={(unitPrice) => updateDetailRow(index, 'unitPrice', unitPrice || 0)}
          disabled={isView}
          min={0}
          precision={2}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '小计',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 100,
      render: (_, record) => {
        const total = (record.demandQuantity || 0) * (record.unitPrice || 0);
        return `¥${total.toFixed(2)}`;
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 150,
      render: (value, _, index) => (
        <Input
          value={value}
          onChange={(e) => updateDetailRow(index, 'remark', e.target.value)}
          disabled={isView}
          placeholder="备注"
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, __, index) =>
        !isView && (
          <Popconfirm title="确定要删除这行吗？" onConfirm={() => removeDetailRow(index)}>
            <Button type="text" danger icon={<DeleteOutlined />} size="small" />
          </Popconfirm>
        ),
    },
  ];

  const pageTitle = isView ? '查看销售需求订单' : isEdit ? '编辑销售需求订单' : '创建销售需求订单';

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.header}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={() => navigate('/sales/demand-orders')}>
              返回
            </Button>
            <h2>{pageTitle}</h2>
          </Space>
          {orderData && (
            <div className={styles.orderInfo}>
              <span>订单编号：{orderData.orderNumber}</span>
              <span>状态：{orderData.status}</span>
            </div>
          )}
        </div>

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            priorityLevel: PriorityLevel.NORMAL,
            demandDate: dayjs(),
            salesPersonCode: !isEdit && currentUser?.code ? currentUser.code : undefined,
          }}
        >
          <div className={styles.formGrid}>
            <Form.Item label="客户" name="customerCode">
              <CustomerSearchSelector
                placeholder="选择客户"
                disabled={isView}
                onChange={(_, customer) => {
                  // 当选择客户时，如果客户有负责人，自动设置为销售人员
                  if (customer && customer.managerCode && !isEdit) {
                    form.setFieldsValue({
                      salesPersonCode: customer.managerCode,
                    });
                  }
                }}
              />
            </Form.Item>

            <Form.Item
              label="销售人员"
              name="salesPersonCode"
              rules={[{ required: true, message: '请选择销售人员' }]}
              extra={
                !isEdit && currentUser?.code
                  ? `默认选择：${currentUser.nickname || currentUser.name || '当前用户'}`
                  : undefined
              }
            >
              <UserSearchSelector placeholder="选择销售人员" disabled={isView} />
            </Form.Item>

            <Form.Item
              label="需求日期"
              name="demandDate"
              rules={[{ required: true, message: '请选择需求日期' }]}
            >
              <DatePicker disabled={isView} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label="期望交货日期" name="expectedDeliveryDate">
              <DatePicker disabled={isView} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="优先级"
              name="priorityLevel"
              rules={[{ required: true, message: '请选择优先级' }]}
            >
              <Select disabled={isView}>
                {Object.values(PriorityLevel).map((level) => (
                  <Option key={level} value={level}>
                    {PRIORITY_LEVEL_TEXT[level]}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="备注" name="remark" className={styles.fullWidth}>
              <TextArea rows={3} disabled={isView} />
            </Form.Item>
          </div>
        </Form>

        {/* 商品变体选择区域 */}
        {!isView && (
          <div className={styles.variantSection}>
            <Card
              title="选择商品变体"
              size="small"
              style={{ marginBottom: 16 }}
              extra={
                <span style={{ fontSize: '12px', color: '#8c8c8c' }}>
                  输入商品编码，然后选择颜色尺码组合
                </span>
              }
            >
              <div style={{ marginBottom: 16 }}>
                <Space>
                  <ProductCodeInput
                    value={currentProductCode}
                    onChange={(productCode, product) => {
                      setCurrentProductCode(productCode);
                      // 只有在验证成功时才更新商品信息和清空变体选择
                      if (product) {
                        setCurrentProduct(product);
                        setSelectedVariants([]); // 清空之前的变体选择
                      }
                    }}
                    onProductSelect={(product) => {
                      if (product) {
                        setCurrentProduct(product);
                        setSelectedVariants([]); // 清空之前的变体选择
                      } else {
                        // 验证失败时，只清空商品信息，不清空输入框
                        setCurrentProduct(null);
                        setSelectedVariants([]);
                      }
                    }}
                    placeholder="输入商品编码"
                    style={{ width: 300 }}
                  />
                  {(currentProductCode || currentProduct) && (
                    <Button
                      onClick={() => {
                        setCurrentProductCode('');
                        setCurrentProduct(null);
                        setSelectedVariants([]);
                      }}
                    >
                      清空
                    </Button>
                  )}
                </Space>
              </div>

              {currentProduct && (
                <div
                  style={{
                    marginBottom: 16,
                    padding: 12,
                    backgroundColor: '#f6ffed',
                    border: '1px solid #b7eb8f',
                    borderRadius: 6,
                  }}
                >
                  <Space>
                    <span style={{ color: '#52c41a', fontWeight: 'bold' }}>✓ 已选择商品：</span>
                    <span>{currentProduct.name}</span>
                    <span style={{ color: '#8c8c8c' }}>({currentProduct.code})</span>
                    <span style={{ color: '#1890ff' }}>
                      ¥{currentProduct.retailPrice?.toFixed(2)}
                    </span>
                  </Space>
                </div>
              )}

              <ProductVariantSelector
                product={currentProduct}
                value={selectedVariants}
                onChange={setSelectedVariants}
                style={{ marginBottom: 16 }}
              />

              {selectedVariants.length > 0 && (
                <div
                  style={{
                    padding: 12,
                    backgroundColor: '#e6f7ff',
                    border: '1px solid #91d5ff',
                    borderRadius: 6,
                    marginBottom: 16,
                  }}
                >
                  <div style={{ marginBottom: 8 }}>
                    <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                      将添加 {selectedVariants.length} 个变体，总数量：
                      {selectedVariants.reduce((sum, v) => sum + v.quantity, 0)}
                    </span>
                  </div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      if (!currentProduct) {
                        message.error('请先选择商品');
                        return;
                      }

                      if (selectedVariants.length === 0) {
                        message.error('请选择至少一个颜色尺码组合');
                        return;
                      }

                      // 检查是否有数量为0的变体
                      const invalidVariants = selectedVariants.filter((v) => v.quantity <= 0);
                      if (invalidVariants.length > 0) {
                        message.error('所有变体的数量必须大于0');
                        return;
                      }

                      // 将选择的变体添加到明细中
                      const newDetails: SalesDemandOrderDetail[] = selectedVariants.map(
                        (variant) => ({
                          productCode: currentProduct.code,
                          colorCode: variant.colorCode,
                          sizeCode: variant.sizeCode,
                          skuCode: variant.skuCode,
                          demandQuantity: variant.quantity,
                          unitPrice: currentProduct.retailPrice || 0,
                          remark: '',
                          product: currentProduct as any, // 临时类型转换
                        }),
                      );

                      setDetails([...details, ...newDetails]);
                      setSelectedVariants([]); // 清空选择
                      message.success(`成功添加 ${newDetails.length} 个商品变体到订单明细`);
                    }}
                  >
                    添加到订单明细
                  </Button>
                </div>
              )}
            </Card>
          </div>
        )}

        <div className={styles.detailSection}>
          <div className={styles.detailHeader}>
            <h3>订单明细</h3>
            <span style={{ fontSize: '12px', color: '#8c8c8c' }}>
              使用上方的商品变体选择器添加明细
            </span>
          </div>

          <Table
            columns={detailColumns}
            dataSource={details}
            rowKey={(record, index) => record.id || `detail-${index}`}
            pagination={false}
            scroll={{ x: 800 }}
            size="small"
          />
        </div>

        {!isView && (
          <div className={styles.footer}>
            <Space>
              <Button onClick={() => navigate('/sales/demand-orders')}>取消</Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={loading}
                onClick={handleSubmit}
                disabled={orderData?.status !== SalesDemandOrderStatus.DRAFT && isEdit}
              >
                {isEdit ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        )}
      </Card>
    </div>
  );
};

export default SalesDemandOrderForm;
