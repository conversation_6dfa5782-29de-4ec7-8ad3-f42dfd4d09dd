import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  message,
  Tag,
  Row,
  Col,
  Modal,
  Tooltip,
  Tabs,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  FileExcelOutlined,
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import dayjs, { Dayjs } from 'dayjs';
import { getSalesOrderList, deleteSalesOrder, exportSalesOrdersExcel } from '@/api/SalesOrderApi';
import type {
  SalesOrderListItem,
  GetSalesOrderListParams,
  SalesOrderStatus,
  OrderPriority,
} from '@/types/salesOrder';
import { SALES_ORDER_STATUS_CONFIG, ORDER_PRIORITY_CONFIG } from '@/types/salesOrder';
import { CustomerSearchSelector, UserSearchSelector } from '@/components';
import { handleApiResponse, logError, getErrorMessage } from '@/utils/errorHandler';
import useAuthStore from '@/store/authStore';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;

const SalesOrderManagement: React.FC = () => {
  const navigate = useNavigate();
  const { hasRoutePermission } = useAuthStore();

  // 状态管理
  const [orders, setOrders] = useState<SalesOrderListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 搜索条件
  const [searchText, setSearchText] = useState('');
  const [customerCode, setCustomerCode] = useState<string | undefined>();
  const [salesPersonCode, setSalesPersonCode] = useState<string | undefined>();
  const [priority, setPriority] = useState<OrderPriority | undefined>();
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);

  // 当前活动的状态标签页
  const [activeStatus, setActiveStatus] = useState<string>('all');

  // 统计数据
  const [statusCounts, setStatusCounts] = useState<Record<string, number>>({});

  // 分页配置
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  });

  // 使用useMemo来稳定searchParams的引用，避免无限循环
  const stableSearchParams = useMemo(
    () => ({
      search: searchText,
      customerCode,
      salesPersonCode,
      priority,
      startDate: dateRange ? dateRange[0].format('YYYY-MM-DD') : undefined,
      endDate: dateRange ? dateRange[1].format('YYYY-MM-DD') : undefined,
    }),
    [searchText, customerCode, salesPersonCode, priority, dateRange],
  );

  // 获取订单列表
  const fetchOrders = useCallback(async () => {
    setLoading(true);
    try {
      const params: GetSalesOrderListParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        search: stableSearchParams.search?.trim() || undefined,
        customerCode: stableSearchParams.customerCode || undefined,
        salesPersonCode: stableSearchParams.salesPersonCode || undefined,
        status: activeStatus !== 'all' ? (activeStatus as SalesOrderStatus) : undefined,
        priority: stableSearchParams.priority,
        startDate: stableSearchParams.startDate || undefined,
        endDate: stableSearchParams.endDate || undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const response = await getSalesOrderList(params);
      const result = handleApiResponse(response, '', '获取订单列表失败');

      if (result.success && response.data) {
        setOrders(response.data.orders);
        setPagination((prev) => ({
          ...prev,
          total: response.data.total,
        }));
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取订单列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  }, [pagination.current, pagination.pageSize, stableSearchParams, activeStatus]);

  // 获取状态统计数据
  const fetchStatusCounts = useCallback(async () => {
    try {
      const params: GetSalesOrderListParams = {
        page: 1,
        pageSize: 1000, // 获取足够多的数据来统计
        search: stableSearchParams.search?.trim() || undefined,
        customerCode: stableSearchParams.customerCode || undefined,
        salesPersonCode: stableSearchParams.salesPersonCode || undefined,
        priority: stableSearchParams.priority,
        startDate: stableSearchParams.startDate || undefined,
        endDate: stableSearchParams.endDate || undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const response = await getSalesOrderList(params);
      const result = handleApiResponse(response, '', '获取状态统计失败');

      if (result.success && response.data) {
        const allOrders = response.data.orders;
        const counts: Record<string, number> = {
          all: allOrders.length,
        };

        // 计算各状态数量
        Object.keys(SALES_ORDER_STATUS_CONFIG).forEach((status) => {
          counts[status] = allOrders.filter((order) => order.status === status).length;
        });

        setStatusCounts(counts);
      }
    } catch (error) {
      console.error('获取状态统计失败:', error);
    }
  }, [stableSearchParams]);

  // 初始化数据
  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  useEffect(() => {
    fetchStatusCounts();
  }, [fetchStatusCounts]);

  // 状态标签页切换
  const handleStatusTabChange = useCallback((status: string) => {
    setActiveStatus(status);
    setPagination((prev) => ({ ...prev, current: 1 }));
  }, []);

  // 搜索处理
  const handleSearch = useCallback(() => {
    setPagination((prev) => ({ ...prev, current: 1 }));
    setTimeout(() => {
      fetchOrders();
      fetchStatusCounts();
    }, 0);
  }, [fetchOrders, fetchStatusCounts]);

  // 重置搜索
  const handleReset = useCallback(() => {
    setSearchText('');
    setCustomerCode(undefined);
    setSalesPersonCode(undefined);
    setPriority(undefined);
    setDateRange(null);
    setPagination((prev) => ({ ...prev, current: 1 }));
    setTimeout(() => {
      fetchOrders();
      fetchStatusCounts();
    }, 0);
  }, [fetchOrders, fetchStatusCounts]);

  // 刷新数据
  const handleRefresh = useCallback(() => {
    fetchOrders();
    fetchStatusCounts();
  }, [fetchOrders, fetchStatusCounts]);

  // 分页变化处理
  const handleTableChange = useCallback((paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 20 } = paginationConfig;
    setPagination((prev) => ({
      ...prev,
      current,
      pageSize,
    }));
  }, []);

  // 新增订单
  const handleAdd = () => {
    navigate('/sales/orders/add');
  };

  // 编辑订单
  const handleEdit = (record: SalesOrderListItem) => {
    navigate(`/sales/orders/edit/${record.id}`);
  };

  // 查看订单
  const handleView = (record: SalesOrderListItem) => {
    navigate(`/sales/orders/view/${record.id}`);
  };

  // 删除订单
  const handleDelete = (record: SalesOrderListItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除订单 ${record.orderNumber} 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await deleteSalesOrder(record.id);
          const result = handleApiResponse(response, '删除成功', '删除失败');

          if (result.success) {
            message.success(result.message);
            handleRefresh();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('删除订单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 导出Excel
  const handleExportExcel = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要导出的订单');
      return;
    }

    setExportLoading(true);
    try {
      const [startDate, endDate] = dateRange
        ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')]
        : ['', ''];

      const blob = await exportSalesOrdersExcel({
        customerCode,
        salesPersonCode,
        status,
        startDate,
        endDate,
        orderIds: selectedRowKeys as string[],
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `销售订单_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error) {
      logError('导出Excel', error);
      message.error(getErrorMessage(error));
    } finally {
      setExportLoading(false);
    }
  };

  // 表格列配置
  const columns: ColumnsType<SalesOrderListItem> = [
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 130,
      fixed: 'left',
      render: (orderNumber: string) => (
        <span style={{ fontWeight: 500, fontSize: '13px' }}>{orderNumber}</span>
      ),
    },
    {
      title: '客户/销售',
      key: 'customerSales',
      width: 110,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, fontSize: '13px' }}>{record.customerName || '-'}</div>
          <div style={{ fontSize: '11px', color: '#999' }}>{record.salesPersonName || '-'}</div>
        </div>
      ),
    },
    {
      title: '数量/金额',
      key: 'quantityAmount',
      width: 90,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, fontSize: '13px' }}>{record.totalQuantity} 件</div>
          <div style={{ fontSize: '11px', color: '#f5222d' }}>¥{record.totalAmount.toFixed(0)}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 70,
      render: (status: SalesOrderStatus) => {
        const config = SALES_ORDER_STATUS_CONFIG[status];
        return (
          <Tag color={config.color} style={{ fontSize: '11px' }}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 60,
      render: (priority?: OrderPriority) => {
        if (!priority) return '-';
        const config = ORDER_PRIORITY_CONFIG[priority];
        return (
          <Tag color={config.color} style={{ fontSize: '11px' }}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '日期',
      key: 'dates',
      width: 90,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '11px' }}>订单: {dayjs(record.orderDate).format('MM-DD')}</div>
          <div style={{ fontSize: '10px', color: '#999' }}>
            {record.expectedDeliveryDate
              ? `交货: ${dayjs(record.expectedDeliveryDate).format('MM-DD')}`
              : '无交货日期'}
          </div>
        </div>
      ),
    },
    {
      title: '标记',
      key: 'flags',
      width: 70,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '11px' }}>
            <Tag
              color={record.isDropShipping ? 'blue' : 'default'}
              style={{ fontSize: '10px', margin: '1px' }}
            >
              {record.isDropShipping ? '代发' : '自发'}
            </Tag>
          </div>
          <div style={{ fontSize: '11px' }}>
            <Tag
              color={record.isReleased ? 'green' : 'default'}
              style={{ fontSize: '10px', margin: '1px' }}
            >
              {record.isReleased ? '已放单' : '未放单'}
            </Tag>
          </div>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 90,
      render: (date: string) => (
        <div style={{ fontSize: '11px' }}>{dayjs(date).format('MM-DD HH:mm')}</div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
          {hasRoutePermission('sales/orders', 'update') && record.status === 'draft' && (
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          )}
          {hasRoutePermission('sales/orders', 'delete') && record.status === 'draft' && (
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            />
          )}
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // 创建状态标签页数据
  const statusTabs = [
    {
      key: 'all',
      label: (
        <Badge count={statusCounts.all || 0} size="small">
          <span style={{ fontSize: '13px' }}>全部</span>
        </Badge>
      ),
    },
    ...Object.entries(SALES_ORDER_STATUS_CONFIG).map(([status, config]) => ({
      key: status,
      label: (
        <Badge count={statusCounts[status] || 0} size="small">
          <span style={{ fontSize: '13px' }}>{config.text}</span>
        </Badge>
      ),
    })),
  ];

  return (
    <div style={{ padding: '12px' }}>
      {/* 状态标签页 */}
      <Card style={{ marginBottom: 12 }}>
        <Tabs
          activeKey={activeStatus}
          onChange={handleStatusTabChange}
          items={statusTabs}
          size="small"
          style={{ marginBottom: 0 }}
        />
      </Card>

      {/* 搜索和操作栏 */}
      <Card style={{ marginBottom: 12 }}>
        <Row gutter={12} align="middle">
          <Col flex="auto">
            <Space size="small">
              <Input
                placeholder="搜索订单号"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 140 }}
                size="small"
              />
              <CustomerSearchSelector
                placeholder="客户"
                value={customerCode}
                onChange={setCustomerCode}
                style={{ width: 110 }}
                allowClear
              />
              <UserSearchSelector
                placeholder="销售人员"
                value={salesPersonCode}
                onChange={setSalesPersonCode}
                style={{ width: 110 }}
                allowClear
              />
              <Select
                value={priority}
                onChange={setPriority}
                placeholder="优先级"
                allowClear
                style={{ width: 90 }}
                size="small"
              >
                {Object.entries(ORDER_PRIORITY_CONFIG).map(([key, config]) => (
                  <Option key={key} value={key}>
                    {config.text}
                  </Option>
                ))}
              </Select>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                placeholder={['开始', '结束']}
                style={{ width: 200 }}
                size="small"
              />
            </Space>
          </Col>
          <Col>
            <Space size="small">
              <Button icon={<SearchOutlined />} onClick={handleSearch} size="small">
                搜索
              </Button>
              <Button onClick={handleReset} size="small">
                重置
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
                size="small"
              >
                刷新
              </Button>
              {hasRoutePermission('sales/orders', 'create') && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} size="small">
                  新建订单
                </Button>
              )}
              {hasRoutePermission('sales/orders', 'export') && (
                <Button
                  icon={<FileExcelOutlined />}
                  onClick={handleExportExcel}
                  loading={exportLoading}
                  disabled={selectedRowKeys.length === 0}
                  size="small"
                >
                  导出 ({selectedRowKeys.length})
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 订单表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          rowSelection={rowSelection}
          loading={loading}
          scroll={{ x: 900 }}
          size="small"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} / ${total}`,
            size: 'small',
            onChange: (page, size) => {
              setPagination((prev) => ({
                ...prev,
                current: page,
                pageSize: size || 20,
              }));
            },
          }}
        />
      </Card>
    </div>
  );
};

export default SalesOrderManagement;
