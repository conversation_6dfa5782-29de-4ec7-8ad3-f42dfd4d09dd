.container {
  padding: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.orderInfo {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.formGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.fullWidth {
  grid-column: 1 / -1;
}

.detailSection {
  margin-top: 32px;
}

.detailHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.detailHeader h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.footer {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .orderInfo {
    flex-direction: column;
    gap: 8px;
  }
  
  .formGrid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .detailHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
