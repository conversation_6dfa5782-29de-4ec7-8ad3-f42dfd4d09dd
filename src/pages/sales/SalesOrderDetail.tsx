import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  message,
  DatePicker,
  Select,
  Switch,
  InputNumber,
  Table,
  Row,
  Col,
  Divider,
  Modal,
  Tag,
  Descriptions,
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined,
  CheckOutlined,
  SendOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  getSalesOrderDetail,
  createSalesOrder,
  updateSalesOrder,
  confirmSalesOrder,
  shipSalesOrder,
  completeSalesOrder,
  cancelSalesOrder,
} from '@/api/SalesOrderApi';
import type {
  SalesOrder,
  SalesOrderDetail,
  CreateSalesOrderParams,
  UpdateSalesOrderParams,
  SalesOrderStatus,
  PriceType,
} from '@/types/salesOrder';
import {
  SALES_ORDER_STATUS_CONFIG,
  ORDER_PRIORITY_CONFIG,
  SHIPPING_METHOD_CONFIG,
  PRICE_TYPE_CONFIG,
} from '@/types/salesOrder';
import {
  CustomerSearchSelector,
  UserSearchSelector,
  ProductCodeInputEnhanced,
  ProductColorSelectorEnhanced,
  ProductSizeSelectorEnhanced,
  ProvinceSelector,
} from '@/components';
import type { ProductOrderInfo } from '@/types/product';
import { handleApiResponse, logError, getErrorMessage } from '@/utils/errorHandler';
import { formatAmount } from '@/utils/formatUtils';
import useAuthStore from '@/store/authStore';

const { Option } = Select;
const { TextArea } = Input;

interface ProductDetailFormData extends Omit<SalesOrderDetail, 'skuCode' | 'totalAmount'> {
  product?: ProductOrderInfo | null;
}

const SalesOrderDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user, hasRoutePermission } = useAuthStore();
  const [form] = Form.useForm();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [order, setOrder] = useState<SalesOrder | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [isView, setIsView] = useState(false);

  // 商品明细相关状态
  const [details, setDetails] = useState<ProductDetailFormData[]>([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingDetail, setEditingDetail] = useState<ProductDetailFormData | null>(null);
  const [editingIndex, setEditingIndex] = useState<number>(-1);

  // 判断页面模式
  useEffect(() => {
    const path = window.location.pathname;
    if (path.includes('/add')) {
      setIsEdit(false);
      setIsView(false);
    } else if (path.includes('/edit/')) {
      setIsEdit(true);
      setIsView(false);
    } else if (path.includes('/view/')) {
      setIsEdit(false);
      setIsView(true);
    }
  }, []);

  // 获取订单详情
  const fetchOrderDetail = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await getSalesOrderDetail(id);
      const result = handleApiResponse(response, '', '获取订单详情失败');

      if (result.success && response.data) {
        const orderData = response.data;
        setOrder(orderData);
        setDetails(orderData.details.map((detail) => ({ ...detail, product: null })));

        // 填充表单
        form.setFieldsValue({
          ...orderData,
          orderDate: orderData.orderDate ? dayjs(orderData.orderDate) : null,
          expectedDeliveryDate: orderData.expectedDeliveryDate
            ? dayjs(orderData.expectedDeliveryDate)
            : null,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取订单详情', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (id) {
      fetchOrderDetail();
    } else {
      // 新增模式，设置默认值
      form.setFieldsValue({
        orderDate: dayjs(),
        salesPersonCode: user?.code,
        priority: 'normal',
        isDropShipping: false,
        isReleased: false,
        shippingMethod: 'collect',
      });
    }
  }, [id, user]);

  // 返回列表
  const handleBack = () => {
    navigate('/sales/orders');
  };

  // 保存订单
  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      if (details.length === 0) {
        message.error('请至少添加一个商品明细');
        return;
      }

      setSaving(true);

      // 构建订单数据
      const orderData: CreateSalesOrderParams | UpdateSalesOrderParams = {
        ...values,
        orderDate: values.orderDate?.format('YYYY-MM-DD'),
        expectedDeliveryDate: values.expectedDeliveryDate?.format('YYYY-MM-DD'),
        createdByUserCode: user?.code,
        details: details.map((detail) => ({
          productCode: detail.productCode,
          colorCode: detail.colorCode,
          sizeCode: detail.sizeCode,
          skuCode: `${detail.productCode}-${detail.colorCode}-${detail.sizeCode}`,
          quantity: detail.quantity,
          priceType: detail.priceType,
          unitPrice: detail.unitPrice,
          discountAmount: detail.discountAmount || 0,
          expectedShipDate: detail.expectedShipDate,
          remark: detail.remark,
        })),
      };

      let response;
      if (id && isEdit) {
        response = await updateSalesOrder(id, { ...orderData, id } as UpdateSalesOrderParams);
      } else {
        response = await createSalesOrder(orderData);
      }

      const result = handleApiResponse(response, '保存成功', '保存失败');

      if (result.success) {
        message.success(result.message);
        navigate('/sales/orders');
      } else {
        message.error(result.message);
      }
    } catch (error) {
      if (error instanceof Error && error.message !== 'Validation failed') {
        logError('保存订单', error);
        message.error(getErrorMessage(error));
      }
    } finally {
      setSaving(false);
    }
  };

  // 保存并新建
  const handleSaveAndNew = async () => {
    try {
      const values = await form.validateFields();

      if (details.length === 0) {
        message.error('请至少添加一个商品明细');
        return;
      }

      setSaving(true);

      // 构建订单数据
      const orderData: CreateSalesOrderParams = {
        ...values,
        orderDate: values.orderDate?.format('YYYY-MM-DD'),
        expectedDeliveryDate: values.expectedDeliveryDate?.format('YYYY-MM-DD'),
        createdByUserCode: user?.code,
        details: details.map((detail) => ({
          productCode: detail.productCode,
          colorCode: detail.colorCode,
          sizeCode: detail.sizeCode,
          skuCode: `${detail.productCode}-${detail.colorCode}-${detail.sizeCode}`,
          quantity: detail.quantity,
          priceType: detail.priceType,
          unitPrice: detail.unitPrice,
          discountAmount: detail.discountAmount || 0,
          expectedShipDate: detail.expectedShipDate,
          remark: detail.remark,
        })),
      };

      const response = await createSalesOrder(orderData);
      const result = handleApiResponse(response, '保存成功', '保存失败');

      if (result.success) {
        message.success(result.message + '，可以继续创建新订单');

        // 保留一些字段，清空其他字段
        const preservedFields = {
          salesPersonCode: values.salesPersonCode,
          customerCode: values.customerCode,
          priority: values.priority,
          shippingMethod: values.shippingMethod,
          isDropShipping: values.isDropShipping,
        };

        // 重置表单，保留部分字段
        form.resetFields();
        form.setFieldsValue({
          ...preservedFields,
          orderDate: dayjs(),
          isReleased: false,
        });

        // 清空订单明细
        setDetails([]);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      if (error instanceof Error && error.message !== 'Validation failed') {
        logError('保存订单', error);
        message.error(getErrorMessage(error));
      }
    } finally {
      setSaving(false);
    }
  };

  // 确认订单
  const handleConfirm = async () => {
    if (!id) return;

    Modal.confirm({
      title: '确认订单',
      content: '确定要确认这个订单吗？确认后将无法修改订单信息。',
      onOk: async () => {
        try {
          const response = await confirmSalesOrder(id, {});
          const result = handleApiResponse(response, '确认成功', '确认失败');

          if (result.success) {
            message.success(result.message);
            fetchOrderDetail();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('确认订单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 发货
  const handleShip = async () => {
    if (!id) return;

    Modal.confirm({
      title: '订单发货',
      content: '确定要将订单标记为已发货吗？',
      onOk: async () => {
        try {
          const response = await shipSalesOrder(id, {
            shippingDate: dayjs().format('YYYY-MM-DD'),
          });
          const result = handleApiResponse(response, '发货成功', '发货失败');

          if (result.success) {
            message.success(result.message);
            fetchOrderDetail();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('订单发货', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 完成订单
  const handleComplete = async () => {
    if (!id) return;

    Modal.confirm({
      title: '完成订单',
      content: '确定要完成这个订单吗？',
      onOk: async () => {
        try {
          const response = await completeSalesOrder(id, {
            completedDate: dayjs().format('YYYY-MM-DD'),
          });
          const result = handleApiResponse(response, '完成成功', '完成失败');

          if (result.success) {
            message.success(result.message);
            fetchOrderDetail();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('完成订单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 取消订单
  const handleCancel = async () => {
    if (!id) return;

    Modal.confirm({
      title: '取消订单',
      content: '确定要取消这个订单吗？取消后无法恢复。',
      onOk: async () => {
        try {
          const response = await cancelSalesOrder(id);
          const result = handleApiResponse(response, '取消成功', '取消失败');

          if (result.success) {
            message.success(result.message);
            fetchOrderDetail();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('取消订单', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 添加商品明细
  const handleAddDetail = () => {
    setEditingDetail({
      productCode: '',
      colorCode: '',
      sizeCode: '',
      quantity: 1,
      priceType: 'retail',
      unitPrice: 0,
      product: null,
    });
    setEditingIndex(-1);
    setDetailModalVisible(true);
  };

  // 编辑商品明细
  const handleEditDetail = (record: ProductDetailFormData, index: number) => {
    setEditingDetail({ ...record });
    setEditingIndex(index);
    setDetailModalVisible(true);
  };

  // 删除商品明细
  const handleDeleteDetail = (index: number) => {
    const newDetails = [...details];
    newDetails.splice(index, 1);
    setDetails(newDetails);
  };

  // 计算总金额
  const calculateTotalAmount = () => {
    return details.reduce((total, detail) => {
      const itemTotal = detail.quantity * detail.unitPrice - (detail.discountAmount || 0);
      return total + itemTotal;
    }, 0);
  };

  // 计算总数量
  const calculateTotalQuantity = () => {
    return details.reduce((total, detail) => total + detail.quantity, 0);
  };

  // 获取状态配置
  const getStatusConfig = (status?: SalesOrderStatus) => {
    if (!status) return { text: '草稿', color: 'gray' };
    return SALES_ORDER_STATUS_CONFIG[status];
  };

  const statusConfig = getStatusConfig(order?.status);
  const canEdit = !isView && (!order || order.status === 'draft');
  const canConfirm = order && order.status === 'draft';
  const canShip = order && (order.status === 'confirmed' || order.status === 'shipped');
  const canComplete = order && (order.status === 'shipped' || order.status === 'delivered');
  const canCancel = order && order.status === 'draft';

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // F8 - 保存
      if (event.key === 'F8' && canEdit) {
        event.preventDefault();
        handleSave();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [canEdit, handleSave]);

  return (
    <div style={{ padding: '16px' }}>
      <Card>
        {/* 页面头部 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '24px',
          }}
        >
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              返回列表
            </Button>
            <h2 style={{ margin: 0 }}>
              {!id ? '新增销售订单' : isEdit ? '编辑销售订单' : '查看销售订单'}
            </h2>
            {order && <Tag color={statusConfig.color}>{statusConfig.text}</Tag>}
          </Space>
          <Space>
            {canEdit && (
              <>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSave}
                  loading={saving}
                >
                  保存 (F8)
                </Button>
                {!id && (
                  <Button
                    type="primary"
                    ghost
                    icon={<PlusOutlined />}
                    onClick={handleSaveAndNew}
                    loading={saving}
                  >
                    保存并新建
                  </Button>
                )}
              </>
            )}
            {canConfirm && hasRoutePermission('sales/orders', 'update') && (
              <Button type="primary" icon={<CheckOutlined />} onClick={handleConfirm}>
                确认订单
              </Button>
            )}
            {canShip && hasRoutePermission('sales/orders', 'update') && (
              <Button type="primary" icon={<SendOutlined />} onClick={handleShip}>
                发货
              </Button>
            )}
            {canComplete && hasRoutePermission('sales/orders', 'update') && (
              <Button type="primary" icon={<CheckOutlined />} onClick={handleComplete}>
                完成订单
              </Button>
            )}
            {canCancel && hasRoutePermission('sales/orders', 'delete') && (
              <Button danger icon={<CloseOutlined />} onClick={handleCancel}>
                取消订单
              </Button>
            )}
          </Space>
        </div>

        {/* 订单信息 */}
        {order && isView && (
          <>
            <Descriptions
              title="订单信息"
              bordered
              column={{ xs: 1, sm: 2, md: 3 }}
              style={{ marginBottom: '24px' }}
            >
              <Descriptions.Item label="订单编号">{order.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="客户">{order.customerName}</Descriptions.Item>
              <Descriptions.Item label="销售人员">{order.salesPersonName}</Descriptions.Item>
              <Descriptions.Item label="订单日期">
                {dayjs(order.orderDate).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="预计交货日期">
                {order.expectedDeliveryDate
                  ? dayjs(order.expectedDeliveryDate).format('YYYY-MM-DD')
                  : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="优先级">
                {order.priority ? ORDER_PRIORITY_CONFIG[order.priority].text : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="总金额">
                {formatAmount(order.totalAmount || 0)}
              </Descriptions.Item>
              <Descriptions.Item label="总数量">{order.totalQuantity || 0}</Descriptions.Item>
              <Descriptions.Item label="代发">
                {order.isDropShipping ? '是' : '否'}
              </Descriptions.Item>
              <Descriptions.Item label="放单">{order.isReleased ? '是' : '否'}</Descriptions.Item>
              <Descriptions.Item label="物流方式">
                {order.shippingMethod ? SHIPPING_METHOD_CONFIG[order.shippingMethod] : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="运费">
                {order.shippingFee ? `¥${order.shippingFee.toFixed(2)}` : '-'}
              </Descriptions.Item>
              {order.remark && (
                <Descriptions.Item label="备注" span={3}>
                  {order.remark}
                </Descriptions.Item>
              )}
            </Descriptions>
            <Divider />
          </>
        )}

        {/* 表单 */}
        {canEdit && (
          <Form form={form} layout="vertical" disabled={loading}>
            <Row gutter={16}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  label="销售人员"
                  name="salesPersonCode"
                  rules={[{ required: true, message: '请选择销售人员' }]}
                >
                  <UserSearchSelector placeholder="请选择销售人员" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  label="客户"
                  name="customerCode"
                  rules={[{ required: true, message: '请选择客户' }]}
                >
                  <CustomerSearchSelector
                    placeholder="请选择客户"
                    onChange={(_, customer) => {
                      // 当选择客户时，如果客户有负责人，自动设置为销售人员
                      if (customer && customer.managerCode) {
                        form.setFieldsValue({
                          salesPersonCode: customer.managerCode,
                        });
                      }
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  label="订单日期"
                  name="orderDate"
                  rules={[{ required: true, message: '请选择订单日期' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="预计交货日期" name="expectedDeliveryDate">
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="优先级" name="priority">
                  <Select placeholder="请选择优先级">
                    {Object.entries(ORDER_PRIORITY_CONFIG).map(([key, config]) => (
                      <Option key={key} value={key}>
                        {config.text}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="物流方式" name="shippingMethod">
                  <Select placeholder="请选择物流方式">
                    {Object.entries(SHIPPING_METHOD_CONFIG).map(([key, text]) => (
                      <Option key={key} value={key}>
                        {text}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="运费金额" name="shippingFee">
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="请输入运费金额"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item label="物流公司" name="shippingCompany">
                  <Input placeholder="请输入物流公司" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24} sm={12}>
                <Form.Item label="代发" name="isDropShipping" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item label="放单" name="isReleased" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            {/* 代发信息 */}
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isDropShipping !== currentValues.isDropShipping
              }
            >
              {({ getFieldValue }) => {
                const isDropShipping = getFieldValue('isDropShipping');
                return isDropShipping ? (
                  <>
                    <Divider orientation="left">代发信息</Divider>
                    <Row gutter={16}>
                      <Col xs={24} sm={12} md={8}>
                        <Form.Item
                          label="收货人姓名"
                          name="dropShipReceiverName"
                          rules={[{ required: true, message: '请输入收货人姓名' }]}
                        >
                          <Input placeholder="请输入收货人姓名" />
                        </Form.Item>
                      </Col>
                      <Col xs={24} sm={12} md={8}>
                        <Form.Item
                          label="收货人电话"
                          name="dropShipReceiverPhone"
                          rules={[{ required: true, message: '请输入收货人电话' }]}
                        >
                          <Input placeholder="请输入收货人电话" />
                        </Form.Item>
                      </Col>
                      <Col xs={24} sm={12} md={8}>
                        <Form.Item label="收货省份" name="dropShipProvinceCode">
                          <ProvinceSelector placeholder="请选择省份" />
                        </Form.Item>
                      </Col>
                      <Col xs={24} sm={12} md={8}>
                        <Form.Item label="收货城市" name="dropShipCity">
                          <Input placeholder="请输入城市" />
                        </Form.Item>
                      </Col>
                      <Col xs={24} sm={12} md={8}>
                        <Form.Item label="收货区县" name="dropShipDistrict">
                          <Input placeholder="请输入区县" />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row gutter={16}>
                      <Col xs={24}>
                        <Form.Item
                          label="收货地址"
                          name="dropShipAddress"
                          rules={[{ required: true, message: '请输入收货地址' }]}
                        >
                          <TextArea rows={2} placeholder="请输入详细收货地址" />
                        </Form.Item>
                      </Col>
                      <Col xs={24}>
                        <Form.Item label="特殊要求" name="dropShipSpecialRequirements">
                          <TextArea rows={2} placeholder="请输入特殊要求" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </>
                ) : null;
              }}
            </Form.Item>

            <Form.Item label="备注" name="remark">
              <TextArea rows={3} placeholder="请输入备注信息" />
            </Form.Item>
          </Form>
        )}

        {/* 商品明细 */}
        <Card title="商品明细" style={{ marginTop: '16px' }}>
          <div style={{ marginBottom: '16px' }}>
            <Space>
              {canEdit && (
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddDetail}>
                  添加商品
                </Button>
              )}
              <span style={{ color: '#666' }}>
                总数量: {calculateTotalQuantity()} | 总金额: {formatAmount(calculateTotalAmount())}
              </span>
            </Space>
          </div>

          <Table
            columns={getDetailColumns()}
            dataSource={details}
            rowKey={(record, index) =>
              `${record.productCode}-${record.colorCode}-${record.sizeCode}-${index}`
            }
            pagination={false}
            scroll={{ x: 1200 }}
            size="small"
          />
        </Card>

        {/* 商品明细弹窗 */}
        <ProductDetailModal
          visible={detailModalVisible}
          detail={editingDetail}
          onOk={handleDetailModalOk}
          onCancel={() => setDetailModalVisible(false)}
        />
      </Card>
    </div>
  );

  // 获取明细表格列配置
  function getDetailColumns(): ColumnsType<ProductDetailFormData> {
    const columns: ColumnsType<ProductDetailFormData> = [
      {
        title: '商品编码',
        dataIndex: 'productCode',
        key: 'productCode',
        width: 120,
      },
      {
        title: '商品名称',
        dataIndex: 'productName',
        key: 'productName',
        width: 150,
        render: (_, record) => record.product?.name || '-',
      },
      {
        title: '颜色',
        dataIndex: 'colorCode',
        key: 'colorCode',
        width: 80,
        render: (colorCode, record) => {
          const color = record.product?.colorCombinations?.find(
            (c: any) => c.colorCode === colorCode,
          );
          return color?.colorName || colorCode;
        },
      },
      {
        title: '尺寸',
        dataIndex: 'sizeCode',
        key: 'sizeCode',
        width: 80,
      },
      {
        title: 'SKU',
        key: 'skuCode',
        width: 150,
        render: (_, record) => `${record.productCode}-${record.colorCode}-${record.sizeCode}`,
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity',
        width: 80,
        align: 'right',
      },
      {
        title: '价格类型',
        dataIndex: 'priceType',
        key: 'priceType',
        width: 100,
        render: (priceType: PriceType) => PRICE_TYPE_CONFIG[priceType],
      },
      {
        title: '单价',
        dataIndex: 'unitPrice',
        key: 'unitPrice',
        width: 100,
        align: 'right',
        render: (price: number) => `¥${price.toFixed(2)}`,
      },
      {
        title: '折扣金额',
        dataIndex: 'discountAmount',
        key: 'discountAmount',
        width: 100,
        align: 'right',
        render: (amount?: number) => (amount ? formatAmount(amount) : '-'),
      },
      {
        title: '小计',
        key: 'totalAmount',
        width: 100,
        align: 'right',
        render: (_, record) => {
          const total = record.quantity * record.unitPrice - (record.discountAmount || 0);
          return formatAmount(total);
        },
      },
      {
        title: '预计发货日期',
        dataIndex: 'expectedShipDate',
        key: 'expectedShipDate',
        width: 120,
        render: (date?: string) => (date ? dayjs(date).format('YYYY-MM-DD') : '-'),
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        width: 120,
        ellipsis: true,
      },
    ];

    if (canEdit) {
      columns.push({
        title: '操作',
        key: 'action',
        width: 100,
        fixed: 'right',
        render: (_, record, index) => (
          <Space size="small">
            <Button type="text" size="small" onClick={() => handleEditDetail(record, index)}>
              编辑
            </Button>
            <Button type="text" size="small" danger onClick={() => handleDeleteDetail(index)}>
              删除
            </Button>
          </Space>
        ),
      });
    }

    return columns;
  }

  // 处理明细弹窗确认
  function handleDetailModalOk(detail: ProductDetailFormData) {
    const newDetails = [...details];
    if (editingIndex >= 0) {
      newDetails[editingIndex] = detail;
    } else {
      newDetails.push(detail);
    }
    setDetails(newDetails);
    setDetailModalVisible(false);
  }
};

// 商品明细弹窗组件
interface ProductDetailModalProps {
  visible: boolean;
  detail: ProductDetailFormData | null;
  onOk: (detail: ProductDetailFormData) => void;
  onCancel: () => void;
}

const ProductDetailModal: React.FC<ProductDetailModalProps> = ({
  visible,
  detail,
  onOk,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [selectedProduct, setSelectedProduct] = useState<ProductOrderInfo | null>(null);

  useEffect(() => {
    if (visible && detail) {
      form.setFieldsValue({
        ...detail,
        expectedShipDate: detail.expectedShipDate ? dayjs(detail.expectedShipDate) : null,
      });
      setSelectedProduct(detail.product || null);
    } else if (visible) {
      form.resetFields();
      setSelectedProduct(null);
    }
  }, [visible, detail, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const detailData: ProductDetailFormData = {
        ...values,
        expectedShipDate: values.expectedShipDate?.format('YYYY-MM-DD'),
        product: selectedProduct,
        productName: selectedProduct?.name,
      };
      onOk(detailData);
    } catch (error) {
      // 表单验证失败
    }
  };

  const handleProductSelect = (product: ProductOrderInfo | null) => {
    setSelectedProduct(product);
    if (product) {
      // 自动设置价格
      form.setFieldsValue({
        unitPrice: product.retailPrice,
      });
      // 清空颜色和尺寸选择
      form.setFieldsValue({
        colorCode: undefined,
        sizeCode: undefined,
      });
    }
  };

  const handleColorChange = () => {
    // 清空尺寸选择
    form.setFieldsValue({
      sizeCode: undefined,
    });
  };

  return (
    <Modal
      title={detail ? '编辑商品明细' : '添加商品明细'}
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          quantity: 1,
          priceType: 'retail',
          unitPrice: 0,
          discountAmount: 0,
        }}
      >
        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              label="商品编码"
              name="productCode"
              rules={[{ required: true, message: '请输入商品编码' }]}
            >
              <ProductCodeInputEnhanced
                placeholder="请输入商品编码"
                onProductSelect={handleProductSelect}
                showStockInfo={true}
                showPriceInfo={true}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              label="颜色"
              name="colorCode"
              rules={[{ required: true, message: '请选择颜色' }]}
            >
              <ProductColorSelectorEnhanced
                product={selectedProduct}
                onChange={handleColorChange}
                showStockInfo={true}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.colorCode !== currentValues.colorCode
              }
            >
              {({ getFieldValue }) => (
                <Form.Item
                  label="尺寸"
                  name="sizeCode"
                  rules={[{ required: true, message: '请选择尺寸' }]}
                >
                  <ProductSizeSelectorEnhanced
                    product={selectedProduct}
                    selectedColorCode={getFieldValue('colorCode')}
                    showStockInfo={true}
                  />
                </Form.Item>
              )}
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              label="数量"
              name="quantity"
              rules={[
                { required: true, message: '请输入数量' },
                { type: 'number', min: 1, message: '数量必须大于0' },
              ]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              label="价格类型"
              name="priceType"
              rules={[{ required: true, message: '请选择价格类型' }]}
            >
              <Select>
                {Object.entries(PRICE_TYPE_CONFIG).map(([key, text]) => (
                  <Option key={key} value={key}>
                    {text}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              label="单价"
              name="unitPrice"
              rules={[
                { required: true, message: '请输入单价' },
                { type: 'number', min: 0, message: '单价不能小于0' },
              ]}
            >
              <InputNumber min={0} precision={2} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item label="折扣金额" name="discountAmount">
              <InputNumber min={0} precision={2} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item label="预计发货日期" name="expectedShipDate">
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col xs={24}>
            <Form.Item label="备注" name="remark">
              <TextArea rows={2} placeholder="请输入备注" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default SalesOrderDetail;
