.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filterCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filterSection {
  padding: 8px 0;
}

.statisticCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.statisticCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 图标样式 */
.demandIcon {
  color: #1890ff;
  font-size: 20px;
}

.purchaseIcon {
  color: #52c41a;
  font-size: 20px;
}

.rateIcon {
  color: #faad14;
  font-size: 20px;
}

.amountIcon {
  color: #f5222d;
  font-size: 20px;
}

/* 链接卡片 */
.linkCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.linkCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.linkContent {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.linkIcon {
  font-size: 32px;
  color: #1890ff;
  flex-shrink: 0;
}

.linkTitle {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.linkDescription {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 统计数值样式 */
.statisticCard .ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.statisticCard .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
  
  .filterSection .ant-space {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .filterSection {
    padding: 4px 0;
  }
  
  .linkContent {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .linkIcon {
    font-size: 28px;
  }
  
  .statisticCard .ant-statistic-content {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  .filterSection .ant-space {
    width: 100%;
  }
  
  .filterSection .ant-space > * {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .linkContent {
    padding: 4px;
  }
}
