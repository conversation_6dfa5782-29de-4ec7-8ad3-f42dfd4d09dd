import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  DatePicker,
  Space,
  Button,
  message,
  Spin,
  Table,
  Tabs,
} from 'antd';
import { ReloadOutlined, FileTextOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { getDemandOrderStatistics } from '@/api/DemandPurchaseStatisticsApi';
import type { GetDemandOrdersParams } from '@/types/demandPurchase';
import { UserSearchSelector } from '@/components';
import { Pie, Line } from '@/components/charts';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './DemandOrderStatistics.module.css';

const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

interface DemandOrderStats {
  totalOrders: number;
  totalQuantity: number;
  totalAmount: number;
  avgAmount: number;
  statusBreakdown: { [status: string]: number };
  priorityBreakdown: { [priority: string]: number };
  salesPersonBreakdown: Array<{
    salesPersonCode: string;
    orderCount: number;
    totalQuantity: number;
    totalAmount: number;
  }>;
  monthlyTrend: Array<{
    month: string;
    orderCount: number;
    totalQuantity: number;
    totalAmount: number;
  }>;
}

const DemandOrderStatistics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<DemandOrderStats | null>(null);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [salesPersonCode, setSalesPersonCode] = useState<string>('');

  // 获取统计数据
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const params: GetDemandOrdersParams = {
        startDate: dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: dateRange?.[1]?.format('YYYY-MM-DD'),
        salesPersonCode: salesPersonCode || undefined,
      };

      const response = await getDemandOrderStatistics(params);
      const result = handleApiResponse(response, '', '获取需求订单统计失败');

      if (result.success && response.data) {
        setStats(response.data);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取需求订单统计', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchStatistics();
  }, []);

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  // 销售人员筛选处理
  const handleSalesPersonChange = (value: string) => {
    setSalesPersonCode(value);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchStatistics();
  };

  // 应用筛选
  const handleApplyFilter = () => {
    fetchStatistics();
  };

  // 状态分布数据
  const statusData = stats?.statusBreakdown
    ? Object.entries(stats.statusBreakdown).map(([key, value]) => ({
        x: getStatusName(key),
        y: value,
      }))
    : [];

  // 优先级分布数据
  const priorityData = stats?.priorityBreakdown
    ? Object.entries(stats.priorityBreakdown).map(([key, value]) => ({
        x: getPriorityName(key),
        y: value,
      }))
    : [];

  // 月度趋势数据
  const monthlyTrendData =
    stats?.monthlyTrend?.map((item) => ({
      x: item.month,
      y: item.orderCount,
    })) || [];

  // 销售人员表格列定义
  const salesPersonColumns = [
    {
      title: '销售人员',
      dataIndex: 'salesPersonCode',
      key: 'salesPersonCode',
    },
    {
      title: '订单数',
      dataIndex: 'orderCount',
      key: 'orderCount',
      sorter: (a: any, b: any) => a.orderCount - b.orderCount,
    },
    {
      title: '总数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      sorter: (a: any, b: any) => a.totalQuantity - b.totalQuantity,
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      sorter: (a: any, b: any) => a.totalAmount - b.totalAmount,
      render: (amount: number) => `¥${amount.toFixed(2)}`,
    },
    {
      title: '平均订单金额',
      key: 'avgAmount',
      render: (_: any, record: any) => `¥${(record.totalAmount / record.orderCount).toFixed(2)}`,
    },
  ];

  return (
    <div className={styles.container}>
      <Card className={styles.filterCard}>
        <div className={styles.filterSection}>
          <Row gutter={16} align="middle">
            <Col>
              <Space size="middle">
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  placeholder={['开始日期', '结束日期']}
                  style={{ width: 240 }}
                />
                <UserSearchSelector
                  placeholder="选择销售人员"
                  allowClear
                  style={{ width: 150 }}
                  onChange={handleSalesPersonChange}
                  value={salesPersonCode || undefined}
                />
                <Button type="primary" onClick={handleApplyFilter} loading={loading}>
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
                  刷新
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      </Card>

      <Spin spinning={loading}>
        {/* 汇总统计 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="总订单数"
                value={stats?.totalOrders || 0}
                prefix={<FileTextOutlined className={styles.icon} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="总数量"
                value={stats?.totalQuantity || 0}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="总金额"
                value={stats?.totalAmount || 0}
                precision={2}
                prefix="¥"
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="平均金额"
                value={stats?.avgAmount || 0}
                precision={2}
                prefix="¥"
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 图表和表格 */}
        <Tabs defaultActiveKey="charts" className={styles.tabs}>
          <TabPane tab="图表分析" key="charts">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card className={styles.chartCard}>
                  <Pie title="状态分布" dataSource={statusData} height={300} />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card className={styles.chartCard}>
                  <Pie title="优先级分布" dataSource={priorityData} height={300} />
                </Card>
              </Col>
              <Col xs={24}>
                <Card className={styles.chartCard}>
                  <Line
                    title="月度趋势"
                    dataSource={monthlyTrendData}
                    height={400}
                    yaxisText="订单数"
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="销售人员分析" key="salesperson">
            <Card>
              <Table
                columns={salesPersonColumns}
                dataSource={stats?.salesPersonBreakdown || []}
                rowKey="salesPersonCode"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                }}
                className={styles.table}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

// 状态名称映射
const getStatusName = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消',
  };
  return statusMap[status] || status;
};

// 优先级名称映射
const getPriorityName = (priority: string): string => {
  const priorityMap: { [key: string]: string } = {
    low: '低优先级',
    medium: '中优先级',
    high: '高优先级',
    urgent: '紧急',
  };
  return priorityMap[priority] || priority;
};

export default DemandOrderStatistics;
