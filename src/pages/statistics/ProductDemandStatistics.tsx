import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  DatePicker,
  Space,
  Button,
  message,
  Spin,
  Table,
  Tabs,
  Progress,
} from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { getProductDemandStatistics } from '@/api/DemandPurchaseStatisticsApi';
import type { GetProductDemandParams } from '@/types/demandPurchase';
import { BrandSearchSelector, ProductCategorySelector } from '@/components';
import { Pie } from '@/components/charts';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './ProductDemandStatistics.module.css';

const { RangePicker } = DatePicker;

interface ProductDemandStats {
  topDemandProducts: Array<{
    productCode: string;
    totalDemand: number;
    totalShortage: number;
    demandOrderCount: number;
    avgUnitPrice: number;
  }>;
  brandAnalysis: Array<{
    brandCode: string;
    totalDemand: number;
    totalShortage: number;
    productCount: number;
  }>;
  supplierAnalysis: Array<{
    supplierCode: string;
    totalDemand: number;
    totalShortage: number;
    productCount: number;
  }>;
}

const ProductDemandStatistics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<ProductDemandStats | null>(null);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [brandCode, setBrandCode] = useState<string>('');
  const [categoryCode, setCategoryCode] = useState<string>('');

  // 获取统计数据
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const params: GetProductDemandParams = {
        startDate: dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: dateRange?.[1]?.format('YYYY-MM-DD'),
        brandCode: brandCode || undefined,
        categoryCode: categoryCode || undefined,
      };

      const response = await getProductDemandStatistics(params);
      const result = handleApiResponse(response, '', '获取商品需求统计失败');

      if (result.success && response.data) {
        setStats(response.data);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取商品需求统计', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchStatistics();
  }, []);

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  // 品牌筛选处理
  const handleBrandChange = (value: string) => {
    setBrandCode(value);
  };

  // 分类筛选处理
  const handleCategoryChange = (value: string) => {
    setCategoryCode(value);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchStatistics();
  };

  // 应用筛选
  const handleApplyFilter = () => {
    fetchStatistics();
  };

  // 热门商品表格列定义
  const productColumns = [
    {
      title: '商品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 150,
    },
    {
      title: '总需求量',
      dataIndex: 'totalDemand',
      key: 'totalDemand',
      sorter: (a: any, b: any) => a.totalDemand - b.totalDemand,
      render: (value: number) => <span className={styles.demandValue}>{value}</span>,
    },
    {
      title: '缺货量',
      dataIndex: 'totalShortage',
      key: 'totalShortage',
      sorter: (a: any, b: any) => a.totalShortage - b.totalShortage,
      render: (value: number) => <span className={styles.shortageValue}>{value}</span>,
    },
    {
      title: '满足率',
      key: 'fulfillmentRate',
      render: (_: any, record: any) => {
        const rate = ((record.totalDemand - record.totalShortage) / record.totalDemand) * 100;
        return (
          <div className={styles.progressCell}>
            <Progress
              percent={rate}
              size="small"
              strokeColor={rate >= 80 ? '#52c41a' : rate >= 60 ? '#faad14' : '#ff4d4f'}
            />
            <span className={styles.progressText}>{rate.toFixed(1)}%</span>
          </div>
        );
      },
    },
    {
      title: '需求订单数',
      dataIndex: 'demandOrderCount',
      key: 'demandOrderCount',
      sorter: (a: any, b: any) => a.demandOrderCount - b.demandOrderCount,
    },
    {
      title: '平均单价',
      dataIndex: 'avgUnitPrice',
      key: 'avgUnitPrice',
      sorter: (a: any, b: any) => a.avgUnitPrice - b.avgUnitPrice,
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
  ];

  // 品牌分析表格列定义
  const brandColumns = [
    {
      title: '品牌编码',
      dataIndex: 'brandCode',
      key: 'brandCode',
    },
    {
      title: '总需求量',
      dataIndex: 'totalDemand',
      key: 'totalDemand',
      sorter: (a: any, b: any) => a.totalDemand - b.totalDemand,
    },
    {
      title: '缺货量',
      dataIndex: 'totalShortage',
      key: 'totalShortage',
      sorter: (a: any, b: any) => a.totalShortage - b.totalShortage,
    },
    {
      title: '商品种类',
      dataIndex: 'productCount',
      key: 'productCount',
      sorter: (a: any, b: any) => a.productCount - b.productCount,
    },
    {
      title: '满足率',
      key: 'fulfillmentRate',
      render: (_: any, record: any) => {
        const rate = ((record.totalDemand - record.totalShortage) / record.totalDemand) * 100;
        return `${rate.toFixed(1)}%`;
      },
    },
  ];

  // 供应商分析表格列定义
  const supplierColumns = [
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      key: 'supplierCode',
    },
    {
      title: '总需求量',
      dataIndex: 'totalDemand',
      key: 'totalDemand',
      sorter: (a: any, b: any) => a.totalDemand - b.totalDemand,
    },
    {
      title: '缺货量',
      dataIndex: 'totalShortage',
      key: 'totalShortage',
      sorter: (a: any, b: any) => a.totalShortage - b.totalShortage,
    },
    {
      title: '商品种类',
      dataIndex: 'productCount',
      key: 'productCount',
      sorter: (a: any, b: any) => a.productCount - b.productCount,
    },
    {
      title: '满足率',
      key: 'fulfillmentRate',
      render: (_: any, record: any) => {
        const rate = ((record.totalDemand - record.totalShortage) / record.totalDemand) * 100;
        return `${rate.toFixed(1)}%`;
      },
    },
  ];

  // 品牌需求分布数据
  const brandDemandData =
    stats?.brandAnalysis?.slice(0, 10).map((item) => ({
      x: item.brandCode,
      y: item.totalDemand,
    })) || [];

  // 供应商需求分布数据
  const supplierDemandData =
    stats?.supplierAnalysis?.slice(0, 10).map((item) => ({
      x: item.supplierCode,
      y: item.totalDemand,
    })) || [];

  return (
    <div className={styles.container}>
      <Card className={styles.filterCard}>
        <div className={styles.filterSection}>
          <Row gutter={16} align="middle">
            <Col>
              <Space size="middle">
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  placeholder={['开始日期', '结束日期']}
                  style={{ width: 240 }}
                />
                <BrandSearchSelector
                  placeholder="选择品牌"
                  allowClear
                  style={{ width: 150 }}
                  onChange={handleBrandChange}
                  value={brandCode || undefined}
                />
                <ProductCategorySelector
                  placeholder="选择分类"
                  allowClear
                  style={{ width: 150 }}
                  onChange={handleCategoryChange}
                  value={categoryCode || undefined}
                  showAll
                />
                <Button type="primary" onClick={handleApplyFilter} loading={loading}>
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
                  刷新
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      </Card>

      <Spin spinning={loading}>
        <Tabs
          defaultActiveKey="products"
          className={styles.tabs}
          items={[
            {
              key: 'products',
              label: '热门商品',
              children: (
                <Card>
                  <Table
                    columns={productColumns}
                    dataSource={stats?.topDemandProducts || []}
                    rowKey="productCode"
                    pagination={{
                      pageSize: 20,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    }}
                    className={styles.table}
                  />
                </Card>
              ),
            },
            {
              key: 'brands',
              label: '品牌分析',
              children: (
                <Row gutter={[16, 16]}>
                  <Col xs={24} lg={12}>
                    <Card className={styles.chartCard}>
                      <Pie title="品牌需求分布" dataSource={brandDemandData} height={400} />
                    </Card>
                  </Col>
                  <Col xs={24} lg={12}>
                    <Card>
                      <Table
                        columns={brandColumns}
                        dataSource={stats?.brandAnalysis || []}
                        rowKey="brandCode"
                        pagination={{
                          pageSize: 10,
                          showSizeChanger: false,
                          showQuickJumper: false,
                        }}
                        size="small"
                        className={styles.table}
                      />
                    </Card>
                  </Col>
                </Row>
              ),
            },
            {
              key: 'suppliers',
              label: '供应商分析',
              children: (
                <Row gutter={[16, 16]}>
                  <Col xs={24} lg={12}>
                    <Card className={styles.chartCard}>
                      <Pie title="供应商需求分布" dataSource={supplierDemandData} height={400} />
                    </Card>
                  </Col>
                  <Col xs={24} lg={12}>
                    <Card>
                      <Table
                        columns={supplierColumns}
                        dataSource={stats?.supplierAnalysis || []}
                        rowKey="supplierCode"
                        pagination={{
                          pageSize: 10,
                          showSizeChanger: false,
                          showQuickJumper: false,
                        }}
                        size="small"
                        className={styles.table}
                      />
                    </Card>
                  </Col>
                </Row>
              ),
            },
          ]}
        />
      </Spin>
    </div>
  );
};

export default ProductDemandStatistics;
