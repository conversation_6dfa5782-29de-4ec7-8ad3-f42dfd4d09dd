.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filterCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filterSection {
  padding: 8px 0;
}

.statisticCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.statisticCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.icon {
  color: #1890ff;
  font-size: 20px;
}

.tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chartCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table {
  background: white;
  border-radius: 8px;
}

/* 统计数值样式 */
.statisticCard .ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.statisticCard .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
  
  .filterSection .ant-space {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .filterSection {
    padding: 4px 0;
  }
  
  .statisticCard .ant-statistic-content {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  .filterSection .ant-space {
    width: 100%;
  }
  
  .filterSection .ant-space > * {
    width: 100%;
    margin-bottom: 8px;
  }
}
