import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, DatePicker, Space, Button, message, Spin } from 'antd';
import {
  ReloadOutlined,
  ShoppingCartOutlined,
  FileTextOutlined,
  DollarOutlined,
  PercentageOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { getDashboardStatistics } from '@/api/DemandPurchaseStatisticsApi';
import type { GetStatisticsParams } from '@/types/demandPurchase';
import { BrandSearchSelector, SupplierSearchSelector, UserSearchSelector } from '@/components';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './DemandPurchaseStatistics.module.css';

const { RangePicker } = DatePicker;

interface StatisticsSummary {
  totalDemandOrders: number;
  totalPurchaseOrders: number;
  totalDemandQuantity: number;
  totalPurchaseQuantity: number;
  totalDemandAmount: number;
  totalPurchaseAmount: number;
  conversionRate: number;
  fulfillmentRate: number;
}

const DemandPurchaseStatistics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [summary, setSummary] = useState<StatisticsSummary | null>(null);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [brandCode, setBrandCode] = useState<string>('');
  const [supplierCode, setSupplierCode] = useState<string>('');
  const [salesPersonCode, setSalesPersonCode] = useState<string>('');

  // 获取统计数据
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const params: GetStatisticsParams = {
        startDate: dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: dateRange?.[1]?.format('YYYY-MM-DD'),
        brandCode: brandCode || undefined,
        supplierCode: supplierCode || undefined,
        salesPersonCode: salesPersonCode || undefined,
      };

      const response = await getDashboardStatistics(params);
      const result = handleApiResponse(response, '', '获取统计数据失败');

      if (result.success && response.data) {
        setSummary(response.data.summary);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取统计数据', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchStatistics();
  }, []);

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  // 品牌筛选处理
  const handleBrandChange = (value: string) => {
    setBrandCode(value);
  };

  // 供应商筛选处理
  const handleSupplierChange = (value: string) => {
    setSupplierCode(value);
  };

  // 销售人员筛选处理
  const handleSalesPersonChange = (value: string) => {
    setSalesPersonCode(value);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchStatistics();
  };

  // 应用筛选
  const handleApplyFilter = () => {
    fetchStatistics();
  };

  return (
    <div className={styles.container}>
      <Card className={styles.filterCard}>
        <div className={styles.filterSection}>
          <Row gutter={16} align="middle">
            <Col>
              <Space size="middle">
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  placeholder={['开始日期', '结束日期']}
                  style={{ width: 240 }}
                />
                <BrandSearchSelector
                  placeholder="选择品牌"
                  allowClear
                  style={{ width: 150 }}
                  onChange={handleBrandChange}
                  value={brandCode || undefined}
                />
                <SupplierSearchSelector
                  placeholder="选择供应商"
                  allowClear
                  style={{ width: 150 }}
                  onChange={handleSupplierChange}
                  value={supplierCode || undefined}
                />
                <UserSearchSelector
                  placeholder="选择销售人员"
                  allowClear
                  style={{ width: 150 }}
                  onChange={handleSalesPersonChange}
                  value={salesPersonCode || undefined}
                />
                <Button type="primary" onClick={handleApplyFilter} loading={loading}>
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
                  刷新
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      </Card>

      <Spin spinning={loading}>
        <Row gutter={[16, 16]}>
          {/* 订单数量统计 */}
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="需求订单数"
                value={summary?.totalDemandOrders || 0}
                prefix={<FileTextOutlined className={styles.demandIcon} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="采购订单数"
                value={summary?.totalPurchaseOrders || 0}
                prefix={<ShoppingCartOutlined className={styles.purchaseIcon} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="转换率"
                value={summary?.conversionRate || 0}
                suffix="%"
                precision={1}
                prefix={<PercentageOutlined className={styles.rateIcon} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="满足率"
                value={summary?.fulfillmentRate || 0}
                suffix="%"
                precision={1}
                prefix={<PercentageOutlined className={styles.rateIcon} />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>

          {/* 数量统计 */}
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="需求数量"
                value={summary?.totalDemandQuantity || 0}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="采购数量"
                value={summary?.totalPurchaseQuantity || 0}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>

          {/* 金额统计 */}
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="需求金额"
                value={summary?.totalDemandAmount || 0}
                precision={2}
                prefix={<DollarOutlined className={styles.amountIcon} />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statisticCard}>
              <Statistic
                title="采购金额"
                value={summary?.totalPurchaseAmount || 0}
                precision={2}
                prefix={<DollarOutlined className={styles.amountIcon} />}
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 详细统计链接 */}
        <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
          <Col xs={24} sm={8}>
            <Card
              className={styles.linkCard}
              hoverable
              onClick={() => window.open('/statistics/demand-orders', '_blank')}
            >
              <div className={styles.linkContent}>
                <FileTextOutlined className={styles.linkIcon} />
                <div>
                  <div className={styles.linkTitle}>需求订单统计</div>
                  <div className={styles.linkDescription}>查看需求订单详细统计分析</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card
              className={styles.linkCard}
              hoverable
              onClick={() => window.open('/statistics/purchase-orders', '_blank')}
            >
              <div className={styles.linkContent}>
                <ShoppingCartOutlined className={styles.linkIcon} />
                <div>
                  <div className={styles.linkTitle}>采购订单统计</div>
                  <div className={styles.linkDescription}>查看采购订单详细统计分析</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card
              className={styles.linkCard}
              hoverable
              onClick={() => window.open('/statistics/product-demand', '_blank')}
            >
              <div className={styles.linkContent}>
                <DollarOutlined className={styles.linkIcon} />
                <div>
                  <div className={styles.linkTitle}>商品需求统计</div>
                  <div className={styles.linkDescription}>查看商品需求详细统计分析</div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default DemandPurchaseStatistics;
