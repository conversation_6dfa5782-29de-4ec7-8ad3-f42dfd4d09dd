.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filterCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filterSection {
  padding: 8px 0;
}

.tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chartCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table {
  background: white;
  border-radius: 8px;
}

/* 数据值样式 */
.demandValue {
  color: #1890ff;
  font-weight: 600;
}

.shortageValue {
  color: #ff4d4f;
  font-weight: 600;
}

/* 进度条单元格 */
.progressCell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progressText {
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
  
  .filterSection .ant-space {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .filterSection {
    padding: 4px 0;
  }
  
  .progressCell {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 576px) {
  .filterSection .ant-space {
    width: 100%;
  }
  
  .filterSection .ant-space > * {
    width: 100%;
    margin-bottom: 8px;
  }
}
