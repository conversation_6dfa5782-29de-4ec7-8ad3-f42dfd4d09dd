/* 佣金发货合同列表页面样式 */
.container {
  padding: 16px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.toolbar {
  margin-bottom: 16px;
}

.searchRow {
  margin-bottom: 8px;
}

.searchInput {
  height: 32px;
}

.filterRow {
  margin-bottom: 8px;
}

.filterSelector {
  height: 32px;
}

.dateRangePicker {
  height: 32px;
}

.amountRangeInput {
  height: 32px;
}

.actionButtons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.refreshButton {
  height: 36px;
  padding: 0 12px;
}

.addButton {
  height: 36px;
  padding: 0 16px;
}

/* 表格样式 */
.tableCard {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.table {
  font-size: 13px;
}

.table :global(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  font-size: 13px;
  padding: 12px 8px;
  border-bottom: 1px solid #e8e8e8;
}

.table :global(.ant-table-tbody > tr > td) {
  padding: 10px 8px;
  font-size: 13px;
  border-bottom: 1px solid #f0f0f0;
}

.table :global(.ant-table-tbody > tr:hover > td) {
  background: #f8f9fa;
}

/* 状态标签样式 */
.statusTag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.statusDraft {
  background: #f6f6f6;
  color: #666;
  border: 1px solid #d9d9d9;
}

.statusSubmitted {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.statusApproved {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.statusRejected {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.statusCompleted {
  background: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

.statusCancelled {
  background: #f5f5f5;
  color: #8c8c8c;
  border: 1px solid #d9d9d9;
}

/* 支付状态样式 */
.paymentUnpaid {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.paymentPartialPaid {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.paymentFullyPaid {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.paymentOverdue {
  background: #fff1f0;
  color: #f5222d;
  border: 1px solid #ffa39e;
}

/* 操作按钮样式 */
.actionButton {
  padding: 4px 8px;
  font-size: 12px;
  height: auto;
  line-height: 1.4;
  border-radius: 4px;
}

.viewButton {
  color: #1890ff;
  border-color: #1890ff;
}

.editButton {
  color: #52c41a;
  border-color: #52c41a;
}

.deleteButton {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.pdfButton {
  color: #722ed1;
  border-color: #722ed1;
}

/* 金额显示样式 */
.amountText {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #262626;
}

.positiveAmount {
  color: #52c41a;
}

.negativeAmount {
  color: #ff4d4f;
}

/* 逾期天数样式 */
.overdueDays {
  color: #ff4d4f;
  font-weight: 600;
}

.normalDays {
  color: #52c41a;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 12px;
  }

  .table {
    font-size: 12px;
  }

  .table :global(.ant-table-thead > tr > th),
  .table :global(.ant-table-tbody > tr > td) {
    padding: 8px 6px;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .searchRow,
  .filterRow {
    flex-direction: column;
    gap: 8px;
  }

  .searchInput,
  .filterSelector,
  .dateRangePicker,
  .amountRangeInput {
    width: 100%;
  }
}
