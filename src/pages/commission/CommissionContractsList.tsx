import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  DatePicker,
  Space,
  Tag,
  Tooltip,
  Row,
  Col,
  message,
  Modal,
  InputNumber,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  FilePdfOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { TablePaginationConfig, ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  getCommissionContractsList,
  deleteCommissionContract,
  exportCommissionContractPdf,
} from '@/api/CommissionContractsApi';
import {
  CommissionContractsListParams,
  CommissionContractStatus,
  PaymentStatus,
} from '@/types/commissionContracts';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import CompanySearchSelector from '@/components/CompanySearchSelector/CompanySearchSelector';
import styles from './CommissionContractsList.module.css';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface ContractItem {
  id: string;
  contractNumber: string;
  companyName: string;
  partyBName: string;
  partyBIdCard: string;
  applicationDate: string;
  applicationDateDisplay: string;
  actualShipmentAmount: number;
  requestedDebtAmount: number;
  totalAccumulatedDebt: number;
  paidAmount: number;
  remainingDebtAmount: number;
  repaymentDate: string;
  repaymentDateDisplay: string;
  overdueDays: number;
  overdueDaysText: string;
  status: string;
  paymentStatus: string;
  paymentStatusText: string;
  lastPaymentDate: string | null;
  createdAt: string;
  updatedAt: string;
}

const CommissionContractsList: React.FC = () => {
  const navigate = useNavigate();
  const [contracts, setContracts] = useState<ContractItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 搜索和筛选状态
  const [searchText, setSearchText] = useState('');
  const [selectedCompany, setSelectedCompany] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('');
  const [applicationDateRange, setApplicationDateRange] = useState<
    [dayjs.Dayjs | null, dayjs.Dayjs | null] | null
  >(null);
  const [repaymentDateRange, setRepaymentDateRange] = useState<
    [dayjs.Dayjs | null, dayjs.Dayjs | null] | null
  >(null);
  const [actualShipmentAmountRange, setActualShipmentAmountRange] = useState<
    [number | null, number | null]
  >([null, null]);
  const [requestedDebtAmountRange, setRequestedDebtAmountRange] = useState<
    [number | null, number | null]
  >([null, null]);
  const [totalAccumulatedDebtRange, setTotalAccumulatedDebtRange] = useState<
    [number | null, number | null]
  >([null, null]);
  const [overdueDaysRange, setOverdueDaysRange] = useState<[number | null, number | null]>([
    null,
    null,
  ]);
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC');

  // 获取合同列表
  const fetchContracts = async (
    page = 1,
    pageSize = 20,
    params: Partial<CommissionContractsListParams> = {},
  ) => {
    setLoading(true);
    try {
      const requestParams: CommissionContractsListParams = {
        page,
        pageSize,
        contractNumber: searchText || undefined,
        companyName: selectedCompany || undefined,
        partyBName: searchText || undefined,
        status: selectedStatus || undefined,
        paymentStatus: selectedPaymentStatus || undefined,
        applicationStartDate: applicationDateRange?.[0]?.format('YYYY-MM-DD'),
        applicationEndDate: applicationDateRange?.[1]?.format('YYYY-MM-DD'),
        repaymentStartDate: repaymentDateRange?.[0]?.format('YYYY-MM-DD'),
        repaymentEndDate: repaymentDateRange?.[1]?.format('YYYY-MM-DD'),
        search: searchText || undefined,
        minActualShipmentAmount: actualShipmentAmountRange[0] || undefined,
        maxActualShipmentAmount: actualShipmentAmountRange[1] || undefined,
        minRequestedDebtAmount: requestedDebtAmountRange[0] || undefined,
        maxRequestedDebtAmount: requestedDebtAmountRange[1] || undefined,
        minTotalAccumulatedDebt: totalAccumulatedDebtRange[0] || undefined,
        maxTotalAccumulatedDebt: totalAccumulatedDebtRange[1] || undefined,
        minOverDueDays: overdueDaysRange[0] || undefined,
        maxOverDueDays: overdueDaysRange[1] || undefined,
        sortField: sortField || undefined,
        sortOrder: sortOrder || undefined,
        ...params,
      };

      const response = await getCommissionContractsList(requestParams);
      const result = handleApiResponse(response, '', '获取合同列表失败');

      if (result.success && response.data) {
        setContracts(response.data.contracts || []);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total || 0,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取合同列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchContracts();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    fetchContracts(1, pagination.pageSize);
  };

  // 分页变化处理
  const handleTableChange = (paginationConfig: TablePaginationConfig, _: any, sorter: any) => {
    const { current = 1, pageSize = 20 } = paginationConfig;

    // 处理排序
    if (sorter.field && sorter.order) {
      setSortField(sorter.field);
      setSortOrder(sorter.order === 'ascend' ? 'ASC' : 'DESC');
    }

    fetchContracts(current, pageSize);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchContracts(pagination.current, pagination.pageSize);
  };

  // 新增合同
  const handleAdd = () => {
    navigate('/commission/contracts/create');
  };

  // 查看合同
  const handleView = (contract: ContractItem) => {
    navigate(`/commission/contracts/view/${contract.id}`);
  };

  // 编辑合同
  const handleEdit = (contract: ContractItem) => {
    navigate(`/commission/contracts/edit/${contract.id}`);
  };

  // 删除合同
  const handleDelete = (contract: ContractItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除合同 ${contract.contractNumber} 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await deleteCommissionContract(contract.id);
          const result = handleApiResponse(response, '删除成功', '删除失败');

          if (result.success) {
            message.success(result.message);
            handleRefresh();
          } else {
            message.error(result.message);
          }
        } catch (error) {
          logError('删除合同', error);
          message.error(getErrorMessage(error));
        }
      },
    });
  };

  // 导出PDF
  const handleExportPdf = async (contract: ContractItem) => {
    try {
      const blob = await exportCommissionContractPdf(contract.id);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `佣金发货合同_${contract.contractNumber}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('PDF导出成功');
    } catch (error) {
      logError('导出PDF', error);
      message.error(getErrorMessage(error));
    }
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      [CommissionContractStatus.DRAFT]: { text: '草稿', className: styles.statusDraft },
      [CommissionContractStatus.SUBMITTED]: { text: '已提交', className: styles.statusSubmitted },
      [CommissionContractStatus.APPROVED]: { text: '已审批', className: styles.statusApproved },
      [CommissionContractStatus.REJECTED]: { text: '已拒绝', className: styles.statusRejected },
      [CommissionContractStatus.COMPLETED]: { text: '已完成', className: styles.statusCompleted },
      [CommissionContractStatus.CANCELLED]: { text: '已取消', className: styles.statusCancelled },
    };

    const statusInfo = statusMap[status as CommissionContractStatus] || {
      text: status,
      className: styles.statusDraft,
    };
    return <Tag className={`${styles.statusTag} ${statusInfo.className}`}>{statusInfo.text}</Tag>;
  };

  // 获取支付状态标签
  const getPaymentStatusTag = (paymentStatus: string) => {
    const paymentStatusMap = {
      [PaymentStatus.UNPAID]: { text: '未还款', className: styles.paymentUnpaid },
      [PaymentStatus.PARTIAL_PAID]: { text: '部分还款', className: styles.paymentPartialPaid },
      [PaymentStatus.FULLY_PAID]: { text: '已还款', className: styles.paymentFullyPaid },
      [PaymentStatus.OVERDUE]: { text: '已逾期', className: styles.paymentOverdue },
    };

    const statusInfo = paymentStatusMap[paymentStatus as PaymentStatus] || {
      text: paymentStatus,
      className: styles.paymentUnpaid,
    };
    return <Tag className={`${styles.statusTag} ${statusInfo.className}`}>{statusInfo.text}</Tag>;
  };

  // 格式化金额显示
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // 表格列定义
  const columns: ColumnsType<ContractItem> = [
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      key: 'contractNumber',
      width: 140,
      fixed: 'left',
      render: (text: string) => (
        <Tooltip title={text}>
          <span style={{ fontWeight: 500, color: '#1890ff' }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '公司名称',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '乙方姓名',
      dataIndex: 'partyBName',
      key: 'partyBName',
      width: 100,
    },
    {
      title: '身份证号',
      dataIndex: 'partyBIdCard',
      key: 'partyBIdCard',
      width: 140,
      render: (text: string) => <span style={{ fontFamily: 'monospace' }}>{text}</span>,
    },
    {
      title: '申请日期',
      dataIndex: 'applicationDate',
      key: 'applicationDate',
      width: 100,
      sorter: true,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
    },
    {
      title: '实际发货金额',
      dataIndex: 'actualShipmentAmount',
      key: 'actualShipmentAmount',
      width: 120,
      sorter: true,
      align: 'right',
      render: (amount: number) => <span className={styles.amountText}>{formatAmount(amount)}</span>,
    },
    {
      title: '申请欠款金额',
      dataIndex: 'requestedDebtAmount',
      key: 'requestedDebtAmount',
      width: 120,
      sorter: true,
      align: 'right',
      render: (amount: number) => <span className={styles.amountText}>{formatAmount(amount)}</span>,
    },
    {
      title: '累计欠款总额',
      dataIndex: 'totalAccumulatedDebt',
      key: 'totalAccumulatedDebt',
      width: 120,
      sorter: true,
      align: 'right',
      render: (amount: number) => <span className={styles.amountText}>{formatAmount(amount)}</span>,
    },
    {
      title: '已还金额',
      dataIndex: 'paidAmount',
      key: 'paidAmount',
      width: 100,
      align: 'right',
      render: (amount: number) => (
        <span className={`${styles.amountText} ${styles.positiveAmount}`}>
          {formatAmount(amount)}
        </span>
      ),
    },
    {
      title: '剩余欠款',
      dataIndex: 'remainingDebtAmount',
      key: 'remainingDebtAmount',
      width: 100,
      sorter: true,
      align: 'right',
      render: (amount: number) => (
        <span
          className={`${styles.amountText} ${amount > 0 ? styles.negativeAmount : styles.positiveAmount}`}
        >
          {formatAmount(amount)}
        </span>
      ),
    },
    {
      title: '还款日期',
      dataIndex: 'repaymentDate',
      key: 'repaymentDate',
      width: 100,
      sorter: true,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDays',
      key: 'overdueDays',
      width: 80,
      sorter: true,
      align: 'center',
      render: (days: number) => (
        <span className={days > 0 ? styles.overdueDays : styles.normalDays}>
          {days > 0 ? `${days}天` : '正常'}
        </span>
      ),
    },
    {
      title: '合同状态',
      dataIndex: 'status',
      key: 'status',
      width: 90,
      align: 'center',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '支付状态',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      width: 90,
      align: 'center',
      render: (paymentStatus: string) => getPaymentStatusTag(paymentStatus),
    },
    {
      title: '最后还款日期',
      dataIndex: 'lastPaymentDate',
      key: 'lastPaymentDate',
      width: 110,
      render: (text: string) => (text ? dayjs(text).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record: ContractItem) => (
        <Space size={4}>
          <Tooltip title="查看">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              className={`${styles.actionButton} ${styles.viewButton}`}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              className={`${styles.actionButton} ${styles.editButton}`}
              onClick={() => handleEdit(record)}
              disabled={record.status === CommissionContractStatus.COMPLETED}
            />
          </Tooltip>
          <Tooltip title="导出PDF">
            <Button
              type="link"
              size="small"
              icon={<FilePdfOutlined />}
              className={`${styles.actionButton} ${styles.pdfButton}`}
              onClick={() => handleExportPdf(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="link"
              size="small"
              icon={<DeleteOutlined />}
              className={`${styles.actionButton} ${styles.deleteButton}`}
              onClick={() => handleDelete(record)}
              disabled={record.status !== CommissionContractStatus.DRAFT}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card className={styles.toolbar}>
        {/* 搜索行 */}
        <Row gutter={12} className={styles.searchRow} align="middle">
          <Col flex="auto">
            <Search
              placeholder="搜索合同编号、乙方姓名或身份证号"
              allowClear
              enterButton={<SearchOutlined />}
              onSearch={handleSearch}
              style={{ maxWidth: 400, height: 32 }}
            />
          </Col>
          <Col>
            <Space className={styles.actionButtons}>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                className={styles.refreshButton}
              >
                刷新
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                className={styles.addButton}
              >
                新增合同
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 筛选行 */}
        <Row gutter={8} className={styles.filterRow} align="middle">
          <Col span={5}>
            <CompanySearchSelector
              value={selectedCompany}
              onChange={(value) => {
                setSelectedCompany(value);
                fetchContracts(1, pagination.pageSize);
              }}
              placeholder="筛选公司"
              allowClear
              style={{ height: 32 }}
            />
          </Col>
          <Col span={3}>
            <Select
              value={selectedStatus}
              onChange={(value) => {
                setSelectedStatus(value);
                fetchContracts(1, pagination.pageSize);
              }}
              placeholder="合同状态"
              allowClear
              style={{ width: '100%', height: 32 }}
              size="small"
            >
              <Option value={CommissionContractStatus.DRAFT}>草稿</Option>
              <Option value={CommissionContractStatus.SUBMITTED}>已提交</Option>
              <Option value={CommissionContractStatus.APPROVED}>已审批</Option>
              <Option value={CommissionContractStatus.REJECTED}>已拒绝</Option>
              <Option value={CommissionContractStatus.COMPLETED}>已完成</Option>
              <Option value={CommissionContractStatus.CANCELLED}>已取消</Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              value={selectedPaymentStatus}
              onChange={(value) => {
                setSelectedPaymentStatus(value);
                fetchContracts(1, pagination.pageSize);
              }}
              placeholder="支付状态"
              allowClear
              style={{ width: '100%', height: 32 }}
              size="small"
            >
              <Option value={PaymentStatus.UNPAID}>未还款</Option>
              <Option value={PaymentStatus.PARTIAL_PAID}>部分还款</Option>
              <Option value={PaymentStatus.FULLY_PAID}>已还款</Option>
              <Option value={PaymentStatus.OVERDUE}>已逾期</Option>
            </Select>
          </Col>
          <Col span={5}>
            <RangePicker
              value={applicationDateRange}
              onChange={(dates) => {
                setApplicationDateRange(dates);
                fetchContracts(1, pagination.pageSize);
              }}
              placeholder={['申请开始', '申请结束']}
              style={{ width: '100%', height: 32 }}
              size="small"
            />
          </Col>
          <Col span={5}>
            <RangePicker
              value={repaymentDateRange}
              onChange={(dates) => {
                setRepaymentDateRange(dates);
                fetchContracts(1, pagination.pageSize);
              }}
              placeholder={['还款开始', '还款结束']}
              style={{ width: '100%', height: 32 }}
              size="small"
            />
          </Col>
        </Row>

        {/* 金额筛选行 */}
        <Row gutter={8} align="middle" style={{ marginTop: 8 }}>
          <Col span={6}>
            <Space.Compact style={{ width: '100%' }}>
              <InputNumber
                placeholder="最小发货金额"
                value={actualShipmentAmountRange[0]}
                onChange={(value) => {
                  setActualShipmentAmountRange([value, actualShipmentAmountRange[1]]);
                }}
                onPressEnter={() => fetchContracts(1, pagination.pageSize)}
                style={{ width: '50%', height: 32 }}
                size="small"
                controls={false}
              />
              <InputNumber
                placeholder="最大发货金额"
                value={actualShipmentAmountRange[1]}
                onChange={(value) => {
                  setActualShipmentAmountRange([actualShipmentAmountRange[0], value]);
                }}
                onPressEnter={() => fetchContracts(1, pagination.pageSize)}
                style={{ width: '50%', height: 32 }}
                size="small"
                controls={false}
              />
            </Space.Compact>
          </Col>
          <Col span={6}>
            <Space.Compact style={{ width: '100%' }}>
              <InputNumber
                placeholder="最小欠款金额"
                value={requestedDebtAmountRange[0]}
                onChange={(value) => {
                  setRequestedDebtAmountRange([value, requestedDebtAmountRange[1]]);
                }}
                onPressEnter={() => fetchContracts(1, pagination.pageSize)}
                style={{ width: '50%', height: 32 }}
                size="small"
                controls={false}
              />
              <InputNumber
                placeholder="最大欠款金额"
                value={requestedDebtAmountRange[1]}
                onChange={(value) => {
                  setRequestedDebtAmountRange([requestedDebtAmountRange[0], value]);
                }}
                onPressEnter={() => fetchContracts(1, pagination.pageSize)}
                style={{ width: '50%', height: 32 }}
                size="small"
                controls={false}
              />
            </Space.Compact>
          </Col>
          <Col span={6}>
            <Space.Compact style={{ width: '100%' }}>
              <InputNumber
                placeholder="最小累计欠款"
                value={totalAccumulatedDebtRange[0]}
                onChange={(value) => {
                  setTotalAccumulatedDebtRange([value, totalAccumulatedDebtRange[1]]);
                }}
                onPressEnter={() => fetchContracts(1, pagination.pageSize)}
                style={{ width: '50%', height: 32 }}
                size="small"
                controls={false}
              />
              <InputNumber
                placeholder="最大累计欠款"
                value={totalAccumulatedDebtRange[1]}
                onChange={(value) => {
                  setTotalAccumulatedDebtRange([totalAccumulatedDebtRange[0], value]);
                }}
                onPressEnter={() => fetchContracts(1, pagination.pageSize)}
                style={{ width: '50%', height: 32 }}
                size="small"
                controls={false}
              />
            </Space.Compact>
          </Col>
          <Col span={6}>
            <Space.Compact style={{ width: '100%' }}>
              <InputNumber
                placeholder="最小逾期天数"
                value={overdueDaysRange[0]}
                onChange={(value) => {
                  setOverdueDaysRange([value, overdueDaysRange[1]]);
                }}
                onPressEnter={() => fetchContracts(1, pagination.pageSize)}
                style={{ width: '50%', height: 32 }}
                size="small"
                controls={false}
              />
              <InputNumber
                placeholder="最大逾期天数"
                value={overdueDaysRange[1]}
                onChange={(value) => {
                  setOverdueDaysRange([overdueDaysRange[0], value]);
                }}
                onPressEnter={() => fetchContracts(1, pagination.pageSize)}
                style={{ width: '50%', height: 32 }}
                size="small"
                controls={false}
              />
            </Space.Compact>
          </Col>
        </Row>
      </Card>

      {/* 表格 */}
      <Card className={styles.tableCard}>
        <Table
          columns={columns}
          dataSource={contracts}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          onChange={handleTableChange}
          scroll={{ x: 1800, y: 'calc(100vh - 400px)' }}
          size="small"
          className={styles.table}
        />
      </Card>
    </div>
  );
};

export default CommissionContractsList;
