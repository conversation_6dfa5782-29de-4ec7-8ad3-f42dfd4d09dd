/* 佣金发货合同表单页面样式 */
.container {
  padding: 16px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.headerCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.backButton {
  color: #666;
  font-size: 14px;
  padding: 4px 8px;
  height: auto;
  border: none;
  background: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.backButton:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.06);
}

.titleSection {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pageTitle {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.contractMeta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #8c8c8c;
}

.contractMeta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.headerRight {
  display: flex;
  align-items: center;
}

/* 表单卡片样式 */
.formCard {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.formContent {
  padding: 24px;
}

/* 表单分组样式 */
.formSection {
  margin-bottom: 24px;
}

.sectionTitle {
  font-size: 15px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.sectionIcon {
  color: #1890ff;
  font-size: 18px;
}

/* 表单项样式 */
.formRow {
  margin-bottom: 12px;
}

.compactFormItem {
  margin-bottom: 8px;
}

.compactFormItem :global(.ant-form-item-label) {
  padding-bottom: 4px;
}

.compactFormItem :global(.ant-form-item-label > label) {
  font-size: 13px;
  font-weight: 500;
  color: #595959;
}

.compactFormItem :global(.ant-input),
.compactFormItem :global(.ant-input-number),
.compactFormItem :global(.ant-select-selector),
.compactFormItem :global(.ant-picker) {
  height: 36px;
  font-size: 13px;
}

.compactFormItem :global(.ant-input-number-input) {
  height: 34px;
  font-size: 13px;
}

/* 日期选择器样式 */
.datePickerGroup {
  display: flex;
  gap: 8px;
  align-items: center;
}

.datePickerGroup :global(.ant-select) {
  min-width: 80px;
}

.datePickerGroup :global(.ant-select-selector) {
  height: 36px;
}

/* 品牌详情表格样式 */
.brandDetailsSection {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.brandDetailsTitle {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.addBrandButton {
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
}

.brandTable {
  background: #fff;
  border-radius: 4px;
}

.brandTable :global(.ant-table-thead > tr > th) {
  background: #f8f9fa;
  font-weight: 600;
  font-size: 12px;
  padding: 8px 12px;
  border-bottom: 1px solid #e8e8e8;
}

.brandTable :global(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
  font-size: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.brandTable :global(.ant-input),
.brandTable :global(.ant-input-number) {
  height: 32px;
  font-size: 12px;
  border: 1px solid #d9d9d9;
}

.brandTable :global(.ant-input-number-input) {
  height: 30px;
}

.deleteBrandButton {
  color: #ff4d4f;
  border: none;
  background: none;
  padding: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.deleteBrandButton:hover {
  background: rgba(255, 77, 79, 0.1);
  border-radius: 4px;
}

/* 金额输入框样式 */
.amountInput {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  text-align: right;
}

.amountInput :global(.ant-input-number-input) {
  text-align: right;
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* 状态管理区域样式 */
.statusSection {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.statusActions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.statusButton {
  height: 36px;
  padding: 0 16px;
  font-size: 13px;
}

.approveButton {
  background: #52c41a;
  border-color: #52c41a;
}

.rejectButton {
  background: #ff4d4f;
  border-color: #ff4d4f;
}

.paymentButton {
  background: #1890ff;
  border-color: #1890ff;
}

/* 只读模式样式 */
.readOnlyValue {
  padding: 8px 12px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 13px;
  color: #262626;
  min-height: 36px;
  display: flex;
  align-items: center;
}

.readOnlyAmount {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 12px;
  }

  .formContent {
    padding: 16px;
  }

  .formRow {
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .pageHeader {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .headerLeft {
    width: 100%;
  }

  .headerRight {
    width: 100%;
    justify-content: flex-end;
  }

  .contractMeta {
    flex-direction: column;
    gap: 4px;
  }

  .datePickerGroup {
    flex-direction: column;
    align-items: stretch;
  }

  .statusActions {
    flex-direction: column;
  }

  .statusButton {
    width: 100%;
  }
}
