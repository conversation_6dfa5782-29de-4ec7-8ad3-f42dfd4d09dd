import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Space,
  Row,
  Col,
  Table,
  message,
  Modal,
  DatePicker,
  Tag,
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  FileTextOutlined,
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PayCircleOutlined,
  EditOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  createCommissionContract,
  getCommissionContractDetail,
  updateCommissionContract,
  updateCommissionContractStatus,
  updateCommissionContractPayment,
} from '@/api/CommissionContractsApi';
import {
  AddCommissionContractsParams,
  UpdateContractsParams,
  UpdateContractsStatusParams,
  UpdateContractsPaymentParams,
  CommissionContractStatus,
  PaymentStatus,
} from '@/types/commissionContracts';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';

import useAuthStore from '@/store/authStore';
import BrandSearchSelector from '@/components/BrandSearchSelector/BrandSearchSelector';
import styles from './CommissionContractsForm.module.css';

const { Option } = Select;
const { TextArea } = Input;

interface BrandDetail {
  id?: string;
  brandName: string;
  quantity: number;
  amount: number;
  sortOrder?: number;
}

interface ContractFormData {
  companyName: string;
  partyBName: string;
  partyBIdCard: string;
  applicationDate: dayjs.Dayjs;
  actualShipmentAmount: number;
  requestedDebtAmount: number;
  repaymentDate: dayjs.Dayjs;
  totalAccumulatedDebt: number;
  remarks?: string;
  brandDetails: BrandDetail[];
}

interface ContractDetailData {
  id: string;
  contractNumber: string;
  companyName: string;
  partyBName: string;
  partyBIdCard: string;
  applicationYear: number;
  applicationMonth: number;
  applicationDay: number;
  actualShipmentAmount: number;
  actualShipmentAmountInWords: string;
  requestedDebtAmount: number;
  requestedDebtAmountInWords: string;
  repaymentYear: number;
  repaymentMonth: number;
  repaymentDay: number;
  totalAccumulatedDebt: number;
  totalAccumulatedDebtInWords: string;
  remarks?: string;
  brandDetails: BrandDetail[];
  status: string;
  createdByUserCode: string | null;
  approvedByUserCode: string | null;
  approvedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

const CommissionContractsForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { user } = useAuthStore();
  const [form] = Form.useForm<ContractFormData>();

  // 页面状态
  const isCreate = !id;
  const isEdit = id && window.location.pathname.includes('/edit/');
  const isView = id && window.location.pathname.includes('/view/');

  // 组件状态
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [contractData, setContractData] = useState<ContractDetailData | null>(null);
  const [brandDetails, setBrandDetails] = useState<BrandDetail[]>([]);

  // 状态管理模态框
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [statusForm] = Form.useForm();
  const [paymentForm] = Form.useForm();

  // 加载合同详情
  const loadContractDetail = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await getCommissionContractDetail(id);
      const result = handleApiResponse(response, '', '获取合同详情失败');

      if (result.success && response.data) {
        const data = response.data;
        setContractData({
          ...data,
          remarks: data.remarks || undefined,
        });
        setBrandDetails(data.brandDetails || []);

        // 设置表单值
        form.setFieldsValue({
          companyName: data.companyName,
          partyBName: data.partyBName,
          partyBIdCard: data.partyBIdCard,
          applicationDate: dayjs(
            `${data.applicationYear}-${String(data.applicationMonth).padStart(2, '0')}-${String(data.applicationDay).padStart(2, '0')}`,
          ),
          actualShipmentAmount: data.actualShipmentAmount,
          requestedDebtAmount: data.requestedDebtAmount,
          repaymentDate: dayjs(
            `${data.repaymentYear}-${String(data.repaymentMonth).padStart(2, '0')}-${String(data.repaymentDay).padStart(2, '0')}`,
          ),
          totalAccumulatedDebt: data.totalAccumulatedDebt,
          remarks: data.remarks || '',
        });
      } else {
        message.error(result.message);
        navigate('/commission/contracts');
      }
    } catch (error) {
      logError('获取合同详情', error);
      message.error(getErrorMessage(error));
      navigate('/commission/contracts');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    if (id) {
      loadContractDetail();
    } else {
      // 新增时设置默认值
      const currentDate = dayjs();
      form.setFieldsValue({
        companyName: '广州莱了贸易有限公司',
        applicationDate: currentDate,
        repaymentDate: currentDate.add(30, 'day'),
        actualShipmentAmount: 0,
        requestedDebtAmount: 0,
        totalAccumulatedDebt: 0,
      });
      setBrandDetails([]);
    }
  }, [id, form]);

  // 返回列表
  const handleBack = () => {
    navigate('/commission/contracts');
  };

  // 添加品牌详情
  const handleAddBrand = () => {
    const newBrand: BrandDetail = {
      brandName: '',
      quantity: 0,
      amount: 0,
      sortOrder: brandDetails.length + 1,
    };
    setBrandDetails([...brandDetails, newBrand]);
  };

  // 删除品牌详情
  const handleDeleteBrand = (index: number) => {
    const newBrandDetails = brandDetails.filter((_, i) => i !== index);
    setBrandDetails(newBrandDetails);
  };

  // 更新品牌详情
  const handleBrandDetailChange = (index: number, field: keyof BrandDetail, value: any) => {
    const newBrandDetails = [...brandDetails];
    newBrandDetails[index] = { ...newBrandDetails[index], [field]: value };
    setBrandDetails(newBrandDetails);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (brandDetails.length === 0) {
        message.error('请至少添加一个品牌详情');
        return;
      }

      // 验证品牌详情
      for (let i = 0; i < brandDetails.length; i++) {
        const brand = brandDetails[i];
        if (!brand.brandName.trim()) {
          message.error(`第 ${i + 1} 行品牌名称不能为空`);
          return;
        }
        if (brand.quantity <= 0) {
          message.error(`第 ${i + 1} 行数量必须大于0`);
          return;
        }
        if (brand.amount <= 0) {
          message.error(`第 ${i + 1} 行金额必须大于0`);
          return;
        }
      }

      setSubmitting(true);

      let contractData: AddCommissionContractsParams | UpdateContractsParams;

      if (isCreate) {
        // 新增合同使用新的接口格式
        contractData = {
          companyName: values.companyName,
          partyBName: values.partyBName,
          partyBIdCard: values.partyBIdCard,
          applicationDate: values.applicationDate.format('YYYY-MM-DD'),
          actualShipmentAmount: values.actualShipmentAmount,
          requestedDebtAmount: values.requestedDebtAmount,
          repaymentDate: values.repaymentDate.format('YYYY-MM-DD'),
          totalAccumulatedDebt: values.totalAccumulatedDebt,
          brandDetails: brandDetails.map((brand) => ({
            brandName: brand.brandName.trim(),
            quantity: brand.quantity,
            amount: brand.amount,
          })),
        } as AddCommissionContractsParams;
      } else {
        // 更新合同使用旧的接口格式（年月日分开）
        contractData = {
          companyName: values.companyName,
          partyBName: values.partyBName,
          partyBIdCard: values.partyBIdCard,
          applicationYear: values.applicationDate.year(),
          applicationMonth: values.applicationDate.month() + 1,
          applicationDay: values.applicationDate.date(),
          actualShipmentAmount: values.actualShipmentAmount,
          requestedDebtAmount: values.requestedDebtAmount,
          repaymentYear: values.repaymentDate.year(),
          repaymentMonth: values.repaymentDate.month() + 1,
          repaymentDay: values.repaymentDate.date(),
          totalAccumulatedDebt: values.totalAccumulatedDebt,
          remarks: values.remarks,
          brandDetails: brandDetails.map((brand, index) => ({
            brandName: brand.brandName.trim(),
            quantity: brand.quantity,
            amount: brand.amount,
            sortOrder: index + 1,
          })),
        } as UpdateContractsParams;
      }

      let response;
      if (isCreate) {
        response = await createCommissionContract(contractData as AddCommissionContractsParams);
      } else {
        response = await updateCommissionContract(id!, contractData as UpdateContractsParams);
      }

      const result = handleApiResponse(
        response,
        isCreate ? '创建成功' : '更新成功',
        isCreate ? '创建失败' : '更新失败',
      );

      if (result.success) {
        message.success(result.message);
        navigate('/commission/contracts');
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError(isCreate ? '创建合同' : '更新合同', error);
      message.error(getErrorMessage(error));
    } finally {
      setSubmitting(false);
    }
  };

  // 更新合同状态
  const handleUpdateStatus = async (status: string) => {
    try {
      const values = await statusForm.validateFields();
      const statusData: UpdateContractsStatusParams = {
        status,
        approvedByUserCode: user?.code || '',
        remarks: values.remarks || '',
      };

      const response = await updateCommissionContractStatus(id!, statusData);
      const result = handleApiResponse(response, '状态更新成功', '状态更新失败');

      if (result.success) {
        message.success(result.message);
        setStatusModalVisible(false);
        statusForm.resetFields();
        loadContractDetail(); // 重新加载数据
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('更新合同状态', error);
      message.error(getErrorMessage(error));
    }
  };

  // 更新支付状态
  const handleUpdatePayment = async () => {
    try {
      const values = await paymentForm.validateFields();
      const paymentData: UpdateContractsPaymentParams = {
        paymentStatus: values.paymentStatus,
        paidAmount: values.paidAmount,
        lastPaymentDate: values.lastPaymentDate.format('YYYY-MM-DD'),
        remarks: values.remarks || '',
      };

      const response = await updateCommissionContractPayment(id!, paymentData);
      const result = handleApiResponse(response, '支付状态更新成功', '支付状态更新失败');

      if (result.success) {
        message.success(result.message);
        setPaymentModalVisible(false);
        paymentForm.resetFields();
        loadContractDetail(); // 重新加载数据
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('更新支付状态', error);
      message.error(getErrorMessage(error));
    }
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      [CommissionContractStatus.DRAFT]: { text: '草稿', color: 'default' },
      [CommissionContractStatus.SUBMITTED]: { text: '已提交', color: 'blue' },
      [CommissionContractStatus.APPROVED]: { text: '已审批', color: 'green' },
      [CommissionContractStatus.REJECTED]: { text: '已拒绝', color: 'red' },
      [CommissionContractStatus.COMPLETED]: { text: '已完成', color: 'purple' },
      [CommissionContractStatus.CANCELLED]: { text: '已取消', color: 'default' },
    };

    const statusInfo = statusMap[status as CommissionContractStatus] || {
      text: status,
      color: 'default',
    };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 品牌详情表格列定义
  const brandColumns: ColumnsType<BrandDetail> = [
    {
      title: '品牌名称',
      dataIndex: 'brandName',
      key: 'brandName',
      width: 200,
      render: (value: string, _: BrandDetail, index: number) =>
        isView ? (
          <span>{value}</span>
        ) : (
          <BrandSearchSelector
            value={value}
            onChange={(brandCode) => {
              handleBrandDetailChange(index, 'brandName', brandCode);
            }}
            placeholder="请选择品牌"
            allowClear
            style={{ width: '100%' }}
          />
        ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 120,
      render: (value: number, _: BrandDetail, index: number) => (
        <InputNumber
          value={value}
          onChange={(val) => handleBrandDetailChange(index, 'quantity', val || 0)}
          placeholder="数量"
          min={0}
          precision={0}
          style={{ width: '100%' }}
          disabled={!!isView}
        />
      ),
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 150,
      render: (value: number, _: BrandDetail, index: number) => (
        <InputNumber
          value={value}
          onChange={(val) => handleBrandDetailChange(index, 'amount', val || 0)}
          placeholder="金额"
          min={0}
          precision={2}
          style={{ width: '100%' }}
          disabled={!!isView}
          addonBefore="¥"
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, __: BrandDetail, index: number) =>
        !isView && (
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteBrand(index)}
            className={styles.deleteBrandButton}
            size="small"
          />
        ),
    },
  ];

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <Card className={styles.headerCard}>
        <div className={styles.pageHeader}>
          <div className={styles.headerLeft}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              className={styles.backButton}
            >
              返回列表
            </Button>
            <div className={styles.titleSection}>
              <h2 className={styles.pageTitle}>
                {isCreate && '新增佣金发货合同'}
                {isEdit && '编辑佣金发货合同'}
                {isView && '佣金发货合同详情'}
              </h2>
              {contractData && (
                <div className={styles.contractMeta}>
                  <span>合同编号: {contractData.contractNumber}</span>
                  <span>状态: {getStatusTag(contractData.status)}</span>
                  <span>创建时间: {dayjs(contractData.createdAt).format('YYYY-MM-DD HH:mm')}</span>
                </div>
              )}
            </div>
          </div>
          {!isView && (
            <div className={styles.headerRight}>
              <Space>
                <Button onClick={handleBack}>取消</Button>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  loading={submitting}
                  onClick={handleSubmit}
                >
                  保存
                </Button>
              </Space>
            </div>
          )}
          {isView && contractData && (
            <div className={styles.headerRight}>
              <Space>
                {contractData.status === CommissionContractStatus.SUBMITTED && (
                  <>
                    <Button
                      icon={<CheckCircleOutlined />}
                      className={styles.approveButton}
                      onClick={() => {
                        setStatusModalVisible(true);
                        statusForm.setFieldsValue({ action: 'approve' });
                      }}
                    >
                      审批通过
                    </Button>
                    <Button
                      icon={<CloseCircleOutlined />}
                      className={styles.rejectButton}
                      onClick={() => {
                        setStatusModalVisible(true);
                        statusForm.setFieldsValue({ action: 'reject' });
                      }}
                    >
                      审批拒绝
                    </Button>
                  </>
                )}
                {contractData.status === CommissionContractStatus.APPROVED && (
                  <Button
                    icon={<PayCircleOutlined />}
                    className={styles.paymentButton}
                    onClick={() => setPaymentModalVisible(true)}
                  >
                    更新支付状态
                  </Button>
                )}
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={() => navigate(`/commission/contracts/edit/${id}`)}
                  disabled={contractData.status === CommissionContractStatus.COMPLETED}
                >
                  编辑
                </Button>
              </Space>
            </div>
          )}
        </div>
      </Card>

      {/* 表单内容 */}
      <Card className={styles.formCard} loading={loading}>
        <div className={styles.formContent}>
          <Form form={form} layout="vertical" disabled={!!isView} onFinish={handleSubmit}>
            {/* 基本信息 */}
            <div className={styles.formSection}>
              <div className={styles.sectionTitle}>
                <UserOutlined className={styles.sectionIcon} />
                基本信息
              </div>
              <Row gutter={16} className={styles.formRow}>
                <Col span={8}>
                  <Form.Item
                    label="公司名称"
                    name="companyName"
                    rules={[{ required: true, message: '请输入公司名称' }]}
                    className={styles.compactFormItem}
                  >
                    {isView && contractData ? (
                      <div className={styles.readOnlyValue}>{contractData.companyName}</div>
                    ) : (
                      <Input placeholder="请输入公司名称" />
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="乙方姓名"
                    name="partyBName"
                    rules={[{ required: true, message: '请输入乙方姓名' }]}
                    className={styles.compactFormItem}
                  >
                    {isView && contractData ? (
                      <div className={styles.readOnlyValue}>{contractData.partyBName}</div>
                    ) : (
                      <Input placeholder="请输入乙方姓名" />
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="身份证号"
                    name="partyBIdCard"
                    rules={[
                      { required: true, message: '请输入身份证号' },
                      {
                        pattern:
                          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
                        message: '请输入正确的身份证号',
                      },
                    ]}
                    className={styles.compactFormItem}
                  >
                    {isView && contractData ? (
                      <div className={styles.readOnlyValue}>{contractData.partyBIdCard}</div>
                    ) : (
                      <Input placeholder="请输入身份证号" />
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* 日期信息 */}
            <div className={styles.formSection}>
              <div className={styles.sectionTitle}>
                <CalendarOutlined className={styles.sectionIcon} />
                日期信息
              </div>
              <Row gutter={16} className={styles.formRow}>
                <Col span={12}>
                  <Form.Item
                    label="申请日期"
                    name="applicationDate"
                    rules={[{ required: true, message: '请选择申请日期' }]}
                    className={styles.compactFormItem}
                  >
                    {isView && contractData ? (
                      <div className={styles.readOnlyValue}>
                        {dayjs(
                          `${contractData.applicationYear}-${String(contractData.applicationMonth).padStart(2, '0')}-${String(contractData.applicationDay).padStart(2, '0')}`,
                        ).format('YYYY-MM-DD')}
                      </div>
                    ) : (
                      <DatePicker
                        placeholder="请选择申请日期"
                        style={{ width: '100%' }}
                        format="YYYY-MM-DD"
                      />
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="还款日期"
                    name="repaymentDate"
                    rules={[{ required: true, message: '请选择还款日期' }]}
                    className={styles.compactFormItem}
                  >
                    {isView && contractData ? (
                      <div className={styles.readOnlyValue}>
                        {dayjs(
                          `${contractData.repaymentYear}-${String(contractData.repaymentMonth).padStart(2, '0')}-${String(contractData.repaymentDay).padStart(2, '0')}`,
                        ).format('YYYY-MM-DD')}
                      </div>
                    ) : (
                      <DatePicker
                        placeholder="请选择还款日期"
                        style={{ width: '100%' }}
                        format="YYYY-MM-DD"
                      />
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* 金额信息 */}
            <div className={styles.formSection}>
              <div className={styles.sectionTitle}>
                <DollarOutlined className={styles.sectionIcon} />
                金额信息
              </div>
              <Row gutter={16} className={styles.formRow}>
                <Col span={8}>
                  <Form.Item
                    label="实际发货金额"
                    name="actualShipmentAmount"
                    rules={[
                      { required: true, message: '请输入实际发货金额' },
                      { type: 'number', min: 0, message: '金额不能小于0' },
                    ]}
                    className={styles.compactFormItem}
                  >
                    {isView && contractData ? (
                      <div className={`${styles.readOnlyValue} ${styles.readOnlyAmount}`}>
                        ¥{contractData.actualShipmentAmount?.toLocaleString() || '0'}
                      </div>
                    ) : (
                      <InputNumber
                        placeholder="请输入实际发货金额"
                        min={0}
                        precision={2}
                        style={{ width: '100%' }}
                        addonBefore="¥"
                      />
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="申请欠款金额"
                    name="requestedDebtAmount"
                    rules={[
                      { required: true, message: '请输入申请欠款金额' },
                      { type: 'number', min: 0, message: '金额不能小于0' },
                    ]}
                    className={styles.compactFormItem}
                  >
                    {isView && contractData ? (
                      <div className={`${styles.readOnlyValue} ${styles.readOnlyAmount}`}>
                        ¥{contractData.requestedDebtAmount.toLocaleString()}
                      </div>
                    ) : (
                      <InputNumber
                        placeholder="请输入申请欠款金额"
                        min={0}
                        precision={2}
                        style={{ width: '100%' }}
                        addonBefore="¥"
                      />
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="累计欠款总额"
                    name="totalAccumulatedDebt"
                    rules={[
                      { required: true, message: '请输入累计欠款总额' },
                      { type: 'number', min: 0, message: '金额不能小于0' },
                    ]}
                    className={styles.compactFormItem}
                  >
                    {isView && contractData ? (
                      <div className={`${styles.readOnlyValue} ${styles.readOnlyAmount}`}>
                        ¥{contractData.totalAccumulatedDebt.toLocaleString()}
                      </div>
                    ) : (
                      <InputNumber
                        placeholder="请输入累计欠款总额"
                        min={0}
                        precision={2}
                        style={{ width: '100%' }}
                        addonBefore="¥"
                      />
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* 品牌详情 */}
            <div className={styles.formSection}>
              <div className={styles.sectionTitle}>
                <FileTextOutlined className={styles.sectionIcon} />
                品牌详情
              </div>
              <div className={styles.brandDetailsSection}>
                <div className={styles.brandDetailsTitle}>
                  <span>品牌明细</span>
                  {!isView && (
                    <Button
                      type="primary"
                      size="small"
                      icon={<PlusOutlined />}
                      onClick={handleAddBrand}
                      className={styles.addBrandButton}
                    >
                      添加品牌
                    </Button>
                  )}
                </div>
                <Table
                  columns={brandColumns}
                  dataSource={brandDetails}
                  rowKey={(_, index) => index?.toString() || '0'}
                  pagination={false}
                  size="small"
                  className={styles.brandTable}
                  locale={{ emptyText: '暂无品牌详情，请点击"添加品牌"按钮添加' }}
                />
              </div>
            </div>

            {/* 备注信息 */}
            <div className={styles.formSection}>
              <div className={styles.sectionTitle}>
                <FileTextOutlined className={styles.sectionIcon} />
                备注信息
              </div>
              <Row gutter={16} className={styles.formRow}>
                <Col span={24}>
                  <Form.Item label="备注" name="remarks" className={styles.compactFormItem}>
                    {isView && contractData ? (
                      <div className={styles.readOnlyValue}>{contractData.remarks || '无备注'}</div>
                    ) : (
                      <TextArea placeholder="请输入备注信息" rows={4} maxLength={500} showCount />
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </Form>
        </div>
      </Card>

      {/* 状态更新模态框 */}
      <Modal
        title="更新合同状态"
        open={statusModalVisible}
        onCancel={() => {
          setStatusModalVisible(false);
          statusForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={statusForm}
          layout="vertical"
          onFinish={(values) => {
            const status =
              values.action === 'approve'
                ? CommissionContractStatus.APPROVED
                : CommissionContractStatus.REJECTED;
            handleUpdateStatus(status);
          }}
        >
          <Form.Item name="action" style={{ display: 'none' }}>
            <Input />
          </Form.Item>
          <Form.Item
            label="备注"
            name="remarks"
            rules={[{ required: true, message: '请输入处理备注' }]}
          >
            <TextArea placeholder="请输入处理备注" rows={4} maxLength={200} showCount />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setStatusModalVisible(false);
                  statusForm.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 支付状态更新模态框 */}
      <Modal
        title="更新支付状态"
        open={paymentModalVisible}
        onCancel={() => {
          setPaymentModalVisible(false);
          paymentForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form form={paymentForm} layout="vertical" onFinish={handleUpdatePayment}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="支付状态"
                name="paymentStatus"
                rules={[{ required: true, message: '请选择支付状态' }]}
              >
                <Select placeholder="请选择支付状态">
                  <Option value={PaymentStatus.UNPAID}>未还款</Option>
                  <Option value={PaymentStatus.PARTIAL_PAID}>部分还款</Option>
                  <Option value={PaymentStatus.FULLY_PAID}>已还款</Option>
                  <Option value={PaymentStatus.OVERDUE}>已逾期</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="已还金额"
                name="paidAmount"
                rules={[
                  { required: true, message: '请输入已还金额' },
                  { type: 'number', min: 0, message: '金额不能小于0' },
                ]}
              >
                <InputNumber
                  placeholder="请输入已还金额"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value!.replace(/¥\s?|(,*)/g, '') as any}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="最后还款日期"
                name="lastPaymentDate"
                rules={[{ required: true, message: '请选择最后还款日期' }]}
              >
                <DatePicker
                  placeholder="请选择最后还款日期"
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item label="备注" name="remarks">
            <TextArea placeholder="请输入备注信息" rows={3} maxLength={200} showCount />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setPaymentModalVisible(false);
                  paymentForm.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CommissionContractsForm;
