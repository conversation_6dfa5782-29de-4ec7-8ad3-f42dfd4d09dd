import React from 'react';
import { Card, Space, Typography, Tag, Divider, Button } from 'antd';
import useAuthStore from '@/store/authStore';
import { getUser } from '@/utils/tokenManager';

const { Title, Text } = Typography;

/**
 * 权限测试页面 - 用于测试和展示当前用户的权限状态
 */
const PermissionTest: React.FC = () => {
  const { user, hasRoutePermission, canAccessRoute, getRoutePermissions } = useAuthStore();

  // 同时获取localStorage中的用户信息进行对比
  const localStorageUser = getUser();

  if (!user) {
    return (
      <Card title="权限测试">
        <Text type="secondary">用户未登录</Text>
        <Divider />
        <div>
          <Title level={5}>localStorage中的用户信息：</Title>
          <pre style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {JSON.stringify(localStorageUser, null, 2)}
          </pre>
        </div>
      </Card>
    );
  }

  // 测试菜单权限检查函数
  const testMenuPermission = (route: string): boolean => {
    if (!user) return false;
    if (user.isSuperAdmin) return true;

    // 确保路由格式一致，添加开头的斜杠
    const normalizedRoute = route.startsWith('/') ? route : `/${route}`;

    // 检查用户是否有该路由的任何权限
    const routePermissions = user.routePermissions?.[normalizedRoute];
    if (!routePermissions) return false;

    return Object.values(routePermissions).some(Boolean);
  };

  // 测试路由列表
  const testRoutes = [
    '/product/brand',
    '/product/category',
    '/product/color',
    '/product/auxiliary',
    '/product/archive',
    '/product/inventory',
    '/company/management',
    '/company/supplier',
    '/company/customer',
    '/customer/profiles',
    '/purchase/purchase-contracts',
    '/purchase/product-orders',
    '/financial/fixed-assets',
    '/financial/operating-assets',
    '/system/users',
    '/admin/memos',
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="用户权限测试" style={{ marginBottom: 16 }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Title level={4}>用户信息</Title>
            <Space>
              <Text strong>用户代码:</Text>
              <Text>{user.code}</Text>
            </Space>
            <br />
            <Space>
              <Text strong>用户昵称:</Text>
              <Text>{user.nickname}</Text>
            </Space>
            <br />
            <Space>
              <Text strong>超级管理员:</Text>
              <Tag color={user.isSuperAdmin ? 'green' : 'red'}>
                {user.isSuperAdmin ? '是' : '否'}
              </Tag>
            </Space>
          </div>

          <Divider />

          <div>
            <Title level={4}>用户权限配置</Title>
            <pre style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
              {JSON.stringify(user.routePermissions, null, 2)}
            </pre>
          </div>

          <Divider />

          <div>
            <Title level={4}>路由权限测试</Title>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {testRoutes.map((route) => {
                const canAccess = canAccessRoute(route);
                const permissions = getRoutePermissions(route);
                const menuCanAccess = testMenuPermission(route);

                return (
                  <Card key={route} size="small" style={{ marginBottom: 8 }}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}
                      >
                        <Text code>{route}</Text>
                        <Space>
                          <Tag color={canAccess ? 'green' : 'red'}>
                            Store: {canAccess ? '可访问' : '无权限'}
                          </Tag>
                          <Tag color={menuCanAccess ? 'blue' : 'orange'}>
                            Menu: {menuCanAccess ? '可访问' : '无权限'}
                          </Tag>
                        </Space>
                      </div>

                      {permissions && (
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            权限详情:{' '}
                          </Text>
                          <Space size="small">
                            {Object.entries(permissions).map(([key, value]) => (
                              <Tag key={key} color={value ? 'blue' : 'default'}>
                                {key}: {value ? '✓' : '✗'}
                              </Tag>
                            ))}
                          </Space>
                        </div>
                      )}
                    </Space>
                  </Card>
                );
              })}
            </Space>
          </div>

          <Divider />

          <div>
            <Title level={4}>数据对比</Title>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>Zustand Store中的用户信息：</Text>
                <pre style={{ backgroundColor: '#f0f8ff', padding: '12px', borderRadius: '4px' }}>
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>

              <div>
                <Text strong>localStorage中的用户信息：</Text>
                <pre style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
                  {JSON.stringify(localStorageUser, null, 2)}
                </pre>
              </div>
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default PermissionTest;
