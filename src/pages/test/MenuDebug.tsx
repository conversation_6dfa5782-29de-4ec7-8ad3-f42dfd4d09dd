import React from 'react';
import { Card, Typography, Space } from 'antd';
import { getMenuItems } from '@/components/layout/menu/MenuItems';
import { getUser } from '@/utils/tokenManager';

const { Title, Text } = Typography;

const MenuDebug: React.FC = () => {
  const user = getUser();
  const menuItems = getMenuItems();

  return (
    <div style={{ padding: '24px' }}>
      <Card title="菜单调试信息">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={4}>当前用户信息</Title>
            <pre style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
              {JSON.stringify(user, null, 2)}
            </pre>
          </div>

          <div>
            <Title level={4}>生成的菜单项</Title>
            <Text type="secondary">菜单项数量: {menuItems.length}</Text>
            <pre style={{ backgroundColor: '#f0f8ff', padding: '12px', borderRadius: '4px' }}>
              {JSON.stringify(menuItems, null, 2)}
            </pre>
          </div>

          <div>
            <Title level={4}>权限检查测试</Title>
            {user && (
              <div>
                <Text>测试路由 'product/brand' 的权限检查：</Text>
                <br />
                <Text>
                  直接检查: {user.routePermissions?.['product/brand'] ? '有权限' : '无权限'}
                </Text>
                <br />
                <Text>
                  带斜杠检查: {user.routePermissions?.['/product/brand'] ? '有权限' : '无权限'}
                </Text>
                <br />
                <Text>
                  权限详情: {JSON.stringify(user.routePermissions?.['/product/brand'] || user.routePermissions?.['product/brand'])}
                </Text>
              </div>
            )}
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default MenuDebug;
