import React from 'react';
import { Card, Button, Space, Typography, Tag } from 'antd';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '@/store/authStore';
import { getUser } from '@/utils/tokenManager';

const { Title, Text } = Typography;

const QuickTest: React.FC = () => {
  const navigate = useNavigate();
  const { user, canAccessRoute, hasRoutePermission, getRoutePermissions } = useAuthStore();
  const localUser = getUser();

  const testRoute = '/product/brand';

  // 直接测试权限
  const directCheck = localUser?.routePermissions?.[testRoute];
  const storeCheck = canAccessRoute(testRoute);
  const permissionCheck = hasRoutePermission(testRoute);
  const permissions = getRoutePermissions(testRoute);

  return (
    <div style={{ padding: '24px' }}>
      <Card title="快速权限测试">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={4}>测试路由: {testRoute}</Title>
            
            <Space direction="vertical">
              <div>
                <Text strong>直接检查localStorage: </Text>
                <Tag color={directCheck ? 'green' : 'red'}>
                  {directCheck ? '有权限' : '无权限'}
                </Tag>
              </div>
              
              <div>
                <Text strong>Store canAccessRoute: </Text>
                <Tag color={storeCheck ? 'green' : 'red'}>
                  {storeCheck ? '有权限' : '无权限'}
                </Tag>
              </div>
              
              <div>
                <Text strong>Store hasRoutePermission: </Text>
                <Tag color={permissionCheck ? 'green' : 'red'}>
                  {permissionCheck ? '有权限' : '无权限'}
                </Tag>
              </div>
            </Space>
          </div>

          <div>
            <Title level={5}>权限详情</Title>
            <pre style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
              {JSON.stringify(directCheck || permissions, null, 2)}
            </pre>
          </div>

          <div>
            <Title level={5}>用户信息对比</Title>
            <div>
              <Text strong>Store用户: </Text>
              <Text>{user ? 'exists' : 'null'}</Text>
            </div>
            <div>
              <Text strong>localStorage用户: </Text>
              <Text>{localUser ? 'exists' : 'null'}</Text>
            </div>
            <div>
              <Text strong>是否相同: </Text>
              <Text>{JSON.stringify(user) === JSON.stringify(localUser) ? '是' : '否'}</Text>
            </div>
          </div>

          <Space>
            <Button 
              type="primary" 
              onClick={() => navigate('/product/brand')}
            >
              直接访问品牌管理
            </Button>
            <Button onClick={() => window.location.reload()}>
              刷新页面
            </Button>
          </Space>
        </Space>
      </Card>
    </div>
  );
};

export default QuickTest;
