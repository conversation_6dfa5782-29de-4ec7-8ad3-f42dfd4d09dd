.container {
  padding: 2px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

/* 统计信息区域 */
.statsSection {
  margin-bottom: 2px;
  padding: 12px;
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  border-radius: 8px;
  border: 1px solid #e8f4fd;
}

.statCard {
  text-align: center;
  padding: 8px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.dateSection {
  padding: 4px 0;
}

.sectionLabel {
  font-size: 13px;
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
}

.actionSection {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 4px 0;
}

/* 搜索区域 */
.searchSection {
  margin-bottom: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e8f0fe;
  overflow: hidden;
}

.searchHeader {
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f4ff 100%);
  border-bottom: 1px solid #d6e4ff;
  display: flex;
  align-items: center;
}

.searchTitle {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.searchContent {
  padding: 12px;
}

.searchItem {
  margin-bottom: 4px;
  text-align: center;
}

.searchLabel {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  text-align: left;
}

.searchInputWrapper {
  min-width: 180px;
  width: 100%;
}

.searchInputWrapper .ant-select,
.searchInputWrapper .ant-input {
  min-width: 180px;
  width: 100%;
}

.searchActions {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding-top: 4px;
}

.toolbar {
  margin-bottom: 16px;
}

.toolbar .ant-row {
  align-items: center;
}

.toolbar .ant-input-search {
  max-width: 300px;
}

.toolbar .ant-btn {
  height: 40px;
}

/* 说明信息样式 */
.notice {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.notice::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #1890ff, #40a9ff);
}

.noticeTitle {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.noticeContent {
  font-size: 14px;
  color: #595959;
  line-height: 1.6;
  margin-left: 32px;
}

.dangerText {
  color: #ff4d4f;
  font-weight: bold;
}

.noticeContent strong {
  color: #1890ff;
  font-weight: 600;
  background: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin: 0 2px;
}
/* 统计卡片样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 2px;
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #f0f5ff;
  font-weight: 600;
  color: #262626;
  padding: 8px 12px;
}

.ant-table-tbody > tr > td {
  padding: 6px 12px;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #e6f7ff;
}

.ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background-color: #bae7ff;
}

/* 紧凑型表格样式 */
.ant-table-small .ant-table-thead > tr > th {
  padding: 6px 8px;
  font-size: 13px;
}

.ant-table-small .ant-table-tbody > tr > td {
  padding: 4px 8px;
  font-size: 13px;
}

/* 图片预览样式 */
.ant-image {
  border-radius: 4px;
  overflow: hidden;
}

.ant-image-img {
  transition: transform 0.3s ease;
}

.ant-image:hover .ant-image-img {
  transform: scale(1.1);
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-number,
.ant-select-selector {
  border-radius: 4px;
}

.ant-input:focus,
.ant-input-focused,
.ant-input-number:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.ant-btn-danger {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.ant-btn-danger:hover,
.ant-btn-danger:focus {
  color: #ff7875;
  border-color: #ff7875;
}

/* 紧凑型卡片样式 */
.ant-card {
  border-radius: 6px;
}

.ant-card-body {
  padding: 12px;
}

/* 紧凑型分页样式 */
.ant-pagination-small .ant-pagination-item {
  min-width: 24px;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
}

.ant-pagination-small .ant-pagination-prev,
.ant-pagination-small .ant-pagination-next {
  min-width: 24px;
  height: 24px;
  line-height: 22px;
}

/* 标签样式 */
.ant-tag {
  font-weight: 500;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 日期选择器样式 */
.ant-picker {
  border-radius: 4px;
}

.ant-picker:hover,
.ant-picker-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 文本域样式 */
.ant-input {
  border-radius: 4px;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 8px;
  }

  .statsSection {
    padding: 8px;
  }

  .actionSection {
    justify-content: center;
    margin-top: 8px;
  }

  .searchActions {
    justify-content: center;
    padding-top: 8px;
  }

  .searchInputWrapper {
    min-width: 160px;
  }

  .toolbar .ant-row {
    flex-direction: column;
    gap: 8px;
  }

  .toolbar .ant-input-search {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 6px;
  }

  .statsSection {
    padding: 6px;
  }

  .searchContent {
    padding: 8px;
  }

  .searchHeader {
    padding: 6px 8px;
  }

  .searchActions {
    padding-top: 6px;
  }

  .searchActions .ant-space {
    width: 100%;
    justify-content: center;
  }

  .searchInputWrapper {
    min-width: 140px;
  }

  .searchInputWrapper .ant-select,
  .searchInputWrapper .ant-input {
    min-width: 140px;
  }

  .ant-statistic-content {
    font-size: 18px;
  }

  .ant-table-scroll {
    overflow-x: auto;
  }
}
