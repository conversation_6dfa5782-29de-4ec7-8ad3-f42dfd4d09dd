import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  InputNumber,
  DatePicker,
  Image,
  Tag,
  Statistic,
  Select,
  Tabs,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  FileImageOutlined,
  FileExcelOutlined,
  UserOutlined,
  CarOutlined,
  SettingOutlined,
  AppstoreOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import {
  getOperatingAssetsList,
  createOperatingAssetDetail,
  updateOperatingAssetDetail,
  deleteOperatingAssetDetail,
  getOperatingAssetDetail,
  exportOperatingAssetsExcel,
} from '@/api/OperatingAssetsApi';
import type { Detail, OperatingAssetType, AddOperatingAssetsParams } from '@/types/operatingAssets';
import { HumanResourcesAuditStatus, WarehouseLogisticsType } from '@/types/operatingAssets';
import { SortBy, SortOrder } from '@/types/commonFinancial';
import { SimpleImageUpload } from '@/components/ImageUpload';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { formatAmountSimple, formatNumber } from '@/utils/formatUtils';
import { getCompanyOptions, getCompanyName } from '@/constants/company';
import styles from './OperatingAssetsManagement.module.css';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Option } = Select;

interface OperatingAssetFormData extends Omit<AddOperatingAssetsParams, 'createDate'> {
  createDate: dayjs.Dayjs | string;
}

const OperatingAssetsManagement: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [assets, setAssets] = useState<Detail[]>([]);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAsset, setEditingAsset] = useState<Detail | null>(null);
  const [searchText, setSearchText] = useState('');
  const [companyCodeSearch, setCompanyCodeSearch] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<Detail[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [currentType, setCurrentType] = useState<OperatingAssetType | 'all'>('all');
  const [auditLoading, setAuditLoading] = useState(false);
  const [form] = Form.useForm<OperatingAssetFormData>();

  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().startOf('year'),
    dayjs(),
  ]);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  // 排序状态 - 初始不排序，让后端使用默认排序
  const [sortBy, setSortBy] = useState<SortBy | undefined>(undefined);
  const [sortOrder, setSortOrder] = useState<SortOrder | undefined>(undefined);
  // 仓储物流类型筛选状态
  const [warehouseLogisticsType, setWarehouseLogisticsType] = useState<
    WarehouseLogisticsType | 'all'
  >('all');

  // 运营资产类型配置
  const assetTypeConfig = {
    all: {
      label: '全部类型',
      color: 'blue',
      icon: <AppstoreOutlined />,
      description: '显示所有运营资产类型',
    },
    human_resources: {
      label: '人力资产',
      color: 'green',
      icon: <UserOutlined />,
      description: '员工工资、培训费用、招聘费用等',
    },
    warehouse_logistics: {
      label: '仓储物流',
      color: 'orange',
      icon: <CarOutlined />,
      description: '仓储费用、物流配送、运输费用等',
    },
    administrative_consumption: {
      label: '管理费用',
      color: 'purple',
      icon: <SettingOutlined />,
      description: '办公用品、水电费、通讯费等',
    },
    other: {
      label: '招待费用',
      color: 'blue',
      icon: <AppstoreOutlined />,
      description: '其他运营相关费用',
    },
  };

  // 处理URL参数，自动打开新增表单
  useEffect(() => {
    const type = searchParams.get('type');
    const employeeInfo = searchParams.get('employeeInfo');
    const humanResourcesAuditStatus = searchParams.get('humanResourcesAuditStatus');
    const createDate = searchParams.get('createDate');

    // 如果有URL参数，自动打开新增模态框并预填充数据
    if (type || employeeInfo || humanResourcesAuditStatus || createDate) {
      setEditingAsset(null);
      form.resetFields();

      const defaultValues: any = {
        createDate: createDate ? dayjs(createDate) : dayjs(),
        companyCode: '01', // 默认设置为广州
      };

      if (type) {
        defaultValues.type = type;
        // 如果是人力资产类型，切换到对应的tab
        if (type === 'human_resources') {
          setCurrentType('human_resources' as OperatingAssetType);
        }
      }

      if (employeeInfo) {
        defaultValues.employeeInfo = employeeInfo;
      }

      if (humanResourcesAuditStatus) {
        defaultValues.humanResourcesAuditStatus = humanResourcesAuditStatus;
      }

      form.setFieldsValue(defaultValues);
      setModalVisible(true);

      // 清除URL参数，避免重复触发
      setSearchParams({});
    }
  }, [searchParams, form, setSearchParams]);

  // 初始化数据
  useEffect(() => {
    const [startTime, endTime] = dateRange;
    fetchAssets(
      1,
      10,
      '',
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      currentType,
      sortBy,
      sortOrder,
      warehouseLogisticsType,
      companyCodeSearch,
    );
  }, [dateRange, currentType]);

  // 快捷键监听
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // F2: 新增
      if (event.key === 'F2' && !modalVisible) {
        event.preventDefault();
        handleAdd();
      }
      // F8: 保存
      if (event.key === 'F8' && modalVisible) {
        event.preventDefault();
        handleSave();
      }
      // F9: 审核（仅在编辑人力资产且状态为未审核时）
      if (event.key === 'F9' && modalVisible && editingAsset) {
        const currentType = form.getFieldValue('type');
        if (
          currentType === 'human_resources' &&
          editingAsset.humanResourcesAuditStatus === HumanResourcesAuditStatus.PENDING
        ) {
          event.preventDefault();
          handleQuickAudit();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modalVisible, editingAsset, form]);

  // 获取运营资产列表
  const fetchAssets = async (
    page = 1,
    pageSize = 10,
    search = '',
    startTime = '',
    endTime = '',
    type: OperatingAssetType | 'all',
    currentSortBy = sortBy,
    currentSortOrder = sortOrder,
    currentWarehouseLogisticsType: WarehouseLogisticsType | 'all' = warehouseLogisticsType,
    companyCodeFilter = '',
  ) => {
    setLoading(true);
    try {
      const params: any = {
        page,
        pageSize,
        startTime,
        endTime,
        search: search.trim() || undefined,
        companyCode: companyCodeFilter.trim() || undefined,
      };

      // 只有在有排序参数时才传递
      if (currentSortBy && currentSortOrder) {
        params.sortBy = currentSortBy;
        params.sortOrder = currentSortOrder;
      }

      // 如果不是"全部类型"，则传递type参数
      if (type !== 'all') {
        params.type = type;
      }

      // 如果是仓储物流类型且不是"全部"，则传递仓储物流类型参数
      if (type === 'warehouse_logistics' && currentWarehouseLogisticsType !== 'all') {
        params.warehouseLogisticsType = currentWarehouseLogisticsType;
      }

      const response = await getOperatingAssetsList(params);

      if (response.code === 200) {
        setAssets(response.data.details);
        setTotalAmount(response.data.totalAmount);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取运营资产列表失败');
      }
    } catch (error: any) {
      logError('获取运营资产列表', error);
      message.error(getErrorMessage(error, '获取运营资产列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    const [startTime, endTime] = dateRange;
    fetchAssets(
      1,
      pagination.pageSize,
      value,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      currentType,
      sortBy,
      sortOrder,
      warehouseLogisticsType,
      companyCodeSearch,
    );
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange([dates[0], dates[1]]);
      fetchAssets(
        1,
        pagination.pageSize,
        searchText,
        dates[0].format('YYYY-MM-DD'),
        dates[1].format('YYYY-MM-DD'),
        currentType,
        sortBy,
        sortOrder,
        warehouseLogisticsType,
        companyCodeSearch,
      );
      setPagination((prev) => ({ ...prev, current: 1 }));
    }
  };

  // 类型切换处理
  const handleTypeChange = (type: string) => {
    setCurrentType(type as OperatingAssetType | 'all');
    setSelectedRowKeys([]);
    setSelectedRows([]);
    const [startTime, endTime] = dateRange;
    fetchAssets(
      1,
      pagination.pageSize,
      searchText,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      type as OperatingAssetType | 'all',
      sortBy,
      sortOrder,
      warehouseLogisticsType,
      companyCodeSearch,
    );
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 仓储物流类型切换处理
  const handleWarehouseLogisticsTypeChange = (activeKey: string) => {
    const type = activeKey as WarehouseLogisticsType | 'all';
    setWarehouseLogisticsType(type);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    const [startTime, endTime] = dateRange;
    fetchAssets(
      1,
      pagination.pageSize,
      searchText,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      currentType,
      sortBy,
      sortOrder,
      type,
      companyCodeSearch,
    );
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 分页和排序处理
  const handleTableChange = (paginationConfig: TablePaginationConfig, _: any, sorter: any) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));

    // 处理排序
    let newSortBy: SortBy | undefined = sortBy;
    let newSortOrder: SortOrder | undefined = sortOrder;

    if (sorter && sorter.field) {
      // 根据排序字段映射到后端字段
      if (sorter.field === 'amount') {
        newSortBy = SortBy.AMOUNT;
      } else if (sorter.field === 'createDate') {
        newSortBy = SortBy.CREATE_DATE;
      }

      // 处理排序方向，包括取消排序的情况
      if (sorter.order === 'ascend') {
        newSortOrder = SortOrder.ASC;
      } else if (sorter.order === 'descend') {
        newSortOrder = SortOrder.DESC;
      } else {
        // 取消排序时，清空排序参数
        newSortBy = undefined;
        newSortOrder = undefined;
      }

      // 更新排序状态
      setSortBy(newSortBy);
      setSortOrder(newSortOrder);
    }

    const [startTime, endTime] = dateRange;
    fetchAssets(
      current,
      pageSize,
      searchText,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      currentType,
      newSortBy,
      newSortOrder,
      warehouseLogisticsType,
      companyCodeSearch,
    );
  };

  // 刷新列表
  const handleRefresh = () => {
    const [startTime, endTime] = dateRange;
    fetchAssets(
      pagination.current,
      pagination.pageSize,
      searchText,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      currentType,
      sortBy,
      sortOrder,
      warehouseLogisticsType,
      companyCodeSearch,
    );
  };

  // 新增运营资产
  const handleAdd = () => {
    setEditingAsset(null);
    form.resetFields();
    // 设置默认类型（如果当前不是"全部类型"）、默认创建日期和默认公司
    const defaultValues: any = {
      createDate: dayjs(),
      companyCode: '01', // 默认设置为广州
    };
    if (currentType !== 'all') {
      defaultValues.type = currentType;
    }
    form.setFieldsValue(defaultValues);
    setModalVisible(true);
  };

  // 编辑运营资产
  const handleEdit = async (record: Detail) => {
    try {
      const response = await getOperatingAssetDetail(record.id);
      if (response.code === 200) {
        setEditingAsset(record);
        const formValues: any = {
          type: response.data.type,
          companyCode: response.data.companyCode,
          amount: response.data.amount,
          screenshot: response.data.screenshot,
          remark: response.data.remark,
          createDate: response.data.createDate ? dayjs(response.data.createDate) : dayjs(),
        };

        // 如果是人力资产，添加特殊字段
        if (response.data.type === 'human_resources') {
          formValues.humanResourcesAuditStatus = response.data.humanResourcesAuditStatus;
          formValues.employeeInfo = response.data.employeeInfo;
        }

        // 如果是仓储物流，添加特殊字段
        if (response.data.type === 'warehouse_logistics') {
          formValues.warehouseLogisticsType = response.data.warehouseLogisticsType;
        }

        form.setFieldsValue(formValues);
        setModalVisible(true);
      } else {
        message.error(response.message || '获取运营资产详情失败');
      }
    } catch (error: any) {
      logError('获取运营资产详情', error);
      message.error(getErrorMessage(error, '获取运营资产详情失败'));
    }
  };

  // 删除运营资产
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteOperatingAssetDetail(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除运营资产', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 表格中的快速审核人力资产
  const handleQuickAuditInTable = async (record: Detail) => {
    if (
      record.type !== 'human_resources' ||
      record.humanResourcesAuditStatus !== HumanResourcesAuditStatus.PENDING
    ) {
      return;
    }

    setAuditLoading(true);
    try {
      // 直接更新审核状态为已审核，保持其他字段不变
      const updateData = {
        type: record.type,
        companyCode: record.companyCode || '01',
        amount: record.amount,
        screenshot: record.screenshot,
        remark: record.remark,
        createDate: record.createDate || dayjs().format('YYYY-MM-DD'),
        employeeInfo: record.employeeInfo || undefined,
        humanResourcesAuditStatus: HumanResourcesAuditStatus.APPROVED,
      };

      const response = await updateOperatingAssetDetail(record.id, updateData);
      const result = handleApiResponse(response, '审核成功', '审核失败');

      if (result.success) {
        message.success('审核状态已更新为已审核');
        // 刷新列表以显示最新状态
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('审核人力资产', error);
      message.error(getErrorMessage(error, '审核失败'));
    } finally {
      setAuditLoading(false);
    }
  };

  // 模态框中的快速审核人力资产
  const handleQuickAudit = async () => {
    if (!editingAsset || editingAsset.type !== 'human_resources') {
      return;
    }

    setAuditLoading(true);
    try {
      // 获取当前表单值
      const currentValues = form.getFieldsValue();

      // 更新审核状态为已审核
      const updateData = {
        ...currentValues,
        createDate: currentValues.createDate
          ? typeof currentValues.createDate === 'string'
            ? currentValues.createDate
            : currentValues.createDate.format('YYYY-MM-DD')
          : dayjs().format('YYYY-MM-DD'),
        humanResourcesAuditStatus: HumanResourcesAuditStatus.APPROVED,
        companyCode: currentValues.companyCode || '01', // 确保有公司编码
      };

      const response = await updateOperatingAssetDetail(editingAsset.id, updateData);
      const result = handleApiResponse(response, '审核成功', '审核失败');

      if (result.success) {
        message.success('审核状态已更新为已审核');
        // 更新表单中的审核状态
        form.setFieldsValue({
          humanResourcesAuditStatus: HumanResourcesAuditStatus.APPROVED,
        });
        // 更新编辑中的资产状态
        setEditingAsset({
          ...editingAsset,
          humanResourcesAuditStatus: HumanResourcesAuditStatus.APPROVED,
          humanResourcesAuditStatusLabel: '已审核',
        });
        // 刷新列表以显示最新状态
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('审核人力资产', error);
      message.error(getErrorMessage(error, '审核失败'));
    } finally {
      setAuditLoading(false);
    }
  };

  // 保存运营资产
  const handleSave = async () => {
    if (saveLoading) return; // 防止重复提交

    setSaveLoading(true);
    try {
      const values = await form.validateFields();
      // 格式化数据
      const formattedValues = {
        ...values,
        createDate: values.createDate
          ? typeof values.createDate === 'string'
            ? values.createDate
            : values.createDate.format('YYYY-MM-DD')
          : dayjs().format('YYYY-MM-DD'),
      };
      let response;

      if (editingAsset) {
        // 编辑模式
        response = await updateOperatingAssetDetail(editingAsset.id, formattedValues);
      } else {
        // 新增模式
        response = await createOperatingAssetDetail(formattedValues);
      }

      const result = handleApiResponse(
        response,
        editingAsset ? '更新成功' : '创建成功',
        editingAsset ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError(editingAsset ? '更新运营资产' : '创建运营资产', error);
      message.error(getErrorMessage(error, editingAsset ? '更新失败' : '创建失败'));
    } finally {
      setSaveLoading(false);
    }
  };

  // 导出Excel（选中的记录）
  const handleExport = async () => {
    try {
      const [startTime, endTime] = dateRange;
      const params = {
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        detailIds: selectedRowKeys.length > 0 ? (selectedRowKeys as string[]) : undefined,
        type: currentType !== 'all' ? currentType : undefined,
        search: searchText.trim() || undefined,
        companyCode: companyCodeSearch.trim() || undefined,
      };

      const blob = await exportOperatingAssetsExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const typeLabel =
        currentType === 'all'
          ? '全部类型'
          : assetTypeConfig[currentType as keyof typeof assetTypeConfig]?.label || '';
      const fileName =
        selectedRowKeys.length > 0
          ? `运营资产_选中记录_${typeLabel}_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`
          : `运营资产_${typeLabel}_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出运营资产Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 导出全部记录
  const handleExportAll = async () => {
    try {
      const [startTime, endTime] = dateRange;
      const params = {
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        type: currentType !== 'all' ? currentType : undefined,
        search: searchText.trim() || undefined,
        companyCode: companyCodeSearch.trim() || undefined,
      };

      const blob = await exportOperatingAssetsExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const typeLabel =
        currentType === 'all'
          ? '全部类型'
          : assetTypeConfig[currentType as keyof typeof assetTypeConfig]?.label || '';
      link.download = `运营资产_全部记录_${typeLabel}_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出全部运营资产Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 表格行选择配置
  const rowSelection: TableRowSelection<Detail> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[], newSelectedRows: Detail[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
      setSelectedRows(newSelectedRows);
    },
    getCheckboxProps: (record: Detail) => ({
      disabled: record.isDeleted,
    }),
  };

  // 获取类型标签样式
  const getTypeTagClass = (type: OperatingAssetType) => {
    const typeMap = {
      human_resources: 'humanResources',
      warehouse_logistics: 'warehouseLogistics',
      administrative_consumption: 'administrativeConsumption',
      other: 'other',
    };
    return `${styles.typeTag} ${styles[typeMap[type]]}`;
  };

  // 表格列定义
  const columns: ColumnsType<Detail> = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: OperatingAssetType) => {
        const config = assetTypeConfig[type];
        return (
          <Tag className={getTypeTagClass(type)} icon={config.icon}>
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 150,
      render: (amount: number) => <Tag color="green">{formatAmountSimple(amount)}</Tag>,
      sorter: true,
      sortOrder:
        sortBy === SortBy.AMOUNT && sortOrder
          ? sortOrder === SortOrder.ASC
            ? 'ascend'
            : 'descend'
          : null,
    },
    {
      title: '所属公司',
      key: 'companyName',
      width: 120,
      render: (_, record) => {
        if (record.companyName) {
          return record.companyName;
        }
        // 如果没有 companyName，使用 companyCode 获取公司名称
        return record.companyCode ? getCompanyName(record.companyCode) : '-';
      },
    },
    {
      title: '截图',
      dataIndex: 'screenshot',
      key: 'screenshot',
      width: 100,
      render: (screenshot: string) =>
        screenshot ? (
          <Image
            width={50}
            height={50}
            src={screenshot}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            placeholder={<FileImageOutlined style={{ fontSize: 20, color: '#ccc' }} />}
          />
        ) : (
          <div
            style={{
              width: 50,
              height: 50,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: '#f5f5f5',
              borderRadius: 4,
            }}
          >
            <FileImageOutlined style={{ fontSize: 20, color: '#ccc' }} />
          </div>
        ),
    },
    {
      title: '员工信息',
      dataIndex: 'employeeInfo',
      key: 'employeeInfo',
      width: 150,
      ellipsis: true,
      render: (text: string, record: Detail) => {
        if (record.type === 'human_resources') {
          return text || '-';
        }
        return '-';
      },
    },
    {
      title: '审核状态',
      dataIndex: 'humanResourcesAuditStatus',
      key: 'humanResourcesAuditStatus',
      width: 120,
      render: (status: HumanResourcesAuditStatus, record: Detail) => {
        if (record.type === 'human_resources') {
          const statusConfig = {
            [HumanResourcesAuditStatus.PENDING]: { color: 'orange', text: '未审核' },
            [HumanResourcesAuditStatus.APPROVED]: { color: 'green', text: '已审核' },
          };
          const config = statusConfig[status] || { color: 'default', text: '未知' };
          return <Tag color={config.color}>{config.text}</Tag>;
        }
        return '-';
      },
    },
    {
      title: '收支类型',
      dataIndex: 'warehouseLogisticsType',
      key: 'warehouseLogisticsType',
      width: 120,
      render: (type: string, record: Detail) => {
        if (record.type === 'warehouse_logistics' && type) {
          const typeConfig = {
            income: { color: 'green', text: '收入' },
            expense: { color: 'red', text: '支出' },
          };
          const config = typeConfig[type as keyof typeof typeConfig] || {
            color: 'default',
            text: '未知',
          };
          return <Tag color={config.color}>{config.text}</Tag>;
        }
        return '-';
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '创建日期',
      dataIndex: 'createDate',
      key: 'createDate',
      width: 120,
      render: (text: string) => (text ? dayjs(text).format('YYYY-MM-DD') : '-'),
      sorter: true,
      sortOrder:
        sortBy === SortBy.CREATE_DATE && sortOrder
          ? sortOrder === SortOrder.ASC
            ? 'ascend'
            : 'descend'
          : null,
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
            disabled={record.isDeleted}
          >
            编辑
          </Button>
          {/* 人力资产审核按钮 */}
          {record.type === 'human_resources' &&
            record.humanResourcesAuditStatus === HumanResourcesAuditStatus.PENDING && (
              <Button
                type="link"
                icon={<CheckOutlined />}
                onClick={() => handleQuickAuditInTable(record)}
                size="small"
                disabled={record.isDeleted}
                style={{ color: '#52c41a' }}
              >
                审核
              </Button>
            )}
          <Popconfirm
            title="确定要删除这条运营资产记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            disabled={record.isDeleted}
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
              disabled={record.isDeleted}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card size="small" style={{ marginBottom: '12px' }}>
        <Row gutter={[12, 6]} style={{ marginBottom: 12 }}>
          <Col span={6}>
            <Statistic
              title="总金额"
              value={totalAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
              formatter={(value) => formatNumber(value as number, 2)}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="记录总数"
              value={pagination.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已选择"
              value={selectedRowKeys.length}
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="选中金额"
              value={selectedRows.reduce((sum, row) => sum + row.amount, 0)}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa8c16' }}
              formatter={(value) => formatNumber(value as number, 2)}
            />
          </Col>
        </Row>

        {/* 类型标签页 */}
        <Tabs
          activeKey={currentType}
          onChange={handleTypeChange}
          style={{ marginBottom: 16 }}
          items={Object.entries(assetTypeConfig).map(([key, config]) => ({
            key,
            label: (
              <span>
                {config.icon}
                <span style={{ marginLeft: 8 }}>{config.label}</span>
              </span>
            ),
          }))}
        />

        {/* 仓储物流类型筛选 - 只在选择仓储物流类型时显示 */}
        {currentType === 'warehouse_logistics' && (
          <div style={{ marginBottom: 16 }}>
            <Tabs
              activeKey={warehouseLogisticsType}
              onChange={handleWarehouseLogisticsTypeChange}
              size="small"
              items={[
                {
                  key: 'all',
                  label: (
                    <span>
                      <AppstoreOutlined />
                      <span style={{ marginLeft: 8 }}>全部</span>
                    </span>
                  ),
                },
                {
                  key: WarehouseLogisticsType.INCOME,
                  label: (
                    <span>
                      <Tag color="green" style={{ margin: 0 }}>
                        收入
                      </Tag>
                    </span>
                  ),
                },
                {
                  key: WarehouseLogisticsType.EXPENSE,
                  label: (
                    <span>
                      <Tag color="red" style={{ margin: 0 }}>
                        支出
                      </Tag>
                    </span>
                  ),
                },
              ]}
            />
          </div>
        )}

        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <Space size="middle">
                <Search
                  placeholder="搜索备注内容"
                  allowClear
                  enterButton={<SearchOutlined />}
                  size="large"
                  onSearch={handleSearch}
                  style={{ width: 300 }}
                />
                <Select
                  placeholder="选择公司"
                  allowClear
                  size="large"
                  style={{ width: 150 }}
                  value={companyCodeSearch || undefined}
                  onChange={(value) => {
                    setCompanyCodeSearch(value || '');
                    const [startTime, endTime] = dateRange;
                    fetchAssets(
                      1,
                      pagination.pageSize,
                      searchText,
                      startTime.format('YYYY-MM-DD'),
                      endTime.format('YYYY-MM-DD'),
                      currentType,
                      sortBy,
                      sortOrder,
                      warehouseLogisticsType,
                      value || '',
                    );
                    setPagination((prev) => ({ ...prev, current: 1 }));
                  }}
                  options={[{ label: '全部公司', value: '' }, ...getCompanyOptions()]}
                />
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  size="large"
                  format="YYYY-MM-DD"
                  allowClear={false}
                />
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<FileExcelOutlined />}
                  onClick={handleExport}
                  disabled={selectedRowKeys.length === 0}
                >
                  导出选中 ({selectedRowKeys.length})
                </Button>
                <Button type="default" icon={<FileExcelOutlined />} onClick={handleExportAll}>
                  导出全部
                </Button>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增运营资产(F2)
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={assets}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 运营资产编辑模态框 */}
      <Modal
        title={
          <div>
            {editingAsset ? '编辑运营资产' : '新增运营资产'}
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              快捷键：F2 新增 | F8 保存
              {editingAsset?.type === 'human_resources' &&
                editingAsset?.humanResourcesAuditStatus === HumanResourcesAuditStatus.PENDING &&
                ' | F9 快速审核'}
            </div>
          </div>
        }
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={700}
        destroyOnClose
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          editingAsset?.type === 'human_resources' &&
            editingAsset?.humanResourcesAuditStatus === HumanResourcesAuditStatus.PENDING && (
              <Button
                key="audit"
                type="default"
                icon={<CheckOutlined />}
                onClick={handleQuickAudit}
                loading={auditLoading}
              >
                快速审核 (F9)
              </Button>
            ),
          <Button key="save" type="primary" onClick={handleSave} loading={saveLoading}>
            保存 (F8)
          </Button>,
        ]}
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="type"
                label="资产类型"
                rules={[{ required: true, message: '请选择资产类型' }]}
              >
                <Select
                  placeholder="请选择资产类型"
                  size="large"
                  onChange={(value) => {
                    // 当类型改变时，清空相关字段
                    if (value !== 'human_resources') {
                      form.setFieldsValue({
                        humanResourcesAuditStatus: undefined,
                        employeeInfo: undefined,
                      });
                    } else {
                      // 新增人力资产时，默认设置为未审核状态
                      if (!editingAsset) {
                        form.setFieldsValue({
                          humanResourcesAuditStatus: HumanResourcesAuditStatus.PENDING,
                        });
                      }
                    }

                    // 当类型改变时，清空仓储物流相关字段
                    if (value !== 'warehouse_logistics') {
                      form.setFieldsValue({
                        warehouseLogisticsType: undefined,
                      });
                    }
                  }}
                >
                  {Object.entries(assetTypeConfig)
                    .filter(([key]) => key !== 'all') // 排除"全部类型"选项
                    .map(([key, config]) => (
                      <Option key={key} value={key}>
                        <Space>
                          {config.icon}
                          {config.label}
                        </Space>
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="companyCode"
                label="所属公司"
                rules={[{ required: true, message: '请选择所属公司' }]}
              >
                <Select placeholder="请选择所属公司" size="large" options={getCompanyOptions()} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="amount"
                label="金额"
                rules={[
                  { required: true, message: '请输入金额' },
                  { type: 'number', min: 0, message: '金额不能小于0' },
                ]}
              >
                <InputNumber
                  placeholder="请输入金额"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  addonBefore="¥"
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="createDate"
                label="创建日期"
                rules={[{ required: true, message: '请选择创建日期' }]}
              >
                <DatePicker
                  placeholder="请选择创建日期"
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          {/* 人力资产专用字段 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const currentType = getFieldValue('type');
              if (currentType === 'human_resources') {
                return (
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="employeeInfo"
                        label="员工信息"
                        rules={[{ required: true, message: '请输入员工信息' }]}
                      >
                        <Input placeholder="例如：张三 - 开发工程师" size="large" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="humanResourcesAuditStatus"
                        label="审核状态"
                        rules={[{ required: true, message: '请选择审核状态' }]}
                      >
                        <Select placeholder="请选择审核状态" size="large">
                          <Option value={HumanResourcesAuditStatus.PENDING}>
                            <Tag color="orange">未审核</Tag>
                          </Option>
                          <Option value={HumanResourcesAuditStatus.APPROVED}>
                            <Tag color="green">已审核</Tag>
                          </Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                );
              }
              return null;
            }}
          </Form.Item>

          {/* 仓储物流专用字段 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const currentType = getFieldValue('type');
              if (currentType === 'warehouse_logistics') {
                return (
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="warehouseLogisticsType"
                        label="收支类型"
                        rules={[{ required: true, message: '请选择收支类型' }]}
                      >
                        <Select placeholder="请选择收支类型" size="large">
                          <Option value={WarehouseLogisticsType.INCOME}>
                            <Tag color="green">收入</Tag>
                          </Option>
                          <Option value={WarehouseLogisticsType.EXPENSE}>
                            <Tag color="red">支出</Tag>
                          </Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                );
              }
              return null;
            }}
          </Form.Item>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="screenshot"
                label="截图"
                rules={[{ required: true, message: '请上传截图' }]}
              >
                <SimpleImageUpload folder="operating-assets" enablePaste={true} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="remark" label="备注">
            <TextArea placeholder="请输入备注信息" rows={4} maxLength={500} showCount />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OperatingAssetsManagement;
