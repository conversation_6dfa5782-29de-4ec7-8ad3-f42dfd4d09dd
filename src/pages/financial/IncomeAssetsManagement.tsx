import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  DatePicker,
  Image,
  Tag,
  Statistic,
  Tabs,
  Spin,
  Select,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  <PERSON><PERSON>mageOutlined,
  FileExcelOutlined,
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  Pie<PERSON>hartOutlined,
  TableOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import {
  getIncomeList,
  createIncomeDetail,
  updateIncomeDetail,
  deleteIncomeDetail,
  getIncomeDetail,
  exportIncomeExcel,
} from '@/api/IncomeApi';
import type { AddIncomeParams, Detail } from '@/types/Income';
import { SortBy, SortOrder } from '@/types/commonFinancial';
import { SimpleImageUpload } from '@/components/ImageUpload';
import { CustomerSearchSelector, UserSearchSelector } from '@/components';
import { formatAmountSimple, formatNumber } from '@/utils/formatUtils';
import FormattedAmountInput from '@/components/FormattedAmountInput';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import Chart from '@/components/charts/Chart';
import type { ChartDataItem } from '@/components/charts/chartsInterface';
import { getCompanyOptions, getCompanyName } from '@/constants/company';
import styles from './FixedAssetsManagement.module.css';

const { RangePicker } = DatePicker;
const { TextArea } = Input;

interface IncomeFormData extends Omit<AddIncomeParams, 'createDate'> {
  createDate: dayjs.Dayjs | string;
}

const IncomeAssetsManagement: React.FC = () => {
  const [form] = Form.useForm<IncomeFormData>();
  const [incomes, setIncomes] = useState<Detail[]>([]);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingIncome, setEditingIncome] = useState<Detail | null>(null);
  const [customerSearch, setCustomerSearch] = useState('');
  const [userSearch, setUserSearch] = useState('');
  const [remarkSearch, setRemarkSearch] = useState('');
  const [companyCodeSearch, setCompanyCodeSearch] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 视图模式状态
  const [viewMode, setViewMode] = useState<'table' | 'chart'>('table');

  // 图表相关状态
  const [chartLoading, setChartLoading] = useState(false);
  const [chartData, setChartData] = useState<Detail[]>([]);
  const [chartPersonnelFilter, setChartPersonnelFilter] = useState<string>('');
  const [chartDateRange, setChartDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().startOf('year'),
    dayjs(),
  ]);

  // 默认日期范围为当前年
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().startOf('year'),
    dayjs(),
  ]);
  // 排序状态 - 初始不排序，让后端使用默认排序
  const [sortBy, setSortBy] = useState<SortBy | undefined>(undefined);
  const [sortOrder, setSortOrder] = useState<SortOrder | undefined>(undefined);

  // 获取收入列表
  const fetchIncomes = async (
    page = 1,
    pageSize = 10,
    startTime = '',
    endTime = '',
    customerSearch = '',
    responsibleUserSearch = '',
    remarkSearch = '',
    companyCodeSearch = '',
    currentSortBy = sortBy,
    currentSortOrder = sortOrder,
  ) => {
    setLoading(true);
    try {
      const params: any = {
        page,
        pageSize,
        startTime,
        endTime,
        customerSearch: customerSearch.trim() || undefined,
        responsibleUserSearch: responsibleUserSearch.trim() || undefined,
        remarkSearch: remarkSearch.trim() || undefined,
        companyCode: companyCodeSearch.trim() || undefined,
      };

      // 只有在有排序参数时才传递
      if (currentSortBy && currentSortOrder) {
        params.sortBy = currentSortBy;
        params.sortOrder = currentSortOrder;
      }

      const response = await getIncomeList(params);

      if (response.code === 200) {
        setIncomes(response.data.details);
        setTotalAmount(response.data.totalAmount);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取收入列表失败');
      }
    } catch (error: any) {
      logError('获取收入列表', error);
      message.error(getErrorMessage(error, '获取收入列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 获取图表数据
  const fetchChartData = async (startTime: string, endTime: string, personnelFilter = '') => {
    setChartLoading(true);
    try {
      const params: any = {
        page: 1,
        pageSize: 10, // 获取更多数据用于图表展示
        startTime,
        endTime,
        responsibleUserSearch: personnelFilter.trim() || undefined,
      };

      const response = await getIncomeList(params);

      if (response.code === 200) {
        setChartData(response.data.details);
      } else {
        message.error(response.message || '获取图表数据失败');
        setChartData([]);
      }
    } catch (error: any) {
      logError('获取图表数据', error);
      message.error(getErrorMessage(error, '获取图表数据失败'));
      setChartData([]);
    } finally {
      setChartLoading(false);
    }
  };

  // 处理图表数据转换
  const processChartData = (data: Detail[], type: 'line' | 'bar' | 'pie'): ChartDataItem[] => {
    if (!data || data.length === 0) return [];

    if (type === 'pie') {
      // 饼图：按人员聚合收入
      const personnelMap = new Map<string, number>();
      data.forEach((item) => {
        const key = item.responsibleUserName || '未知';
        personnelMap.set(key, (personnelMap.get(key) || 0) + item.amount);
      });

      return Array.from(personnelMap.entries()).map(([name, amount]) => ({
        x: name,
        y: amount,
      }));
    } else {
      // 线图和柱图：按日期聚合收入
      const dateMap = new Map<string, number>();
      data.forEach((item) => {
        const date = item.createDate;
        dateMap.set(date, (dateMap.get(date) || 0) + item.amount);
      });

      // 按日期排序
      const sortedEntries = Array.from(dateMap.entries()).sort(([a], [b]) => a.localeCompare(b));

      return sortedEntries.map(([date, amount]) => ({
        x: date,
        y: amount,
      }));
    }
  };

  // 初始化加载
  useEffect(() => {
    const [startTime, endTime] = dateRange;
    fetchIncomes(
      1,
      10,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      '',
      '',
      '',
      '',
      sortBy,
      sortOrder,
    );
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, current: 1 }));
    const [startTime, endTime] = dateRange;
    fetchIncomes(
      1,
      pagination.pageSize,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      customerSearch,
      userSearch,
      remarkSearch,
      companyCodeSearch,
      sortBy,
      sortOrder,
    );
  };

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
      setPagination((prev) => ({ ...prev, current: 1 }));
      fetchIncomes(
        1,
        pagination.pageSize,
        dates[0].format('YYYY-MM-DD'),
        dates[1].format('YYYY-MM-DD'),
        customerSearch,
        userSearch,
        remarkSearch,
        companyCodeSearch,
        sortBy,
        sortOrder,
      );
    }
  };

  // 分页和排序处理
  const handleTableChange = (paginationConfig: TablePaginationConfig, _: any, sorter: any) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));

    // 处理排序
    let newSortBy: SortBy | undefined = sortBy;
    let newSortOrder: SortOrder | undefined = sortOrder;

    if (sorter && sorter.field) {
      // 根据排序字段映射到后端字段
      if (sorter.field === 'amount') {
        newSortBy = SortBy.AMOUNT;
      } else if (sorter.field === 'createDate') {
        newSortBy = SortBy.CREATE_DATE;
      }

      // 处理排序方向，包括取消排序的情况
      if (sorter.order === 'ascend') {
        newSortOrder = SortOrder.ASC;
      } else if (sorter.order === 'descend') {
        newSortOrder = SortOrder.DESC;
      } else {
        // 取消排序时，清空排序参数
        newSortBy = undefined;
        newSortOrder = undefined;
      }

      // 更新排序状态
      setSortBy(newSortBy);
      setSortOrder(newSortOrder);
    }

    const [startTime, endTime] = dateRange;
    fetchIncomes(
      current,
      pageSize,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      customerSearch,
      userSearch,
      remarkSearch,
      companyCodeSearch,
      newSortBy,
      newSortOrder,
    );
  };

  // 刷新列表
  const handleRefresh = () => {
    const [startTime, endTime] = dateRange;
    fetchIncomes(
      pagination.current,
      pagination.pageSize,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      customerSearch,
      userSearch,
      remarkSearch,
      companyCodeSearch,
      sortBy,
      sortOrder,
    );
  };

  // 新增收入
  const handleAdd = () => {
    setEditingIncome(null);
    form.resetFields();
    // 设置默认的创建日期为当天和默认公司
    form.setFieldsValue({
      createDate: dayjs(),
      companyCode: '01', // 默认设置为广州
    });
    setModalVisible(true);
  };

  // 编辑收入
  const handleEdit = async (record: Detail) => {
    try {
      const response = await getIncomeDetail(record.id);
      if (response.code === 200) {
        setEditingIncome(record);
        form.setFieldsValue({
          companyCode: response.data.companyCode,
          customerCode: response.data.customerCode,
          responsibleUserCode: response.data.responsibleUserCode,
          amount: response.data.amount,
          screenshot: response.data.screenshot,
          remark: response.data.remark,
          createDate: response.data.createDate ? dayjs(response.data.createDate) : dayjs(),
        });
        setModalVisible(true);
      } else {
        message.error(response.message || '获取收入详情失败');
      }
    } catch (error: any) {
      logError('获取收入详情', error);
      message.error(getErrorMessage(error, '获取收入详情失败'));
    }
  };

  // 删除收入
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteIncomeDetail(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除收入', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 保存收入
  const handleSave = async () => {
    if (saveLoading) return; // 防止重复提交

    setSaveLoading(true);
    try {
      const values = await form.validateFields();
      // 格式化数据
      const formattedValues = {
        ...values,
        createDate: values.createDate
          ? typeof values.createDate === 'string'
            ? values.createDate
            : values.createDate.format('YYYY-MM-DD')
          : dayjs().format('YYYY-MM-DD'),
      };
      let response;

      if (editingIncome) {
        // 编辑模式
        response = await updateIncomeDetail(editingIncome.id, formattedValues);
      } else {
        // 新增模式
        response = await createIncomeDetail(formattedValues);
      }

      const result = handleApiResponse(
        response,
        editingIncome ? '更新成功' : '创建成功',
        editingIncome ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError(editingIncome ? '更新收入' : '创建收入', error);
      message.error(getErrorMessage(error, editingIncome ? '更新失败' : '创建失败'));
    } finally {
      setSaveLoading(false);
    }
  };

  // 导出Excel（选中的记录）
  const handleExport = async () => {
    try {
      const [startTime, endTime] = dateRange;
      const params = {
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        detailIds: selectedRowKeys.length > 0 ? (selectedRowKeys as string[]) : undefined,
        customerSearch: customerSearch.trim() || undefined,
        responsibleUserSearch: userSearch.trim() || undefined,
        remarkSearch: remarkSearch.trim() || undefined,
        companyCode: companyCodeSearch.trim() || undefined,
      };

      const blob = await exportIncomeExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const fileName =
        selectedRowKeys.length > 0
          ? `收入资产_选中记录_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`
          : `收入资产_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出收入Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 导出全部记录
  const handleExportAll = async () => {
    try {
      const [startTime, endTime] = dateRange;
      const params = {
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        // 不传detailIds表示导出全部
        customerSearch: customerSearch.trim() || undefined,
        responsibleUserSearch: userSearch.trim() || undefined,
        remarkSearch: remarkSearch.trim() || undefined,
        companyCode: companyCodeSearch.trim() || undefined,
      };

      const blob = await exportIncomeExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `收入资产_全部记录_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出全部收入Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 图表相关处理函数
  const handleChartDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setChartDateRange(dates);
      fetchChartData(
        dates[0].format('YYYY-MM-DD'),
        dates[1].format('YYYY-MM-DD'),
        chartPersonnelFilter,
      );
    }
  };

  const handleChartPersonnelFilterChange = (value: string) => {
    setChartPersonnelFilter(value);
    const [startTime, endTime] = chartDateRange;
    fetchChartData(startTime.format('YYYY-MM-DD'), endTime.format('YYYY-MM-DD'), value);
  };

  const handleViewModeChange = (mode: 'table' | 'chart') => {
    setViewMode(mode);
    if (mode === 'chart') {
      // 切换到图表视图时，获取图表数据
      const [startTime, endTime] = chartDateRange;
      fetchChartData(
        startTime.format('YYYY-MM-DD'),
        endTime.format('YYYY-MM-DD'),
        chartPersonnelFilter,
      );
    }
  };

  // 表格行选择
  const rowSelection: TableRowSelection<Detail> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record: Detail) => ({
      disabled: record.isDeleted,
    }),
  };

  // 表格列定义
  const columns: ColumnsType<Detail> = [
    {
      title: '客户',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 150,
      render: (customerName: string, record: Detail) => (
        <div>
          <div style={{ fontWeight: 500 }}>{customerName}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>{record.customerCode}</div>
        </div>
      ),
    },
    {
      title: '经办人',
      dataIndex: 'responsibleUserName',
      key: 'responsibleUserName',
      width: 180,
      render: (responsibleUserName: string, record: Detail) => (
        <div>
          <div style={{ fontWeight: 500 }}>{responsibleUserName}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>{record.responsibleUserCode}</div>
        </div>
      ),
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 130,
      render: (amount: number) => <Tag color="green">{formatAmountSimple(amount)}</Tag>,
      sorter: true,
      sortOrder:
        sortBy === SortBy.AMOUNT && sortOrder
          ? sortOrder === SortOrder.ASC
            ? 'ascend'
            : 'descend'
          : null,
    },
    {
      title: '所属公司',
      key: 'companyName',
      width: 120,
      render: (_, record) => {
        if (record.companyName) {
          return record.companyName;
        }
        // 如果没有 companyName，使用 companyCode 获取公司名称
        return record.companyCode ? getCompanyName(record.companyCode) : '-';
      },
    },
    {
      title: '截图',
      dataIndex: 'screenshot',
      key: 'screenshot',
      width: 100,
      render: (screenshot: string) =>
        screenshot ? (
          <Image
            width={50}
            height={50}
            src={screenshot}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            placeholder={<FileImageOutlined style={{ fontSize: 20, color: '#ccc' }} />}
          />
        ) : (
          <div
            style={{
              width: 50,
              height: 50,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: '#f5f5f5',
              borderRadius: 4,
            }}
          >
            <FileImageOutlined style={{ fontSize: 20, color: '#ccc' }} />
          </div>
        ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      render: (remark: string) => remark || '-',
    },
    {
      title: '创建日期',
      dataIndex: 'createDate',
      key: 'createDate',
      width: 180,
      render: (createDate: string) => createDate || '-',
      sorter: true,
      sortOrder:
        sortBy === SortBy.CREATE_DATE && sortOrder
          ? sortOrder === SortOrder.ASC
            ? 'ascend'
            : 'descend'
          : null,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条收入记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // F2 - 新增
      if (event.key === 'F2' && !modalVisible) {
        event.preventDefault();
        handleAdd();
      }

      // F5 - 搜索
      if (event.key === 'F5') {
        event.preventDefault();
        if (modalVisible) {
          // 如果模态框打开，不处理
          return;
        }
        handleSearch();
        message.info('检索完毕 (F5)');
      }

      // F8 - 保存 (仅在模态框打开时)
      if (event.key === 'F8') {
        event.preventDefault();
        if (modalVisible && !saveLoading) {
          handleSave();
          message.info('正在保存... (F8)');
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [
    modalVisible,
    saveLoading,
    customerSearch,
    userSearch,
    remarkSearch,
    companyCodeSearch,
    dateRange,
    pagination,
  ]);

  return (
    <div className={styles.container}>
      <Card size="small" style={{ marginBottom: '12px' }}>
        {/* 统计信息区域 */}
        <div className={styles.statsSection}>
          <Row gutter={[12, 8]} align="middle">
            <Col xs={24} sm={12} lg={8}>
              <div className={styles.statCard}>
                <Statistic
                  title="总收入金额"
                  value={totalAmount}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#52c41a', fontSize: '28px', fontWeight: 'bold' }}
                  formatter={(value) => formatNumber(value as number, 2)}
                />
              </div>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <div className={styles.dateSection}>
                <div className={styles.sectionLabel}>日期范围</div>
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  style={{ width: '100%' }}
                  size="large"
                />
              </div>
            </Col>
            <Col xs={24} sm={24} lg={8}>
              <div className={styles.actionSection}>
                <Space wrap size="middle">
                  <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} size="large">
                    新增收入(F2)
                  </Button>
                  <Button
                    icon={<FileExcelOutlined />}
                    onClick={handleExport}
                    disabled={loading}
                    size="large"
                  >
                    {selectedRowKeys.length > 0 ? '导出选中' : '导出当前'}
                  </Button>
                  <Button
                    icon={<FileExcelOutlined />}
                    onClick={handleExportAll}
                    disabled={loading}
                    size="large"
                  >
                    导出全部
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleRefresh}
                    disabled={loading}
                    size="large"
                  >
                    刷新
                  </Button>
                </Space>
              </div>
            </Col>
          </Row>
        </div>

        {/* 搜索筛选区域 */}
        <div className={styles.searchSection}>
          <div className={styles.searchHeader}>
            <SearchOutlined style={{ marginRight: 8, color: '#1890ff' }} />
            <span className={styles.searchTitle}>筛选条件</span>
          </div>
          <div className={styles.searchContent}>
            <Row gutter={[20, 24]} align="middle" justify="center">
              <Col xs={24} sm={12} lg={5}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>客户</label>
                  <div className={styles.searchInputWrapper}>
                    <CustomerSearchSelector
                      value={customerSearch}
                      onChange={(value) => setCustomerSearch(value)}
                      placeholder="请选择客户"
                      allowClear
                      style={{ width: '100%' }}
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} lg={5}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>经办人</label>
                  <div className={styles.searchInputWrapper}>
                    <UserSearchSelector
                      value={userSearch}
                      onChange={(value) => setUserSearch(value)}
                      placeholder="请选择经办人"
                      allowClear
                      style={{ width: '100%' }}
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} lg={5}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>备注</label>
                  <div className={styles.searchInputWrapper}>
                    <Input
                      placeholder="请输入备注关键词"
                      value={remarkSearch}
                      onChange={(e) => setRemarkSearch(e.target.value)}
                      allowClear
                      size="large"
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} lg={4}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>公司</label>
                  <div className={styles.searchInputWrapper}>
                    <Select
                      placeholder="请选择公司"
                      value={companyCodeSearch || undefined}
                      onChange={(value) => setCompanyCodeSearch(value || '')}
                      allowClear
                      size="large"
                      style={{ width: '100%' }}
                      options={[{ label: '全部', value: '' }, ...getCompanyOptions()]}
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={24} lg={5}>
                <div className={styles.searchActions}>
                  <Space size="large" wrap>
                    <Button
                      type="primary"
                      onClick={handleSearch}
                      size="large"
                      loading={loading}
                      title="搜索"
                    >
                      搜索 (F5)
                    </Button>
                    <Button
                      onClick={() => {
                        setCustomerSearch('');
                        setUserSearch('');
                        setRemarkSearch('');
                        setCompanyCodeSearch('');
                        handleSearch();
                      }}
                      size="large"
                    >
                      重置
                    </Button>
                  </Space>
                </div>
              </Col>
            </Row>
          </div>
        </div>

        {/* 视图切换按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type={viewMode === 'table' ? 'primary' : 'default'}
              icon={<TableOutlined />}
              onClick={() => handleViewModeChange('table')}
            >
              表格视图
            </Button>
            <Button
              type={viewMode === 'chart' ? 'primary' : 'default'}
              icon={<BarChartOutlined />}
              onClick={() => handleViewModeChange('chart')}
            >
              图表视图
            </Button>
          </Space>
        </div>

        {viewMode === 'table' ? (
          <Table
            columns={columns}
            dataSource={incomes}
            rowKey="id"
            loading={loading}
            rowSelection={rowSelection}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 1200 }}
          />
        ) : (
          <div>
            {/* 图表控制区域 */}
            <div
              style={{ marginBottom: 24, padding: '16px', background: '#fafafa', borderRadius: 6 }}
            >
              <Row gutter={[16, 16]} align="middle">
                <Col xs={24} sm={12} lg={8}>
                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                      图表日期范围
                    </label>
                    <RangePicker
                      value={chartDateRange}
                      onChange={handleChartDateRangeChange}
                      style={{ width: '100%' }}
                    />
                  </div>
                </Col>
                <Col xs={24} sm={12} lg={8}>
                  <div>
                    <label style={{ display: 'block', marginBottom: 8, fontWeight: 500 }}>
                      经办人筛选 (线图/柱图)
                    </label>
                    <UserSearchSelector
                      value={chartPersonnelFilter}
                      onChange={handleChartPersonnelFilterChange}
                      placeholder="全部经办人"
                      allowClear
                      style={{ width: '100%' }}
                    />
                  </div>
                </Col>
              </Row>
            </div>

            {/* 图表展示区域 */}
            <Spin spinning={chartLoading}>
              <Tabs
                defaultActiveKey="line"
                items={[
                  {
                    key: 'line',
                    label: (
                      <span>
                        <LineChartOutlined />
                        线图
                      </span>
                    ),
                    children: (
                      <Chart
                        type="line"
                        dataSource={processChartData(chartData, 'line')}
                        height={400}
                        yaxisText="收入金额 (¥)"
                        child={
                          <div style={{ marginBottom: 16, fontSize: 16, fontWeight: 500 }}>
                            收入趋势图
                          </div>
                        }
                      />
                    ),
                  },
                  {
                    key: 'bar',
                    label: (
                      <span>
                        <BarChartOutlined />
                        柱图
                      </span>
                    ),
                    children: (
                      <Chart
                        type="bar"
                        dataSource={processChartData(chartData, 'bar')}
                        height={400}
                        yaxisText="收入金额 (¥)"
                        child={
                          <div style={{ marginBottom: 16, fontSize: 16, fontWeight: 500 }}>
                            收入柱状图
                          </div>
                        }
                      />
                    ),
                  },
                  {
                    key: 'pie',
                    label: (
                      <span>
                        <PieChartOutlined />
                        饼图
                      </span>
                    ),
                    children: (
                      <Chart
                        type="pie"
                        dataSource={processChartData(chartData, 'pie')}
                        height={400}
                        child={
                          <div style={{ marginBottom: 16, fontSize: 16, fontWeight: 500 }}>
                            收入分布图
                          </div>
                        }
                      />
                    ),
                  },
                ]}
              />
            </Spin>
          </div>
        )}
      </Card>

      {/* 收入编辑模态框 */}
      <Modal
        title={editingIncome ? '编辑收入' : '新增收入'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
        confirmLoading={saveLoading}
        okText={`保存 (F8)`}
        cancelText="取消"
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="companyCode"
                label="所属公司"
                rules={[{ required: true, message: '请选择所属公司' }]}
              >
                <Select placeholder="请选择所属公司" size="large" options={getCompanyOptions()} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="customerCode"
                label="客户"
                rules={[{ required: true, message: '请选择客户' }]}
              >
                <CustomerSearchSelector
                  placeholder="请选择客户"
                  onChange={(_, customer) => {
                    // 当选择客户时，自动设置经办人为该客户的负责人
                    if (customer && customer.managerCode) {
                      form.setFieldsValue({
                        responsibleUserCode: customer.managerCode,
                      });
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="responsibleUserCode"
                label="经办人"
                rules={[{ required: true, message: '请选择经办人' }]}
              >
                <UserSearchSelector placeholder="请选择经办人" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="金额"
                rules={[
                  { required: true, message: '请输入金额' },
                  { type: 'number', min: 0, message: '金额不能小于0' },
                ]}
              >
                <FormattedAmountInput placeholder="请输入金额" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="createDate"
                label="创建日期"
                rules={[{ required: true, message: '请选择创建日期' }]}
              >
                <DatePicker
                  placeholder="请选择创建日期"
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="screenshot" label="截图">
                <SimpleImageUpload folder="income-assets" enablePaste={true} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="remark" label="备注">
                <TextArea placeholder="请输入备注" rows={3} maxLength={500} showCount />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default IncomeAssetsManagement;
