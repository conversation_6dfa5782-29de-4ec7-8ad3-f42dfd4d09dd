import React from 'react';
import ErrorPage from './ErrorPage';

/**
 * 500 服务器错误页面组件
 */
const ServerError: React.FC = () => {
  // 500页面的SVG插图
  const illustration = (
    <svg
      width="300"
      height="200"
      viewBox="0 0 300 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* 服务器图标 */}
      <rect
        x="100"
        y="40"
        width="100"
        height="120"
        rx="5"
        stroke="#1890ff"
        strokeWidth="8"
        fill="none"
      />
      
      {/* 服务器指示灯 */}
      <circle cx="120" cy="60" r="5" fill="#ff4d4f" />
      <circle cx="120" cy="80" r="5" fill="#52c41a" />
      <circle cx="120" cy="100" r="5" fill="#faad14" />
      
      {/* 服务器线条 */}
      <line
        x1="140"
        y1="60"
        x2="180"
        y2="60"
        stroke="#1890ff"
        strokeWidth="4"
        strokeLinecap="round"
      />
      <line
        x1="140"
        y1="80"
        x2="180"
        y2="80"
        stroke="#1890ff"
        strokeWidth="4"
        strokeLinecap="round"
      />
      <line
        x1="140"
        y1="100"
        x2="180"
        y2="100"
        stroke="#1890ff"
        strokeWidth="4"
        strokeLinecap="round"
      />
      
      {/* 闪电图标 */}
      <path
        d="M150 20L130 70H150L130 120"
        stroke="#faad14"
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      
      {/* VASA 字母 */}
      <text
        x="150"
        y="170"
        fontSize="24"
        fill="#1890ff"
        textAnchor="middle"
        fontWeight="bold"
      >
        VASA
      </text>
    </svg>
  );

  return (
    <ErrorPage
      code="500"
      title="服务器错误"
      description="抱歉，服务器出现了问题。我们正在努力修复，请稍后再试。"
      illustration={illustration}
    />
  );
};

export default ServerError;
