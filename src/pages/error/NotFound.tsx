import React from 'react';
import ErrorPage from './ErrorPage';

/**
 * 404 页面组件
 * 展示一个美观的404错误页面，包含VASA品牌元素
 */
const NotFound: React.FC = () => {
  // 404页面的SVG插图
  const illustration = (
    <svg
      width="300"
      height="200"
      viewBox="0 0 300 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* 公司标志 - V */}
      <path
        d="M60 50L80 150L100 50"
        stroke="#1890ff"
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      {/* 公司标志 - A */}
      <path
        d="M120 150L140 50L160 150"
        stroke="#1890ff"
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <line
        x1="125"
        y1="100"
        x2="155"
        y2="100"
        stroke="#1890ff"
        strokeWidth="8"
        strokeLinecap="round"
      />
      {/* 公司标志 - S */}
      <path
        d="M180 60C180 60 180 50 190 50C200 50 210 50 210 60C210 70 210 80 190 90C170 100 170 110 170 120C170 130 170 140 180 140C190 140 200 140 200 140"
        stroke="#1890ff"
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      {/* 公司标志 - A */}
      <path
        d="M220 150L240 50L260 150"
        stroke="#1890ff"
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <line
        x1="225"
        y1="100"
        x2="255"
        y2="100"
        stroke="#1890ff"
        strokeWidth="8"
        strokeLinecap="round"
      />

      {/* 404 错误指示 */}
      <circle cx="150" cy="180" r="10" fill="#ff4d4f" />
      <path d="M150 180L150 170" stroke="#ff4d4f" strokeWidth="4" strokeLinecap="round" />
    </svg>
  );

  return (
    <ErrorPage
      code="404"
      title="页面未找到"
      description="抱歉，您访问的页面不存在或已被移除。可能是链接已过期，或者您输入的地址有误。"
      illustration={illustration}
    />
  );
};

export default NotFound;
