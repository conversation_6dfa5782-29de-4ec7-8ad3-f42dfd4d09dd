import React from 'react';
import ErrorPage from './ErrorPage';

/**
 * 403 禁止访问页面组件
 */
const Forbidden: React.FC = () => {
  // 403页面的SVG插图
  const illustration = (
    <svg
      width="300"
      height="200"
      viewBox="0 0 300 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* 锁图标 */}
      <rect
        x="120"
        y="80"
        width="60"
        height="60"
        rx="5"
        stroke="#1890ff"
        strokeWidth="8"
        fill="none"
      />
      <path
        d="M135 80V60C135 45 165 45 165 60V80"
        stroke="#1890ff"
        strokeWidth="8"
        strokeLinecap="round"
        fill="none"
      />
      <circle cx="150" cy="110" r="10" fill="#1890ff" />
      <line
        x1="150"
        y1="110"
        x2="150"
        y2="125"
        stroke="#1890ff"
        strokeWidth="8"
        strokeLinecap="round"
      />
      
      {/* VASA 字母 */}
      <text
        x="150"
        y="170"
        fontSize="24"
        fill="#1890ff"
        textAnchor="middle"
        fontWeight="bold"
      >
        VASA
      </text>
      
      {/* 禁止标志 */}
      <circle cx="150" cy="80" r="70" stroke="#ff4d4f" strokeWidth="8" fill="none" />
      <line
        x1="100"
        y1="30"
        x2="200"
        y2="130"
        stroke="#ff4d4f"
        strokeWidth="8"
        strokeLinecap="round"
      />
    </svg>
  );

  return (
    <ErrorPage
      code="403"
      title="禁止访问"
      description="抱歉，您没有权限访问此页面。请联系系统管理员获取访问权限。"
      illustration={illustration}
    />
  );
};

export default Forbidden;
