import React from 'react';
import { Button, Typography, Space } from 'antd';
import { useNavigate } from 'react-router-dom';
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import useStyles from './ErrorStyles';

const { Title, Paragraph } = Typography;

interface ErrorPageProps {
  code: string | number;
  title: string;
  description: string;
  illustration?: React.ReactNode;
}

/**
 * 通用错误页面组件
 * 可以用于显示各种错误，如404、403、500等
 */
const ErrorPage: React.FC<ErrorPageProps> = ({ code, title, description, illustration }) => {
  const { styles } = useStyles();
  const navigate = useNavigate();

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };

  // 返回首页
  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <div className={styles.container}>
      {/* 背景装饰 */}
      <div className={styles.decorationCircle1}></div>
      <div className={styles.decorationCircle2}></div>

      <div className={styles.content}>
        <div className={styles.logoContainer}>
          <div className={styles.logo}>VASA</div>
        </div>

        <div className={styles.errorCode}>{code}</div>

        <Title level={2} className={styles.title}>
          {title}
        </Title>

        <Paragraph className={styles.description}>{description}</Paragraph>

        <div className={styles.illustration}>{illustration}</div>

        <Space size="middle" className={styles.actions}>
          <Button type="primary" icon={<HomeOutlined />} onClick={handleGoHome} size="large">
            返回首页
          </Button>
          <Button icon={<ArrowLeftOutlined />} onClick={handleGoBack} size="large">
            返回上一页
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default ErrorPage;
