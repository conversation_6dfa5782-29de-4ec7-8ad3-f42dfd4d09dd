import { createStyles } from 'antd-style';

/**
 * 错误页面样式
 */
const useStyles = createStyles(({ token }) => ({
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    width: '100vw',
    backgroundColor: token.colorBgContainer,
    overflow: 'hidden',
    position: 'relative',
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '40px',
    maxWidth: '600px',
    textAlign: 'center',
    position: 'relative',
    zIndex: 2,
  },
  logoContainer: {
    marginBottom: '20px',
  },
  logo: {
    fontSize: '32px',
    fontWeight: 'bold',
    color: token.colorPrimary,
    letterSpacing: '4px',
    fontFamily: '"Arial", sans-serif',
    background: `linear-gradient(135deg, ${token.colorPrimary}, ${token.colorPrimaryActive})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    padding: '8px 16px',
    borderRadius: '4px',
    border: `2px solid ${token.colorPrimary}`,
  },
  errorCode: {
    fontSize: '120px',
    fontWeight: 'bold',
    color: token.colorPrimaryBg,
    lineHeight: 1,
    marginBottom: '16px',
    textShadow: `2px 2px 0 ${token.colorPrimary}, 
                 4px 4px 0 ${token.colorPrimaryActive}`,
    fontFamily: '"Arial", sans-serif',
    letterSpacing: '8px',
  },
  title: {
    fontSize: '28px',
    fontWeight: 'bold',
    color: token.colorText,
    marginBottom: '16px',
  },
  description: {
    fontSize: '16px',
    color: token.colorTextSecondary,
    marginBottom: '32px',
  },
  illustration: {
    marginBottom: '32px',
    '& svg': {
      maxWidth: '100%',
      height: 'auto',
    },
  },
  actions: {
    marginTop: '16px',
  },
  // 添加一些背景装饰
  '@keyframes float': {
    '0%': {
      transform: 'translateY(0px)',
    },
    '50%': {
      transform: 'translateY(-20px)',
    },
    '100%': {
      transform: 'translateY(0px)',
    },
  },
  decorationCircle1: {
    position: 'absolute',
    width: '300px',
    height: '300px',
    borderRadius: '50%',
    backgroundColor: `${token.colorPrimary}10`,
    top: '-100px',
    right: '-100px',
    zIndex: 1,
  },
  decorationCircle2: {
    position: 'absolute',
    width: '200px',
    height: '200px',
    borderRadius: '50%',
    backgroundColor: `${token.colorPrimary}08`,
    bottom: '-50px',
    left: '-50px',
    zIndex: 1,
  },
}));

export default useStyles;
