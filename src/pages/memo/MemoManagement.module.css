.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 16px;
  padding: 16px 0;
}

.titleCell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.titleIcon {
  color: #1890ff;
  flex-shrink: 0;
}

/* Form styles */
.formContainer {
  margin-bottom: 24px;
}

.formContainer .ant-card-body {
  padding: 24px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.form {
  max-width: 800px;
}

.breadcrumbContainer {
  margin-bottom: 24px;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.headerTitle {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.actionButtons {
  display: flex;
  gap: 8px;
}

.imagePreview {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}

.filePreview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f6f6f6;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.fileIcon {
  color: #1890ff;
  font-size: 16px;
}

.fileName {
  color: #262626;
  font-weight: 500;
}

.detailsContainer {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}

.attachmentSection {
  margin-top: 16px;
}

.attachmentTitle {
  font-weight: 600;
  margin-bottom: 12px;
  color: #262626;
}

.attachmentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.attachmentItem {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
}

.attachmentImage {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.attachmentFile {
  padding: 16px;
  text-align: center;
}

.attachmentFileIcon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 8px;
}

.attachmentFileName {
  font-weight: 500;
  color: #262626;
  word-break: break-all;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .toolbar {
    padding: 12px 0;
  }
  
  .headerContainer {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .actionButtons {
    width: 100%;
    justify-content: flex-end;
  }
  
  .attachmentGrid {
    grid-template-columns: 1fr;
  }
}

/* Table customization */
.container :global(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

.container :global(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* Form customization */
.form :global(.ant-form-item-label > label) {
  font-weight: 600;
}

.form :global(.ant-input),
.form :global(.ant-input-number),
.form :global(.ant-select-selector) {
  border-radius: 6px;
}

.form :global(.ant-btn) {
  border-radius: 6px;
}

/* Upload component customization */
.form :global(.ant-upload-drag) {
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  background-color: #fafafa;
}

.form :global(.ant-upload-drag:hover) {
  border-color: #1890ff;
}

.form :global(.ant-upload-drag .ant-upload-text) {
  color: #666;
  font-size: 14px;
}

.form :global(.ant-upload-drag .ant-upload-hint) {
  color: #999;
  font-size: 12px;
}
