import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Form, Input, Button, Row, Col, message, Spin, Image } from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  EditOutlined,
  PaperClipOutlined,
} from '@ant-design/icons';
import { getMemoDetail, createMemo, updateMemo } from '@/api/MemoApi';
import type { AddMemoParams, UpdateMemoParams } from '@/types/memo';
import { getErrorMessage, handleApiResponse, logError } from '@/utils/errorHandler';
import { formatDateTime } from '@/utils/dateUtils';
import { UPLOAD_FOLDERS } from '@/api/fileUpload';
import SimpleImageUpload from '@/components/ImageUpload/SimpleImageUpload';
import SimpleFileUpload from '@/components/ImageUpload/SimpleFileUpload';
import styles from './MemoManagement.module.css';

const { TextArea } = Input;

interface MemoFormData {
  title: string;
  details?: string;
  image?: string;
  file?: string;
}

interface MemoDetail {
  id: string;
  title: string;
  details: string | null;
  image: string | null;
  file: string | null;
  createdAt: string;
  updatedAt: string;
}

const MemoForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm<MemoFormData>();

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [memo, setMemo] = useState<MemoDetail | null>(null);
  const [mode, setMode] = useState<'create' | 'edit' | 'view'>('create');
  const [imageUrl, setImageUrl] = useState<string>('');
  const [fileUrl, setFileUrl] = useState<string>('');

  // 根据路径确定模式
  useEffect(() => {
    const path = window.location.pathname;
    if (path.includes('/create')) {
      setMode('create');
    } else if (path.includes('/edit')) {
      setMode('edit');
    } else if (path.includes('/view')) {
      setMode('view');
    }
  }, []);

  // 获取备忘录详情
  const fetchMemoDetail = async (memoId: string) => {
    setLoading(true);
    try {
      const response = await getMemoDetail(memoId);
      if (response.code === 200) {
        const memoData = response.data;
        setMemo(memoData);
        form.setFieldsValue({
          title: memoData.title,
          details: memoData.details || '',
        });

        // 设置文件URL
        if (memoData.image) {
          setImageUrl(memoData.image);
        }

        if (memoData.file) {
          setFileUrl(memoData.file);
        }
      } else {
        message.error(response.message || '获取备忘录详情失败');
        navigate('/admin/memos');
      }
    } catch (error: any) {
      logError('获取备忘录详情', error);
      message.error(getErrorMessage(error, '获取备忘录详情失败'));
      navigate('/admin/memos');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    if (id && (mode === 'edit' || mode === 'view')) {
      fetchMemoDetail(id);
    }
  }, [id, mode]);

  // 保存备忘录
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);

      const memoData: AddMemoParams | UpdateMemoParams = {
        title: values.title.trim(),
        details: values.details?.trim() || null,
        image: imageUrl || null,
        file: fileUrl || null,
      };

      if (mode === 'create') {
        const response = await createMemo(memoData as AddMemoParams);
        const result = handleApiResponse(response, '创建成功', '创建失败');

        if (result.success) {
          message.success(result.message);
          navigate('/admin/memos');
        } else {
          message.error(result.message);
        }
      } else if (mode === 'edit' && id) {
        const response = await updateMemo(id, memoData as UpdateMemoParams);
        const result = handleApiResponse(response, '更新成功', '更新失败');

        if (result.success) {
          message.success(result.message);
          navigate('/admin/memos');
        } else {
          message.error(result.message);
        }
      }
    } catch (error: any) {
      logError('保存备忘录', error);
      message.error(getErrorMessage(error, '保存失败'));
    } finally {
      setSaving(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/admin/memos');
  };

  // 切换到编辑模式
  const handleEdit = () => {
    navigate(`/admin/memos/edit/${id}`);
  };

  // 处理图片上传变化
  const handleImageChange = (url: string) => {
    setImageUrl(url);
  };

  // 处理文件上传变化
  const handleFileChange = (value: string | string[]) => {
    // 由于我们只支持单个文件，取第一个值
    const fileValue = Array.isArray(value) ? value[0] || '' : value;
    setFileUrl(fileValue);
  };

  const isReadOnly = mode === 'view';
  const pageTitle =
    mode === 'create' ? '新增备忘录' : mode === 'edit' ? '编辑备忘录' : '查看备忘录';

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.headerContainer}>
          <h2 className={styles.headerTitle}>{pageTitle}</h2>
          <div className={styles.actionButtons}>
            {mode === 'view' && (
              <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
                编辑
              </Button>
            )}
            {mode !== 'view' && (
              <Button type="primary" icon={<SaveOutlined />} onClick={handleSave} loading={saving}>
                保存
              </Button>
            )}
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              返回
            </Button>
          </div>
        </div>

        <Spin spinning={loading}>
          <Form form={form} layout="vertical" className={styles.form} disabled={isReadOnly}>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  name="title"
                  label="标题"
                  rules={[
                    { required: true, message: '请输入备忘录标题' },
                    { max: 200, message: '标题不能超过200个字符' },
                  ]}
                >
                  <Input placeholder="请输入备忘录标题" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  name="details"
                  label="详情"
                  rules={[{ max: 2000, message: '详情不能超过2000个字符' }]}
                >
                  <TextArea placeholder="请输入备忘录详情" rows={6} showCount maxLength={2000} />
                </Form.Item>
              </Col>
            </Row>

            {!isReadOnly && (
              <>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item label="图片附件">
                      <SimpleImageUpload
                        value={imageUrl}
                        onChange={handleImageChange}
                        folder={UPLOAD_FOLDERS.MEMO}
                        enablePaste={true}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="文件附件">
                      <SimpleFileUpload
                        value={fileUrl}
                        onChange={handleFileChange}
                        folder={UPLOAD_FOLDERS.MEMO}
                        maxSize={10}
                        multiple={false}
                        accept="*/*"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            {isReadOnly && memo && (
              <>
                {memo.image && (
                  <Row gutter={24}>
                    <Col span={24}>
                      <div className={styles.attachmentSection}>
                        <div className={styles.attachmentTitle}>图片附件</div>
                        <Image src={memo.image} alt="备忘录图片" className={styles.imagePreview} />
                      </div>
                    </Col>
                  </Row>
                )}

                {memo.file && (
                  <Row gutter={24}>
                    <Col span={24}>
                      <div className={styles.attachmentSection}>
                        <div className={styles.attachmentTitle}>文件附件</div>
                        <div className={styles.filePreview}>
                          <PaperClipOutlined className={styles.fileIcon} />
                          <a
                            href={memo.file}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={styles.fileName}
                          >
                            下载文件
                          </a>
                        </div>
                      </div>
                    </Col>
                  </Row>
                )}

                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item label="创建时间">
                      <Input value={formatDateTime(memo.createdAt)} disabled />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="更新时间">
                      <Input value={formatDateTime(memo.updatedAt)} disabled />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}
          </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default MemoForm;
