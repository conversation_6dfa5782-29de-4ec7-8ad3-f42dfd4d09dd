import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Popconfirm,
  Row,
  Col,
  DatePicker,
  Tag,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  EyeOutlined,
  FileImageOutlined,
  PaperClipOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';
import { getMemoList, deleteMemo } from '@/api/MemoApi';
import type { MemosListParams } from '@/types/memo';
import { formatDateTime } from '@/utils/dateUtils';
import { getErrorMessage, handleApiResponse, logError } from '@/utils/errorHandler';
import styles from './MemoManagement.module.css';

const { Search } = Input;
const { RangePicker } = DatePicker;

interface MemoItem {
  id: string;
  title: string;
  details: string | null;
  image: string | null;
  file: string | null;
  createdAt: string;
  updatedAt: string;
}

const MemoManagement: React.FC = () => {
  const navigate = useNavigate();

  // 状态管理
  const [memos, setMemos] = useState<MemoItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs(),
  ]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC' | ''>('');

  // 获取备忘录列表
  const fetchMemos = async (
    page = 1,
    pageSize = 20,
    title = '',
    startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
    endDate = dayjs().format('YYYY-MM-DD'),
    sort: 'ASC' | 'DESC' | '' = sortOrder,
  ) => {
    setLoading(true);
    try {
      const params: MemosListParams = {
        page,
        pageSize,
        title: title.trim() || null,
        startDate,
        endDate,
        sortOrder: sort,
      };

      const response = await getMemoList(params);

      if (response.code === 200 && response.data) {
        setMemos(response.data.items || []);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取备忘录列表失败');
      }
    } catch (error: any) {
      logError('获取备忘录列表', error);
      message.error(getErrorMessage(error, '获取备忘录列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchMemos();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
    const [start, end] = dateRange;
    fetchMemos(
      1,
      pagination.pageSize,
      value,
      start.format('YYYY-MM-DD'),
      end.format('YYYY-MM-DD'),
      sortOrder,
    );
  };

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      const [start, end] = dates;
      setDateRange([start, end]);
      setPagination((prev) => ({ ...prev, current: 1 }));
      fetchMemos(
        1,
        pagination.pageSize,
        searchText,
        start.format('YYYY-MM-DD'),
        end.format('YYYY-MM-DD'),
        sortOrder,
      );
    }
  };

  // 分页处理
  const handleTableChange = (
    paginationConfig: TablePaginationConfig,
    _filters: any,
    sorter: any,
  ) => {
    const { current = 1, pageSize = 20 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));

    // 处理排序
    let newSortOrder: 'ASC' | 'DESC' | '' = '';
    if (sorter && sorter.field === 'createdAt') {
      if (sorter.order === 'ascend') {
        newSortOrder = 'ASC';
      } else if (sorter.order === 'descend') {
        newSortOrder = 'DESC';
      }
    }
    setSortOrder(newSortOrder);

    const [start, end] = dateRange;
    fetchMemos(
      current,
      pageSize,
      searchText,
      start.format('YYYY-MM-DD'),
      end.format('YYYY-MM-DD'),
      newSortOrder,
    );
  };

  // 刷新列表
  const handleRefresh = () => {
    const [start, end] = dateRange;
    fetchMemos(
      pagination.current,
      pagination.pageSize,
      searchText,
      start.format('YYYY-MM-DD'),
      end.format('YYYY-MM-DD'),
      sortOrder,
    );
  };

  // 新增备忘录
  const handleAdd = () => {
    navigate('/admin/memos/create');
  };

  // 查看备忘录
  const handleView = (memo: MemoItem) => {
    navigate(`/admin/memos/view/${memo.id}`);
  };

  // 编辑备忘录
  const handleEdit = (memo: MemoItem) => {
    navigate(`/admin/memos/edit/${memo.id}`);
  };

  // 删除备忘录
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteMemo(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除备忘录', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 渲染附件标签
  const renderAttachments = (record: MemoItem) => {
    const attachments = [];

    if (record.image) {
      attachments.push(
        <Tag key="image" icon={<FileImageOutlined />} color="blue">
          图片
        </Tag>,
      );
    }

    if (record.file) {
      attachments.push(
        <Tag key="file" icon={<PaperClipOutlined />} color="green">
          文件
        </Tag>,
      );
    }

    return attachments.length > 0 ? <Space>{attachments}</Space> : '-';
  };

  // 表格列定义
  const columns: ColumnsType<MemoItem> = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
    },
    {
      title: '详情',
      dataIndex: 'details',
      key: 'details',
      width: 300,
      ellipsis: true,
      render: (text: string | null) => text || '-',
    },
    {
      title: '附件',
      key: 'attachments',
      width: 120,
      render: (_, record) => renderAttachments(record),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => formatDateTime(text),
      sorter: true,
      sortOrder: sortOrder === 'ASC' ? 'ascend' : sortOrder === 'DESC' ? 'descend' : null,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      render: (text: string) => formatDateTime(text),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个备忘录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <Space size="middle">
                <Search
                  placeholder="搜索备忘录标题"
                  allowClear
                  enterButton={<SearchOutlined />}
                  size="large"
                  onSearch={handleSearch}
                  style={{ width: 300 }}
                />
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  format="YYYY-MM-DD"
                  size="large"
                  placeholder={['开始日期', '结束日期']}
                />
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增备忘录
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={memos}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default MemoManagement;
