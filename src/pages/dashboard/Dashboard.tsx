import React from 'react';
import { Card, Row, Col, Typography, Space, Button } from 'antd';
import { UserOutlined, SettingOutlined, DashboardOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { getUser } from '@/utils/tokenManager';

const { Title, Paragraph } = Typography;

const Dashboard: React.FC = () => {
  const user = getUser();

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DashboardOutlined /> 欢迎使用 VASA 企业管理系统
      </Title>

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={8}>
          <Card title="用户信息" extra={<UserOutlined />} style={{ height: '200px' }}>
            <Space direction="vertical">
              <div>
                <strong>用户编码：</strong>
                {user?.code}
              </div>
              <div>
                <strong>用户昵称：</strong>
                {user?.nickname}
              </div>
              <div>
                <strong>管理员权限：</strong>
                {user?.isSuperAdmin ? '是' : '否'}
              </div>
              <div>
                <strong>账户状态：</strong>
                {user?.isActive ? '激活' : '禁用'}
              </div>
            </Space>
          </Card>
        </Col>

        {user?.isSuperAdmin && (
          <Col xs={24} sm={12} md={8}>
            <Card title="系统管理" extra={<SettingOutlined />} style={{ height: '200px' }}>
              <Paragraph>您拥有超级管理员权限，可以访问系统管理功能。</Paragraph>
              <Space direction="vertical">
                <Link to="/system/users">
                  <Button type="primary" icon={<UserOutlined />}>
                    用户管理
                  </Button>
                </Link>
              </Space>
            </Card>
          </Col>
        )}

        <Col xs={24} sm={12} md={8}>
          <Card title="快速导航" style={{ height: '200px' }}>
            <Space direction="vertical">
              <Link to="/company/management">
                <Button type="link">公司管理</Button>
              </Link>
              <Link to="/product/archive">
                <Button type="link">商品档案</Button>
              </Link>
              <Link to="/sales/order">
                <Button type="link">销售开单</Button>
              </Link>
              <Link to="/warehouse/total">
                <Button type="link">库存管理</Button>
              </Link>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
