import React, { useState, useEffect } from 'react';
import { Card, Button, Space, message, Row, Col, Descriptions, Table, Tag, Divider } from 'antd';
import { ArrowLeftOutlined, EditOutlined, FileExcelOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  getPurchaseContractDetail,
  exportPurchaseContractsExcel,
} from '@/api/PurchaseContractsApi';
import type { ContractDetailItem } from '@/types/purchaseContracts';
import { logError, getErrorMessage } from '@/utils/errorHandler';
import styles from './PurchaseContractDetail.module.css';

interface ContractData {
  id: string;
  orderNumber: string;
  supplierCode: string;
  supplierName: string;
  orderDate: string;
  status: string;
  expectedDeliveryDate: string;
  remark: string;
  createdByUserCode: string;
  createdByUserName: string;
  createdAt: string;
  updatedAt: string;
}

const PurchaseContractView: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [contractData, setContractData] = useState<ContractData | null>(null);
  const [details, setDetails] = useState<ContractDetailItem[]>([]);

  // 获取合同详情
  const fetchContractDetail = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await getPurchaseContractDetail(id);
      if (response.code === 200) {
        setContractData(response?.data);
        setDetails(response?.data?.details || []);
      } else {
        message.error(response.message || '获取合同详情失败');
      }
    } catch (error: any) {
      logError('获取合同详情', error);
      message.error(getErrorMessage(error, '获取合同详情失败'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContractDetail();
  }, [id]);

  // 导出当前合同
  const handleExportContract = async () => {
    if (!contractData) {
      message.warning('合同数据不存在');
      return;
    }

    setExporting(true);
    try {
      const blob = await exportPurchaseContractsExcel({
        contractIds: [contractData.id],
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `采购合同_${contractData.orderNumber}_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出采购合同', error);
      message.error(getErrorMessage(error, '导出失败'));
    } finally {
      setExporting(false);
    }
  };

  // 跳转到编辑页面
  const handleEdit = () => {
    navigate(`/product/purchase-contracts/edit/${id}`);
  };

  // 返回列表
  const handleBack = () => {
    navigate('/product/purchase-contracts');
  };

  // 状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'default';
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'error';
      case 'CANCELLED':
        return 'warning';
      default:
        return 'default';
    }
  };

  // 状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return '草稿';
      case 'APPROVED':
        return '已批准';
      case 'REJECTED':
        return '已拒绝';
      case 'CANCELLED':
        return '已取消';
      default:
        return status;
    }
  };

  // 明细表格列定义
  const detailColumns: ColumnsType<ContractDetailItem> = [
    {
      title: '序号',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: '辅料',
      dataIndex: 'accessory',
      width: 200,
      render: (text: string, record: ContractDetailItem) => (
        <div style={{ fontSize: '12px' }}>
          {text || '未知辅料'}
          {record.accessoryId && (
            <div style={{ color: '#8c8c8c', fontSize: '11px' }}>
              ID: {record.accessoryId.slice(-8)}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      width: 100,
      render: (quantity: number) => <span style={{ fontWeight: 500 }}>{quantity}</span>,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      width: 120,
      render: (price: number) => (
        <span style={{ color: '#1890ff', fontWeight: 500 }}>¥{price.toFixed(2)}</span>
      ),
    },
    {
      title: '小计',
      width: 120,
      render: (_, record: ContractDetailItem) => (
        <span style={{ color: '#52c41a', fontWeight: 500 }}>
          ¥{((record.quantity || 0) * (record.unitPrice || 0)).toFixed(2)}
        </span>
      ),
    },
    {
      title: '交货日期',
      dataIndex: 'deliveryDate',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
      render: (remark: string) => remark || '-',
    },
  ];

  if (loading) {
    return (
      <div className={styles.container}>
        <Card loading={true} />
      </div>
    );
  }

  if (!contractData) {
    return (
      <div className={styles.container}>
        <Card>
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <p>合同不存在或已被删除</p>
            <Button type="primary" onClick={handleBack}>
              返回列表
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <Card className={styles.headerCard}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              className={styles.backButton}
            >
              返回
            </Button>
            <div className={styles.title}>
              <EditOutlined className={styles.titleIcon} />
              查看采购订单明细 - {contractData.orderNumber}
            </div>
          </div>
          <div className={styles.headerRight}>
            <Space>
              <Button
                type="default"
                icon={<FileExcelOutlined />}
                onClick={handleExportContract}
                loading={exporting}
              >
                导出Excel
              </Button>
              <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
                编辑
              </Button>
            </Space>
          </div>
        </div>
      </Card>

      {/* 基本信息 */}
      <Card title="基本信息" style={{ marginBottom: 16 }}>
        <Descriptions column={3} bordered>
          <Descriptions.Item label="订单编号">{contractData.orderNumber}</Descriptions.Item>
          <Descriptions.Item label="供应商">
            <div>
              <div style={{ fontWeight: 500 }}>{contractData.supplierName}</div>
              <div style={{ fontSize: '12px', color: '#8c8c8c' }}>{contractData.supplierCode}</div>
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={getStatusColor(contractData.status)}>
              {getStatusText(contractData.status)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="订单日期">
            {dayjs(contractData.orderDate).format('YYYY-MM-DD')}
          </Descriptions.Item>
          <Descriptions.Item label="预期交货日期">
            {dayjs(contractData.expectedDeliveryDate).format('YYYY-MM-DD')}
          </Descriptions.Item>
          <Descriptions.Item label="创建人">
            <div>
              <div style={{ fontWeight: 500 }}>{contractData.createdByUserName}</div>
              <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                {contractData.createdByUserCode}
              </div>
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="创建时间" span={2}>
            {dayjs(contractData.createdAt).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="备注" span={3}>
            {contractData.remark || '-'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 订单明细 */}
      <Card title="订单明细">
        <div style={{ marginBottom: 16 }}>
          <Space>
            <span style={{ fontSize: '14px', color: '#666' }}>共 {details.length} 项明细</span>
            <Divider type="vertical" />
            <span style={{ fontSize: '14px', fontWeight: 500 }}>
              总金额:
              <Tag color="green" style={{ marginLeft: 8 }}>
                ¥
                {details
                  .reduce((sum, item) => sum + (item.quantity || 0) * (item.unitPrice || 0), 0)
                  .toFixed(2)}
              </Tag>
            </span>
          </Space>
        </div>

        <Table
          columns={detailColumns}
          dataSource={details}
          rowKey={(record, index) => `${record.accessoryId || 'unknown'}-${index}`}
          pagination={false}
          size="small"
          scroll={{ x: 800 }}
          summary={() => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={4}>
                <strong>合计</strong>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1}>
                <strong style={{ color: '#52c41a' }}>
                  ¥
                  {details
                    .reduce((sum, item) => sum + (item.quantity || 0) * (item.unitPrice || 0), 0)
                    .toFixed(2)}
                </strong>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={2} colSpan={2} />
            </Table.Summary.Row>
          )}
        />
      </Card>

      {/* 固定操作栏 */}
      <div className={styles.fixedActionBar}>
        <Button
          type="primary"
          icon={<EditOutlined />}
          onClick={handleEdit}
          className={styles.fixedSaveButton}
        >
          编辑订单
        </Button>
        <Button
          type="primary"
          icon={<FileExcelOutlined />}
          onClick={handleExportContract}
          loading={exporting}
          className={styles.fixedExportButton}
        >
          导出Excel
        </Button>
      </div>
    </div>
  );
};

export default PurchaseContractView;
