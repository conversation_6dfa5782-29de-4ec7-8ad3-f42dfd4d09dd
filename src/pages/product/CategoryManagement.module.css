.container {
  padding: 4px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 16px;
}

.tableContainer {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.sizeTag {
  margin: 2px;
  border-radius: 4px;
}

.actionButton {
  margin-right: 8px;
}

.actionButton:last-child {
  margin-right: 0;
}

.formItem {
  margin-bottom: 16px;
}

.sizesContainer {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px;
  min-height: 32px;
  background-color: #fafafa;
}

.sizesContainer:hover {
  border-color: #40a9ff;
}

.sizesContainer:focus-within {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.emptyText {
  color: #bfbfbf;
  font-style: italic;
}
