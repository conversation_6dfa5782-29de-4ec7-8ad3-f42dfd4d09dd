.container {
  padding: 8px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.backButton {
  margin-right: 12px;
  padding: 4px 8px;
}

.header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.infoCard {
  margin-bottom: 12px;
}

.infoCard .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.infoCard .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.formCard {
  margin-bottom: 12px;
}

.formCard .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.formCard .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 0;
  border-top: 1px solid #e8e8e8;
  background-color: #fafafa;
  margin: 0 -8px -8px -8px;
  padding: 12px 16px;
}

/* 描述列表样式 */
.infoCard .ant-descriptions-item-label {
  font-weight: 600;
  color: #595959;
}

.infoCard .ant-descriptions-item-content {
  color: #262626;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 6px;
  }

  .infoCard,
  .formCard {
    margin-bottom: 8px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 4px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 8px;
  }

  .header h2 {
    font-size: 16px;
  }

  .footer {
    padding: 8px 12px;
    margin: 0 -4px -4px -4px;
  }
}
