.container {
  padding: 8px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.headerCard {
  margin-bottom: 12px;
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.backButton {
  padding: 4px 8px;
  color: #1890ff;
}

.productInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.productName {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.productMeta {
  display: flex;
  gap: 24px;
  font-size: 14px;
}

.productCode {
  color: #1890ff;
  font-weight: 500;
}

.brandName {
  color: #52c41a;
  font-weight: 500;
}

.supplierName {
  color: #fa8c16;
  font-weight: 500;
}

.headerRight {
  display: flex;
  align-items: center;
}

.productImage {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}

.statsCard {
  margin-bottom: 12px;
}

.colorCard {
  margin-bottom: 12px;
}

.colorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.colorInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.colorSwatch {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.colorName {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.colorCode {
  font-size: 14px;
  color: #8c8c8c;
}

.colorStats {
  display: flex;
  gap: 8px;
}

.colorImage {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

/* 图片展示样式 */
.imageSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.imageContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.imageControls {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.imageButton {
  border: none;
  background: transparent;
  color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.imageButton:hover {
  background-color: #f0f8ff;
  color: #40a9ff;
}

.imageIndex {
  font-size: 12px;
  color: #595959;
  min-width: 40px;
  text-align: center;
  font-weight: 500;
}

.noImage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  color: #bfbfbf;
  font-size: 12px;
  gap: 8px;
}

.noImage .anticon {
  font-size: 24px;
}

.priceSection {
  margin-bottom: 16px;
}

.priceSection h4 {
  margin-bottom: 8px;
  color: #262626;
  font-weight: 600;
}

.priceItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  text-align: center;
}

.priceLabel {
  font-size: 12px;
  color: #8c8c8c;
}

.priceValue {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

/* 辅料信息样式 */
.accessorySection {
  margin-bottom: 16px;
}

.accessorySection h4 {
  margin-bottom: 8px;
  color: #262626;
  font-weight: 600;
}

.accessoryItem {
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  transition: all 0.2s ease;
}

.accessoryItem:hover {
  border-color: #d9d9d9;
  background-color: #f5f5f5;
}

.accessoryName {
  font-size: 13px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.accessoryQuantity {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
  margin-bottom: 2px;
}

.accessoryCode {
  font-size: 11px;
  color: #8c8c8c;
}

.sizeSection {
  margin-bottom: 16px;
}

.sizeSection h4 {
  margin-bottom: 8px;
  color: #262626;
  font-weight: 600;
}

.sizeTable {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.remarkSection h4 {
  margin-bottom: 8px;
  color: #262626;
  font-weight: 600;
}

.remarkSection p {
  margin: 0;
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 4px;
  color: #262626;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .pageHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .headerLeft {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .productMeta {
    flex-direction: column;
    gap: 8px;
  }

  .colorHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .priceItem {
    margin-bottom: 8px;
  }
}
