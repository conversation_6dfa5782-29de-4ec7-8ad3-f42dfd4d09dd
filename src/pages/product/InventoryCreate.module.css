.container {
  padding: 8px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.backButton {
  margin-right: 12px;
  padding: 4px 8px;
}

.header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.formCard {
  margin-bottom: 12px;
}

.imageCard {
  margin-bottom: 12px;
}

.formCard .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.formCard .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.tableCard {
  margin-bottom: 12px;
}

.tableCard .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.tableCard .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 0;
  border-top: 1px solid #e8e8e8;
  background-color: #fafafa;
  margin: 0 -8px -8px -8px;
  padding: 12px 16px;
}

/* 表格头部样式 */
.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.tableStats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.infoIcon {
  color: #1890ff;
  cursor: help;
}

/* 商品信息样式 */
.productInfo {
  line-height: 1.2;
}

.productName {
  font-weight: 600;
  font-size: 13px;
  color: #262626;
  margin-bottom: 2px;
}

.productCode {
  font-size: 11px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.colorSize {
  display: flex;
  gap: 4px;
}

/* 数量和价格样式 */
.quantityText {
  font-weight: 600;
  color: #1890ff;
}

.costText {
  font-weight: 600;
  color: #52c41a;
}

.noCost {
  color: #d9d9d9;
}

.locationText {
  color: #595959;
  font-size: 12px;
}

.noLocation {
  color: #d9d9d9;
}

.remarkText {
  color: #595959;
  font-size: 12px;
}

/* 删除按钮样式 */
.deleteButton {
  color: #ff4d4f;
}

.deleteButton:hover {
  color: #ff7875;
  background-color: #fff2f0;
}

/* 图片预览样式 */
.imagePreview {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.imageContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.imageControls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.imageIndex {
  font-size: 12px;
  color: #8c8c8c;
  min-width: 60px;
  text-align: center;
}

.imageInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 8px;
}

/* 空状态样式 */
.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

.emptyIcon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

/* 表格样式 */
.inventoryTable {
  background: white;
  border-radius: 6px;
}

.inventoryTable .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  font-size: 12px;
  padding: 8px 6px;
  border-bottom: 1px solid #f0f0f0;
}

.inventoryTable .ant-table-tbody > tr > td {
  padding: 6px;
  border-bottom: 1px solid #f5f5f5;
  vertical-align: top;
}

.inventoryTable .ant-table-tbody > tr:hover > td {
  background-color: #f8f9fa;
}

/* 保存按钮样式 */
.saveButton {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 6px;
  }

  .formCard,
  .imageCard,
  .tableCard {
    margin-bottom: 8px;
  }

  .imagePreview {
    flex-direction: column;
    align-items: center;
  }

  .imageInfo {
    flex-direction: row;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 4px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 8px;
  }

  .header h2 {
    font-size: 16px;
  }

  .footer {
    padding: 8px 12px;
    margin: 0 -4px -4px -4px;
  }

  .tableStats {
    flex-direction: column;
    gap: 4px;
  }

  .productInfo {
    font-size: 11px;
  }

  .productName {
    font-size: 12px;
  }

  .productCode {
    font-size: 10px;
  }

  .emptyState {
    padding: 20px 10px;
  }

  .emptyIcon {
    font-size: 32px;
    margin-bottom: 8px;
  }
}
