import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Popconfirm,
  Image,
  Tooltip,
  Row,
  Col,
  Upload,
  Dropdown,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CopyOutlined,
  ExportOutlined,
  ImportOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  DownOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import { useNavigate } from 'react-router-dom';
import {
  getSkuList,
  deleteSku,
  exportSkuExcel,
  downloadSkuImportTemplate,
  importSkuExcel,
} from '@/api/SkuApi';
import type { Sku, PaginatedParams } from '@/types/skus';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { formatAmountSimple } from '@/utils/formatUtils';
import {
  BrandSearchSelector,
  SupplierSearchSelector,
  ProductCategorySelector,
  ColorSelector,
} from '@/components';
import styles from './SkuManagement.module.css';

const { Search } = Input;

const SkuManagement: React.FC = () => {
  const navigate = useNavigate();
  const [skus, setSkus] = useState<Sku[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 搜索条件
  const [searchFilters, setSearchFilters] = useState({
    nameSearch: '',
    codeSearch: '',
    brandCodeSearch: '',
    supplierCodeSearch: '',
    categoryCodeSearch: '',
    colorCodeSearch: '',
  });

  // 获取SKU列表
  const fetchSkus = async (page = 1, pageSize = 10, filters = searchFilters) => {
    setLoading(true);
    try {
      const params: PaginatedParams = {
        page,
        pageSize,
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value)),
      };

      const response = await getSkuList(params);
      const result = handleApiResponse(response, '', '获取SKU列表失败');

      if (result.success && response.data) {
        setSkus(response.data.skus || []);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total || 0,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取SKU列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchSkus();
  }, []);

  // 处理表格变化
  const handleTableChange = (newPagination: TablePaginationConfig) => {
    fetchSkus(newPagination.current, newPagination.pageSize);
  };

  // 处理搜索
  const handleSearch = () => {
    fetchSkus(1, pagination.pageSize, searchFilters);
  };

  // 重置搜索
  const handleReset = () => {
    const resetFilters = {
      nameSearch: '',
      codeSearch: '',
      brandCodeSearch: '',
      supplierCodeSearch: '',
      categoryCodeSearch: '',
      colorCodeSearch: '',
    };
    setSearchFilters(resetFilters);
    fetchSkus(1, pagination.pageSize, resetFilters);
  };

  // 刷新
  const handleRefresh = () => {
    fetchSkus(pagination.current, pagination.pageSize);
  };

  // 删除SKU
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteSku(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('删除SKU', error);
      message.error(getErrorMessage(error));
    }
  };

  // 复制编码
  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    });
  };

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      const blob = await downloadSkuImportTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'SKU导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('模板下载成功');
    } catch (error) {
      logError('下载导入模板', error);
      message.error('下载模板失败');
    }
  };

  // 导入Excel
  const handleImport = async (file: File) => {
    setImportLoading(true);
    try {
      const response = await importSkuExcel(file);
      const result = handleApiResponse(response, '导入成功', '导入失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('导入SKU', error);
      message.error('导入失败');
    } finally {
      setImportLoading(false);
    }
    return false; // 阻止默认上传行为
  };

  // 导出选中记录
  const handleExportSelected = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要导出的记录');
      return;
    }

    if (selectedRowKeys.length > 50) {
      message.warning('一次最多只能导出50条记录');
      return;
    }

    setExportLoading(true);
    try {
      const params = {
        skuIds: selectedRowKeys as string[],
        ...Object.fromEntries(Object.entries(searchFilters).filter(([_, value]) => value)),
      };

      const blob = await exportSkuExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `SKU列表_选中记录_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error) {
      logError('导出选中SKU', error);
      message.error('导出失败');
    } finally {
      setExportLoading(false);
    }
  };

  // 导出全部记录
  const handleExportAll = async () => {
    setExportLoading(true);
    try {
      const params = {
        ...Object.fromEntries(Object.entries(searchFilters).filter(([_, value]) => value)),
      };

      const blob = await exportSkuExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `SKU列表_全部记录_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error) {
      logError('导出全部SKU', error);
      message.error('导出失败');
    } finally {
      setExportLoading(false);
    }
  };

  // 表格列定义
  const columns: ColumnsType<Sku> = [
    {
      title: '图片',
      key: 'images',
      width: 120,
      fixed: 'left',
      render: (_, record) => (
        <div className={styles.imageCell}>
          {record.images && record.images.length > 0 ? (
            <Image
              src={record.images[0]}
              alt={record.name}
              width={100}
              height={100}
              style={{ objectFit: 'cover', borderRadius: '8px' }}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
          ) : (
            <div className={styles.noImage}>无图</div>
          )}
        </div>
      ),
    },
    {
      title: '商品编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      fixed: 'left',
      render: (text) => (
        <Space>
          <span className={styles.code}>{text}</span>
          <Tooltip title="复制编码">
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopy(text)}
            />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      ellipsis: true,
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      key: 'brandName',
      width: 100,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 120,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 100,
    },
    {
      title: '颜色',
      dataIndex: 'colorName',
      key: 'colorName',
      width: 80,
    },
    {
      title: '服装成本',
      dataIndex: 'clothingCost',
      key: 'clothingCost',
      width: 100,
      render: (cost) => (cost != null ? formatAmountSimple(cost) : '-'),
    },
    {
      title: '辅料成本',
      dataIndex: 'accessoryCost',
      key: 'accessoryCost',
      width: 100,
      render: (cost) => (cost != null ? formatAmountSimple(cost) : '-'),
    },
    {
      title: '总成本',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 100,
      render: (cost) => (cost != null ? formatAmountSimple(cost) : '-'),
    },
    {
      title: '零售价',
      dataIndex: 'retailPrice',
      key: 'retailPrice',
      width: 100,
      render: (price) => (price != null ? formatAmountSimple(price) : '-'),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/product/sku/view/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => navigate(`/product/sku/edit/${record.id}`)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个SKU吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="text" size="small" icon={<DeleteOutlined />} danger />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        {/* 搜索区域 */}
        <div className={styles.searchSection}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="搜索商品名称"
                value={searchFilters.nameSearch}
                onChange={(e) =>
                  setSearchFilters((prev) => ({ ...prev, nameSearch: e.target.value }))
                }
                onSearch={handleSearch}
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="搜索商品编码"
                value={searchFilters.codeSearch}
                onChange={(e) =>
                  setSearchFilters((prev) => ({ ...prev, codeSearch: e.target.value }))
                }
                onSearch={handleSearch}
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <BrandSearchSelector
                placeholder="选择品牌"
                value={searchFilters.brandCodeSearch || undefined}
                onChange={(value) =>
                  setSearchFilters((prev) => ({ ...prev, brandCodeSearch: value || '' }))
                }
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <SupplierSearchSelector
                placeholder="选择供应商"
                value={searchFilters.supplierCodeSearch || undefined}
                onChange={(value) =>
                  setSearchFilters((prev) => ({ ...prev, supplierCodeSearch: value || '' }))
                }
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <ProductCategorySelector
                placeholder="选择分类"
                value={searchFilters.categoryCodeSearch || undefined}
                onChange={(value) =>
                  setSearchFilters((prev) => ({ ...prev, categoryCodeSearch: value || '' }))
                }
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <ColorSelector
                placeholder="选择颜色"
                value={searchFilters.colorCodeSearch || undefined}
                onChange={(value) =>
                  setSearchFilters((prev) => ({ ...prev, colorCodeSearch: value || '' }))
                }
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
          </Row>

          <div className={styles.searchActions}>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
              <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                刷新
              </Button>
            </Space>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className={styles.actionSection}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/product/sku/add')}
            >
              新增SKU
            </Button>

            <Dropdown
              menu={{
                items: [
                  {
                    key: 'exportSelected',
                    label: `导出选中记录 (${selectedRowKeys.length})`,
                    icon: <ExportOutlined />,
                    disabled: selectedRowKeys.length === 0 || selectedRowKeys.length > 50,
                    onClick: handleExportSelected,
                  },
                  {
                    key: 'exportAll',
                    label: '导出全部记录',
                    icon: <FileExcelOutlined />,
                    onClick: handleExportAll,
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button loading={exportLoading}>
                <ExportOutlined />
                导出Excel
                <DownOutlined />
              </Button>
            </Dropdown>

            <Dropdown
              menu={{
                items: [
                  {
                    key: 'downloadTemplate',
                    label: '下载导入模板',
                    icon: <DownloadOutlined />,
                    onClick: handleDownloadTemplate,
                  },
                  {
                    key: 'import',
                    label: (
                      <Upload
                        accept=".xlsx,.xls"
                        beforeUpload={handleImport}
                        showUploadList={false}
                      >
                        <span>
                          <ImportOutlined style={{ marginRight: 8 }} />
                          导入Excel文件
                        </span>
                      </Upload>
                    ),
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button loading={importLoading}>
                <ImportOutlined />
                导入Excel
                <DownOutlined />
              </Button>
            </Dropdown>
          </Space>
        </div>

        {/* 表格 */}
        <div className={styles.tableContainer}>
          <Table
            columns={columns}
            dataSource={skus}
            rowKey="id"
            loading={loading}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
              getCheckboxProps: () => ({
                disabled: false,
              }),
            }}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 1400 }}
            size="small"
          />
        </div>
      </Card>
    </div>
  );
};

export default SkuManagement;
