.container {
  padding: 0;
}

.actionSection {
  margin-top: 24px;
  padding: 16px 24px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
}

/* 表单样式优化 */
:global(.ant-form-item) {
  margin-bottom: 16px;
}

:global(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0;
  font-weight: 600;
  color: #262626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .actionSection {
    flex-direction: column;
    gap: 12px;
  }

  .actionSection :global(.ant-btn) {
    width: 100%;
  }
}
