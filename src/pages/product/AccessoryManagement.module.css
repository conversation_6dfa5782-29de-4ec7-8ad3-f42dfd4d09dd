.container {
  padding: 4px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 16px;
}

.tableContainer {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.accessoryInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.accessoryImage {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #d9d9d9;
}

.accessoryDetails {
  flex: 1;
}

.accessoryName {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.accessoryMeta {
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  gap: 8px;
}

.articleNumber {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: #f5f5f5;
  padding: 1px 4px;
  border-radius: 2px;
  border: 1px solid #e8e8e8;
}

.priceTag {
  color: #f5222d;
  font-weight: 500;
}

.supplierTag {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.colorSwatch {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  margin-right: 4px;
  vertical-align: middle;
}

.sizeTag {
  background-color: #f6ffed;
  color: #52c41a;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 11px;
  border: 1px solid #b7eb8f;
}

.actionButton {
  margin-right: 8px;
}

.actionButton:last-child {
  margin-right: 0;
}

.formItem {
  margin-bottom: 16px;
}

.imageUpload {
  display: flex;
  align-items: center;
  gap: 12px;
}

.imagePreview {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #d9d9d9;
}

.searchFilters {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.searchFilters .ant-input-search {
  min-width: 200px;
}

/* 表格样式优化 */
.tableContainer .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.tableContainer .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

/* 分页样式 */
.tableContainer .ant-pagination {
  margin: 16px;
  text-align: right;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-modal-body {
  padding: 24px;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 10px 16px;
  text-align: right;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-number,
.ant-select-selector {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused,
.ant-input-number:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 价格输入框样式 */
.priceInput .ant-input-number {
  width: 100%;
}

.priceInput .ant-input-number-prefix {
  color: #f5222d;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
  }

  .searchFilters {
    flex-direction: column;
  }

  .searchFilters .ant-input-search {
    min-width: auto;
    width: 100%;
  }

  .accessoryImage {
    width: 32px;
    height: 32px;
  }

  .accessoryMeta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
