import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Card,
  Form,
  Button,
  Space,
  message,
  Row,
  Col,
  InputNumber,
  Input,
  Descriptions,
  Spin,
  Tag,
  Image,
  Tooltip,
} from 'antd';
import { SaveOutlined, ArrowLeftOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { updateInventory } from '@/api/InventoryApi';
import { getProductInventory } from '@/api/InventoryApi';
import type {
  UpdateInventoryParams,
  SizeInventory,
  ProductInventoryResponse,
} from '@/types/inventory';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './InventoryEdit.module.css';

const InventoryEdit: React.FC = () => {
  const navigate = useNavigate();
  const { inventoryId } = useParams<{ inventoryId: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [inventoryData, setInventoryData] = useState<SizeInventory | null>(null);
  const [productInfo, setProductInfo] = useState<ProductInventoryResponse | null>(null);

  // 获取库存详情
  const fetchInventoryDetail = async () => {
    if (!inventoryId) return;

    setPageLoading(true);
    try {
      // 这里需要根据inventoryId获取具体的库存明细
      // 由于API文档中没有单独获取库存明细的接口，我们需要通过商品库存接口来查找
      // 这里暂时使用模拟数据，实际应该调用相应的API

      // 模拟获取库存详情的逻辑
      // 实际实现时需要根据后端提供的接口来获取
      message.error('获取库存详情功能需要后端提供相应的API接口');
      navigate('/product/inventory');
    } catch (error) {
      logError('获取库存详情', error);
      message.error(getErrorMessage(error));
      navigate('/product/inventory');
    } finally {
      setPageLoading(false);
    }
  };

  useEffect(() => {
    fetchInventoryDetail();
  }, [inventoryId]);

  // 保存修改
  const handleSave = useCallback(async () => {
    if (!inventoryId || !inventoryData) return;

    try {
      const values = await form.validateFields();
      setLoading(true);

      const updateParams: UpdateInventoryParams = {
        quantity: values.quantity,
        reservedQuantity: values.reservedQuantity || 0,
        safetyStock: values.safetyStock || 0,
        latestCost: values.latestCost,
        warehouseLocation: values.warehouseLocation,
        remark: values.remark,
      };

      const response = await updateInventory(inventoryId, updateParams);
      const result = handleApiResponse(response, '库存更新成功', '库存更新失败');

      if (result.success) {
        message.success(result.message);
        navigate('/product/inventory');
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('更新库存', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  }, [inventoryId, inventoryData, form, navigate]);

  // F8快捷键保存
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F8') {
        event.preventDefault();
        handleSave();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleSave]);

  // 返回列表
  const handleBack = () => {
    navigate('/product/inventory');
  };

  if (pageLoading) {
    return (
      <div className={styles.container}>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>加载中...</div>
          </div>
        </Card>
      </div>
    );
  }

  if (!inventoryData) {
    return (
      <div className={styles.container}>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <div>库存数据不存在</div>
            <Button type="primary" onClick={handleBack} style={{ marginTop: 16 }}>
              返回列表
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.header}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            className={styles.backButton}
          >
            返回列表
          </Button>
          <h2>编辑库存明细</h2>
        </div>

        {/* 商品信息 */}
        <Card title="商品信息" className={styles.infoCard}>
          <Descriptions column={2} size="small">
            <Descriptions.Item label="商品名称">{productInfo?.productInfo.name}</Descriptions.Item>
            <Descriptions.Item label="商品编码">{productInfo?.productInfo.code}</Descriptions.Item>
            <Descriptions.Item label="品牌">{productInfo?.productInfo.brandName}</Descriptions.Item>
            <Descriptions.Item label="供应商">
              {productInfo?.productInfo.supplierName}
            </Descriptions.Item>
            <Descriptions.Item label="尺寸">{inventoryData.size}</Descriptions.Item>
            <Descriptions.Item label="当前状态">
              {inventoryData.isLowStock ? (
                <span style={{ color: '#ff4d4f' }}>低库存</span>
              ) : (
                <span style={{ color: '#52c41a' }}>正常</span>
              )}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 编辑表单 */}
        <Card title="库存信息" className={styles.formCard}>
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              quantity: inventoryData.quantity,
              reservedQuantity: inventoryData.reservedQuantity,
              safetyStock: inventoryData.safetyStock,
              latestCost: inventoryData.latestCost,
              warehouseLocation: inventoryData.warehouseLocation,
              remark: inventoryData.remark,
            }}
          >
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name="quantity"
                  label="库存数量"
                  rules={[
                    { required: true, message: '请输入库存数量' },
                    { type: 'number', min: 0, message: '库存数量不能小于0' },
                  ]}
                >
                  <InputNumber style={{ width: '100%' }} placeholder="请输入库存数量" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="reservedQuantity"
                  label="预留数量"
                  rules={[{ type: 'number', min: 0, message: '预留数量不能小于0' }]}
                >
                  <InputNumber style={{ width: '100%' }} placeholder="请输入预留数量" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="safetyStock"
                  label="安全库存"
                  rules={[{ type: 'number', min: 0, message: '安全库存不能小于0' }]}
                >
                  <InputNumber style={{ width: '100%' }} placeholder="请输入安全库存" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="latestCost"
                  label="最新成本"
                  rules={[{ type: 'number', min: 0, message: '成本不能小于0' }]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    precision={2}
                    placeholder="请输入最新成本"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="warehouseLocation" label="仓库位置">
                  <Input placeholder="请输入仓库位置" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="remark" label="备注">
                  <Input.TextArea rows={3} placeholder="请输入备注" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>

        {/* 操作按钮 */}
        <div className={styles.footer}>
          <Space>
            <Button onClick={handleBack} size="small">
              取消
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={loading}
              size="small"
            >
              保存 (F8)
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default InventoryEdit;
