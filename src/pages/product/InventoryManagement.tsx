import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Row,
  Col,
  Select,
  Tag,
  Tooltip,
  Modal,
  Form,
  InputNumber,
  Image,
  Popconfirm,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  FileExcelOutlined,
  WarningOutlined,
  EyeOutlined,
  PlusOutlined,
  LeftOutlined,
  RightOutlined,
  AppstoreOutlined,
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import {
  getInventoryList,
  adjustInventory,
  exportInventoryExcel,
  deleteInventory,
} from '@/api/InventoryApi';
import type {
  InventoryDetail,
  InventoryGroupByColor,
  InventoryListParams,
  InventoryAdjustParams,
  ExportInventoryParams,
} from '@/types/inventory';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './InventoryManagement.module.css';

const { Search } = Input;
const { Option } = Select;

// 图片组件
const ColorImages: React.FC<{ images: string[]; alt: string }> = ({ images, alt }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  if (!images || images.length === 0) {
    return (
      <div className={styles.colorImages}>
        <div className={styles.noImage}>
          <AppstoreOutlined />
        </div>
      </div>
    );
  }

  const handlePrev = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
  };

  const handleNext = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
  };

  return (
    <div className={styles.colorImages}>
      <Image.PreviewGroup>
        {images.map((image, index) => (
          <Image
            key={index}
            src={image}
            alt={`${alt} ${index + 1}`}
            className={styles.colorImage}
            style={{ display: index === currentIndex ? 'block' : 'none' }}
          />
        ))}
      </Image.PreviewGroup>
      {images.length > 1 && (
        <>
          <button className={`${styles.imageNavigation} ${styles.prevButton}`} onClick={handlePrev}>
            <LeftOutlined />
          </button>
          <button className={`${styles.imageNavigation} ${styles.nextButton}`} onClick={handleNext}>
            <RightOutlined />
          </button>
          <div className={styles.imageIndicator}>
            {currentIndex + 1}/{images.length}
          </div>
        </>
      )}
    </div>
  );
};

const InventoryManagement: React.FC = () => {
  const navigate = useNavigate();
  const [inventoryList, setInventoryList] = useState<InventoryGroupByColor[]>([]);
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchText, setSearchText] = useState('');
  const [brandCode, setBrandCode] = useState<string>('');
  const [supplierCode, setSupplierCode] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC');
  const [lowStockOnly, setLowStockOnly] = useState<boolean | undefined>(undefined);
  const [hasStockOnly, setHasStockOnly] = useState<boolean | undefined>(undefined);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 库存调整模态框
  const [adjustModalVisible, setAdjustModalVisible] = useState(false);
  const [adjustingRecord, setAdjustingRecord] = useState<InventoryDetail | null>(null);
  const [adjustForm] = Form.useForm();

  // 获取库存列表
  const fetchInventoryList = async (
    page = 1,
    pageSize = 20,
    search = '',
    brand = '',
    supplier = '',
    sortField = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
    lowStock?: boolean,
    hasStock?: boolean,
  ) => {
    setLoading(true);
    try {
      const params: InventoryListParams = {
        page,
        pageSize,
        search: search || undefined,
        brandCode: brand || undefined,
        supplierCode: supplier || undefined,
        sortBy: sortField as any,
        sortOrder: sortDirection,
        lowStockOnly: lowStock,
        hasStockOnly: hasStock,
      };

      const response = await getInventoryList(params);
      const result = handleApiResponse(response, '', '获取库存列表失败');

      if (result.success && response.data) {
        setInventoryList(response.data.inventoryDetails || []);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total || 0,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取库存列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchInventoryList();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    fetchInventoryList(
      1,
      pagination.pageSize,
      value,
      brandCode,
      supplierCode,
      sortBy,
      sortOrder,
      lowStockOnly,
      hasStockOnly,
    );
  };

  // 品牌筛选处理
  const handleBrandChange = (value: string) => {
    setBrandCode(value);
    fetchInventoryList(
      1,
      pagination.pageSize,
      searchText,
      value,
      supplierCode,
      sortBy,
      sortOrder,
      lowStockOnly,
      hasStockOnly,
    );
  };

  // 供应商筛选处理
  const handleSupplierChange = (value: string) => {
    setSupplierCode(value);
    fetchInventoryList(
      1,
      pagination.pageSize,
      searchText,
      brandCode,
      value,
      sortBy,
      sortOrder,
      lowStockOnly,
      hasStockOnly,
    );
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchInventoryList(
      pagination.current,
      pagination.pageSize,
      searchText,
      brandCode,
      supplierCode,
      sortBy,
      sortOrder,
      lowStockOnly,
      hasStockOnly,
    );
  };

  // 分页变化处理
  const handleTableChange = (
    paginationConfig: TablePaginationConfig,
    filters: any,
    sorter: any,
  ) => {
    const { current = 1, pageSize = 20 } = paginationConfig;

    let newSortBy = sortBy;
    let newSortOrder = sortOrder;

    if (sorter && sorter.field) {
      newSortBy = sorter.field;
      newSortOrder = sorter.order === 'ascend' ? 'ASC' : 'DESC';
      setSortBy(newSortBy);
      setSortOrder(newSortOrder);
    }

    fetchInventoryList(
      current,
      pageSize,
      searchText,
      brandCode,
      supplierCode,
      newSortBy,
      newSortOrder,
      lowStockOnly,
      hasStockOnly,
    );
  };

  // 筛选变化处理
  const handleFilterChange = (type: 'lowStock' | 'hasStock', value: boolean | undefined) => {
    if (type === 'lowStock') {
      setLowStockOnly(value);
    } else {
      setHasStockOnly(value);
    }
    fetchInventoryList(
      1,
      pagination.pageSize,
      searchText,
      brandCode,
      supplierCode,
      sortBy,
      sortOrder,
      type === 'lowStock' ? value : lowStockOnly,
      type === 'hasStock' ? value : hasStockOnly,
    );
  };

  // 查看商品库存详情
  const handleViewProductInventory = (productCode: string) => {
    navigate(`/product/inventory/detail/${productCode}`);
  };

  // 创建库存
  const handleCreateInventory = () => {
    navigate('/product/inventory/create');
  };

  // 复制到剪贴板
  const handleCopy = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success(`${label}已复制到剪贴板`);
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        message.success(`${label}已复制到剪贴板`);
      } catch (err) {
        message.error('复制失败');
      }
      document.body.removeChild(textArea);
    }
  };

  // 删除库存
  const handleDeleteInventory = async (
    inventoryId: string,
    productName: string,
    colorName: string,
    size: string,
  ) => {
    try {
      const response = await deleteInventory(inventoryId);
      const result = handleApiResponse(response, '库存删除成功', '库存删除失败');

      if (result.success) {
        message.success(`${productName} - ${colorName} - ${size} 库存删除成功`);
        // 重新加载当前页数据
        fetchInventoryList(
          pagination.current,
          pagination.pageSize,
          searchText,
          brandCode,
          supplierCode,
          sortBy,
          sortOrder,
          lowStockOnly,
          hasStockOnly,
        );
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('删除库存', error);
      message.error(getErrorMessage(error));
    }
  };

  // 库存调整
  const handleAdjust = (record: InventoryDetail) => {
    setAdjustingRecord(record);
    adjustForm.resetFields();
    adjustForm.setFieldsValue({
      adjustType: 'ADD',
      quantity: 1,
      reason: '',
    });
    setAdjustModalVisible(true);
  };

  // 确认库存调整
  const handleAdjustConfirm = async () => {
    if (!adjustingRecord) return;

    try {
      const values = await adjustForm.validateFields();
      const params: InventoryAdjustParams = {
        productCode: adjustingRecord.productCode,
        colorCode: adjustingRecord.colorCode,
        size: adjustingRecord.size,
        adjustType: values.adjustType,
        quantity: values.quantity,
        reason: values.reason,
        referenceNumber: values.referenceNumber,
        operatorCode: 'current_user', // 这里应该从用户上下文获取
      };

      const response = await adjustInventory(params);
      const result = handleApiResponse(response, '库存调整成功', '库存调整失败');

      if (result.success) {
        message.success(result.message);
        setAdjustModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('库存调整', error);
      message.error(getErrorMessage(error));
    }
  };

  // 导出Excel
  const handleExportExcel = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要导出的库存记录');
      return;
    }

    setExportLoading(true);
    try {
      const exportParams: ExportInventoryParams = {
        inventoryIds: selectedRowKeys.map((key) => key.toString()),
      };
      const blob = await exportInventoryExcel(exportParams);

      // 下载文件
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `库存明细_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('Excel导出成功');
    } catch (error: any) {
      logError('导出库存Excel', error);
      message.error(getErrorMessage(error, '导出Excel失败'));
    } finally {
      setExportLoading(false);
    }
  };

  // 表格行选择
  const rowSelection: TableRowSelection<InventoryGroupByColor> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // 表格列定义
  const columns: ColumnsType<InventoryGroupByColor> = [
    {
      title: '图片',
      key: 'image',
      width: 70,
      fixed: 'left',
      render: (_, record) => (
        <div className={styles.colorImageCell}>
          <ColorImages images={record.colorImages || []} alt={record.colorName} />
          {/* 库存状态指示器 */}
          <div className={styles.stockIndicator}>
            <div
              className={`${styles.stockDot} ${
                record.totalQuantity === 0
                  ? styles.noStock
                  : record.sizeDetails.some((s) => s.isLowStock)
                    ? styles.lowStock
                    : styles.normalStock
              }`}
            />
          </div>
        </div>
      ),
    },
    {
      title: '商品信息',
      key: 'productInfo',
      width: 180,
      render: (_, record) => (
        <div className={styles.productInfoCell}>
          <div className={styles.productName}>{record.productName}</div>
          <div className={styles.productCode}>
            <span>编码: {record.productCode}</span>
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopy(record.productCode, '商品编码')}
              className={styles.copyButton}
              title="复制编码"
            />
          </div>
          <div className={styles.brandName}>品牌: {record.brandName}</div>
          <div className={styles.supplierName}>供应商: {record.supplierName}</div>
        </div>
      ),
    },
    {
      title: '颜色',
      key: 'colorInfo',
      width: 70,
      render: (_, record) => (
        <div className={styles.colorInfoCell}>
          <div className={styles.colorName}>{record.colorName}</div>
          <div className={styles.colorCode}>({record.colorCode})</div>
        </div>
      ),
    },
    {
      title: '总库存',
      key: 'totalQuantity',
      width: 60,
      align: 'center',
      render: (_, record) => (
        <div className={styles.quantityCell}>
          <Tag color={record.totalQuantity > 0 ? 'green' : 'red'} className={styles.quantityTag}>
            {record.totalQuantity}
          </Tag>
        </div>
      ),
    },
    {
      title: '可用库存',
      key: 'totalAvailableQuantity',
      width: 60,
      align: 'center',
      render: (_, record) => (
        <Tag
          color={record.totalAvailableQuantity > 0 ? 'blue' : 'default'}
          className={styles.quantityTag}
        >
          {record.totalAvailableQuantity}
        </Tag>
      ),
    },
    {
      title: '尺寸明细',
      key: 'sizeDetails',
      width: 350,
      render: (_, record) => (
        <div className={styles.sizeDetailsCell}>
          <div className={styles.sizeDetailsGrid}>
            {record.sizeDetails.map((size) => (
              <div
                key={size.size}
                className={`${styles.sizeDetailItem} ${size.isLowStock ? styles.lowStockItem : ''}`}
              >
                <div className={styles.sizeInfo}>
                  <span className={styles.sizeName}>{size.size}</span>
                  {size.isLowStock && (
                    <Tooltip title="低库存警告">
                      <WarningOutlined className={styles.lowStockIcon} />
                    </Tooltip>
                  )}
                </div>
                <div className={styles.sizeQuantity}>
                  <span className={styles.availableQty}>{size.availableQuantity}</span>
                  <span className={styles.separator}>/</span>
                  <span className={styles.totalQty}>{size.quantity}</span>
                </div>
                {size.reservedQuantity > 0 && (
                  <div className={styles.reservedQty}>
                    <Tooltip title="预留数量">
                      <span>预留: {size.reservedQuantity}</span>
                    </Tooltip>
                  </div>
                )}
                <div className={styles.sizeActions}>
                  <Tooltip title="编辑库存">
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => navigate(`/product/inventory/edit/${size.inventoryId}`)}
                      className={styles.editButton}
                    />
                  </Tooltip>
                  <Popconfirm
                    title="确定要删除这个库存项吗？"
                    description={`${record.productName} - ${record.colorName} - ${size.size}`}
                    onConfirm={() =>
                      handleDeleteInventory(
                        size.inventoryId,
                        record.productName,
                        record.colorName,
                        size.size,
                      )
                    }
                    okText="确定"
                    cancelText="取消"
                    placement="topRight"
                  >
                    <Tooltip title="删除库存">
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        className={styles.deleteButton}
                      />
                    </Tooltip>
                  </Popconfirm>
                </div>
              </div>
            ))}
          </div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看库存详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewProductInventory(record.productCode)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="库存调整">
            <Button
              type="text"
              icon={<PlusOutlined />}
              onClick={() => handleViewProductInventory(record.productCode)}
              size="small"
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        {/* 工具栏 */}
        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <div className={styles.searchSection}>
                <Space size="small">
                  <Search
                    placeholder="搜索商品编码、名称、颜色等"
                    allowClear
                    enterButton={<SearchOutlined />}
                    onSearch={handleSearch}
                    style={{ width: 320 }}
                  />
                  <Select
                    placeholder="库存状态"
                    allowClear
                    style={{ width: 100 }}
                    onChange={(value) => handleFilterChange('lowStock', value)}
                    value={lowStockOnly}
                  >
                    <Option value={true}>低库存</Option>
                    <Option value={false}>正常库存</Option>
                  </Select>
                  <Select
                    placeholder="有无库存"
                    allowClear
                    style={{ width: 100 }}
                    onChange={(value) => handleFilterChange('hasStock', value)}
                    value={hasStockOnly}
                  >
                    <Option value={true}>有库存</Option>
                    <Option value={false}>无库存</Option>
                  </Select>
                </Space>
                <div className={styles.quickFilters}>
                  <Space size="small">
                    <Button
                      size="small"
                      type={lowStockOnly === true ? 'primary' : 'default'}
                      onClick={() =>
                        handleFilterChange('lowStock', lowStockOnly === true ? undefined : true)
                      }
                      icon={<WarningOutlined />}
                    >
                      低库存
                    </Button>
                    <Button
                      size="small"
                      type={hasStockOnly === false ? 'primary' : 'default'}
                      onClick={() =>
                        handleFilterChange('hasStock', hasStockOnly === false ? undefined : false)
                      }
                    >
                      零库存
                    </Button>
                  </Space>
                </div>
              </div>
            </Col>
            <Col>
              <Space size="small">
                <Button
                  type="default"
                  icon={<FileExcelOutlined />}
                  onClick={handleExportExcel}
                  loading={exportLoading}
                  disabled={selectedRowKeys.length === 0}
                  size="small"
                >
                  导出Excel ({selectedRowKeys.length})
                </Button>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                  size="small"
                >
                  刷新
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreateInventory}
                  size="small"
                >
                  创建库存
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={inventoryList}
          rowKey={(record) => `${record.productCode}-${record.colorCode}`}
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            size: 'small',
          }}
          onChange={handleTableChange}
          scroll={{ x: 850, y: 'calc(100vh - 280px)' }}
          size="small"
          className={styles.inventoryTable}
        />
      </Card>

      {/* 库存调整模态框 */}
      <Modal
        title="库存调整"
        open={adjustModalVisible}
        onOk={handleAdjustConfirm}
        onCancel={() => setAdjustModalVisible(false)}
        width={500}
        destroyOnClose
      >
        {adjustingRecord && (
          <div style={{ marginBottom: 16 }}>
            <p>
              <strong>商品:</strong> {adjustingRecord.productName} ({adjustingRecord.productCode})
            </p>
            <p>
              <strong>颜色/尺寸:</strong> {adjustingRecord.colorName} / {adjustingRecord.size}
            </p>
            <p>
              <strong>当前库存:</strong> {adjustingRecord.quantity}
            </p>
            <p>
              <strong>可用数量:</strong> {adjustingRecord.availableQuantity}
            </p>
          </div>
        )}

        <Form form={adjustForm} layout="vertical">
          <Form.Item
            name="adjustType"
            label="调整类型"
            rules={[{ required: true, message: '请选择调整类型' }]}
          >
            <Select>
              <Option value="SET">设置为指定数量</Option>
              <Option value="ADD">增加指定数量</Option>
              <Option value="SUBTRACT">减少指定数量</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="quantity"
            label="数量"
            rules={[{ required: true, message: '请输入数量' }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="reason"
            label="调整原因"
            rules={[{ required: true, message: '请输入调整原因' }]}
          >
            <Input.TextArea rows={3} placeholder="请输入调整原因" />
          </Form.Item>

          <Form.Item name="referenceNumber" label="参考单号">
            <Input placeholder="请输入参考单号（可选）" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default InventoryManagement;
