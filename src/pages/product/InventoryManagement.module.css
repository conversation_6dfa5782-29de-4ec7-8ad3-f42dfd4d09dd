.container {
  padding: 6px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 10px;
  padding: 6px 0;
}

.searchSection {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
}

.quickFilters {
  display: flex;
  align-items: center;
}

/* 图片展示 */
.colorImageCell {
  width: 60px;
  height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.stockIndicator {
  position: absolute;
  top: 2px;
  right: 2px;
  z-index: 10;
}

.stockDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.noStock {
  background-color: #ff4d4f;
}

.lowStock {
  background-color: #fa8c16;
}

.normalStock {
  background-color: #52c41a;
}

.colorImages {
  position: relative;
  width: 50px;
  height: 50px;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.colorImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.imageNavigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  font-size: 9px;
  z-index: 2;
}

.colorImages:hover .imageNavigation {
  opacity: 0.8;
}

.imageNavigation:hover {
  opacity: 1 !important;
  background: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.1);
}

.prevButton {
  left: 2px;
}

.nextButton {
  right: 2px;
}

.imageIndicator {
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 9px;
  line-height: 1.2;
}

.noImage {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #bfbfbf;
  font-size: 20px;
}

/* 商品信息 */
.productInfoCell {
  line-height: 1.3;
  padding: 4px 0;
}

.productName {
  font-weight: 600;
  color: #262626;
  margin-bottom: 2px;
  font-size: 13px;
}

.productCode {
  color: #1890ff;
  font-size: 11px;
  margin-bottom: 1px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.copyButton {
  padding: 0 4px;
  height: 16px;
  font-size: 10px;
  opacity: 0.6;
  transition: opacity 0.2s;
  min-width: 16px;
}

.copyButton:hover {
  opacity: 1;
}

.brandName {
  color: #1890ff;
  font-size: 11px;
  margin-bottom: 1px;
}

.supplierName {
  color: #52c41a;
  font-size: 11px;
}

/* 颜色信息 */
.colorInfoCell {
  text-align: center;
  padding: 4px 0;
}

.colorName {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
  font-size: 12px;
}

.colorCode {
  color: #8c8c8c;
  font-size: 10px;
}

/* 库存数量 */
.quantityCell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantityTag {
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

/* 尺寸明细 */
.sizeDetailsCell {
  padding: 4px 0;
}

.sizeDetailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(55px, 1fr));
  gap: 3px;
  max-height: 100px;
  overflow-y: auto;
}

.sizeDetailItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2px;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  background-color: #fafafa;
  min-width: 50px;
  transition: all 0.2s ease;
}

.sizeDetailItem:hover {
  background-color: #f0f8ff;
  border-color: #d9d9d9;
}

.sizeDetailItem:hover .sizeActions {
  opacity: 1;
}

.lowStockItem {
  background-color: #fff2f0;
  border-color: #ffccc7;
}

.lowStockItem:hover {
  background-color: #fff1f0;
  border-color: #ffa39e;
}

.sizeInfo {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-bottom: 2px;
}

.sizeName {
  font-weight: 500;
  color: #262626;
  font-size: 11px;
}

.lowStockIcon {
  color: #ff4d4f;
  font-size: 10px;
}

.sizeQuantity {
  display: flex;
  align-items: center;
  font-size: 10px;
  line-height: 1;
}

.availableQty {
  color: #52c41a;
  font-weight: 500;
}

.separator {
  color: #d9d9d9;
  margin: 0 1px;
}

.totalQty {
  color: #8c8c8c;
}

.reservedQty {
  font-size: 9px;
  color: #fa8c16;
  margin-top: 1px;
  text-align: center;
  line-height: 1;
}

/* 尺寸操作按钮样式 */
.sizeActions {
  display: flex;
  justify-content: center;
  gap: 2px;
  margin-top: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.editButton {
  color: #1890ff;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editButton:hover {
  color: #40a9ff;
  background-color: #f0f8ff;
}

.deleteButton {
  color: #ff4d4f;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.deleteButton:hover {
  color: #ff7875;
  background-color: #fff2f0;
}

/* 表格样式 */
.inventoryTable {
  background: white;
  border-radius: 6px;
}

.inventoryTable .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  font-size: 12px;
  padding: 5px 6px;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
}

.inventoryTable .ant-table-tbody > tr > td {
  padding: 3px 6px;
  border-bottom: 1px solid #f5f5f5;
  vertical-align: top;
}

.inventoryTable .ant-table-tbody > tr:hover > td {
  background-color: #f8f9fa;
}

.inventoryTable .ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #e6f7ff;
}

.inventoryTable .ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background-color: #bae7ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 4px;
  }

  .toolbar {
    margin-bottom: 8px;
  }

  .searchSection {
    gap: 6px;
  }

  .quickFilters {
    margin-top: 4px;
  }

  .colorImageCell {
    width: 60px;
    height: 60px;
  }

  .colorImages {
    width: 50px;
    height: 50px;
  }

  .stockDot {
    width: 6px;
    height: 6px;
  }

  .productInfoCell {
    font-size: 11px;
  }

  .productName {
    font-size: 12px;
  }

  .sizeDetailsGrid {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 2px;
  }

  .sizeDetailItem {
    min-width: 45px;
    padding: 2px;
  }
}
