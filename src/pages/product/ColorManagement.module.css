.container {
  padding: 4px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 16px;
}

.tableContainer {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.colorPreview {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.colorSwatch {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  display: inline-block;
  vertical-align: middle;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.colorCode {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 15px;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  color: #147ffa;
  font-weight: bold;
  border: 1px solid #e8e8e8;
}

.colorName {
  color: black;
}

.actionButton {
  margin-right: 8px;
}

.actionButton:last-child {
  margin-right: 0;
}

.formItem {
  margin-bottom: 16px;
}

.colorInput {
  display: flex;
  align-items: center;
  gap: 8px;
}

.colorPicker {
  width: 40px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
}

.colorPicker:hover {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.colorPickerInput {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  cursor: pointer;
  background: transparent;
}

.colorPickerInput::-webkit-color-swatch-wrapper {
  padding: 0;
}

.colorPickerInput::-webkit-color-swatch {
  border: none;
  border-radius: 4px;
}

.colorPickerInput::-moz-color-swatch {
  border: none;
  border-radius: 4px;
}

/* 表格行悬停效果 */
.tableContainer .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 表格头部样式 */
.tableContainer .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

/* 分页样式 */
.tableContainer .ant-pagination {
  margin: 16px;
  text-align: right;
}

/* 搜索框样式 */
.toolbar .ant-input-search {
  max-width: 300px;
}

/* 按钮样式 */
.toolbar .ant-btn {
  margin-left: 8px;
}

.toolbar .ant-btn:first-child {
  margin-left: 0;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-modal-body {
  padding: 24px;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 10px 16px;
  text-align: right;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input:focus,
.ant-input-focused {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
  }

  .toolbar .ant-input-search {
    max-width: 100%;
  }

  .colorSwatch {
    width: 16px;
    height: 16px;
  }

  .colorCode {
    font-size: 11px;
    padding: 1px 4px;
  }
}
