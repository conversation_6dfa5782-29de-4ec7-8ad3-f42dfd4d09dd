import React, { useState, useEffect, useCallback } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Button,
  Card,
  Row,
  Col,
  message,
  Space,
  Divider,
  Affix,
} from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import {
  BrandSearchSelector,
  SupplierSearchSelector,
  ProductCategorySelector,
  ColorSelector,
  SkuAccessorySelector,
} from '@/components';
import { SingleImageArrayAdapter } from '@/components/ImageUpload';
import { createSku, updateSku, getSkuDetail } from '@/api/SkuApi';
import type { AddSkuParams, UpdateSkuParams } from '@/types/skus';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './SkuForm.module.css';

const { TextArea } = Input;

const SkuForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [saveAndContinueLoading, setSaveAndContinueLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isView, setIsView] = useState(false);

  // 判断页面模式
  useEffect(() => {
    const path = window.location.pathname;
    setIsEdit(path.includes('/edit/'));
    setIsView(path.includes('/view/'));
  }, []);

  // 加载SKU详情
  const loadSkuDetail = async (skuId: string) => {
    setLoading(true);
    try {
      const response = await getSkuDetail(skuId);
      const result = handleApiResponse(response, '', '获取SKU详情失败');

      if (result.success && response.data) {
        const sku = response.data;
        form.setFieldsValue({
          name: sku.name,
          manufacturerCode: sku.manufacturerCode,
          brandCode: sku.brandCode,
          supplierCode: sku.supplierCode,
          categoryCode: sku.categoryCode,
          colorCode: sku.colorCode,
          craftDescription: sku.craftDescription,
          clothingCost: sku.clothingCost,
          retailPrice: sku.retailPrice,
          preOrderPrice: sku.preOrderPrice,
          restockPrice: sku.restockPrice,
          spotPrice: sku.spotPrice,
          accessories: sku.accessories || [],
          images: sku.images,
        });
      } else {
        message.error(result.message);
        navigate('/product/sku');
      }
    } catch (error) {
      logError('获取SKU详情', error);
      message.error(getErrorMessage(error));
      navigate('/product/sku');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    if (id && (isEdit || isView)) {
      loadSkuDetail(id);
    }
  }, [id, isEdit, isView]);

  // F8快捷键保存
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === 'F8' && !isView) {
        event.preventDefault();
        form.submit();
      }
    },
    [form, isView],
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // 提交表单
  const handleSubmit = async (values: any) => {
    setSubmitLoading(true);
    try {
      const skuData = {
        ...values,
        accessories: values.accessories || [],
        images: values.images || [],
      };

      let response;
      if (isEdit && id) {
        response = await updateSku(id, skuData as UpdateSkuParams);
      } else {
        response = await createSku(skuData as AddSkuParams);
      }

      const result = handleApiResponse(
        response,
        isEdit ? '更新成功' : '创建成功',
        isEdit ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        navigate('/product/sku');
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError(isEdit ? '更新SKU' : '创建SKU', error);
      message.error(getErrorMessage(error));
    } finally {
      setSubmitLoading(false);
    }
  };

  // 保存并继续录入（保存当前数据，清空颜色和配件信息）
  const handleSaveAndContinue = async () => {
    try {
      // 先验证表单
      const values = await form.validateFields();

      setSaveAndContinueLoading(true);

      const skuData = {
        ...values,
        accessories: values.accessories || [],
        images: values.images || [],
      };

      // 创建SKU（只在新增模式下可用）
      const response = await createSku(skuData as AddSkuParams);

      const result = handleApiResponse(response, '保存成功，可以继续录入下一个颜色', '保存失败');

      if (result.success) {
        message.success(result.message);

        // 保留需要保持的字段值
        const preservedValues = {
          brandCode: values.brandCode,
          supplierCode: values.supplierCode,
          categoryCode: values.categoryCode,
          name: values.name,
          manufacturerCode: values.manufacturerCode,
          clothingCost: values.clothingCost,
          retailPrice: values.retailPrice,
          preOrderPrice: values.preOrderPrice,
          restockPrice: values.restockPrice,
          spotPrice: values.spotPrice,
        };

        // 重置表单，但保留指定字段
        form.resetFields();

        // 使用 setTimeout 确保表单重置完成后再设置值
        setTimeout(() => {
          form.setFieldsValue(preservedValues);
        }, 100);

        // 滚动到顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });

        message.info('已清空颜色和配件信息，可以继续录入下一个颜色');
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      if (error.errorFields) {
        message.error('请先完善必填信息');
        return;
      }
      logError('保存并继续录入SKU', error);
      message.error(getErrorMessage(error));
    } finally {
      setSaveAndContinueLoading(false);
    }
  };

  // 页面标题
  const getPageTitle = () => {
    if (isView) return '查看SKU';
    if (isEdit) return '编辑SKU';
    return '新增SKU';
  };

  return (
    <div className={styles.container}>
      <Card
        title={getPageTitle()}
        extra={
          <Space>
            <Button onClick={() => navigate('/product/sku')}>返回列表</Button>
          </Space>
        }
        loading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          disabled={isView}
          initialValues={{
            clothingCost: 0,
            retailPrice: 0,
            preOrderPrice: 0,
            restockPrice: 0,
            spotPrice: 0,
          }}
        >
          <Row gutter={24}>
            {/* 基本信息 */}
            <Col span={24}>
              <Divider orientation="left">基本信息</Divider>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="brandCode"
                label="品牌"
                rules={[{ required: true, message: '请选择品牌' }]}
              >
                <BrandSearchSelector placeholder="请选择品牌" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="supplierCode"
                label="供应商"
                rules={[{ required: true, message: '请选择供应商' }]}
              >
                <SupplierSearchSelector placeholder="请选择供应商" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="categoryCode"
                label="商品分类"
                rules={[{ required: true, message: '请选择商品分类' }]}
              >
                <ProductCategorySelector placeholder="请选择商品分类" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="colorCode"
                label="颜色"
                rules={[{ required: true, message: '请选择颜色' }]}
              >
                <ColorSelector placeholder="请选择颜色" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="name"
                label="商品名称"
                rules={[{ max: 30, message: '名称不能超过30个字符' }]}
              >
                <Input placeholder="自定义商品名称（可选）" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="manufacturerCode"
                label="厂商编码"
                rules={[{ max: 100, message: '厂商编码不能超过100个字符' }]}
              >
                <Input placeholder="请输入厂商编码（可选）" />
              </Form.Item>
            </Col>

            {/* 工艺描述 */}
            <Col span={24}>
              <Form.Item
                name="craftDescription"
                label="工艺描述"
                rules={[{ max: 500, message: '工艺描述不能超过500个字符' }]}
              >
                <TextArea placeholder="请输入工艺描述" rows={3} showCount maxLength={500} />
              </Form.Item>
            </Col>

            {/* 价格信息 */}
            <Col span={24}>
              <Divider orientation="left">价格信息</Divider>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="clothingCost"
                label="服装成本"
                rules={[
                  { required: true, message: '请输入服装成本' },
                  { type: 'number', min: 0, message: '服装成本不能为负数' },
                ]}
              >
                <InputNumber
                  placeholder="请输入服装成本"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/¥\s?|(,*)/g, '')) as any}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="retailPrice"
                label="零售价"
                rules={[
                  { required: true, message: '请输入零售价' },
                  { type: 'number', min: 0, message: '零售价不能为负数' },
                ]}
              >
                <InputNumber
                  placeholder="请输入零售价"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/¥\s?|(,*)/g, '')) as any}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="preOrderPrice"
                label="预订价"
                rules={[
                  { required: true, message: '请输入预订价' },
                  { type: 'number', min: 0, message: '预订价不能为负数' },
                ]}
              >
                <InputNumber
                  placeholder="请输入预订价"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/¥\s?|(,*)/g, '')) as any}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="restockPrice"
                label="补货价"
                rules={[
                  { required: true, message: '请输入补货价' },
                  { type: 'number', min: 0, message: '补货价不能为负数' },
                ]}
              >
                <InputNumber
                  placeholder="请输入补货价"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/¥\s?|(,*)/g, '')) as any}
                />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="spotPrice"
                label="现货价"
                rules={[
                  { required: true, message: '请输入现货价' },
                  { type: 'number', min: 0, message: '现货价不能为负数' },
                ]}
              >
                <InputNumber
                  placeholder="请输入现货价"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/¥\s?|(,*)/g, '')) as any}
                />
              </Form.Item>
            </Col>

            {/* 配件信息 */}
            <Col span={24}>
              <Divider orientation="left">配件信息</Divider>
            </Col>

            <Col span={24}>
              <Form.Item name="accessories" label="配件配置">
                <SkuAccessorySelector />
              </Form.Item>
            </Col>

            {/* 图片信息 */}
            <Col span={24}>
              <Divider orientation="left">图片信息</Divider>
            </Col>

            <Col span={24}>
              <Form.Item name="images" label="SKU图片">
                <SingleImageArrayAdapter />
              </Form.Item>
            </Col>
          </Row>

          {/* 操作按钮 */}
          {!isView && (
            <Affix offsetBottom={20}>
              <div className={styles.actionSection}>
                <Space>
                  <Button onClick={() => navigate('/product/sku')}>取消</Button>
                  {!isEdit && (
                    <Button
                      type="default"
                      onClick={handleSaveAndContinue}
                      loading={saveAndContinueLoading}
                      style={{
                        backgroundColor: '#52c41a',
                        borderColor: '#52c41a',
                        color: 'white',
                      }}
                    >
                      补录
                    </Button>
                  )}
                  <Button type="primary" htmlType="submit" loading={submitLoading}>
                    {isEdit ? '更新' : '创建'} (F8)
                  </Button>
                </Space>
              </div>
            </Affix>
          )}
        </Form>
      </Card>
    </div>
  );
};

export default SkuForm;
