.container {
  padding: 0;
}

.searchSection {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.searchActions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.actionSection {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tableContainer {
  /* 紧凑的表格样式 */
}

.imageCell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.noImage {
  width: 100px;
  height: 100px;
  background: #f5f5f5;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #999;
}

.code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .searchSection {
    padding: 12px;
  }

  .searchActions {
    justify-content: center;
  }

  .actionSection {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
