.container {
  padding: 2px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

/* 统计信息区域 */
.statsSection {
  margin-bottom: 2px;
  padding: 12px;
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  border-radius: 6px;
  border: 1px solid #e8f4fd;
}

.statCard {
  text-align: center;
  padding: 8px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

/* 搜索区域 */
.searchSection {
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8f0fe;
  overflow: hidden;
}

.searchHeader {
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f4ff 100%);
  border-bottom: 1px solid #d6e4ff;
  display: flex;
  align-items: center;
}

.searchTitle {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.searchContent {
  gap: 12px;
  display: flex;
  align-items: center;
  height: 60px;
  justify-content: space-between;
}

.searchItem {
  display: flex;
  align-items: center;
  gap: 12px;
}

.searchLabel {
  display: block;
  width: 100px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.searchInputWrapper {
  width: 100%;
}

.searchInputWrapper .ant-select,
.searchInputWrapper .ant-input,
.searchInputWrapper .ant-picker {
  width: 100%;
}

.searchActions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  border-top: 1px solid #f0f0f0;
}

.toolbar {
  margin-bottom: 8px;
}

.toolbar .ant-row {
  align-items: center;
}

.toolbar .ant-input-search {
  max-width: 400px;
}

.toolbar .ant-btn {
  height: 40px;
}

/* 统计卡片样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #f0f5ff;
  font-weight: 600;
  color: #262626;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #e6f7ff;
}

.ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background-color: #bae7ff;
}

/* 状态标签样式 */
.statusTag {
  font-weight: 500;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-number,
.ant-select-selector,
.ant-picker {
  border-radius: 4px;
}

.ant-input:focus,
.ant-input-focused,
.ant-input-number:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector,
.ant-picker:focus,
.ant-picker-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.ant-btn-danger {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.ant-btn-danger:hover,
.ant-btn-danger:focus {
  color: #ff7875;
  border-color: #ff7875;
}

/* 标签样式 */
.ant-tag {
  font-weight: 500;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }

  .statsSection {
    padding: 16px;
  }

  .searchContent {
    padding: 16px;
  }

  .toolbar .ant-row {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar .ant-input-search {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .statsSection {
    padding: 12px;
  }

  .searchContent {
    padding: 12px;
  }

  .searchHeader {
    padding: 12px 16px;
  }

  .searchActions {
    flex-direction: column;
    gap: 8px;
  }

  .ant-statistic-content {
    font-size: 20px;
  }

  .ant-table-scroll {
    overflow-x: auto;
  }
}
