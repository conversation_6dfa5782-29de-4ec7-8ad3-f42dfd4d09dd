import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Button,
  Space,
  message,
  Row,
  Col,
  Select,
  InputNumber,
  Input,
  Table,
  Popconfirm,
  Image,
  Tag,
  Tooltip,
  Divider,
} from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  PictureOutlined,
  InfoCircleOutlined,
  LeftOutlined,
  RightOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { createInventory, batchCreateInventory } from '@/api/InventoryApi';
import { getProductList } from '@/api/ProductApi';
import { getColorList } from '@/api/ColorApi';
import type { CreateInventoryParams, BatchCreateInventoryParams } from '@/types/inventory';
import type { ProductListItem } from '@/types/product';
import type { ColorListProps } from '@/types/color';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './InventoryCreate.module.css';

const { Option } = Select;

interface InventoryFormItem extends CreateInventoryParams {
  key: string;
  productName?: string;
  colorName?: string;
}

const InventoryCreate: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<ProductListItem[]>([]);
  const [colors, setColors] = useState<ColorListProps[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryFormItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<ProductListItem | null>(null);
  const [selectedColorCode, setSelectedColorCode] = useState<string>('');
  const [selectedColorImages, setSelectedColorImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // 获取商品列表
  const fetchProducts = async () => {
    try {
      const response = await getProductList({ page: 1, pageSize: 10 });
      if (response.code === 200 && response.data) {
        setProducts(response.data.products || []);
      }
    } catch (error) {
      logError('获取商品列表', error);
      message.error(getErrorMessage(error));
    }
  };

  // 获取颜色列表
  const fetchColors = async () => {
    try {
      const response = await getColorList({ page: 1, pageSize: 10 });
      if (response.code === 200 && response.data) {
        setColors(response.data.colors || []);
      }
    } catch (error) {
      logError('获取颜色列表', error);
      message.error(getErrorMessage(error));
    }
  };

  useEffect(() => {
    fetchProducts();
    fetchColors();
  }, []);

  // 商品选择变化
  const handleProductChange = (productCode: string) => {
    const product = products.find((p) => p.code === productCode);
    setSelectedProduct(product || null);
    setSelectedColorCode('');
    setSelectedColorImages([]);
    setCurrentImageIndex(0);
    form.setFieldsValue({ colorCode: undefined, size: undefined });
  };

  // 颜色选择变化
  const handleColorChange = (colorCode: string) => {
    setSelectedColorCode(colorCode);
    const colorCombo = selectedProduct?.colorSizeCombinations?.find(
      (combo) => combo.colorCode === colorCode,
    );
    setSelectedColorImages(colorCombo?.images || []);
    setCurrentImageIndex(0);
    form.setFieldsValue({ size: undefined });
  };

  // 获取可用尺寸
  const getAvailableSizes = (colorCode?: string): string[] => {
    if (!selectedProduct || !colorCode) return [];

    const colorCombo = selectedProduct.colorSizeCombinations?.find(
      (combo) => combo.colorCode === colorCode,
    );
    return colorCombo?.sizes || [];
  };

  // 添加库存项
  const handleAddItem = async () => {
    try {
      const values = await form.validateFields();
      const product = products.find((p) => p.code === values.productCode);
      const color = colors.find((c) => c.code === values.colorCode);

      // 检查是否已存在相同的商品-颜色-尺寸组合
      const exists = inventoryItems.some(
        (item) =>
          item.productCode === values.productCode &&
          item.colorCode === values.colorCode &&
          item.size === values.size,
      );

      if (exists) {
        message.error('该商品-颜色-尺寸组合已存在');
        return;
      }

      const newItem: InventoryFormItem = {
        key: `${values.productCode}-${values.colorCode}-${values.size}`,
        productCode: values.productCode,
        colorCode: values.colorCode,
        size: values.size,
        quantity: values.quantity || 0,
        reservedQuantity: values.reservedQuantity || 0,
        safetyStock: values.safetyStock || 0,
        latestCost: values.latestCost,
        warehouseLocation: values.warehouseLocation,
        remark: values.remark,
        productName: product?.name,
        colorName: color?.name,
      };

      setInventoryItems((prev) => [...prev, newItem]);

      // 清空表单
      form.resetFields([
        'colorCode',
        'size',
        'quantity',
        'reservedQuantity',
        'safetyStock',
        'latestCost',
        'warehouseLocation',
        'remark',
      ]);

      message.success('添加成功');
    } catch (error) {
      // 表单验证失败
    }
  };

  // 删除库存项
  const handleDeleteItem = (key: string) => {
    setInventoryItems((prev) => prev.filter((item) => item.key !== key));
    message.success('删除成功');
  };

  // 批量保存
  const handleSave = useCallback(async () => {
    if (inventoryItems.length === 0) {
      message.error('请至少添加一个库存项');
      return;
    }

    setLoading(true);
    try {
      const params: BatchCreateInventoryParams = {
        inventoryDetails: inventoryItems.map((item) => ({
          productCode: item.productCode,
          colorCode: item.colorCode,
          size: item.size,
          quantity: item.quantity,
          reservedQuantity: item.reservedQuantity || 0,
          safetyStock: item.safetyStock || 0,
          latestCost: item.latestCost,
          warehouseLocation: item.warehouseLocation,
          remark: item.remark,
        })),
      };

      const response = await batchCreateInventory(params);
      const result = handleApiResponse(response, '库存创建成功', '库存创建失败');

      if (result.success) {
        message.success(result.message);
        navigate('/product/inventory');
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('批量创建库存', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  }, [inventoryItems, navigate]);

  // F8快捷键保存
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F8') {
        event.preventDefault();
        handleSave();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleSave]);

  // 返回列表
  const handleBack = () => {
    navigate('/product/inventory');
  };

  // 图片切换
  const handleImageChange = (direction: 'prev' | 'next') => {
    if (selectedColorImages.length === 0) return;

    if (direction === 'prev') {
      setCurrentImageIndex((prev) => (prev === 0 ? selectedColorImages.length - 1 : prev - 1));
    } else {
      setCurrentImageIndex((prev) => (prev === selectedColorImages.length - 1 ? 0 : prev + 1));
    }
  };

  // 表格列定义
  const columns: ColumnsType<InventoryFormItem> = [
    {
      title: '商品信息',
      key: 'product',
      width: 180,
      render: (_, record) => (
        <div className={styles.productInfo}>
          <div className={styles.productName}>{record.productName}</div>
          <div className={styles.productCode}>编码: {record.productCode}</div>
          <div className={styles.colorSize}>
            <Tag color="blue">{record.colorName}</Tag>
            <Tag>{record.size}</Tag>
          </div>
        </div>
      ),
    },
    {
      title: '库存数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'center',
      render: (quantity: number) => <span className={styles.quantityText}>{quantity}</span>,
    },
    {
      title: '预留数量',
      dataIndex: 'reservedQuantity',
      key: 'reservedQuantity',
      width: 80,
      align: 'center',
      render: (quantity?: number) => quantity || 0,
    },
    {
      title: '安全库存',
      dataIndex: 'safetyStock',
      key: 'safetyStock',
      width: 80,
      align: 'center',
      render: (quantity?: number) => quantity || 0,
    },
    {
      title: '最新成本',
      dataIndex: 'latestCost',
      key: 'latestCost',
      width: 90,
      align: 'right',
      render: (cost?: number) => (
        <span className={cost ? styles.costText : styles.noCost}>
          {cost ? `¥${cost.toFixed(2)}` : '-'}
        </span>
      ),
    },
    {
      title: '仓库位置',
      dataIndex: 'warehouseLocation',
      key: 'warehouseLocation',
      width: 100,
      render: (location?: string) => (
        <span className={location ? styles.locationText : styles.noLocation}>
          {location || '-'}
        </span>
      ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 120,
      render: (remark?: string) => (
        <Tooltip title={remark} placement="topLeft">
          <span className={styles.remarkText}>
            {remark ? (remark.length > 10 ? `${remark.slice(0, 10)}...` : remark) : '-'}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 60,
      fixed: 'right',
      align: 'center',
      render: (_, record) => (
        <Popconfirm
          title="确定要删除这个库存项吗？"
          onConfirm={() => handleDeleteItem(record.key)}
          okText="确定"
          cancelText="取消"
          placement="topRight"
        >
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            size="small"
            className={styles.deleteButton}
          />
        </Popconfirm>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.header}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            className={styles.backButton}
          >
            返回列表
          </Button>
          <h2>创建库存明细</h2>
        </div>

        {/* 添加库存项表单 */}
        <Card title="添加库存项" className={styles.formCard} size="small">
          <Form form={form} layout="vertical" size="small">
            <Row gutter={12}>
              <Col span={5}>
                <Form.Item
                  name="productCode"
                  label="商品"
                  rules={[{ required: true, message: '请选择商品' }]}
                >
                  <Select
                    placeholder="请选择商品"
                    showSearch
                    optionFilterProp="children"
                    onChange={handleProductChange}
                    size="small"
                  >
                    {products.map((product) => (
                      <Option key={product.code} value={product.code}>
                        {product.name} ({product.code})
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  name="colorCode"
                  label="颜色"
                  rules={[{ required: true, message: '请选择颜色' }]}
                >
                  <Select
                    placeholder="请选择颜色"
                    disabled={!selectedProduct}
                    onChange={handleColorChange}
                    size="small"
                  >
                    {selectedProduct?.colorSizeCombinations?.map((combo) => {
                      const color = colors.find((c) => c.code === combo.colorCode);
                      return (
                        <Option key={combo.colorCode} value={combo.colorCode}>
                          {color?.name || combo.colorCode}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={3}>
                <Form.Item
                  name="size"
                  label="尺寸"
                  rules={[{ required: true, message: '请选择尺寸' }]}
                >
                  <Select placeholder="请选择尺寸" disabled={!selectedColorCode} size="small">
                    {getAvailableSizes(selectedColorCode).map((size) => (
                      <Option key={size} value={size}>
                        {size}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={3}>
                <Form.Item
                  name="quantity"
                  label="库存数量"
                  rules={[{ required: true, message: '请输入库存数量' }]}
                >
                  <InputNumber min={0} style={{ width: '100%' }} placeholder="0" size="small" />
                </Form.Item>
              </Col>
              <Col span={3}>
                <Form.Item name="reservedQuantity" label="预留数量">
                  <InputNumber min={0} style={{ width: '100%' }} placeholder="0" size="small" />
                </Form.Item>
              </Col>
              <Col span={3}>
                <Form.Item name="safetyStock" label="安全库存">
                  <InputNumber min={0} style={{ width: '100%' }} placeholder="0" size="small" />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item name="latestCost" label="最新成本">
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    size="small"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={12}>
              <Col span={6}>
                <Form.Item name="warehouseLocation" label="仓库位置">
                  <Input placeholder="请输入仓库位置" size="small" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="remark" label="备注">
                  <Input placeholder="请输入备注" size="small" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label=" ">
                  <Space>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleAddItem}
                      size="small"
                    >
                      添加
                    </Button>
                    {selectedColorImages.length > 0 && (
                      <Tooltip title="查看颜色图片">
                        <Button
                          icon={<PictureOutlined />}
                          size="small"
                          onClick={() => setCurrentImageIndex(0)}
                        >
                          图片({selectedColorImages.length})
                        </Button>
                      </Tooltip>
                    )}
                  </Space>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>

        {/* 颜色图片预览 */}
        {selectedColorImages.length > 0 && (
          <Card title="颜色图片预览" className={styles.imageCard} size="small">
            <div className={styles.imagePreview}>
              <div className={styles.imageContainer}>
                <Image
                  src={selectedColorImages[currentImageIndex]}
                  alt="颜色图片"
                  width={200}
                  height={200}
                  style={{ objectFit: 'cover' }}
                  fallback="/placeholder-image.svg"
                />
                {selectedColorImages.length > 1 && (
                  <div className={styles.imageControls}>
                    <Button
                      size="small"
                      icon={<LeftOutlined />}
                      onClick={() => handleImageChange('prev')}
                    />
                    <span className={styles.imageIndex}>
                      {currentImageIndex + 1} / {selectedColorImages.length}
                    </span>
                    <Button
                      size="small"
                      icon={<RightOutlined />}
                      onClick={() => handleImageChange('next')}
                    />
                  </div>
                )}
              </div>
              <div className={styles.imageInfo}>
                <Tag color="blue">{selectedProduct?.name}</Tag>
                <Tag color="green">
                  {colors.find((c) => c.code === selectedColorCode)?.name || selectedColorCode}
                </Tag>
              </div>
            </div>
          </Card>
        )}

        {/* 库存项列表 */}
        <Card
          title={
            <div className={styles.tableHeader}>
              <span>库存项列表</span>
              <div className={styles.tableStats}>
                <Tag color="blue">总计: {inventoryItems.length} 项</Tag>
                <Tag color="green">
                  总库存: {inventoryItems.reduce((sum, item) => sum + item.quantity, 0)}
                </Tag>
                {inventoryItems.length > 0 && (
                  <Tooltip title="库存项统计信息">
                    <InfoCircleOutlined className={styles.infoIcon} />
                  </Tooltip>
                )}
              </div>
            </div>
          }
          className={styles.tableCard}
          size="small"
        >
          {inventoryItems.length === 0 ? (
            <div className={styles.emptyState}>
              <PictureOutlined className={styles.emptyIcon} />
              <p>暂无库存项，请先添加库存项</p>
            </div>
          ) : (
            <Table
              columns={columns}
              dataSource={inventoryItems}
              rowKey="key"
              pagination={false}
              scroll={{ x: 800 }}
              size="small"
              className={styles.inventoryTable}
            />
          )}
        </Card>

        {/* 操作按钮 */}
        <div className={styles.footer}>
          <Space>
            <Button onClick={handleBack} size="small">
              取消
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={loading}
              disabled={inventoryItems.length === 0}
              size="small"
              className={styles.saveButton}
            >
              保存 (F8) - {inventoryItems.length} 项
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default InventoryCreate;
