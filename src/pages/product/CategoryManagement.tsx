import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  Tag,
  Select,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import {
  getProductCategoryList,
  createProductCategory,
  updateProductCategory,
  deleteProductCategory,
  getProductCategoryDetail,
} from '@/api/ProductCategoryApi';
import type {
  ProductCategoryListProps,
  UpdateProductCategoryParams,
} from '@/types/productCategory';
import { sizeOptions } from '@/utils/sizeConfig';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './CategoryManagement.module.css';

const { Search } = Input;
const { Option } = Select;

const CategoryManagement: React.FC = () => {
  const [categories, setCategories] = useState<ProductCategoryListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ProductCategoryListProps | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取商品分类列表
  const fetchCategories = async (page = 1, pageSize = 10, search = '') => {
    setLoading(true);
    try {
      const response = await getProductCategoryList({
        page,
        pageSize,
        search,
      });

      const result = handleApiResponse(response, '', '获取商品分类列表失败');

      if (result.success && response.data) {
        setCategories(response.data.categories || []);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total || 0,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取商品分类列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchCategories();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    fetchCategories(1, pagination.pageSize, value);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchCategories(pagination.current, pagination.pageSize, searchText);
  };

  // 分页变化处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchCategories(current, pageSize, searchText);
  };

  // 新增商品分类
  const handleAdd = () => {
    setEditingCategory(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑商品分类
  const handleEdit = async (category: ProductCategoryListProps) => {
    try {
      const response = await getProductCategoryDetail(category.id);
      const result = handleApiResponse(response, '', '获取商品分类详情失败');

      if (result.success && response.data) {
        setEditingCategory(response.data);
        form.setFieldsValue({
          code: response.data.code,
          name: response.data.name,
          sizes: response.data.sizes || [],
        });
        setModalVisible(true);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取商品分类详情', error);
      message.error(getErrorMessage(error));
    }
  };

  // 删除商品分类
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteProductCategory(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('删除商品分类', error);
      message.error(getErrorMessage(error));
    }
  };

  // 保存商品分类
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      let response;

      if (editingCategory) {
        // 编辑模式
        const updateData: UpdateProductCategoryParams = {
          name: values.name,
          sizes: values.sizes || [],
        };
        response = await updateProductCategory(editingCategory.id, updateData);
      } else {
        // 新增模式
        response = await createProductCategory(values);
      }

      const result = handleApiResponse(
        response,
        editingCategory ? '更新成功' : '创建成功',
        editingCategory ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError(editingCategory ? '更新商品分类' : '创建商品分类', error);
      message.error(getErrorMessage(error));
    }
  };

  // 表格列定义
  const columns: ColumnsType<ProductCategoryListProps> = [
    {
      title: '分类编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      fixed: 'left',
      render: (text: string) => (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: '#147ffa',
            fontWeight: 'bold',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string) => (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: '#000',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: '尺码',
      dataIndex: 'sizes',
      key: 'sizes',
      width: 300,
      render: (sizes: string[]) => (
        <div className={styles.sizesContainer}>
          {sizes && sizes.length > 0 ? (
            sizes.map((size, index) => (
              <Tag key={index} className={styles.sizeTag} color="blue">
                {size}
              </Tag>
            ))
          ) : (
            <span className={styles.emptyText}>暂无尺码</span>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            className={styles.actionButton}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个商品分类吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />} className={styles.actionButton}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <Search
                placeholder="搜索分类编码或名称"
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                style={{ maxWidth: 400 }}
              />
            </Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增分类
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <div className={styles.tableContainer}>
          <Table
            columns={columns}
            dataSource={categories}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 1200 }}
          />
        </div>
      </Card>

      {/* 商品分类编辑模态框 */}
      <Modal
        title={editingCategory ? '编辑商品分类' : '新增商品分类'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="分类编码"
                rules={[
                  { required: true, message: '请输入分类编码' },
                  { min: 2, message: '分类编码至少2个字符' },
                  { max: 20, message: '分类编码至多20个字符' },
                ]}
              >
                <Input placeholder="请输入分类编码" disabled={!!editingCategory} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="分类名称"
                rules={[{ required: true, message: '请输入分类名称' }]}
              >
                <Input placeholder="请输入分类名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item name="sizes" label="尺码" help="可选择多个尺码，也可以留空">
                <Select
                  mode="multiple"
                  placeholder="请选择尺码"
                  allowClear
                  style={{ width: '100%' }}
                >
                  {sizeOptions.map((size) => (
                    <Option key={size} value={size}>
                      {size}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default CategoryManagement;
