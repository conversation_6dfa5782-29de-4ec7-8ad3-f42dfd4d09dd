import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Row, Col, Statistic, Table, Tag, Button, Image, Tooltip, message, Spin } from 'antd';
import {
  ArrowLeftOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  EditOutlined,
  LeftOutlined,
  RightOutlined,
  PictureOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getProductInventory } from '@/api/InventoryApi';
import type { ProductInventoryResponse, SizeInventory } from '@/types/inventory';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './ProductInventoryDetail.module.css';

const ProductInventoryDetail: React.FC = () => {
  const { productCode } = useParams<{ productCode: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [inventoryData, setInventoryData] = useState<ProductInventoryResponse | null>(null);
  const [currentImageIndexes, setCurrentImageIndexes] = useState<Record<string, number>>({});

  // 获取商品库存信息
  const fetchInventoryData = async () => {
    if (!productCode) return;

    setLoading(true);
    try {
      const response = await getProductInventory(productCode);
      const result = handleApiResponse(response, '', '获取库存信息失败');

      if (result.success && response.data) {
        setInventoryData(response.data);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取商品库存信息', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInventoryData();
  }, [productCode]);

  // 返回列表
  const handleBack = () => {
    navigate('/product/inventory');
  };

  // 编辑库存
  const handleEditInventory = (inventoryId: string) => {
    navigate(`/product/inventory/edit/${inventoryId}`);
  };

  // 图片切换
  const handleImageChange = (colorCode: string, direction: 'prev' | 'next') => {
    const currentIndex = currentImageIndexes[colorCode] || 0;
    const colorInventory = inventoryData?.colorInventories.find((c) => c.colorCode === colorCode);
    if (!colorInventory || colorInventory.images.length === 0) return;

    let newIndex;
    if (direction === 'prev') {
      newIndex = currentIndex === 0 ? colorInventory.images.length - 1 : currentIndex - 1;
    } else {
      newIndex = currentIndex === colorInventory.images.length - 1 ? 0 : currentIndex + 1;
    }

    setCurrentImageIndexes((prev) => ({
      ...prev,
      [colorCode]: newIndex,
    }));
  };

  // 尺寸库存表格列定义
  const sizeColumns: ColumnsType<SizeInventory> = [
    {
      title: '尺寸',
      dataIndex: 'size',
      key: 'size',
      width: 80,
      render: (size: string) => (
        <Tag color="blue" style={{ fontWeight: 'bold' }}>
          {size}
        </Tag>
      ),
    },
    {
      title: '库存数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (quantity: number, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ fontWeight: 'bold', color: quantity > 0 ? '#52c41a' : '#ff4d4f' }}>
            {quantity}
          </span>
          {record.isLowStock && (
            <Tooltip title="低库存警告">
              <WarningOutlined style={{ color: '#ff4d4f', marginLeft: 4 }} />
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: '可用数量',
      dataIndex: 'availableQuantity',
      key: 'availableQuantity',
      width: 100,
      render: (quantity: number) => (
        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{quantity}</span>
      ),
    },
    {
      title: '预留数量',
      dataIndex: 'reservedQuantity',
      key: 'reservedQuantity',
      width: 100,
      render: (quantity: number) => <span style={{ color: '#fa8c16' }}>{quantity}</span>,
    },
    {
      title: '安全库存',
      dataIndex: 'safetyStock',
      key: 'safetyStock',
      width: 100,
      render: (stock: number) => <span style={{ color: '#722ed1' }}>{stock}</span>,
    },
    {
      title: '最新成本',
      dataIndex: 'latestCost',
      key: 'latestCost',
      width: 100,
      render: (cost?: number) => (cost ? `¥${cost.toFixed(2)}` : '-'),
    },
    {
      title: '仓库位置',
      dataIndex: 'warehouseLocation',
      key: 'warehouseLocation',
      width: 120,
      render: (location?: string) => location || '-',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      render: (remark?: string) => remark || '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Button
          type="text"
          icon={<EditOutlined />}
          size="small"
          onClick={() => handleEditInventory(record.inventoryId)}
          title="编辑库存"
        />
      ),
    },
  ];

  if (loading) {
    return (
      <div className={styles.container}>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </div>
    );
  }

  if (!inventoryData) {
    return (
      <div className={styles.container}>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <InfoCircleOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
            <p style={{ marginTop: '16px', color: '#999' }}>暂无库存数据</p>
            <Button type="primary" onClick={handleBack}>
              返回列表
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  const { productInfo, colorInventories, totalQuantity, totalAvailableQuantity } = inventoryData;

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <Card className={styles.headerCard}>
        <div className={styles.pageHeader}>
          <div className={styles.headerLeft}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              className={styles.backButton}
            >
              返回列表
            </Button>
            <div className={styles.productInfo}>
              <h2 className={styles.productName}>{productInfo.name}</h2>
              <div className={styles.productMeta}>
                <span className={styles.productCode}>商品编码: {productInfo.code}</span>
                <span className={styles.brandName}>品牌: {productInfo.brandName}</span>
                <span className={styles.supplierName}>供应商: {productInfo.supplierName}</span>
              </div>
            </div>
          </div>
          <div className={styles.headerRight}>{/* 主图片已移除，现在使用颜色专属图片 */}</div>
        </div>
      </Card>

      {/* 库存统计 */}
      <Card className={styles.statsCard}>
        <Row gutter={24}>
          <Col span={6}>
            <Statistic
              title="总库存数量"
              value={totalQuantity}
              valueStyle={{ color: '#52c41a' }}
              suffix="件"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="可用数量"
              value={totalAvailableQuantity}
              valueStyle={{ color: '#1890ff' }}
              suffix="件"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="预留数量"
              value={totalQuantity - totalAvailableQuantity}
              valueStyle={{ color: '#fa8c16' }}
              suffix="件"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="颜色数量"
              value={colorInventories.length}
              valueStyle={{ color: '#722ed1' }}
              suffix="种"
            />
          </Col>
        </Row>
      </Card>

      {/* 按颜色分组的库存详情 */}
      {colorInventories.map((colorInventory) => (
        <Card
          key={colorInventory.colorCode}
          className={styles.colorCard}
          title={
            <div className={styles.colorHeader}>
              <div className={styles.colorInfo}>
                {colorInventory.colorHex && (
                  <div
                    className={styles.colorSwatch}
                    style={{ backgroundColor: colorInventory.colorHex }}
                  />
                )}
                <span className={styles.colorName}>{colorInventory.colorName}</span>
                <span className={styles.colorCode}>({colorInventory.colorCode})</span>
              </div>
              <div className={styles.colorStats}>
                <Tag color="green">总计: {colorInventory.totalQuantity}件</Tag>
                <Tag color="blue">可用: {colorInventory.totalAvailableQuantity}件</Tag>
              </div>
            </div>
          }
          extra={
            <div className={styles.imageSection}>
              {colorInventory.images && colorInventory.images.length > 0 ? (
                <div className={styles.imageContainer}>
                  <Image
                    src={colorInventory.images[currentImageIndexes[colorInventory.colorCode] || 0]}
                    alt={`${colorInventory.colorName}`}
                    width={120}
                    height={120}
                    style={{ objectFit: 'cover', borderRadius: '6px' }}
                    fallback="/placeholder-image.svg"
                    preview={{
                      src: colorInventory.images[
                        currentImageIndexes[colorInventory.colorCode] || 0
                      ],
                    }}
                  />
                  {colorInventory.images.length > 1 && (
                    <div className={styles.imageControls}>
                      <Button
                        size="small"
                        icon={<LeftOutlined />}
                        onClick={() => handleImageChange(colorInventory.colorCode, 'prev')}
                        className={styles.imageButton}
                      />
                      <span className={styles.imageIndex}>
                        {(currentImageIndexes[colorInventory.colorCode] || 0) + 1} /{' '}
                        {colorInventory.images.length}
                      </span>
                      <Button
                        size="small"
                        icon={<RightOutlined />}
                        onClick={() => handleImageChange(colorInventory.colorCode, 'next')}
                        className={styles.imageButton}
                      />
                    </div>
                  )}
                </div>
              ) : (
                <div className={styles.noImage}>
                  <PictureOutlined />
                  <span>暂无图片</span>
                </div>
              )}
            </div>
          }
        >
          {/* 价格信息 */}
          <div className={styles.priceSection}>
            <h4>价格信息</h4>
            <Row gutter={16}>
              <Col span={4}>
                <div className={styles.priceItem}>
                  <span className={styles.priceLabel}>服装成本:</span>
                  <span className={styles.priceValue}>
                    ¥{colorInventory.priceInfo.clothingCost.toFixed(2)}
                  </span>
                </div>
              </Col>
              <Col span={4}>
                <div className={styles.priceItem}>
                  <span className={styles.priceLabel}>辅料成本:</span>
                  <span className={styles.priceValue}>
                    ¥{colorInventory.priceInfo.accessoryCost.toFixed(2)}
                  </span>
                </div>
              </Col>
              <Col span={4}>
                <div className={styles.priceItem}>
                  <span className={styles.priceLabel}>零售价:</span>
                  <span className={styles.priceValue}>
                    ¥{colorInventory.priceInfo.retailPrice.toFixed(2)}
                  </span>
                </div>
              </Col>
              <Col span={4}>
                <div className={styles.priceItem}>
                  <span className={styles.priceLabel}>预订价:</span>
                  <span className={styles.priceValue}>
                    ¥{colorInventory.priceInfo.preOrderPrice.toFixed(2)}
                  </span>
                </div>
              </Col>
              <Col span={4}>
                <div className={styles.priceItem}>
                  <span className={styles.priceLabel}>补货价:</span>
                  <span className={styles.priceValue}>
                    ¥{colorInventory.priceInfo.restockPrice.toFixed(2)}
                  </span>
                </div>
              </Col>
              <Col span={4}>
                <div className={styles.priceItem}>
                  <span className={styles.priceLabel}>现货价:</span>
                  <span className={styles.priceValue}>
                    ¥{colorInventory.priceInfo.spotPrice.toFixed(2)}
                  </span>
                </div>
              </Col>
            </Row>
          </div>

          {/* 辅料信息 */}
          {colorInventory.accessories && colorInventory.accessories.length > 0 && (
            <div className={styles.accessorySection}>
              <h4>辅料信息</h4>
              <Row gutter={12}>
                {colorInventory.accessories.map((accessory, index) => (
                  <Col span={6} key={index}>
                    <div className={styles.accessoryItem}>
                      <div className={styles.accessoryName}>
                        {accessory.accessoryName ||
                          accessory.accessoryCode ||
                          accessory.accessoryId}
                      </div>
                      <div className={styles.accessoryQuantity}>数量: {accessory.quantity}</div>
                      {accessory.accessoryCode && (
                        <div className={styles.accessoryCode}>编码: {accessory.accessoryCode}</div>
                      )}
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          )}

          {/* 尺寸库存表格 */}
          <div className={styles.sizeSection}>
            <h4>尺寸库存明细</h4>
            <Table
              columns={sizeColumns}
              dataSource={colorInventory.sizeDetails}
              rowKey="size"
              pagination={false}
              size="small"
              className={styles.sizeTable}
            />
          </div>

          {/* 颜色备注 */}
          {colorInventory.remark && (
            <div className={styles.remarkSection}>
              <h4>备注</h4>
              <p>{colorInventory.remark}</p>
            </div>
          )}
        </Card>
      ))}
    </div>
  );
};

export default ProductInventoryDetail;
