import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  InputNumber,
  Image,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import {
  getAccessoryList,
  createAccessory,
  updateAccessory,
  deleteAccessory,
  getAccessoryDetail,
} from '@/api/AccessoryApi';
import type {
  AccessoryListProps,
  AddAccessorParams,
  UpdateAccessoryParams,
} from '@/types/accessories';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { SupplierSearchSelector } from '@/components';
import { SimpleImageUpload } from '@/components/ImageUpload';
import styles from './AccessoryManagement.module.css';

const { Search } = Input;

const AccessoryManagement: React.FC = () => {
  const [accessories, setAccessories] = useState<AccessoryListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAccessory, setEditingAccessory] = useState<AccessoryListProps | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [articleNumberSearch, setArticleNumberSearch] = useState('');
  const [supplierCodeSearch, setSupplierCodeSearch] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取辅料列表
  const fetchAccessories = async (
    page = 1,
    pageSize = 10,
    nameSearch = '',
    articleNumberSearch = '',
    supplierCodeSearch = '',
  ) => {
    setLoading(true);
    try {
      const response = await getAccessoryList({
        page,
        pageSize,
        nameSearch: nameSearch || undefined,
        articleNumberSearch: articleNumberSearch || undefined,
        supplierCodeSearch: supplierCodeSearch || undefined,
      });

      const result = handleApiResponse(response, '', '获取辅料列表失败');

      if (result.success && response.data) {
        setAccessories(response.data.accessories || []);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total || 0,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取辅料列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchAccessories();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    fetchAccessories(1, pagination.pageSize, searchText, articleNumberSearch, supplierCodeSearch);
  };

  // 重置搜索
  const handleResetSearch = () => {
    setSearchText('');
    setArticleNumberSearch('');
    setSupplierCodeSearch('');
    fetchAccessories(1, pagination.pageSize, '', '', '');
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchAccessories(
      pagination.current,
      pagination.pageSize,
      searchText,
      articleNumberSearch,
      supplierCodeSearch,
    );
  };

  // 分页变化处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchAccessories(current, pageSize, searchText, articleNumberSearch, supplierCodeSearch);
  };

  // 新增辅料
  const handleAdd = () => {
    setEditingAccessory(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑辅料
  const handleEdit = async (accessory: AccessoryListProps) => {
    try {
      const response = await getAccessoryDetail(accessory.id);
      const result = handleApiResponse(response, '', '获取辅料详情失败');

      if (result.success && response.data) {
        setEditingAccessory(response.data);
        form.setFieldsValue({
          articleNumber: response.data.articleNumber,
          name: response.data.name,
          brandName: response.data.brandName,
          supplierStyleNumber: response.data.supplierStyleNumber,
          color: response.data.color,
          size: response.data.size,
          costPrice: response.data.costPrice,
          supplierCode: response.data.supplierCode,
          imageUrl: response.data.imageUrl,
        });
        setModalVisible(true);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取辅料详情', error);
      message.error(getErrorMessage(error));
    }
  };

  // 删除辅料
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteAccessory(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('删除辅料', error);
      message.error(getErrorMessage(error));
    }
  };

  // 保存辅料
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      let response;

      if (editingAccessory) {
        // 编辑模式
        const updateData: UpdateAccessoryParams = {
          articleNumber: values.articleNumber,
          name: values.name,
          brandName: values.brandName,
          supplierStyleNumber: values.supplierStyleNumber,
          color: values.color,
          size: values.size,
          costPrice: values.costPrice,
          supplierCode: values.supplierCode,
          imageUrl: values.imageUrl,
        };
        response = await updateAccessory(editingAccessory.id, updateData);
      } else {
        // 新增模式
        const createData: AddAccessorParams = {
          articleNumber: values.articleNumber,
          name: values.name,
          brandName: values.brandName,
          supplierStyleNumber: values.supplierStyleNumber,
          color: values.color,
          size: values.size,
          costPrice: values.costPrice,
          supplierCode: values.supplierCode,
          imageUrl: values.imageUrl,
        };
        response = await createAccessory(createData);
      }

      const result = handleApiResponse(
        response,
        editingAccessory ? '更新成功' : '创建成功',
        editingAccessory ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError(editingAccessory ? '更新辅料' : '创建辅料', error);
      message.error(getErrorMessage(error));
    }
  };

  // 表格列定义
  const columns: ColumnsType<AccessoryListProps> = [
    {
      title: '辅料信息',
      key: 'accessoryInfo',
      width: 300,
      fixed: 'left',
      render: (_, record) => (
        <div className={styles.accessoryInfo}>
          {record.imageUrl ? (
            <Image
              src={record.imageUrl}
              alt={record.name}
              className={styles.accessoryImage}
              preview={false}
            />
          ) : (
            <div
              className={styles.accessoryImage}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                color: '#bfbfbf',
              }}
            >
              <AppstoreOutlined />
            </div>
          )}
          <div className={styles.accessoryDetails}>
            <div className={styles.accessoryName}>{record.name}</div>
            <div className={styles.accessoryMeta}>
              <span className={styles.articleNumber}>{record.articleNumber}</span>
              <span className={styles.priceTag}>¥{record.costPrice.toFixed(2)}</span>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 150,
      render: (text: string, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>{record.supplierCode}</div>
        </div>
      ),
    },
    {
      title: '品牌名称',
      dataIndex: 'brandName',
      key: 'brandName',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '供应商款号',
      dataIndex: 'supplierStyleNumber',
      key: 'supplierStyleNumber',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '颜色',
      dataIndex: 'color',
      key: 'color',
      width: 100,
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <span className={styles.colorSwatch} style={{ backgroundColor: text }} />
          {text}
        </div>
      ),
    },
    {
      title: '尺码',
      dataIndex: 'size',
      key: 'size',
      width: 80,
      render: (text: string) => <span className={styles.sizeTag}>{text}</span>,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            className={styles.actionButton}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个辅料吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />} className={styles.actionButton}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.toolbar}>
          <div className={styles.searchFilters}>
            <Search
              placeholder="搜索辅料名称"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={handleSearch}
              style={{ width: 200 }}
            />
            <Search
              placeholder="搜索货号"
              allowClear
              value={articleNumberSearch}
              onChange={(e) => setArticleNumberSearch(e.target.value)}
              onSearch={handleSearch}
              style={{ width: 200 }}
            />
            <Search
              placeholder="搜索供应商编码"
              allowClear
              value={supplierCodeSearch}
              onChange={(e) => setSupplierCodeSearch(e.target.value)}
              onSearch={handleSearch}
              style={{ width: 200 }}
            />
            <Button onClick={handleResetSearch}>重置</Button>
          </div>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto"></Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增辅料
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <div className={styles.tableContainer}>
          <Table
            columns={columns}
            dataSource={accessories}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 1400 }}
          />
        </div>
      </Card>

      {/* 辅料编辑模态框 */}
      <Modal
        title={editingAccessory ? '编辑辅料' : '新增辅料'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="articleNumber"
                label="货号"
                rules={[{ required: true, message: '请输入货号' }]}
              >
                <Input placeholder="请输入货号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="辅料名称"
                rules={[{ required: true, message: '请输入辅料名称' }]}
              >
                <Input placeholder="请输入辅料名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="brandName" label="品牌名称">
                <Input placeholder="请输入品牌名称（可选）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="supplierStyleNumber" label="供应商款号">
                <Input placeholder="请输入供应商款号（可选）" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="color"
                label="颜色"
                rules={[{ required: true, message: '请输入颜色' }]}
              >
                <Input placeholder="请输入颜色" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="size"
                label="尺码"
                rules={[{ required: true, message: '请输入尺码' }]}
              >
                <Input placeholder="请输入尺码" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="costPrice"
                label="成本价"
                rules={[
                  { required: true, message: '请输入成本价' },
                  { type: 'number', min: 0, message: '成本价不能小于0' },
                ]}
              >
                <InputNumber
                  placeholder="请输入成本价"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="supplierCode"
                label="供应商"
                rules={[{ required: true, message: '请选择供应商' }]}
              >
                <SupplierSearchSelector placeholder="请选择供应商" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="imageUrl" label="辅料图片">
                <SimpleImageUpload folder="accessories" enablePaste={true} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default AccessoryManagement;
