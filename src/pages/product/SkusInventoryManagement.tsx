import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Popconfirm,
  Image,
  Tooltip,
  Row,
  Col,
  Upload,
  Dropdown,
  Tag,
  Select,
  Modal,
  Form,
  InputNumber,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CopyOutlined,
  ExportOutlined,
  ImportOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  DownOutlined,
  AuditOutlined,
  SearchOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import { useNavigate } from 'react-router-dom';
import {
  getSkusInventoryList,
  deleteSkusInventory,
  exportSkusInventoryExcel,
  downloadSkusInventoryImportTemplate,
  importSkusInventoryExcel,
  adjustSkusInventoryStock,
  countSkusInventory,
} from '@/api/SkusInventoryApi';
import type {
  SkusInventoryData,
  GetSkusInventoryListParams,
  AdjustSkusInventoryStock,
  CountSkusInventory,
} from '@/types/skusInventory';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './SkusInventoryManagement.module.css';

const { Search } = Input;
const { Option } = Select;

const SkusInventoryManagement: React.FC = () => {
  const navigate = useNavigate();
  const [inventoryList, setInventoryList] = useState<SkusInventoryData[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [adjustModalVisible, setAdjustModalVisible] = useState(false);
  const [countModalVisible, setCountModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<SkusInventoryData | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 搜索条件
  const [searchFilters, setSearchFilters] = useState({
    search: '',
    skuCode: '',
    brandCode: '',
    colorCode: '',
    size: '',
    stockStatus: '',
  });

  const [adjustForm] = Form.useForm();
  const [countForm] = Form.useForm();

  // 获取库存明细列表
  const fetchInventoryList = async (page = 1, pageSize = 10, filters = searchFilters) => {
    setLoading(true);
    try {
      const params: GetSkusInventoryListParams = {
        page,
        pageSize,
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value)),
      };

      const response = await getSkusInventoryList(params);
      const result = handleApiResponse(response, '', '获取库存明细列表失败');

      if (result.success && response.data) {
        setInventoryList(response.data.data || []);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total || 0,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取库存明细列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchInventoryList();
  }, []);

  // 处理表格变化
  const handleTableChange = (newPagination: TablePaginationConfig) => {
    fetchInventoryList(newPagination.current, newPagination.pageSize);
  };

  // 处理搜索
  const handleSearch = () => {
    fetchInventoryList(1, pagination.pageSize, searchFilters);
  };

  // 重置搜索
  const handleReset = () => {
    const resetFilters = {
      search: '',
      skuCode: '',
      brandCode: '',
      colorCode: '',
      size: '',
      stockStatus: '',
    };
    setSearchFilters(resetFilters);
    fetchInventoryList(1, pagination.pageSize, resetFilters);
  };

  // 刷新
  const handleRefresh = () => {
    fetchInventoryList(pagination.current, pagination.pageSize);
  };

  // 删除库存明细
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteSkusInventory(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('删除库存明细', error);
      message.error(getErrorMessage(error));
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    });
  };

  // 导出选中记录
  const handleExportSelected = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要导出的记录');
      return;
    }

    if (selectedRowKeys.length > 50) {
      message.warning('最多只能选择50条记录进行导出');
      return;
    }

    setExportLoading(true);
    try {
      const blob = await exportSkusInventoryExcel({
        skusInventoryIds: selectedRowKeys as string[],
      });

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `库存明细_选中记录_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success('导出成功');
    } catch (error) {
      logError('导出库存明细', error);
      message.error(getErrorMessage(error));
    } finally {
      setExportLoading(false);
    }
  };

  // 导出全部记录
  const handleExportAll = async () => {
    setExportLoading(true);
    try {
      const exportParams = {
        ...searchFilters,
        stockStatus: searchFilters.stockStatus as 'normal' | 'out_of_stock' | undefined,
      };
      const blob = await exportSkusInventoryExcel(exportParams);

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `库存明细_全部记录_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success('导出成功');
    } catch (error) {
      logError('导出库存明细', error);
      message.error(getErrorMessage(error));
    } finally {
      setExportLoading(false);
    }
  };

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      const blob = await downloadSkusInventoryImportTemplate();

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = '库存明细导入模板.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success('模板下载成功');
    } catch (error) {
      logError('下载模板', error);
      message.error(getErrorMessage(error));
    }
  };

  // 导入Excel
  const handleImport = async (file: File) => {
    setImportLoading(true);
    try {
      const response = await importSkusInventoryExcel(file);
      const result = handleApiResponse(response, '导入成功', '导入失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('导入库存明细', error);
      message.error(getErrorMessage(error));
    } finally {
      setImportLoading(false);
    }
    return false; // 阻止默认上传行为
  };

  // 打开库存调整弹窗
  const handleOpenAdjustModal = (record: SkusInventoryData) => {
    setCurrentRecord(record);
    setAdjustModalVisible(true);
    adjustForm.resetFields();
  };

  // 库存调整
  const handleAdjustStock = async () => {
    if (!currentRecord) return;

    try {
      const values = await adjustForm.validateFields();
      const adjustData: AdjustSkusInventoryStock = {
        adjustmentType: values.adjustmentType,
        quantity: values.quantity,
        reason: values.reason,
      };

      const response = await adjustSkusInventoryStock(currentRecord.id, adjustData);
      const result = handleApiResponse(response, '库存调整成功', '库存调整失败');

      if (result.success) {
        message.success(result.message);
        setAdjustModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('库存调整', error);
      message.error(getErrorMessage(error));
    }
  };

  // 打开库存盘点弹窗
  const handleOpenCountModal = (record: SkusInventoryData) => {
    setCurrentRecord(record);
    setCountModalVisible(true);
    countForm.setFieldsValue({
      actualStock: record.currentStock,
    });
  };

  // 库存盘点
  const handleCountStock = async () => {
    if (!currentRecord) return;

    try {
      const values = await countForm.validateFields();
      const countData: CountSkusInventory = {
        actualStock: values.actualStock,
        remarks: values.remarks,
      };

      const response = await countSkusInventory(currentRecord.id, countData);
      const result = handleApiResponse(response, '库存盘点成功', '库存盘点失败');

      if (result.success) {
        message.success(result.message);
        setCountModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('库存盘点', error);
      message.error(getErrorMessage(error));
    }
  };

  // 获取库存状态样式
  const getStockStatusClass = (record: SkusInventoryData) => {
    if (record.isOutOfStock) return styles.stockOut;
    if (record.currentStock <= record.needRestockStock) return styles.stockLow;
    return styles.stockNormal;
  };

  // 表格列定义
  const columns: ColumnsType<SkusInventoryData> = [
    {
      title: '图片',
      key: 'skuImages',
      width: 120,
      fixed: 'left',
      render: (_, record) => (
        <div className={styles.imageCell}>
          {record.skuImages && record.skuImages.length > 0 ? (
            <Image
              src={record.skuImages[0]}
              alt={record.skuName}
              width={100}
              height={100}
              style={{ objectFit: 'cover', borderRadius: '8px' }}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
          ) : (
            <div className={styles.noImage}>无图</div>
          )}
        </div>
      ),
    },
    {
      title: 'SKU编码',
      dataIndex: 'skuCode',
      key: 'skuCode',
      width: 150,
      fixed: 'left',
      render: (text) => (
        <Space>
          <span className={styles.code}>{text}</span>
          <Tooltip title="复制编码">
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => copyToClipboard(text)}
            />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: 'SKU名称',
      dataIndex: 'skuName',
      key: 'skuName',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      key: 'brandName',
      width: 120,
    },
    {
      title: '颜色',
      dataIndex: 'colorName',
      key: 'colorName',
      width: 100,
    },
    {
      title: '尺码',
      dataIndex: 'size',
      key: 'size',
      width: 80,
      align: 'center',
    },
    {
      title: '当前库存',
      dataIndex: 'currentStock',
      key: 'currentStock',
      width: 100,
      align: 'center',
      render: (value, record) => (
        <div className={styles.stockCell}>
          <span className={`${styles.stockValue} ${getStockStatusClass(record)}`}>{value}</span>
        </div>
      ),
    },
    {
      title: '采购中',
      dataIndex: 'purchasingStock',
      key: 'purchasingStock',
      width: 100,
      align: 'center',
      render: (value) => (
        <div className={styles.stockCell}>
          <span className={styles.stockValue}>{value}</span>
        </div>
      ),
    },
    {
      title: '需补货',
      dataIndex: 'needRestockStock',
      key: 'needRestockStock',
      width: 100,
      align: 'center',
      render: (value) => (
        <div className={styles.stockCell}>
          <span className={styles.stockValue}>{value}</span>
        </div>
      ),
    },
    {
      title: '总库存',
      dataIndex: 'totalStock',
      key: 'totalStock',
      width: 100,
      align: 'center',
      render: (value) => (
        <div className={styles.stockCell}>
          <span className={styles.stockValue}>{value}</span>
        </div>
      ),
    },
    {
      title: '可用库存',
      dataIndex: 'availableStock',
      key: 'availableStock',
      width: 100,
      align: 'center',
      render: (value) => (
        <div className={styles.stockCell}>
          <span className={styles.stockValue}>{value}</span>
        </div>
      ),
    },
    {
      title: '库存状态',
      dataIndex: 'stockStatus',
      key: 'stockStatus',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Tag className={styles.statusTag} color={record.isOutOfStock ? 'red' : 'green'}>
          {record.isOutOfStock ? '缺货' : '正常'}
        </Tag>
      ),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          <div className={styles.remarkCell}>{text}</div>
        </Tooltip>
      ),
    },
    {
      title: '最后盘点',
      dataIndex: 'lastInventoryDate',
      key: 'lastInventoryDate',
      width: 120,
      render: (text) => (
        <div className={styles.dateCell}>{text ? new Date(text).toLocaleDateString() : '-'}</div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/product/skus-inventory/view/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => navigate(`/product/skus-inventory/edit/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="库存调整">
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => handleOpenAdjustModal(record)}
            />
          </Tooltip>
          <Tooltip title="库存盘点">
            <Button
              type="text"
              size="small"
              icon={<AuditOutlined />}
              onClick={() => handleOpenCountModal(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这条库存明细吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="text" size="small" icon={<DeleteOutlined />} danger />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        {/* 搜索区域 */}
        <div className={styles.searchSection}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="搜索SKU名称或编码"
                value={searchFilters.search}
                onChange={(e) => setSearchFilters((prev) => ({ ...prev, search: e.target.value }))}
                onSearch={handleSearch}
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="搜索SKU编码"
                value={searchFilters.skuCode}
                onChange={(e) => setSearchFilters((prev) => ({ ...prev, skuCode: e.target.value }))}
                onSearch={handleSearch}
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="搜索品牌编码"
                value={searchFilters.brandCode}
                onChange={(e) =>
                  setSearchFilters((prev) => ({ ...prev, brandCode: e.target.value }))
                }
                onSearch={handleSearch}
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="搜索颜色编码"
                value={searchFilters.colorCode}
                onChange={(e) =>
                  setSearchFilters((prev) => ({ ...prev, colorCode: e.target.value }))
                }
                onSearch={handleSearch}
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Input
                placeholder="搜索尺码"
                value={searchFilters.size}
                onChange={(e) => setSearchFilters((prev) => ({ ...prev, size: e.target.value }))}
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Select
                placeholder="库存状态"
                value={searchFilters.stockStatus || undefined}
                onChange={(value) =>
                  setSearchFilters((prev) => ({ ...prev, stockStatus: value || '' }))
                }
                allowClear
                style={{ width: '100%', minWidth: '200px' }}
              >
                <Option value="normal">正常</Option>
                <Option value="out_of_stock">缺货</Option>
              </Select>
            </Col>
          </Row>

          <div className={styles.searchActions}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
            </Space>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className={styles.actionSection}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/product/skus-inventory/add')}
            >
              新增库存明细
            </Button>

            <Dropdown
              menu={{
                items: [
                  {
                    key: 'exportSelected',
                    label: `导出选中记录 (${selectedRowKeys.length})`,
                    icon: <ExportOutlined />,
                    disabled: selectedRowKeys.length === 0 || selectedRowKeys.length > 50,
                    onClick: handleExportSelected,
                  },
                  {
                    key: 'exportAll',
                    label: '导出全部记录',
                    icon: <FileExcelOutlined />,
                    onClick: handleExportAll,
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button loading={exportLoading}>
                <ExportOutlined />
                导出Excel
                <DownOutlined />
              </Button>
            </Dropdown>

            <Dropdown
              menu={{
                items: [
                  {
                    key: 'downloadTemplate',
                    label: '下载导入模板',
                    icon: <DownloadOutlined />,
                    onClick: handleDownloadTemplate,
                  },
                ],
              }}
              trigger={['click']}
            >
              <Upload
                accept=".xlsx,.xls"
                showUploadList={false}
                beforeUpload={handleImport}
                disabled={importLoading}
              >
                <Button loading={importLoading}>
                  <ImportOutlined />
                  导入Excel
                  <DownOutlined />
                </Button>
              </Upload>
            </Dropdown>
          </Space>

          <Space>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
              刷新
            </Button>
          </Space>
        </div>

        {/* 表格 */}
        <div className={styles.tableContainer}>
          <Table
            columns={columns}
            dataSource={inventoryList}
            rowKey="id"
            loading={loading}
            rowSelection={{
              selectedRowKeys,
              onChange: setSelectedRowKeys,
              getCheckboxProps: () => ({
                disabled: false,
              }),
            }}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 1600 }}
            size="small"
          />
        </div>
      </Card>

      {/* 库存调整弹窗 */}
      <Modal
        title="库存调整"
        open={adjustModalVisible}
        onOk={handleAdjustStock}
        onCancel={() => setAdjustModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Form form={adjustForm} layout="vertical">
          <Form.Item
            label="调整类型"
            name="adjustmentType"
            rules={[{ required: true, message: '请选择调整类型' }]}
          >
            <Select placeholder="请选择调整类型">
              <Option value="increase">增加</Option>
              <Option value="decrease">减少</Option>
              <Option value="set">设置为</Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="调整数量"
            name="quantity"
            rules={[{ required: true, message: '请输入调整数量' }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入调整数量" />
          </Form.Item>
          <Form.Item
            label="调整原因"
            name="reason"
            rules={[{ required: true, message: '请输入调整原因' }]}
          >
            <Input.TextArea rows={3} placeholder="请输入调整原因" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 库存盘点弹窗 */}
      <Modal
        title="库存盘点"
        open={countModalVisible}
        onOk={handleCountStock}
        onCancel={() => setCountModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Form form={countForm} layout="vertical">
          <Form.Item
            label="实际库存"
            name="actualStock"
            rules={[{ required: true, message: '请输入实际库存' }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入实际库存" />
          </Form.Item>
          <Form.Item label="盘点备注" name="remarks">
            <Input.TextArea rows={3} placeholder="请输入盘点备注" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SkusInventoryManagement;
