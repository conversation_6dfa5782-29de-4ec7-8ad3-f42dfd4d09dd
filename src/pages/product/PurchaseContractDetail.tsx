import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  message,
  Row,
  Col,
  Select,
  DatePicker,
  Tag,
  Descriptions,
  Table,
  InputNumber,
  Popconfirm,
} from 'antd';
import {
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  EditOutlined,
  FileExcelOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  getPurchaseContractDetail,
  updatePurchaseContract,
  createPurchaseContract,
  exportPurchaseContractsExcel,
} from '@/api/PurchaseContractsApi';
import { PurchaseContractStatus } from '@/types/purchaseContracts';
import { SupplierSearchSelector, SupplierAccessoryMultipleSelector } from '@/components';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './PurchaseContractDetail.module.css';

const { Option } = Select;

// 采购合同状态配置
const statusConfig = {
  [PurchaseContractStatus.DRAFT]: { color: 'default', text: '草稿' },
  [PurchaseContractStatus.CONFIRMED]: { color: 'blue', text: '已确认' },
  [PurchaseContractStatus.IN_PRODUCTION]: { color: 'orange', text: '生产中' },
  [PurchaseContractStatus.SHIPPED]: { color: 'purple', text: '已发货' },
  [PurchaseContractStatus.RECEIVED]: { color: 'cyan', text: '已收货' },
  [PurchaseContractStatus.COMPLETED]: { color: 'green', text: '已完成' },
  [PurchaseContractStatus.CANCELLED]: { color: 'red', text: '已取消' },
};

interface ContractDetailItem {
  id?: string;
  accessoryId: string | null; // 辅料UUID，可能为null
  quantity: number;
  unitPrice: number;
  totalAmount?: number;
  deliveryDate: string;
  remark: string;
  accessory?: string | null;
}

interface ContractData {
  id: string;
  orderNumber: string;
  supplierCode: string;
  supplierName: string;
  orderDate: string;
  totalAmount: number;
  totalQuantity: number;
  status: PurchaseContractStatus;
  expectedDeliveryDate: string;
  remark: string;
  createdByUserCode: string;
  details: ContractDetailItem[];
}

const PurchaseContractDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [contractData, setContractData] = useState<ContractData | null>(null);
  const [details, setDetails] = useState<ContractDetailItem[]>([]);
  const [selectedSupplierCode, setSelectedSupplierCode] = useState<string>('');

  // 判断是否为新增模式
  const isAddMode = id === 'new';

  // 获取采购合同详情
  const fetchContractDetail = async () => {
    if (!id || isAddMode) return;

    setLoading(true);
    try {
      const response = await getPurchaseContractDetail(id);
      if (response.code === 200) {
        setContractData(response.data);
        setDetails(response.data.details || []);

        // 设置表单初始值
        form.setFieldsValue({
          supplierCode: response.data.supplierCode,
          orderDate: dayjs(response.data.orderDate),
          status: response.data.status,
          expectedDeliveryDate: dayjs(response.data.expectedDeliveryDate),
          remark: response.data.remark,
          createdByUserCode: response.data.createdByUserCode,
        });

        // 设置选中的供应商编码
        setSelectedSupplierCode(response.data.supplierCode);
      } else {
        message.error(response.message || '获取采购合同详情失败');
      }
    } catch (error: any) {
      logError('获取采购合同详情', error);
      message.error(getErrorMessage(error, '获取采购合同详情失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化新增模式的数据
  const initAddMode = () => {
    if (isAddMode) {
      // 设置默认值
      form.setFieldsValue({
        orderDate: dayjs(),
        status: PurchaseContractStatus.DRAFT,
        expectedDeliveryDate: dayjs().add(7, 'day'), // 默认7天后交货
        createdByUserCode: 'ADMIN', // 默认创建人，实际应该从用户信息获取
      });

      // 添加一个空的明细行
      setDetails([
        {
          accessoryId: null,
          quantity: 1,
          unitPrice: 0,
          deliveryDate: dayjs().add(7, 'day').format('YYYY-MM-DD'),
          remark: '',
        },
      ]);
    }
  };

  // 保存采购合同
  const handleSave = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);

      const detailsData = details
        .filter((detail) => detail.accessoryId) // 过滤掉accessoryId为空的项
        .map((detail) => ({
          accessoryId: detail.accessoryId!,
          quantity: detail.quantity,
          unitPrice: detail.unitPrice,
          deliveryDate: detail.deliveryDate,
          remark: detail.remark || '',
        }));

      let response;
      if (isAddMode) {
        // 新增模式
        const addData = {
          supplierCode: values.supplierCode,
          orderDate: values.orderDate.format('YYYY-MM-DD'),
          status: values.status,
          expectedDeliveryDate: values.expectedDeliveryDate.format('YYYY-MM-DD'),
          remark: values.remark || '',
          createdByUserCode: values.createdByUserCode,
          details: detailsData,
        };
        response = await createPurchaseContract(addData);
      } else {
        // 编辑模式
        if (!contractData) return;
        const updateData = {
          orderNumber: contractData.orderNumber,
          supplierCode: values.supplierCode,
          orderDate: values.orderDate.format('YYYY-MM-DD'),
          status: values.status,
          expectedDeliveryDate: values.expectedDeliveryDate.format('YYYY-MM-DD'),
          remark: values.remark || '',
          createdByUserCode: values.createdByUserCode,
          details: detailsData,
        };
        response = await updatePurchaseContract(contractData.id, updateData);
      }

      const result = handleApiResponse(
        response,
        isAddMode ? '创建成功' : '保存成功',
        isAddMode ? '创建失败' : '保存失败',
      );

      if (result.success) {
        message.success(result.message);
        if (isAddMode) {
          // 新增成功后跳转到列表页
          navigate('/purchase/purchase-contracts');
        } else {
          // 编辑成功后重新获取数据
          fetchContractDetail();
        }
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError(isAddMode ? '创建采购合同' : '保存采购合同', error);
      message.error(getErrorMessage(error, isAddMode ? '创建失败' : '保存失败'));
    } finally {
      setSaving(false);
    }
  }, [isAddMode, details, form, navigate]);

  // F8快捷键保存
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F8') {
        event.preventDefault();
        handleSave();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleSave]);

  // 初始化加载
  useEffect(() => {
    if (isAddMode) {
      initAddMode();
    } else {
      fetchContractDetail();
    }
  }, [id, isAddMode]);

  // 添加明细
  const handleAddDetail = () => {
    const newDetail: ContractDetailItem = {
      accessoryId: '',
      quantity: 1,
      unitPrice: 0,
      deliveryDate: dayjs().format('YYYY-MM-DD'),
      remark: '',
    };
    setDetails([...details, newDetail]);
  };

  // 批量添加辅料明细
  const handleBatchAddAccessories = (_accessoryIds: string[], accessories: any[]) => {
    const newDetails = accessories.map((accessory) => ({
      accessoryId: accessory.id,
      quantity: 1, // 默认数量为1
      unitPrice: accessory.costPrice || 0, // 使用辅料的成本价作为单价
      deliveryDate: dayjs().add(7, 'day').format('YYYY-MM-DD'), // 默认7天后交货
      remark: '',
      accessory: accessory.name, // 保存辅料名称用于显示
    }));

    // 过滤掉已经存在的辅料
    const existingAccessoryIds = details.map((detail) => detail.accessoryId);
    const filteredNewDetails = newDetails.filter(
      (detail) => !existingAccessoryIds.includes(detail.accessoryId),
    );

    if (filteredNewDetails.length > 0) {
      setDetails([...details, ...filteredNewDetails]);
      message.success(`成功添加 ${filteredNewDetails.length} 个辅料明细`);
    } else {
      message.warning('所选辅料已存在于明细中');
    }
  };

  // 处理供应商选择变化
  const handleSupplierChange = (supplierCode: string) => {
    setSelectedSupplierCode(supplierCode);
    // 更新表单值
    form.setFieldsValue({ supplierCode });
  };

  // 导出当前合同
  const handleExportContract = async () => {
    if (!contractData) {
      message.warning('请先保存合同后再导出');
      return;
    }

    setExporting(true);
    try {
      const blob = await exportPurchaseContractsExcel({
        contractIds: [contractData.id],
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `采购合同_${contractData.orderNumber}_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出采购合同', error);
      message.error(getErrorMessage(error, '导出失败'));
    } finally {
      setExporting(false);
    }
  };

  // 删除明细
  const handleDeleteDetail = (index: number) => {
    const newDetails = details.filter((_, i) => i !== index);
    setDetails(newDetails);
  };

  // 更新明细
  const handleUpdateDetail = (index: number, field: string, value: any) => {
    const newDetails = [...details];
    newDetails[index] = { ...newDetails[index], [field]: value };

    // 自动计算总金额
    if (field === 'quantity' || field === 'unitPrice') {
      newDetails[index].totalAmount = newDetails[index].quantity * newDetails[index].unitPrice;
    }

    setDetails(newDetails);
  };

  // 返回列表
  const handleBack = () => {
    navigate('/purchase/purchase-contracts');
  };

  // 明细表格列定义
  const detailColumns: ColumnsType<ContractDetailItem> = [
    {
      title: '辅料',
      dataIndex: 'accessory',
      width: 200,
      render: (text: string, record: ContractDetailItem) => (
        <div style={{ fontSize: '12px' }}>
          {text || '未知辅料'}
          {record.accessoryId && (
            <div style={{ color: '#8c8c8c', fontSize: '11px' }}>
              ID: {record.accessoryId.slice(-8)}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      width: 100,
      render: (text: number, _: ContractDetailItem, index: number) => (
        <InputNumber
          value={text}
          onChange={(value) => handleUpdateDetail(index, 'quantity', value || 0)}
          min={1}
          size="small"
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      width: 120,
      render: (text: number, _: ContractDetailItem, index: number) => (
        <InputNumber
          value={text}
          onChange={(value) => handleUpdateDetail(index, 'unitPrice', value || 0)}
          min={0}
          precision={2}
          addonBefore="¥"
          size="small"
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      width: 120,
      render: (_: number, record: ContractDetailItem) => (
        <Tag color="green">¥{((record.quantity || 0) * (record.unitPrice || 0)).toFixed(2)}</Tag>
      ),
    },
    {
      title: '交货日期',
      dataIndex: 'deliveryDate',
      width: 130,
      render: (text: string, _: ContractDetailItem, index: number) => (
        <DatePicker
          value={text ? dayjs(text) : null}
          onChange={(date) =>
            handleUpdateDetail(index, 'deliveryDate', date?.format('YYYY-MM-DD') || '')
          }
          format="YYYY-MM-DD"
          size="small"
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      render: (text: string, _: ContractDetailItem, index: number) => (
        <Input
          value={text}
          onChange={(e) => handleUpdateDetail(index, 'remark', e.target.value)}
          placeholder="备注"
          size="small"
        />
      ),
    },
    {
      title: '操作',
      width: 80,
      render: (_: any, __: ContractDetailItem, index: number) => (
        <Popconfirm
          title="确定要删除这条明细吗？"
          onConfirm={() => handleDeleteDetail(index)}
          okText="确定"
          cancelText="取消"
        >
          <Button type="link" danger icon={<DeleteOutlined />} size="small">
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  if (loading) {
    return (
      <div className={styles.container}>
        <Card loading={loading}>
          <div style={{ height: 400 }} />
        </Card>
      </div>
    );
  }

  if (!contractData && !isAddMode) {
    return (
      <div className={styles.container}>
        <Card>
          <div style={{ textAlign: 'center', padding: 40 }}>
            <p>采购合同不存在</p>
            <Button onClick={handleBack}>返回列表</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <Card className={styles.headerCard}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack} className={styles.backButton}>
              返回列表
            </Button>
            <div className={styles.title}>
              <EditOutlined className={styles.titleIcon} />
              {isAddMode ? '新增采购订单' : `编辑采购订单明细 - ${contractData?.orderNumber || ''}`}
            </div>
          </div>
          <div className={styles.headerRight}>
            <Space>
              {!isAddMode && contractData && (
                <Button
                  type="default"
                  icon={<FileExcelOutlined />}
                  onClick={handleExportContract}
                  loading={exporting}
                >
                  导出Excel
                </Button>
              )}
            </Space>
          </div>
        </div>
      </Card>

      {/* 基本信息 - 仅在编辑模式下显示 */}
      {!isAddMode && contractData && (
        <Card title="基本信息" className={styles.infoCard}>
          <Row gutter={16}>
            <Col span={8}>
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="订单号">{contractData.orderNumber}</Descriptions.Item>
                <Descriptions.Item label="供应商">{contractData.supplierName}</Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={8}>
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="当前状态">
                  <Tag color={statusConfig[contractData.status].color}>
                    {statusConfig[contractData.status].text}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="总金额">
                  <Tag color="green">¥{contractData.totalAmount.toFixed(2)}</Tag>
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={8}>
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="总数量">
                  <Tag color="blue">{contractData.totalQuantity}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="创建人">
                  {contractData.createdByUserCode}
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        </Card>
      )}

      {/* 编辑表单 */}
      <Card title={isAddMode ? '新增采购订单' : '订单信息编辑'} className={styles.formCard}>
        <Form form={form} layout="vertical">
          <Row gutter={12}>
            <Col span={6}>
              <Form.Item
                name="supplierCode"
                label="供应商"
                rules={[{ required: true, message: '请选择供应商' }]}
              >
                <SupplierSearchSelector
                  placeholder="选择供应商"
                  onChange={handleSupplierChange}
                  style={{ minHeight: '32px' }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="status"
                label="订单状态"
                rules={[{ required: true, message: '请选择订单状态' }]}
              >
                <Select placeholder="选择状态" size="small">
                  {Object.entries(statusConfig).map(([key, config]) => (
                    <Option key={key} value={key}>
                      {config.text}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="orderDate"
                label="订单日期"
                rules={[{ required: true, message: '请选择订单日期' }]}
              >
                <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" size="small" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="expectedDeliveryDate"
                label="预期交货日期"
                rules={[{ required: true, message: '请选择预期交货日期' }]}
              >
                <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" size="small" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={12}>
            <Col span={12}>
              <Form.Item
                name="createdByUserCode"
                label="创建人用户编码"
                rules={[{ required: true, message: '请输入创建人用户编码' }]}
              >
                <Input placeholder="请输入创建人用户编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="remark" label="备注">
                <Input placeholder="请输入备注信息" size="small" />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {/* 辅料批量选择区域 */}
        {selectedSupplierCode && (
          <div
            style={{
              marginTop: 16,
              padding: 20,
              background: '#f8f9fa',
              borderRadius: 8,
              border: '1px solid #e9ecef',
            }}
          >
            <Row gutter={16} align="top">
              <Col span={24}>
                <div
                  style={{
                    marginBottom: 12,
                    fontWeight: 600,
                    fontSize: '14px',
                    color: '#495057',
                  }}
                >
                  批量添加辅料:
                </div>
                <SupplierAccessoryMultipleSelector
                  supplierCode={selectedSupplierCode}
                  placeholder="选择要添加的辅料（可多选）"
                  onChange={handleBatchAddAccessories}
                  style={{ minHeight: '40px' }}
                />
                <div
                  style={{
                    fontSize: '12px',
                    color: '#6c757d',
                    marginTop: 8,
                    lineHeight: 1.5,
                  }}
                >
                  💡 提示：选择辅料后将自动添加到订单明细中，数量默认为1，单价为辅料成本价
                </div>
              </Col>
            </Row>
          </div>
        )}
      </Card>

      {/* 订单明细 */}
      <Card
        title="订单明细"
        className={styles.detailCard}
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddDetail} size="small">
            添加明细
          </Button>
        }
      >
        <Table
          columns={detailColumns}
          dataSource={details}
          rowKey={(_, index) => index?.toString() || '0'}
          pagination={false}
          size="small"
          scroll={{ x: 800 }}
          className={styles.detailTable}
        />

        {/* 汇总信息 */}
        <div className={styles.summary}>
          <Space size="large">
            <span>
              总数量:{' '}
              <Tag color="blue">{details.reduce((sum, item) => sum + (item.quantity || 0), 0)}</Tag>
            </span>
            <span>
              总金额:{' '}
              <Tag color="green">
                ¥
                {details
                  .reduce((sum, item) => sum + (item.quantity || 0) * (item.unitPrice || 0), 0)
                  .toFixed(2)}
              </Tag>
            </span>
          </Space>
        </div>
      </Card>

      {/* 固定操作栏 */}
      <div className={styles.fixedActionBar}>
        <Button
          type="primary"
          icon={<SaveOutlined />}
          onClick={handleSave}
          loading={saving}
          className={styles.fixedSaveButton}
        >
          保存 (F8)
        </Button>
        {!isAddMode && contractData && (
          <Button
            type="primary"
            icon={<FileExcelOutlined />}
            onClick={handleExportContract}
            loading={exporting}
            className={styles.fixedExportButton}
          >
            导出Excel
          </Button>
        )}
      </div>
    </div>
  );
};

export default PurchaseContractDetail;
