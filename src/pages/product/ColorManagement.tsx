import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import {
  getColorList,
  createColor,
  updateColor,
  deleteColor,
  getColorDetail,
} from '@/api/ColorApi';
import type { ColorListProps, UpdateColorParams } from '@/types/color';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './ColorManagement.module.css';

const { Search } = Input;

const ColorManagement: React.FC = () => {
  const [colors, setColors] = useState<ColorListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingColor, setEditingColor] = useState<ColorListProps | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取颜色列表
  const fetchColors = async (page = 1, pageSize = 10, search = '') => {
    setLoading(true);
    try {
      const response = await getColorList({
        page,
        pageSize,
        search,
      });

      const result = handleApiResponse(response, '', '获取颜色列表失败');

      if (result.success && response.data) {
        setColors(response.data.colors || []);
        setPagination({
          current: page,
          pageSize,
          total: response.data.total || 0,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取颜色列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchColors();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    fetchColors(1, pagination.pageSize, value);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchColors(pagination.current, pagination.pageSize, searchText);
  };

  // 分页变化处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    fetchColors(current, pageSize, searchText);
  };

  // 新增颜色
  const handleAdd = () => {
    setEditingColor(null);
    form.resetFields();
    form.setFieldsValue({ name: '#000000' }); // 设置默认颜色
    setModalVisible(true);
  };

  // 编辑颜色
  const handleEdit = async (color: ColorListProps) => {
    try {
      const response = await getColorDetail(color.id);
      const result = handleApiResponse(response, '', '获取颜色详情失败');

      if (result.success && response.data) {
        setEditingColor(response.data);
        form.setFieldsValue({
          code: response.data.code,
          name: response.data.name,
        });
        setModalVisible(true);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取颜色详情', error);
      message.error(getErrorMessage(error));
    }
  };

  // 删除颜色
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteColor(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('删除颜色', error);
      message.error(getErrorMessage(error));
    }
  };

  // 保存颜色
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      let response;

      if (editingColor) {
        // 编辑模式
        const updateData: UpdateColorParams = {
          code: values.code,
          name: values.name,
        };
        response = await updateColor(editingColor.id, updateData);
      } else {
        // 新增模式
        response = await createColor(values);
      }

      const result = handleApiResponse(
        response,
        editingColor ? '更新成功' : '创建成功',
        editingColor ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError(editingColor ? '更新颜色' : '创建颜色', error);
      message.error(getErrorMessage(error));
    }
  };

  // 颜色值变化处理
  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const colorValue = e.target.value;
    form.setFieldsValue({ name: colorValue });
  };

  // 表格列定义
  const columns: ColumnsType<ColorListProps> = [
    {
      title: '颜色编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      fixed: 'left',
      render: (text: string) => <span className={styles.colorCode}>{text}</span>,
    },
    {
      title: '颜色名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string) => (
        <div className={styles.colorPreview}>
          <span className={styles.colorName}>{text}</span>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            className={styles.actionButton}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个颜色吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />} className={styles.actionButton}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <Search
                placeholder="搜索颜色编码或名称"
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                style={{ maxWidth: 400 }}
              />
            </Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增颜色
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <div className={styles.tableContainer}>
          <Table
            columns={columns}
            dataSource={colors}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 800 }}
          />
        </div>
      </Card>

      {/* 颜色编辑模态框 */}
      <Modal
        title={editingColor ? '编辑颜色' : '新增颜色'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={500}
        destroyOnClose
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="颜色编码"
                rules={[
                  { required: true, message: '请输入颜色编码' },
                  { min: 1, message: '颜色编码至少1个字符' },
                  { max: 20, message: '颜色编码至多20个字符' },
                ]}
              >
                <Input placeholder="请输入颜色编码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="颜色名称"
                rules={[{ required: true, message: '请选择颜色值' }]}
              >
                <div className={styles.colorInput}>
                  <Input
                    placeholder="必须以色字结尾, 比如黑色"
                    maxLength={7}
                    onChange={(e) => form.setFieldsValue({ name: e.target.value })}
                  />
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default ColorManagement;
