.container {
  padding: 8px;
  background-color: #f0f2f5;
  min-height: 100vh;
  padding-bottom: 100px; /* 为固定按钮留出空间 */
}

/* 页面头部 */
.headerCard {
  margin-bottom: 8px;
  border-radius: 6px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.headerRight {
  display: flex;
  align-items: center;
}

.backButton {
  border: none;
  box-shadow: none;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.titleIcon {
  color: #1890ff;
}

.saveButton {
  background: #52c41a;
  border-color: #52c41a;
}

.saveButton:hover,
.saveButton:focus {
  background: #73d13d;
  border-color: #73d13d;
}

/* 卡片样式 */
.infoCard,
.formCard,
.detailCard {
  margin-bottom: 8px;
  border-radius: 6px;
}

.infoCard .ant-card-head,
.formCard .ant-card-head,
.detailCard .ant-card-head {
  padding: 0 16px;
  min-height: 40px;
}

.infoCard .ant-card-head-title,
.formCard .ant-card-head-title,
.detailCard .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
}

.infoCard .ant-card-body,
.formCard .ant-card-body,
.detailCard .ant-card-body {
  padding: 12px 16px;
}

/* 表单样式 */
.ant-form-item {
  margin-bottom: 12px;
}

.ant-form-item-label {
  padding-bottom: 2px;
}

.ant-form-item-label > label {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
}

/* 明细表格 */
.detailTable {
  margin-bottom: 12px;
}

.detailTable .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
  font-size: 13px;
  padding: 8px 12px;
}

.detailTable .ant-table-tbody > tr > td {
  padding: 6px 12px;
  font-size: 13px;
}

.detailTable .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 汇总信息 */
.summary {
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
  text-align: right;
  font-size: 14px;
  font-weight: 500;
}

/* 描述列表 */
.ant-descriptions-item-label {
  font-size: 13px;
  font-weight: 500;
  color: #595959;
  width: 80px;
}

.ant-descriptions-item-content {
  font-size: 13px;
  color: #262626;
}

/* 输入框和选择器 */
.ant-input,
.ant-input-number,
.ant-select-selector,
.ant-picker {
  border-radius: 4px;
  font-size: 13px;
}

.ant-input:focus,
.ant-input-focused,
.ant-input-number:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector,
.ant-picker:focus,
.ant-picker-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.ant-btn {
  border-radius: 4px;
  font-size: 13px;
  height: 28px;
  padding: 0 12px;
}

.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.ant-btn-link {
  padding: 0 4px;
  height: auto;
}

/* 标签样式 */
.ant-tag {
  font-weight: 500;
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 6px;
  margin: 0;
}

/* 表格行操作 */
.ant-table-tbody > tr > td .ant-btn-link {
  padding: 0;
  height: auto;
  line-height: 1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 6px;
  }

  .header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .headerLeft {
    width: 100%;
  }

  .headerRight {
    width: 100%;
    justify-content: flex-end;
  }

  .title {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 4px;
  }

  .infoCard .ant-card-body,
  .formCard .ant-card-body,
  .detailCard .ant-card-body {
    padding: 8px 12px;
  }

  .ant-form-item {
    margin-bottom: 8px;
  }

  .detailTable .ant-table-thead > tr > th,
  .detailTable .ant-table-tbody > tr > td {
    padding: 4px 8px;
    font-size: 12px;
  }

  .summary {
    padding: 8px 0;
    font-size: 13px;
  }

  .title {
    font-size: 14px;
  }
}

/* 紧凑布局优化 */
.ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.ant-descriptions-small .ant-descriptions-item-label,
.ant-descriptions-small .ant-descriptions-item-content {
  padding-bottom: 4px;
}

/* 表格紧凑样式 */
.ant-table-small .ant-table-thead > tr > th,
.ant-table-small .ant-table-tbody > tr > td {
  padding: 4px 8px;
}

.ant-table-small .ant-table-thead > tr > th {
  font-size: 12px;
}

.ant-table-small .ant-table-tbody > tr > td {
  font-size: 12px;
}

/* 输入框紧凑样式 */
.ant-input-sm,
.ant-input-number-sm,
.ant-select-sm .ant-select-selector,
.ant-picker-sm {
  height: 24px;
  font-size: 12px;
}

.ant-btn-sm {
  height: 24px;
  padding: 0 8px;
  font-size: 12px;
}

/* 固定操作栏 */
.fixedActionBar {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
}

.fixedActionBar .ant-btn {
  height: 36px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.fixedSaveButton {
  background: #52c41a;
  border-color: #52c41a;
  color: #fff;
}

.fixedSaveButton:hover,
.fixedSaveButton:focus {
  background: #73d13d;
  border-color: #73d13d;
  color: #fff;
}

.fixedExportButton {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
}

.fixedExportButton:hover,
.fixedExportButton:focus {
  background: #40a9ff;
  border-color: #40a9ff;
  color: #fff;
}

/* 为了避免内容被固定按钮遮挡，给容器添加底部间距 */
