import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  InputNumber,
  Switch,
  Tag,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import {
  getBrandList,
  createBrand,
  updateBrand,
  deleteBrand,
  getBrandDetail,
} from '@/api/BrandApi';
import type { BrandListProps, AddBrandParams, UpdateBrandParams } from '@/types/brands';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './BrandManagement.module.css';

const { Search } = Input;

interface BrandFormData extends AddBrandParams {}

const BrandManagement: React.FC = () => {
  const [brands, setBrands] = useState<BrandListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBrand, setEditingBrand] = useState<BrandListProps | null>(null);
  const [form] = Form.useForm<BrandFormData>();

  // 获取品牌列表
  const fetchBrands = async (page = 1, pageSize = 10, search = '') => {
    setLoading(true);
    try {
      const response = await getBrandList({
        page,
        pageSize,
        search: search.trim() || undefined,
      });

      if (response.code === 200) {
        setBrands(response.data.brands);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取品牌列表失败');
      }
    } catch (error: any) {
      logError('获取品牌列表', error);
      message.error(getErrorMessage(error, '获取品牌列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchBrands();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
    fetchBrands(1, pagination.pageSize, value);
  };

  // 分页处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));
    fetchBrands(current, pageSize, searchText);
  };

  // 刷新列表
  const handleRefresh = () => {
    fetchBrands(pagination.current, pagination.pageSize, searchText);
  };

  // 新增品牌
  const handleAdd = () => {
    setEditingBrand(null);
    form.resetFields();
    form.setFieldsValue({
      isActive: true,
    });
    setModalVisible(true);
  };

  // 编辑品牌
  const handleEdit = async (brand: BrandListProps) => {
    try {
      const response = await getBrandDetail(brand.code);
      if (response.code === 200) {
        setEditingBrand(brand);
        form.setFieldsValue({
          code: response.data.code,
          name: response.data.name,
          orderPrice: response.data.orderPrice,
          restockPrice: response.data.restockPrice,
          spotPrice: response.data.spotPrice,
          isActive: response.data.isActive,
        });
        setModalVisible(true);
      } else {
        message.error(response.message || '获取品牌详情失败');
      }
    } catch (error: any) {
      logError('获取品牌详情', error);
      message.error(getErrorMessage(error, '获取品牌详情失败'));
    }
  };

  // 删除品牌
  const handleDelete = async (brandCode: string) => {
    try {
      const response = await deleteBrand(brandCode);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除品牌', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 保存品牌
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      let response;

      if (editingBrand) {
        // 编辑模式
        const updateData: UpdateBrandParams = {
          name: values.name,
          orderPrice: values.orderPrice,
          restockPrice: values.restockPrice,
          spotPrice: values.spotPrice,
          isActive: values.isActive,
        };
        response = await updateBrand(editingBrand.code, updateData);
      } else {
        // 新增模式
        response = await createBrand(values);
      }

      const result = handleApiResponse(
        response,
        editingBrand ? '更新成功' : '创建成功',
        editingBrand ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError(editingBrand ? '更新品牌' : '创建品牌', error);
      message.error(getErrorMessage(error, editingBrand ? '更新失败' : '创建失败'));
    }
  };

  // 表格列定义
  const columns: ColumnsType<BrandListProps> = [
    {
      title: '品牌编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      fixed: 'left',
      render: (text: string) => (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: '#147ffa',
            fontWeight: 'bold',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: '品牌名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string) => (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: '#000',
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: '预订折扣',
      dataIndex: 'orderPrice',
      key: 'orderPrice',
      width: 120,
      render: (price: number) => <Tag color="blue">{price}</Tag>,
    },
    {
      title: '补货折扣',
      dataIndex: 'restockPrice',
      key: 'restockPrice',
      width: 120,
      render: (price: number) => <Tag color="green">{price}</Tag>,
    },
    {
      title: '现货折扣',
      dataIndex: 'spotPrice',
      key: 'spotPrice',
      width: 120,
      render: (price: number) => <Tag color="orange">{price}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'default'}>{isActive ? '启用' : '禁用'}</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => (text ? new Date(text).toLocaleString() : '-'),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个品牌吗？"
            onConfirm={() => handleDelete(record.code)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <Search
                placeholder="搜索品牌编码或名称"
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                style={{ maxWidth: 400 }}
              />
            </Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增品牌
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={brands}
          rowKey="code"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 品牌编辑模态框 */}
      <Modal
        title={editingBrand ? '编辑品牌' : '新增品牌'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="品牌编码"
                rules={[
                  { required: true, message: '请输入品牌编码' },
                  { min: 2, message: '品牌编码至少2个字符' },
                  { max: 20, message: '品牌编码至多20个字符' },
                ]}
              >
                <Input placeholder="请输入品牌编码" disabled={!!editingBrand} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="品牌名称"
                rules={[{ required: true, message: '请输入品牌名称' }]}
              >
                <Input placeholder="请输入品牌名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="orderPrice"
                label="预订折扣"
                rules={[
                  { required: true, message: '请输入预订折扣' },
                  { type: 'number', min: 0, message: '折扣不能小于0' },
                  { type: 'number', max: 1, message: '折扣不能大于0' },
                ]}
              >
                <InputNumber
                  placeholder="请输入预订折扣"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  step={0.01}
                  max={1}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="restockPrice"
                label="补货折扣"
                rules={[
                  { required: true, message: '请输入补货折扣' },
                  { type: 'number', min: 0, message: '折扣不能小于0' },
                ]}
              >
                <InputNumber
                  placeholder="请输入补货折扣"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  step={0.01}
                  max={1}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="spotPrice"
                label="现货折扣"
                rules={[
                  { required: true, message: '请输入现货折扣' },
                  { type: 'number', min: 0, message: '折扣不能小于0' },
                ]}
              >
                <InputNumber
                  placeholder="请输入现货折扣"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  step={0.01}
                  max={1}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="isActive" label="状态" valuePropName="checked">
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BrandManagement;
