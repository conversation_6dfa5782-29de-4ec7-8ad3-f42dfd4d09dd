import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Button,
  Space,
  message,
  Row,
  Col,
  Select,
  Divider,
} from 'antd';
import { SaveOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import {
  createSkusInventory,
  updateSkusInventory,
  getSkusInventoryDetail,
} from '@/api/SkusInventoryApi';
import { getSkuList } from '@/api/SkuApi';
import type { AddSkusInventory, UpdateSkusInventory } from '@/types/skusInventory';
import type { Sku } from '@/types/skus';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './SkuForm.module.css';

const { Option } = Select;
const { TextArea } = Input;

const SkusInventoryForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [skuOptions, setSkuOptions] = useState<Sku[]>([]);
  const [skuLoading, setSkuLoading] = useState(false);

  const isEdit = Boolean(id);
  const isView = window.location.pathname.includes('/view/');
  const pageTitle = isView ? '查看库存明细' : isEdit ? '编辑库存明细' : '新增库存明细';

  // 获取SKU选项
  const fetchSkuOptions = async () => {
    setSkuLoading(true);
    try {
      const response = await getSkuList({ page: 1, pageSize: 1000 });
      const result = handleApiResponse(response, '', '获取SKU列表失败');

      if (result.success && response.data) {
        setSkuOptions(response.data.skus || []);
      }
    } catch (error) {
      logError('获取SKU列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setSkuLoading(false);
    }
  };

  // 获取库存明细详情
  const fetchInventoryDetail = async (inventoryId: string) => {
    setLoading(true);
    try {
      const response = await getSkusInventoryDetail(inventoryId);
      const result = handleApiResponse(response, '', '获取库存明细详情失败');

      if (result.success && response.data) {
        const data = response.data;
        form.setFieldsValue({
          skuId: data.skuId,
          size: data.size,
          currentStock: data.currentStock,
          purchasingStock: data.purchasingStock,
          needRestockStock: data.needRestockStock,
          remarks: data.remarks,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取库存明细详情', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchSkuOptions();
    if (isEdit && id) {
      fetchInventoryDetail(id);
    }
  }, [isEdit, id]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (isEdit && id) {
        // 编辑
        const updateData: UpdateSkusInventory = {
          currentStock: values.currentStock,
          purchasingStock: values.purchasingStock,
          needRestockStock: values.needRestockStock,
          remarks: values.remarks,
        };

        const response = await updateSkusInventory(id, updateData);
        const result = handleApiResponse(response, '更新成功', '更新失败');

        if (result.success) {
          message.success(result.message);
          navigate('/product/skus-inventory');
        } else {
          message.error(result.message);
        }
      } else {
        // 新增
        const createData: AddSkusInventory = {
          skuId: values.skuId,
          size: values.size,
          currentStock: values.currentStock,
          purchasingStock: values.purchasingStock,
          needRestockStock: values.needRestockStock,
          remarks: values.remarks,
        };

        const response = await createSkusInventory(createData);
        const result = handleApiResponse(response, '创建成功', '创建失败');

        if (result.success) {
          message.success(result.message);
          navigate('/product/skus-inventory');
        } else {
          message.error(result.message);
        }
      }
    } catch (error) {
      logError(isEdit ? '更新库存明细' : '创建库存明细', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 快捷键保存
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F8') {
        event.preventDefault();
        if (!isView) {
          handleSubmit();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isView]);

  return (
    <div className={styles.container}>
      <Card title={pageTitle}>
        <Form form={form} layout="vertical" disabled={isView}>
          <Divider orientation="left">基本信息</Divider>

          <Row gutter={24}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                label="SKU"
                name="skuId"
                rules={[{ required: true, message: '请选择SKU' }]}
              >
                <Select
                  placeholder="请选择SKU"
                  loading={skuLoading}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                  disabled={isEdit}
                >
                  {skuOptions.map((sku) => (
                    <Option key={sku.id} value={sku.id}>
                      {sku.code} - {sku.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                label="尺码"
                name="size"
                rules={[{ required: true, message: '请输入尺码' }]}
              >
                <Input placeholder="请输入尺码" disabled={isEdit} />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">库存信息</Divider>

          <Row gutter={24}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                label="当前库存"
                name="currentStock"
                rules={[{ required: true, message: '请输入当前库存' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入当前库存" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                label="采购中库存"
                name="purchasingStock"
                rules={[{ required: true, message: '请输入采购中库存' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入采购中库存" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12} md={8}>
              <Form.Item
                label="需补货库存"
                name="needRestockStock"
                rules={[{ required: true, message: '请输入需补货库存' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入需补货库存" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24}>
              <Form.Item label="备注" name="remarks">
                <TextArea rows={4} placeholder="请输入备注信息" />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {/* 操作按钮 */}
        <div className={styles.actionSection}>
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/product/skus-inventory')}
            >
              返回
            </Button>
            {!isView && (
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={loading}
                onClick={handleSubmit}
              >
                保存 (F8)
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default SkusInventoryForm;
