import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Popconfirm,
  Row,
  Col,
  Select,
  DatePicker,
  Tag,
  Statistic,
} from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  FileExcelOutlined,
  ShoppingCartOutlined,
  FilterOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import {
  getPurchaseContractsList,
  deletePurchaseContract,
  exportPurchaseContractsExcel,
} from '@/api/PurchaseContractsApi';
import { GetPurchaseListParams, PurchaseContractStatus } from '@/types/purchaseContracts';
import { SupplierSearchSelector } from '@/components';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './PurchaseContractsManagement.module.css';

const { Search } = Input;
const { RangePicker } = DatePicker;

const { Option } = Select;

// 采购合同状态配置
const statusConfig = {
  [PurchaseContractStatus.DRAFT]: { color: 'default', text: '草稿' },
  [PurchaseContractStatus.CONFIRMED]: { color: 'blue', text: '已确认' },
  [PurchaseContractStatus.IN_PRODUCTION]: { color: 'orange', text: '生产中' },
  [PurchaseContractStatus.SHIPPED]: { color: 'purple', text: '已发货' },
  [PurchaseContractStatus.RECEIVED]: { color: 'cyan', text: '已收货' },
  [PurchaseContractStatus.COMPLETED]: { color: 'green', text: '已完成' },
  [PurchaseContractStatus.CANCELLED]: { color: 'red', text: '已取消' },
};

interface ContractDetail {
  id: string;
  orderNumber: string;
  supplierCode: string;
  supplierName: string;
  supplierAddress: string | null;
  supplierContactName: string | null;
  supplierContactPhone: string | null;
  orderDate: string;
  totalAmount: number;
  totalQuantity: number;
  status: PurchaseContractStatus;
  expectedDeliveryDate: string;
  remark: string;
  createdByUserCode: string;
  createdAt: string;
  updatedAt: string;
}

const PurchaseContractsManagement: React.FC = () => {
  const navigate = useNavigate();
  const [contracts, setContracts] = useState<ContractDetail[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [supplierCode, setSupplierCode] = useState<string>('');
  const [status, setStatus] = useState<PurchaseContractStatus | undefined>(undefined);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalQuantity, setTotalQuantity] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [exportLoading, setExportLoading] = useState(false);

  // 获取采购合同列表
  const fetchContracts = async (
    page = 1,
    pageSize = 10,
    search = '',
    supplierCodeFilter = '',
    statusFilter?: PurchaseContractStatus,
    startDate = '',
    endDate = '',
  ) => {
    setLoading(true);
    try {
      const params: GetPurchaseListParams = {
        page,
        pageSize,
        search: search.trim() || undefined,
        supplierCode: supplierCodeFilter || undefined,
        status: statusFilter,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
      };

      const response = await getPurchaseContractsList(params);

      if (response.code === 200) {
        setContracts(response.data.contracts);
        setTotalAmount(
          response.data.contracts.reduce((sum, contract) => sum + contract.totalAmount, 0),
        );
        setTotalQuantity(
          response.data.contracts.reduce((sum, contract) => sum + contract.totalQuantity, 0),
        );
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取采购合同列表失败');
      }
    } catch (error: any) {
      logError('获取采购合同列表', error);
      message.error(getErrorMessage(error, '获取采购合同列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchContracts();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, current: 1 }));
    const [startTime, endTime] = dateRange || [null, null];
    fetchContracts(
      1,
      pagination.pageSize,
      searchText,
      supplierCode,
      status,
      startTime?.format('YYYY-MM-DD') || '',
      endTime?.format('YYYY-MM-DD') || '',
    );
  };

  // 重置搜索
  const handleReset = () => {
    setSearchText('');
    setSupplierCode('');
    setStatus(undefined);
    setDateRange(null);
    setPagination((prev) => ({ ...prev, current: 1 }));
    fetchContracts(1, pagination.pageSize);
  };

  // 分页处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));
    const [startTime, endTime] = dateRange || [null, null];
    fetchContracts(
      current,
      pageSize,
      searchText,
      supplierCode,
      status,
      startTime?.format('YYYY-MM-DD') || '',
      endTime?.format('YYYY-MM-DD') || '',
    );
  };

  // 刷新列表
  const handleRefresh = () => {
    const [startTime, endTime] = dateRange || [null, null];
    fetchContracts(
      pagination.current,
      pagination.pageSize,
      searchText,
      supplierCode,
      status,
      startTime?.format('YYYY-MM-DD') || '',
      endTime?.format('YYYY-MM-DD') || '',
    );
  };

  // 新增采购合同
  const handleAdd = () => {
    navigate('/purchase/purchase-contracts/add');
  };

  // 查看采购合同明细
  const handleViewDetails = (contract: ContractDetail) => {
    navigate(`/purchase/purchase-contracts/view/${contract.id}`);
  };

  // 编辑采购合同明细
  const handleEditDetails = (contract: ContractDetail) => {
    navigate(`/purchase/purchase-contracts/edit/${contract.id}`);
  };

  // 删除采购合同
  const handleDelete = async (contractId: string) => {
    try {
      const response = await deletePurchaseContract(contractId);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
        // 清空选中项
        setSelectedRowKeys([]);
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除采购合同', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 导出Excel
  const handleExportExcel = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要导出的采购合同记录');
      return;
    }

    setExportLoading(true);
    try {
      const [startTime, endTime] = dateRange || [null, null];
      const contractIds = selectedRowKeys.map((key) => key.toString());

      const blob = await exportPurchaseContractsExcel({
        startTime: startTime?.format('YYYY-MM-DD'),
        endTime: endTime?.format('YYYY-MM-DD'),
        contractIds,
        supplierCode: supplierCode || undefined,
        status,
      });

      // 下载文件
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const fileName = `辅料采购订单_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('Excel导出成功');
    } catch (error: any) {
      logError('导出采购合同Excel', error);
      message.error(getErrorMessage(error, '导出Excel失败'));
    } finally {
      setExportLoading(false);
    }
  };

  // 表格行选择配置
  const rowSelection: TableRowSelection<ContractDetail> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // 表格列定义
  const columns: ColumnsType<ContractDetail> = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 200,
      fixed: 'left',
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '订单日期',
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: 120,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
      sorter: (a, b) => dayjs(a.orderDate).unix() - dayjs(b.orderDate).unix(),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: PurchaseContractStatus) => {
        const config = statusConfig[status];
        return (
          <Tag color={config.color} className={styles.statusTag}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => <Tag color="green">¥{amount.toFixed(2)}</Tag>,
      sorter: (a, b) => a.totalAmount - b.totalAmount,
    },
    {
      title: '总数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 100,
      render: (quantity: number) => <Tag color="blue">{quantity}</Tag>,
      sorter: (a, b) => a.totalQuantity - b.totalQuantity,
    },
    {
      title: '预期交货日期',
      dataIndex: 'expectedDeliveryDate',
      key: 'expectedDeliveryDate',
      width: 180,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
      sorter: (a, b) => dayjs(a.expectedDeliveryDate).unix() - dayjs(b.expectedDeliveryDate).unix(),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 240,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: '操作',
      key: 'action',
      width: 240,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
            size="small"
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditDetails(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个采购合同吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        {/* 统计信息 */}
        <Row gutter={8} style={{ marginBottom: 8 }}>
          <Col span={6}>
            <Statistic
              title="总金额"
              value={totalAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="订单总数"
              value={pagination.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic title="总数量" value={totalQuantity} valueStyle={{ color: '#722ed1' }} />
          </Col>
          <Col span={6}>
            <Statistic
              title="已选择"
              value={selectedRowKeys.length}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Col>
        </Row>

        {/* 搜索过滤区域 */}
        <div className={styles.searchSection}>
          <div className={styles.searchContent}>
            <Row gutter={[8, 8]}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>关键词</label>
                  <div className={styles.searchInputWrapper}>
                    <Input
                      placeholder="搜索订单号、备注等"
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      allowClear
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>供应商</label>
                  <div className={styles.searchInputWrapper}>
                    <SupplierSearchSelector
                      value={supplierCode}
                      onChange={setSupplierCode}
                      placeholder="选择供应商"
                      allowClear
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>订单状态</label>
                  <div className={styles.searchInputWrapper}>
                    <Select value={status} onChange={setStatus} placeholder="选择状态" allowClear>
                      {Object.entries(statusConfig).map(([key, config]) => (
                        <Option key={key} value={key}>
                          {config.text}
                        </Option>
                      ))}
                    </Select>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>订单日期</label>
                  <div className={styles.searchInputWrapper}>
                    <RangePicker
                      value={dateRange}
                      onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
                      format="YYYY-MM-DD"
                      placeholder={['开始日期', '结束日期']}
                    />
                  </div>
                </div>
              </Col>
            </Row>
            <div className={styles.searchActions}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </div>
          </div>
        </div>

        {/* 工具栏 */}
        <div className={styles.toolbar}>
          <Row gutter={8} justify="space-between" align="middle">
            <Col flex="auto">
              <Space size="middle">
                <Search
                  placeholder="快速搜索订单号"
                  allowClear
                  enterButton={<SearchOutlined />}
                  size="middle"
                  onSearch={(value) => {
                    setSearchText(value);
                    handleSearch();
                  }}
                  style={{ width: 250 }}
                />
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<FileExcelOutlined />}
                  onClick={handleExportExcel}
                  loading={exportLoading}
                  disabled={selectedRowKeys.length === 0}
                >
                  导出Excel ({selectedRowKeys.length})
                </Button>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增采购订单
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={contracts}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          size="small"
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            size: 'small',
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
        />
      </Card>
    </div>
  );
};

export default PurchaseContractsManagement;
