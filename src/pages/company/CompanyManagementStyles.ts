import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => ({
  container: {
    padding: '24px',
    minHeight: 'calc(100vh - 112px)',
  },

  header: {
    marginBottom: '24px',
    paddingBottom: '16px',
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
  },

  toolbar: {
    marginBottom: '16px',
    padding: '16px',
    backgroundColor: token.colorFillAlter,
    borderRadius: token.borderRadius,
  },

  searchContainer: {
    display: 'flex',
    gap: '12px',
    alignItems: 'center',
    flexWrap: 'wrap',
  },

  actionButtons: {
    display: 'flex',
    gap: '8px',
    alignItems: 'center',
  },

  tableContainer: {
    '& .ant-table-thead > tr > th': {
      backgroundColor: token.colorFillAlter,
      fontWeight: 600,
    },
    
    '& .ant-table-tbody > tr:hover > td': {
      backgroundColor: token.colorFillContent,
    },
  },

  companyCode: {
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
    fontSize: '13px',
    backgroundColor: token.colorFillTertiary,
    padding: '2px 6px',
    borderRadius: '4px',
    border: `1px solid ${token.colorBorder}`,
  },

  licenseImage: {
    borderRadius: '4px',
    border: `1px solid ${token.colorBorder}`,
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    
    '&:hover': {
      transform: 'scale(1.05)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
  },

  modalForm: {
    '& .ant-form-item-label > label': {
      fontWeight: 500,
    },
  },

  uploadSection: {
    '& .ant-form-item-label': {
      marginBottom: '8px',
    },
  },
}));

export default useStyles;
