import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  Descriptions,
  Button,
  Modal,
  Form,
  InputNumber,
  Select,
  message,
  Spin,
  Divider,
} from 'antd';
import {
  EditOutlined,
  TrophyOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  SafetyOutlined,
  ClockCircleOutlined,
  StarOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import {
  getSupplierArchivesSummaryDetail,
  updateSupplierArchivesSummary,
} from '@/api/SupplierArchivesApi';
import {
  type ListDetail,
  CreditLevelOptions,
  getCreditLevelColor,
} from '@/types/supplierArchives';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { getUser } from '@/utils/tokenManager';

const { Option } = Select;

interface SupplierArchivesSummaryDetailProps {
  supplierCode: string;
}

const SupplierArchivesSummaryDetail: React.FC<SupplierArchivesSummaryDetailProps> = ({
  supplierCode,
}) => {
  const [form] = Form.useForm();
  const [detail, setDetail] = useState<ListDetail | null>(null);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [updating, setUpdating] = useState(false);

  // 获取当前用户信息
  const currentUser = getUser();
  const isSuperAdmin = currentUser?.isSuperAdmin;

  // 获取汇总详情
  const fetchDetail = async () => {
    if (!supplierCode) return;

    setLoading(true);
    try {
      const response = await getSupplierArchivesSummaryDetail(supplierCode);
      if (response.code === 200) {
        setDetail(response.data);
      } else {
        message.error(response.message || '获取汇总详情失败');
      }
    } catch (error: any) {
      logError('获取汇总详情', error);
      message.error(getErrorMessage(error, '获取汇总详情失败'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (supplierCode) {
      fetchDetail();
    }
  }, [supplierCode]);

  // 编辑汇总数据
  const handleEdit = () => {
    if (!detail) return;
    
    form.setFieldsValue({
      totalPurchaseAmount: detail.totalPurchaseAmount,
      defectRate: detail.defectRate * 100, // 转换为百分比显示
      repairRate: detail.repairRate * 100,
      repairSuccessRate: detail.repairSuccessRate * 100,
      creditLevel: detail.creditLevel,
      qualityScore: detail.qualityScore,
      returnAmount: detail.returnAmount,
      returnRate: detail.returnRate * 100,
      unsettledAmount: detail.unsettledAmount,
      prepaymentBalance: detail.prepaymentBalance,
      onTimeDeliveryRate: detail.onTimeDeliveryRate * 100,
      responseSpeedScore: detail.responseSpeedScore,
      serviceAttitudeScore: detail.serviceAttitudeScore,
      overallScore: detail.overallScore,
    });
    setModalVisible(true);
  };

  // 保存更新
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      // 将百分比转换回小数
      const submitData = {
        ...values,
        defectRate: values.defectRate / 100,
        repairRate: values.repairRate / 100,
        repairSuccessRate: values.repairSuccessRate / 100,
        returnRate: values.returnRate / 100,
        onTimeDeliveryRate: values.onTimeDeliveryRate / 100,
      };

      setUpdating(true);
      const response = await updateSupplierArchivesSummary(supplierCode, submitData);
      
      const result = handleApiResponse(response, '更新成功', '更新失败');
      
      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        fetchDetail(); // 重新获取数据
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      if (error.errorFields) {
        message.error('请检查表单填写');
      } else {
        logError('更新汇总数据', error);
        message.error(getErrorMessage(error, '更新失败'));
      }
    } finally {
      setUpdating(false);
    }
  };

  // 格式化数字
  const formatNumber = (value: number, precision = 2): string => {
    if (value === null || value === undefined) return '-';
    return value.toFixed(precision);
  };

  // 格式化百分比
  const formatPercent = (value: number): string => {
    if (value === null || value === undefined) return '-';
    return `${(value * 100).toFixed(2)}%`;
  };

  // 格式化金额
  const formatAmount = (value: number): string => {
    if (value === null || value === undefined) return '-';
    return `¥${value.toLocaleString()}`;
  };

  // 获取评分颜色
  const getScoreColor = (score: number): string => {
    if (score >= 90) return '#52c41a';
    if (score >= 80) return '#faad14';
    if (score >= 70) return '#fa8c16';
    if (score >= 60) return '#ff7875';
    return '#f5222d';
  };

  // 获取评级文本
  const getScoreText = (score: number): string => {
    if (score >= 90) return '优秀';
    if (score >= 80) return '良好';
    if (score >= 70) return '一般';
    if (score >= 60) return '较差';
    return '很差';
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!detail) {
    return (
      <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
        暂无汇总数据
      </div>
    );
  }

  return (
    <div>
      {/* 核心指标卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="采购总金额"
              value={detail.totalPurchaseAmount}
              precision={2}
              prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
              formatter={(value) => `¥${value?.toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="采购总数量"
              value={detail.totalPurchaseQuantity}
              prefix={<ShoppingCartOutlined style={{ color: '#52c41a' }} />}
              formatter={(value) => value?.toLocaleString()}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="质量评分"
              value={detail.qualityScore}
              suffix="分"
              prefix={<SafetyOutlined style={{ color: getScoreColor(detail.qualityScore) }} />}
              valueStyle={{ color: getScoreColor(detail.qualityScore) }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="综合评分"
              value={detail.overallScore}
              suffix="分"
              prefix={<TrophyOutlined style={{ color: getScoreColor(detail.overallScore) }} />}
              valueStyle={{ color: getScoreColor(detail.overallScore) }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细信息 */}
      <Card
        title="供应商档案汇总详情"
        extra={
          isSuperAdmin && (
            <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
              编辑数据
            </Button>
          )
        }
      >
        <Row gutter={24}>
          {/* 基本信息 */}
          <Col span={12}>
            <Card size="small" title="基本信息" style={{ marginBottom: 16 }}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="供应商编码">{detail.supplierCode}</Descriptions.Item>
                <Descriptions.Item label="供应商名称">{detail.supplierName}</Descriptions.Item>
                <Descriptions.Item label="信用等级">
                  <Tag color={getCreditLevelColor(detail.creditLevel)}>
                    {CreditLevelOptions.find(opt => opt.value === detail.creditLevel)?.label || detail.creditLevel}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="合作开始时间">
                  {detail.cooperationStartDate ? dayjs(detail.cooperationStartDate).format('YYYY-MM-DD') : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="最后交易时间">
                  {detail.lastTransactionDate ? dayjs(detail.lastTransactionDate).format('YYYY-MM-DD') : '-'}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          {/* 采购数据 */}
          <Col span={12}>
            <Card size="small" title="采购数据" style={{ marginBottom: 16 }}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="采购总金额">{formatAmount(detail.totalPurchaseAmount)}</Descriptions.Item>
                <Descriptions.Item label="采购总数量">{detail.totalPurchaseQuantity?.toLocaleString()}</Descriptions.Item>
                <Descriptions.Item label="平均单价">{formatAmount(detail.averageUnitPrice)}</Descriptions.Item>
                <Descriptions.Item label="订单完成率">{formatPercent(detail.orderCompletionRate)}</Descriptions.Item>
                <Descriptions.Item label="付款周期">{detail.paymentCycle}天</Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          {/* 质量指标 */}
          <Col span={12}>
            <Card size="small" title="质量指标" style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 12 }}>
                <div style={{ marginBottom: 8 }}>瑕疵率: {formatPercent(detail.defectRate)}</div>
                <Progress 
                  percent={detail.defectRate * 100} 
                  strokeColor={detail.defectRate > 0.05 ? '#ff4d4f' : '#52c41a'}
                  showInfo={false}
                />
              </div>
              <div style={{ marginBottom: 12 }}>
                <div style={{ marginBottom: 8 }}>退货率: {formatPercent(detail.returnRate)}</div>
                <Progress 
                  percent={detail.returnRate * 100} 
                  strokeColor={detail.returnRate > 0.03 ? '#ff4d4f' : '#52c41a'}
                  showInfo={false}
                />
              </div>
              <div>
                <div style={{ marginBottom: 8 }}>质量评分: {detail.qualityScore}分 ({getScoreText(detail.qualityScore)})</div>
                <Progress 
                  percent={detail.qualityScore} 
                  strokeColor={getScoreColor(detail.qualityScore)}
                  showInfo={false}
                />
              </div>
            </Card>
          </Col>

          {/* 服务指标 */}
          <Col span={12}>
            <Card size="small" title="服务指标" style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 12 }}>
                <div style={{ marginBottom: 8 }}>准时交付率: {formatPercent(detail.onTimeDeliveryRate)}</div>
                <Progress 
                  percent={detail.onTimeDeliveryRate * 100} 
                  strokeColor={detail.onTimeDeliveryRate > 0.9 ? '#52c41a' : detail.onTimeDeliveryRate > 0.8 ? '#faad14' : '#ff4d4f'}
                  showInfo={false}
                />
              </div>
              <div style={{ marginBottom: 12 }}>
                <div style={{ marginBottom: 8 }}>响应速度: {detail.responseSpeedScore}分</div>
                <Progress 
                  percent={detail.responseSpeedScore} 
                  strokeColor={getScoreColor(detail.responseSpeedScore)}
                  showInfo={false}
                />
              </div>
              <div>
                <div style={{ marginBottom: 8 }}>服务态度: {detail.serviceAttitudeScore}分</div>
                <Progress 
                  percent={detail.serviceAttitudeScore} 
                  strokeColor={getScoreColor(detail.serviceAttitudeScore)}
                  showInfo={false}
                />
              </div>
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 财务状况 */}
        <Row gutter={24}>
          <Col span={8}>
            <Statistic
              title="未结算金额"
              value={detail.unsettledAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: detail.unsettledAmount > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="预付款余额"
              value={detail.prepaymentBalance}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="平均付款周期"
              value={detail.averagePaymentPeriod}
              suffix="天"
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑供应商汇总数据"
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={800}
        confirmLoading={updating}
        destroyOnClose
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="totalPurchaseAmount"
                label="采购总金额"
                rules={[{ required: true, message: '请输入采购总金额' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  placeholder="请输入采购总金额"
                  style={{ width: '100%' }}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value!.replace(/\¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="creditLevel"
                label="信用等级"
                rules={[{ required: true, message: '请选择信用等级' }]}
              >
                <Select placeholder="请选择信用等级">
                  {CreditLevelOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      <Tag color={option.color} style={{ margin: 0 }}>
                        {option.label}
                      </Tag>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="defectRate"
                label="瑕疵率(%)"
                rules={[{ required: true, message: '请输入瑕疵率' }]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={2}
                  placeholder="请输入瑕疵率"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="repairRate"
                label="维修率(%)"
                rules={[{ required: true, message: '请输入维修率' }]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={2}
                  placeholder="请输入维修率"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="returnRate"
                label="退货率(%)"
                rules={[{ required: true, message: '请输入退货率' }]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={2}
                  placeholder="请输入退货率"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="qualityScore"
                label="质量评分"
                rules={[{ required: true, message: '请输入质量评分' }]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={0}
                  placeholder="请输入质量评分"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="responseSpeedScore"
                label="响应速度评分"
                rules={[{ required: true, message: '请输入响应速度评分' }]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={0}
                  placeholder="请输入响应速度评分"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="serviceAttitudeScore"
                label="服务态度评分"
                rules={[{ required: true, message: '请输入服务态度评分' }]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={0}
                  placeholder="请输入服务态度评分"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="unsettledAmount"
                label="未结算金额"
                rules={[{ required: true, message: '请输入未结算金额' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  placeholder="请输入未结算金额"
                  style={{ width: '100%' }}
                  formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => value!.replace(/\¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="overallScore"
                label="综合评分"
                rules={[{ required: true, message: '请输入综合评分' }]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={0}
                  placeholder="请输入综合评分"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default SupplierArchivesSummaryDetail;
