.container {
  padding: 4px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.toolbar {
  margin-bottom: 16px;
}

.toolbar .ant-row {
  align-items: center;
}

.toolbar .ant-input-search {
  max-width: 400px;
}

.toolbar .ant-btn {
  height: 40px;
}

.toolbar .ant-upload {
  display: inline-block;
}

.toolbar .ant-upload .ant-btn {
  height: 40px;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #f0f5ff;
  font-weight: 600;
  color: #262626;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-number,
.ant-select-selector {
  border-radius: 4px;
}

.ant-input:focus,
.ant-input-focused,
.ant-input-number:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.ant-btn-danger {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.ant-btn-danger:hover,
.ant-btn-danger:focus {
  color: #ff7875;
  border-color: #ff7875;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .toolbar .ant-row {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar .ant-input-search {
    max-width: 100%;
  }
}
