import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  Image,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  EyeOutlined,
  UserOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import {
  getCompanyList,
  createCompany,
  updateCompany,
  deleteCompany,
  getCompanyDetail,
} from '@/api/CompanyApi';
import type { CompanyProps, CompanyDetailProps } from '@/types/company';
import UserSearchSelector from '@/components/UserSearchSelector';
import UserMultipleSelector from '@/components/UserMultipleSelector';
import SimpleImageUpload from '@/components/ImageUpload/SimpleImageUpload';
import { UPLOAD_FOLDERS } from '@/api/fileUpload';
import { handleApiResponse, getErrorMessage, logError } from '@/utils/errorHandler';
import useStyles from './CompanyManagementStyles';

const { Search } = Input;

interface CompanyFormData {
  code: string;
  name: string;
  address: string;
  socialCreditCode: string;
  businessLicenseUrl: string;
  managerCode: string;
}

const CompanyManagement: React.FC = () => {
  const { styles } = useStyles();
  const [companies, setCompanies] = useState<CompanyProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCompany, setEditingCompany] = useState<CompanyProps | null>(null);
  const [viewingCompany, setViewingCompany] = useState<CompanyDetailProps | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');
  const [form] = Form.useForm<CompanyFormData>();

  // 获取公司列表
  const fetchCompanies = async (page = 1, pageSize = 10, search = '') => {
    setLoading(true);
    try {
      const response = await getCompanyList({
        page,
        pageSize,
        search: search.trim() || undefined,
      });

      if (response.code === 200) {
        setCompanies(response.data.companies);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取公司列表失败');
      }
    } catch (error: any) {
      logError('获取公司列表', error);
      message.error(getErrorMessage(error, '获取公司列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchCompanies();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
    fetchCompanies(1, pagination.pageSize, value);
  };

  // 分页处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));
    fetchCompanies(current, pageSize, searchText);
  };

  // 刷新列表
  const handleRefresh = () => {
    fetchCompanies(pagination.current, pagination.pageSize, searchText);
  };

  // 新增公司
  const handleAdd = () => {
    setEditingCompany(null);
    setViewingCompany(null);
    setModalMode('create');
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑公司
  const handleEdit = async (company: CompanyProps) => {
    try {
      setLoading(true);
      const response = await getCompanyDetail(company.code);

      if (response.code === 200) {
        const detail = response.data;
        setEditingCompany(company);
        setViewingCompany(detail);
        setModalMode('edit');

        form.setFieldsValue({
          code: detail.code,
          name: detail.name,
          address: detail.address || '',
          socialCreditCode: detail.socialCreditCode || '',
          businessLicenseUrl: detail.businessLicenseUrl || '',
          managerCode: detail.managerCode || '',
        });
        setModalVisible(true);
      } else {
        message.error(response.message || '获取公司详情失败');
      }
    } catch (error: any) {
      logError('获取公司详情', error);
      message.error(getErrorMessage(error, '获取公司详情失败'));
    } finally {
      setLoading(false);
    }
  };

  // 查看详情
  const handleView = async (company: CompanyProps) => {
    try {
      setLoading(true);
      const response = await getCompanyDetail(company.code);

      if (response.code === 200) {
        const detail = response.data;
        setEditingCompany(company);
        setViewingCompany(detail);
        setModalMode('view');

        form.setFieldsValue({
          code: detail.code,
          name: detail.name,
          address: detail.address || '',
          socialCreditCode: detail.socialCreditCode || '',
          businessLicenseUrl: detail.businessLicenseUrl || '',
          managerCode: detail.managerCode || '',
        });
        setModalVisible(true);
      } else {
        message.error(response.message || '获取公司详情失败');
      }
    } catch (error: any) {
      logError('获取公司详情', error);
      message.error(getErrorMessage(error, '获取公司详情失败'));
    } finally {
      setLoading(false);
    }
  };

  // 删除公司
  const handleDelete = async (companyCode: string) => {
    try {
      const response = await deleteCompany(companyCode);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除公司', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 保存公司
  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      if (editingCompany) {
        // 更新公司
        const { code, ...updateData } = values;
        const response = await updateCompany(editingCompany.code, updateData);
        const result = handleApiResponse(response, '更新成功', '更新失败');

        if (result.success) {
          message.success(result.message);
          setModalVisible(false);
          handleRefresh();
        } else {
          message.error(result.message);
        }
      } else {
        // 新增公司
        const response = await createCompany(values);
        const result = handleApiResponse(response, '创建成功', '创建失败');

        if (result.success) {
          message.success(result.message);
          setModalVisible(false);
          handleRefresh();
        } else {
          message.error(result.message);
        }
      }
    } catch (error: any) {
      logError('保存公司', error);
      message.error(getErrorMessage(error, '保存失败'));
    }
  };

  // 表格列定义
  const columns: ColumnsType<CompanyProps> = [
    {
      title: '公司编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span>,
    },
    {
      title: '公司名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 250,
      ellipsis: true,
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'socialCreditCode',
      key: 'socialCreditCode',
      width: 180,
      render: (text) => text || '-',
    },
    {
      title: '营业执照',
      dataIndex: 'businessLicenseUrl',
      key: 'businessLicenseUrl',
      width: 120,
      align: 'center',
      render: (url) =>
        url ? (
          <Image
            src={url}
            alt="营业执照"
            width={40}
            height={40}
            style={{ objectFit: 'cover', borderRadius: '4px' }}
            preview={{
              mask: <EyeOutlined style={{ color: 'white' }} />,
            }}
          />
        ) : (
          <span style={{ color: '#999' }}>-</span>
        ),
    },
    {
      title: '管理员',
      dataIndex: 'managerName',
      key: 'managerName',
      width: 120,
      render: (text) => text || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个公司吗？"
            onConfirm={() => handleDelete(record.code)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <Search
                placeholder="搜索公司编码或名称"
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                style={{ maxWidth: 400 }}
              />
            </Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增公司
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={companies}
          rowKey="code"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 公司编辑模态框 */}
      <Modal
        title={
          modalMode === 'view' ? '查看公司详情' : modalMode === 'edit' ? '编辑公司' : '新增公司'
        }
        open={modalVisible}
        onOk={modalMode === 'view' ? undefined : handleSave}
        onCancel={() => setModalVisible(false)}
        width={900}
        destroyOnClose
        footer={
          modalMode === 'view' ? (
            <Button onClick={() => setModalVisible(false)}>关闭</Button>
          ) : undefined
        }
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="公司编码"
                rules={[
                  { required: true, message: '请输入公司编码' },
                  { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和横线' },
                ]}
              >
                <Input
                  placeholder="请输入公司编码"
                  disabled={modalMode === 'edit' || modalMode === 'view'}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="公司名称"
                rules={[{ required: true, message: '请输入公司名称' }]}
              >
                <Input placeholder="请输入公司名称" disabled={modalMode === 'view'} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="address" label="公司地址">
            <Input.TextArea placeholder="请输入公司地址" rows={2} disabled={modalMode === 'view'} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="socialCreditCode"
                label="统一社会信用代码"
                rules={[{ len: 18, message: '统一社会信用代码应为18位' }]}
              >
                <Input placeholder="请输入18位统一社会信用代码" disabled={modalMode === 'view'} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="managerCode" label="管理员">
                <UserSearchSelector placeholder="请选择管理员" disabled={modalMode === 'view'} />
              </Form.Item>
            </Col>
          </Row>

          {modalMode === 'view' ? (
            // 查看模式：只有在有图片时才显示
            viewingCompany?.businessLicenseUrl && (
              <Form.Item label="营业执照">
                <div
                  style={{
                    padding: '12px',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '6px',
                    border: '1px solid #d9d9d9',
                    textAlign: 'center',
                  }}
                >
                  <Image
                    src={viewingCompany.businessLicenseUrl}
                    alt="营业执照"
                    style={{
                      maxWidth: '100%',
                      maxHeight: '300px',
                      objectFit: 'contain',
                      borderRadius: '6px',
                    }}
                    preview={{
                      mask: (
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            gap: '8px',
                            color: 'white',
                          }}
                        >
                          <EyeOutlined style={{ fontSize: '24px' }} />
                          <div>点击查看大图</div>
                        </div>
                      ),
                    }}
                  />
                </div>
              </Form.Item>
            )
          ) : (
            // 新增/编辑模式：显示上传组件
            <Form.Item
              name="businessLicenseUrl"
              label="营业执照"
              rules={[{ required: true, message: '请上传营业执照' }]}
            >
              <SimpleImageUpload folder={UPLOAD_FOLDERS.DOCUMENT} enablePaste={true} />
            </Form.Item>
          )}

          {modalMode === 'view' && viewingCompany && (
            <Form.Item label="员工详情">
              <div
                style={{
                  padding: '12px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '6px',
                  border: '1px solid #d9d9d9',
                }}
              >
                {viewingCompany.employees && viewingCompany.employees.length > 0 ? (
                  <Space wrap>
                    {viewingCompany.employees.map((emp) => (
                      <div
                        key={emp.code}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          padding: '6px 12px',
                          backgroundColor: emp.isCompanyAdmin ? '#e6f7ff' : '#fff',
                          borderRadius: '6px',
                          border: emp.isCompanyAdmin ? '1px solid #91d5ff' : '1px solid #d9d9d9',
                          position: 'relative',
                        }}
                      >
                        <TeamOutlined
                          style={{ color: emp.isCompanyAdmin ? '#1890ff' : '#8c8c8c' }}
                        />
                        <span style={{ fontWeight: emp.isCompanyAdmin ? 600 : 500 }}>
                          {emp.name}
                        </span>
                        <span style={{ color: '#8c8c8c', fontSize: '12px' }}>({emp.code})</span>
                        {emp.isCompanyAdmin && (
                          <span
                            style={{
                              fontSize: '10px',
                              color: '#1890ff',
                              backgroundColor: '#fff',
                              padding: '1px 4px',
                              borderRadius: '2px',
                              border: '1px solid #91d5ff',
                              marginLeft: '4px',
                            }}
                          >
                            管理员
                          </span>
                        )}
                      </div>
                    ))}
                  </Space>
                ) : (
                  <span style={{ color: '#8c8c8c' }}>暂无员工</span>
                )}
              </div>
            </Form.Item>
          )}

          {modalMode === 'view' && viewingCompany && (
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="管理员信息">
                  <div
                    style={{
                      padding: '8px 12px',
                      backgroundColor: '#f0f9ff',
                      borderRadius: '6px',
                      border: '1px solid #91d5ff',
                    }}
                  >
                    {viewingCompany.managerName ? (
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <UserOutlined style={{ color: '#1890ff' }} />
                        <span style={{ fontWeight: 500 }}>{viewingCompany.managerName}</span>
                        <span style={{ color: '#8c8c8c', fontSize: '12px' }}>
                          ({viewingCompany.managerCode})
                        </span>
                      </div>
                    ) : (
                      <span style={{ color: '#8c8c8c' }}>暂无管理员</span>
                    )}
                  </div>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="创建时间">
                  <div
                    style={{
                      padding: '8px 12px',
                      backgroundColor: '#f6ffed',
                      borderRadius: '6px',
                      border: '1px solid #b7eb8f',
                    }}
                  >
                    {new Date(viewingCompany.createdAt).toLocaleString()}
                  </div>
                </Form.Item>
              </Col>
            </Row>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default CompanyManagement;
