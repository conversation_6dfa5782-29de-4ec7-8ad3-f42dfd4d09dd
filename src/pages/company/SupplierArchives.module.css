.container {
  padding: 4px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.supplierInfo {
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 4px;
}

.emptyState {
  text-align: center;
  padding: 60px 0;
  color: #999;
}

.emptyState .icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.backButton {
  padding: 4px 8px;
  margin-right: 16px;
}

.title {
  margin: 0;
  display: flex;
  align-items: center;
}

.titleIcon {
  margin-right: 8px;
  color: #1890ff;
}

.selectorContainer {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.selectorLabel {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #262626;
}

.loadingContainer {
  text-align: center;
  padding: 40px 0;
}

/* Tab样式优化 */
.container :global(.ant-tabs-tab) {
  padding: 12px 16px;
}

.container :global(.ant-tabs-tab-disabled) {
  color: #d9d9d9;
  cursor: not-allowed;
}

.container :global(.ant-tabs-tab-disabled .anticon) {
  color: #d9d9d9;
}

/* 卡片样式优化 */
.container :global(.ant-card-small > .ant-card-head) {
  min-height: 48px;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
}

.container :global(.ant-card-small > .ant-card-body) {
  padding: 16px;
}

/* 统计卡片样式 */
.container :global(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.container :global(.ant-statistic-content) {
  font-size: 20px;
  font-weight: 600;
}

/* 进度条样式 */
.container :global(.ant-progress-line) {
  margin-bottom: 8px;
}

.container :global(.ant-progress-text) {
  font-size: 12px;
}

/* 描述列表样式 */
.container :global(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #666;
}

.container :global(.ant-descriptions-item-content) {
  color: #262626;
}

/* 表格样式优化 */
.container :global(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

.container :global(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 标签样式 */
.container :global(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 按钮样式 */
.container :global(.ant-btn-link) {
  padding: 0;
  height: auto;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .title {
    font-size: 18px;
  }

  .container :global(.ant-tabs-tab) {
    padding: 8px 12px;
    font-size: 14px;
  }
}
