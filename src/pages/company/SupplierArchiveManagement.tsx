import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  InputNumber,
  DatePicker,
  Tag,
  Tabs,
  Select,
  Tooltip,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  FileExcelOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import {
  getSupplierArchivesList,
  createSupplierArchiveDetail,
  updateSupplierArchiveDetail,
  deleteSupplierArchiveDetail,
  getSupplierArchiveDetail,
  exportSupplierArchivesExcel,
} from '@/api/SupplierArchivesApi';
import {
  SupplierArchiveType,
  SupplierArchiveTypeLabels,
  type Detail,
} from '@/types/supplierArchives';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

interface SupplierArchiveManagementProps {
  supplierCode: string;
}

const SupplierArchiveManagement: React.FC<SupplierArchiveManagementProps> = ({ supplierCode }) => {
  const [form] = Form.useForm();
  const [archives, setArchives] = useState<Detail[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingArchive, setEditingArchive] = useState<Detail | null>(null);
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState<string>('all');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().startOf('month'),
    dayjs().endOf('month'),
  ]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [exportLoading, setExportLoading] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取档案列表
  const fetchArchives = async (
    page = 1,
    pageSize = 10,
    search = '',
    type?: SupplierArchiveType,
  ) => {
    if (!supplierCode) return;

    setLoading(true);
    try {
      const [startTime, endTime] = dateRange;
      const response = await getSupplierArchivesList({
        page,
        pageSize,
        supplierCode,
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        search: search.trim() || undefined,
        type,
      });

      if (response.code === 200) {
        setArchives(response.data.details);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取档案列表失败');
      }
    } catch (error: any) {
      logError('获取档案列表', error);
      message.error(getErrorMessage(error, '获取档案列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    if (supplierCode) {
      fetchArchives();
    }
  }, [supplierCode, dateRange]);

  // Tab切换时重新加载数据
  useEffect(() => {
    if (supplierCode) {
      const type = activeTab === 'all' ? undefined : (activeTab as SupplierArchiveType);
      fetchArchives(1, pagination.pageSize, searchText, type);
    }
  }, [activeTab]);

  // 处理表格变化
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const type = activeTab === 'all' ? undefined : (activeTab as SupplierArchiveType);
    fetchArchives(paginationConfig.current, paginationConfig.pageSize, searchText, type);
  };

  // 搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    const type = activeTab === 'all' ? undefined : (activeTab as SupplierArchiveType);
    fetchArchives(1, pagination.pageSize, value, type);
  };

  // 刷新
  const handleRefresh = () => {
    const type = activeTab === 'all' ? undefined : (activeTab as SupplierArchiveType);
    fetchArchives(pagination.current, pagination.pageSize, searchText, type);
  };

  // 日期范围变化
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange([dates[0], dates[1]]);
    }
  };

  // 新增档案
  const handleAdd = () => {
    setEditingArchive(null);
    form.resetFields();
    form.setFieldsValue({
      supplierCode,
      type: SupplierArchiveType.PURCHASE,
    });
    setModalVisible(true);
  };

  // 编辑档案
  const handleEdit = async (archive: Detail) => {
    try {
      const response = await getSupplierArchiveDetail(archive.id);
      if (response.code === 200) {
        setEditingArchive(archive);
        form.setFieldsValue({
          ...response.data,
        });
        setModalVisible(true);
      } else {
        message.error(response.message || '获取档案详情失败');
      }
    } catch (error: any) {
      logError('获取档案详情', error);
      message.error(getErrorMessage(error, '获取档案详情失败'));
    }
  };

  // 删除档案
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteSupplierArchiveDetail(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除档案', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 保存档案
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      let response;

      if (editingArchive) {
        // 编辑模式
        response = await updateSupplierArchiveDetail(editingArchive.id, values);
      } else {
        // 新增模式 - 确保包含supplierCode
        const submitData = {
          ...values,
          supplierCode,
        };
        response = await createSupplierArchiveDetail(submitData);
      }

      const result = handleApiResponse(
        response,
        editingArchive ? '更新成功' : '创建成功',
        editingArchive ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      if (error.errorFields) {
        message.error('请检查表单填写');
      } else {
        logError('保存档案', error);
        message.error(getErrorMessage(error, '保存失败'));
      }
    }
  };

  // 导出Excel
  const handleExportExcel = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要导出的档案记录');
      return;
    }

    setExportLoading(true);
    try {
      const [startTime, endTime] = dateRange;
      const type = activeTab === 'all' ? undefined : (activeTab as SupplierArchiveType);
      const detailIds = selectedRowKeys.map((key) => key.toString());

      const blob = await exportSupplierArchivesExcel({
        supplierCode,
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        type,
        detailIds,
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `供应商档案_${supplierCode}_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
      setSelectedRowKeys([]);
    } catch (error: any) {
      logError('导出Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    } finally {
      setExportLoading(false);
    }
  };

  // 行选择配置
  const rowSelection: TableRowSelection<Detail> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16} justify="space-between" align="middle">
          <Col flex="auto">
            <Space>
              <Search
                placeholder="搜索档案信息"
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                style={{ width: 300 }}
              />
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                format="YYYY-MM-DD"
                size="large"
              />
            </Space>
          </Col>
          <Col>
            <Space>
              <Tooltip title="导出选中的档案记录">
                <Button
                  type="default"
                  icon={<FileExcelOutlined />}
                  onClick={handleExportExcel}
                  loading={exportLoading}
                  disabled={selectedRowKeys.length === 0}
                >
                  导出Excel
                </Button>
              </Tooltip>
              <Button
                type="default"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                新增档案
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        style={{ marginBottom: 16 }}
        items={[
          { key: 'all', label: '全部' },
          { key: SupplierArchiveType.PURCHASE, label: '采购' },
          { key: SupplierArchiveType.ARRIVAL, label: '到货' },
          { key: SupplierArchiveType.REPAIR_SEND, label: '返厂修复' },
          { key: SupplierArchiveType.REPAIR_ARRIVAL, label: '修复到货' },
        ]}
      />

      <Table
        columns={getColumns()}
        dataSource={archives}
        rowKey="id"
        loading={loading}
        rowSelection={rowSelection}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        onChange={handleTableChange}
        scroll={{ x: 1400 }}
      />

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingArchive ? '编辑档案' : '新增档案'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="档案类型"
                rules={[{ required: true, message: '请选择档案类型' }]}
              >
                <Select placeholder="请选择档案类型">
                  {Object.entries(SupplierArchiveTypeLabels).map(([value, label]) => (
                    <Select.Option key={value} value={value}>
                      {label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="remark" label="备注">
                <Input.TextArea rows={2} placeholder="请输入备注" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const currentType = getFieldValue('type');
              return renderFormFieldsByType(currentType);
            }}
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );

  // 根据档案类型渲染不同的表单字段
  function renderFormFieldsByType(type: SupplierArchiveType) {
    switch (type) {
      case SupplierArchiveType.PURCHASE:
        return (
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="totalQuantity"
                label="商品总数量"
                rules={[{ required: true, message: '请输入商品总数量' }]}
              >
                <InputNumber min={0} placeholder="请输入商品总数量" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="totalAmount"
                label="商品总金额"
                rules={[{ required: true, message: '请输入商品总金额' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  placeholder="请输入商品总金额"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="purchaseOrderId" label="采购订单ID">
                <Input placeholder="请输入采购订单ID" />
              </Form.Item>
            </Col>
          </Row>
        );

      case SupplierArchiveType.ARRIVAL:
        return (
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="actualArrivalQuantity"
                label="实际到货数量"
                rules={[{ required: true, message: '请输入实际到货数量' }]}
              >
                <InputNumber min={0} placeholder="请输入实际到货数量" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="defectQuantity"
                label="瑕疵数量"
                rules={[{ required: true, message: '请输入瑕疵数量' }]}
              >
                <InputNumber min={0} placeholder="请输入瑕疵数量" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="logisticsOrderId" label="物流订单ID">
                <Input placeholder="请输入物流订单ID" />
              </Form.Item>
            </Col>
          </Row>
        );

      case SupplierArchiveType.REPAIR_SEND:
        return (
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="repairQuantity"
                label="送修商品数量"
                rules={[{ required: true, message: '请输入送修商品数量' }]}
              >
                <InputNumber min={0} placeholder="请输入送修商品数量" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="logisticsOrderId" label="物流订单号">
                <Input placeholder="请输入物流订单号" />
              </Form.Item>
            </Col>
          </Row>
        );

      case SupplierArchiveType.REPAIR_ARRIVAL:
        return (
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="actualRepairedQuantity"
                label="实际修复商品数"
                rules={[{ required: true, message: '请输入实际修复商品数' }]}
              >
                <InputNumber min={0} placeholder="请输入实际修复商品数" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="totalArrivalQuantity"
                label="总到货数"
                rules={[{ required: true, message: '请输入总到货数' }]}
              >
                <InputNumber min={0} placeholder="请输入总到货数" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="logisticsOrderId" label="物流订单号">
                <Input placeholder="请输入物流订单号" />
              </Form.Item>
            </Col>
          </Row>
        );

      default:
        return null;
    }
  }

  // 获取表格列配置
  function getColumns(): ColumnsType<Detail> {
    const baseColumns: ColumnsType<Detail> = [
      {
        title: '档案类型',
        dataIndex: 'type',
        key: 'type',
        width: 100,
        render: (type: SupplierArchiveType) => (
          <Tag color={getTypeColor(type)}>{SupplierArchiveTypeLabels[type]}</Tag>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 160,
        render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        width: 200,
        ellipsis: true,
        render: (text: string) => text || '-',
      },
      {
        title: '操作',
        key: 'action',
        width: 120,
        fixed: 'right',
        render: (_, record) => (
          <Space size="small">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这条档案记录吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        ),
      },
    ];

    // 根据当前Tab添加特定字段列
    const typeSpecificColumns = getTypeSpecificColumns();

    // 在基础列的倒数第二个位置插入类型特定列
    const result = [...baseColumns];
    result.splice(-2, 0, ...typeSpecificColumns);

    return result;
  }

  // 获取类型特定的列
  function getTypeSpecificColumns(): ColumnsType<Detail> {
    if (activeTab === 'all') {
      // 全部Tab显示所有可能的字段
      return [
        {
          title: '商品总数量',
          dataIndex: 'totalQuantity',
          key: 'totalQuantity',
          width: 120,
          render: (value: number) => value || '-',
        },
        {
          title: '商品总金额',
          dataIndex: 'totalAmount',
          key: 'totalAmount',
          width: 120,
          render: (value: number) => (value ? `¥${value.toFixed(2)}` : '-'),
        },
        {
          title: '采购订单ID',
          dataIndex: 'purchaseOrderId',
          key: 'purchaseOrderId',
          width: 150,
          ellipsis: true,
          render: (text: string) => text || '-',
        },
        {
          title: '实际到货数量',
          dataIndex: 'actualArrivalQuantity',
          key: 'actualArrivalQuantity',
          width: 120,
          render: (value: number) => value || '-',
        },
        {
          title: '瑕疵数量',
          dataIndex: 'defectQuantity',
          key: 'defectQuantity',
          width: 100,
          render: (value: number) => value || '-',
        },
        {
          title: '物流订单ID',
          dataIndex: 'logisticsOrderId',
          key: 'logisticsOrderId',
          width: 150,
          ellipsis: true,
          render: (text: string) => text || '-',
        },
      ];
    }

    switch (activeTab as SupplierArchiveType) {
      case SupplierArchiveType.PURCHASE:
        return [
          {
            title: '商品总数量',
            dataIndex: 'totalQuantity',
            key: 'totalQuantity',
            width: 120,
            render: (value: number) => value || '-',
          },
          {
            title: '商品总金额',
            dataIndex: 'totalAmount',
            key: 'totalAmount',
            width: 120,
            render: (value: number) => (value ? `¥${value.toFixed(2)}` : '-'),
          },
          {
            title: '采购订单ID',
            dataIndex: 'purchaseOrderId',
            key: 'purchaseOrderId',
            width: 200,
            ellipsis: true,
            render: (text: string) => text || '-',
          },
        ];

      case SupplierArchiveType.ARRIVAL:
        return [
          {
            title: '实际到货数量',
            dataIndex: 'actualArrivalQuantity',
            key: 'actualArrivalQuantity',
            width: 120,
            render: (value: number) => value || '-',
          },
          {
            title: '瑕疵数量',
            dataIndex: 'defectQuantity',
            key: 'defectQuantity',
            width: 100,
            render: (value: number) => value || '-',
          },
          {
            title: '物流订单ID',
            dataIndex: 'logisticsOrderId',
            key: 'logisticsOrderId',
            width: 200,
            ellipsis: true,
            render: (text: string) => text || '-',
          },
        ];

      case SupplierArchiveType.REPAIR_SEND:
        return [
          {
            title: '送修商品数量',
            dataIndex: 'repairQuantity',
            key: 'repairQuantity',
            width: 120,
            render: (value: number) => value || '-',
          },
          {
            title: '物流订单号',
            dataIndex: 'logisticsOrderId',
            key: 'logisticsOrderId',
            width: 200,
            ellipsis: true,
            render: (text: string) => text || '-',
          },
        ];

      case SupplierArchiveType.REPAIR_ARRIVAL:
        return [
          {
            title: '实际修复商品数',
            dataIndex: 'actualRepairedQuantity',
            key: 'actualRepairedQuantity',
            width: 140,
            render: (value: number) => value || '-',
          },
          {
            title: '总到货数',
            dataIndex: 'totalArrivalQuantity',
            key: 'totalArrivalQuantity',
            width: 100,
            render: (value: number) => value || '-',
          },
          {
            title: '物流订单号',
            dataIndex: 'logisticsOrderId',
            key: 'logisticsOrderId',
            width: 200,
            ellipsis: true,
            render: (text: string) => text || '-',
          },
        ];

      default:
        return [];
    }
  }

  // 获取类型颜色
  function getTypeColor(type: SupplierArchiveType): string {
    switch (type) {
      case SupplierArchiveType.PURCHASE:
        return 'blue';
      case SupplierArchiveType.ARRIVAL:
        return 'green';
      case SupplierArchiveType.REPAIR_SEND:
        return 'orange';
      case SupplierArchiveType.REPAIR_ARRIVAL:
        return 'purple';
      default:
        return 'default';
    }
  }
};

export default SupplierArchiveManagement;
