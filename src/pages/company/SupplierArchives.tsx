import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, Button, message, Row, Col, Typography, Descriptions, Spin, Tabs } from 'antd';
import {
  ArrowLeftOutlined,
  FileTextOutlined,
  BarChartOutlined,
  UnorderedListOutlined,
  DashboardOutlined,
} from '@ant-design/icons';
import SupplierSearchSelector from '@/components/SupplierSearchSelector/SupplierSearchSelector';
import { getSupplierDetail } from '@/api/SupplierApi';
import type { SupplierListProps } from '@/types/supplier';
import { logError, getErrorMessage } from '@/utils/errorHandler';
import SupplierArchiveManagement from './SupplierArchiveManagement';
import SupplierArchivesSummaryList from './SupplierArchivesSummaryList';
import SupplierArchivesSummaryDetail from './SupplierArchivesSummaryDetail';
import styles from './SupplierArchives.module.css';

const { Title } = Typography;
const { TabPane } = Tabs;

const SupplierArchives: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedSupplierCode, setSelectedSupplierCode] = useState<string>('');
  const [supplierDetail, setSupplierDetail] = useState<SupplierListProps | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('summary-list');

  // 从URL参数获取supplierCode和tab
  useEffect(() => {
    const supplierCode = searchParams.get('supplierCode');
    const tab = searchParams.get('tab') || 'summary-list';

    setActiveTab(tab);

    if (supplierCode) {
      setSelectedSupplierCode(supplierCode);
      loadSupplierDetail(supplierCode);
    }
  }, [searchParams]);

  // 加载供应商详情
  const loadSupplierDetail = async (supplierCode: string) => {
    if (!supplierCode) {
      setSupplierDetail(null);
      return;
    }

    setLoading(true);
    try {
      const response = await getSupplierDetail(supplierCode);
      if (response.code === 200 && response.data) {
        setSupplierDetail(response.data);
      } else {
        message.error(response.message || '获取供应商详情失败');
        setSupplierDetail(null);
      }
    } catch (error: any) {
      logError('获取供应商详情', error);
      message.error(getErrorMessage(error, '获取供应商详情失败'));
      setSupplierDetail(null);
    } finally {
      setLoading(false);
    }
  };

  // 处理供应商选择
  const handleSupplierChange = (supplierCode: string, supplier: SupplierListProps | null) => {
    setSelectedSupplierCode(supplierCode);
    if (supplierCode && supplier) {
      setSupplierDetail(supplier);
      // 如果当前在汇总列表页面，切换到档案详情页面
      if (activeTab === 'summary-list') {
        setActiveTab('details');
        setSearchParams({ supplierCode, tab: 'details' });
      } else {
        // 更新URL参数，保持当前tab
        const newParams: any = { supplierCode };
        if (activeTab !== 'summary-list') {
          newParams.tab = activeTab;
        }
        setSearchParams(newParams);
      }
    } else {
      setSupplierDetail(null);
      // 如果清空选择，回到汇总列表
      setActiveTab('summary-list');
      setSearchParams({});
    }
  };

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    const newParams: any = {};

    // 如果切换到汇总列表，清除供应商选择
    if (key === 'summary-list') {
      setSelectedSupplierCode('');
      setSupplierDetail(null);
      setSearchParams({});
    } else {
      // 其他Tab允许切换，但会显示提示选择供应商
      if (selectedSupplierCode) {
        newParams.supplierCode = selectedSupplierCode;
        newParams.tab = key;
        setSearchParams(newParams);
      } else {
        // 没有选择供应商也允许切换，只更新tab参数
        newParams.tab = key;
        setSearchParams(newParams);
      }
    }
  };

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };

  // 渲染内容
  const renderContent = () => {
    if (activeTab === 'summary-list') {
      return <SupplierArchivesSummaryList />;
    }

    if (!selectedSupplierCode) {
      return (
        <div className={styles.emptyState}>
          <FileTextOutlined className={styles.icon} />
          <div>请先选择供应商查看档案信息</div>
        </div>
      );
    }

    if (loading) {
      return (
        <div className={styles.loadingContainer}>
          <Spin size="large" />
        </div>
      );
    }

    if (!supplierDetail) {
      return (
        <div className={styles.emptyState}>
          <FileTextOutlined className={styles.icon} />
          <div>供应商信息加载失败</div>
        </div>
      );
    }

    return (
      <>
        {/* 供应商基本信息 */}
        <Card size="small" title="供应商信息" className={styles.supplierInfo}>
          <Descriptions column={2} size="small">
            <Descriptions.Item label="供应商编码">{supplierDetail.code}</Descriptions.Item>
            <Descriptions.Item label="供应商名称">{supplierDetail.name}</Descriptions.Item>
            <Descriptions.Item label="联系人">
              {supplierDetail.contactName || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="联系电话">
              {supplierDetail.contactPhone || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="地址" span={2}>
              {supplierDetail.address || '-'}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 根据Tab显示不同内容 */}
        {activeTab === 'details' && (
          <SupplierArchiveManagement supplierCode={selectedSupplierCode} />
        )}
        {activeTab === 'summary' && (
          <SupplierArchivesSummaryDetail supplierCode={selectedSupplierCode} />
        )}
      </>
    );
  };

  return (
    <div className={styles.container}>
      <Card>
        {/* 供应商选择器 - 在需要供应商的Tab中显示 */}
        {(activeTab === 'details' || activeTab === 'summary') && (
          <Row gutter={24} style={{ marginBottom: 24 }}>
            <Col span={8}>
              <div>
                <label className={styles.selectorLabel}>选择供应商：</label>
                <SupplierSearchSelector
                  value={selectedSupplierCode}
                  onChange={handleSupplierChange}
                  placeholder="请选择供应商"
                  style={{ width: '100%' }}
                />
              </div>
            </Col>
            {!selectedSupplierCode && (
              <Col span={16}>
                <div
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#fff7e6',
                    border: '1px solid #ffd591',
                    borderRadius: '6px',
                    marginTop: '26px',
                    color: '#d46b08',
                    fontSize: '14px',
                  }}
                >
                  💡 请先选择供应商以查看相关数据
                </div>
              </Col>
            )}
          </Row>
        )}

        {/* Tab导航 */}
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          style={{ marginBottom: 16 }}
          items={[
            {
              key: 'summary-list',
              label: (
                <span>
                  <BarChartOutlined />
                  汇总列表
                </span>
              ),
            },
            {
              key: 'details',
              label: (
                <span>
                  <UnorderedListOutlined />
                  档案详情
                  {!selectedSupplierCode && (
                    <span style={{ color: '#faad14', fontSize: '12px', marginLeft: '4px' }}>
                      ⚠
                    </span>
                  )}
                </span>
              ),
            },
            {
              key: 'summary',
              label: (
                <span>
                  <DashboardOutlined />
                  汇总详情
                  {!selectedSupplierCode && (
                    <span style={{ color: '#faad14', fontSize: '12px', marginLeft: '4px' }}>
                      ⚠
                    </span>
                  )}
                </span>
              ),
            },
          ]}
        />

        {/* 内容区域 */}
        {renderContent()}
      </Card>
    </div>
  );
};

export default SupplierArchives;
