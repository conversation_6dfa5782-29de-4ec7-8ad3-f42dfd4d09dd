import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Row,
  Col,
  Select,
  Tag,
  Tooltip,
  Progress,
  Statistic,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  FileExcelOutlined,
  EyeOutlined,
  BarChartOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  getSupplierArchivesSummaryList,
  exportSupplierArchivesSummaryExcel,
} from '@/api/SupplierArchivesApi';
import { type ListDetail, CreditLevelOptions, getCreditLevelColor } from '@/types/supplierArchives';
import { logError, getErrorMessage } from '@/utils/errorHandler';

const { Search } = Input;
const { Option } = Select;

const SupplierArchivesSummaryList: React.FC = () => {
  const navigate = useNavigate();
  const [archives, setArchives] = useState<ListDetail[]>([]);
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [creditLevel, setCreditLevel] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<string>('');
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取汇总列表
  const fetchArchivesSummary = async (
    page = 1,
    pageSize = 10,
    search = '',
    credit = '',
    sort = '',
    order = '',
  ) => {
    setLoading(true);
    try {
      const response = await getSupplierArchivesSummaryList({
        page,
        pageSize,
        search: search.trim() || undefined,
        creditLevel: credit || undefined,
        sortBy: sort || undefined,
        sortOrder: order || undefined,
      });

      if (response.code === 200) {
        setArchives(response.data.archives);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取汇总列表失败');
      }
    } catch (error: any) {
      logError('获取汇总列表', error);
      message.error(getErrorMessage(error, '获取汇总列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchArchivesSummary();
  }, []);

  // 处理表格变化
  const handleTableChange = (
    paginationConfig: TablePaginationConfig,
    filters: any,
    sorter: any,
  ) => {
    let newSortBy = '';
    let newSortOrder = '';

    if (sorter.field && sorter.order) {
      newSortBy = sorter.field;
      newSortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
    }

    setSortBy(newSortBy);
    setSortOrder(newSortOrder);

    fetchArchivesSummary(
      paginationConfig.current,
      paginationConfig.pageSize,
      searchText,
      creditLevel,
      newSortBy,
      newSortOrder,
    );
  };

  // 搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    fetchArchivesSummary(1, pagination.pageSize, value, creditLevel, sortBy, sortOrder);
  };

  // 信用等级筛选
  const handleCreditLevelChange = (value: string) => {
    setCreditLevel(value);
    fetchArchivesSummary(1, pagination.pageSize, searchText, value, sortBy, sortOrder);
  };

  // 刷新
  const handleRefresh = () => {
    fetchArchivesSummary(
      pagination.current,
      pagination.pageSize,
      searchText,
      creditLevel,
      sortBy,
      sortOrder,
    );
  };

  // 查看档案详情
  const handleViewDetail = (supplierCode: string) => {
    navigate(`/company/supplier-archives?supplierCode=${supplierCode}&tab=details`);
  };

  // 查看汇总详情
  const handleViewSummary = (supplierCode: string) => {
    navigate(`/company/supplier-archives?supplierCode=${supplierCode}&tab=summary`);
  };

  // 导出Excel
  const handleExportExcel = async () => {
    setExportLoading(true);
    try {
      const blob = await exportSupplierArchivesSummaryExcel({
        search: searchText || undefined,
        creditLevel: creditLevel || undefined,
        sortBy: sortBy || undefined,
        sortOrder: sortOrder || undefined,
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `供应商档案汇总_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error: any) {
      logError('导出Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    } finally {
      setExportLoading(false);
    }
  };

  // 格式化数字
  const formatNumber = (value: number, precision = 2): string => {
    if (value === null || value === undefined) return '-';
    return value.toFixed(precision);
  };

  // 格式化百分比
  const formatPercent = (value: number): string => {
    if (value === null || value === undefined) return '-';
    return `${(value * 100).toFixed(2)}%`;
  };

  // 格式化金额
  const formatAmount = (value: number): string => {
    if (value === null || value === undefined) return '-';
    return `¥${value.toLocaleString()}`;
  };

  // 获取评分颜色
  const getScoreColor = (score: number): string => {
    if (score >= 90) return '#52c41a';
    if (score >= 80) return '#faad14';
    if (score >= 70) return '#fa8c16';
    if (score >= 60) return '#ff7875';
    return '#f5222d';
  };

  // 表格列配置
  const columns: ColumnsType<ListDetail> = [
    {
      title: '供应商',
      children: [
        {
          title: '编码',
          dataIndex: 'supplierCode',
          key: 'supplierCode',
          width: 120,
          fixed: 'left',
        },
        {
          title: '名称',
          dataIndex: 'supplierName',
          key: 'supplierName',
          width: 150,
          fixed: 'left',
          ellipsis: true,
        },
      ],
    },
    {
      title: '采购数据',
      children: [
        {
          title: '采购金额',
          dataIndex: 'totalPurchaseAmount',
          key: 'totalPurchaseAmount',
          width: 120,
          sorter: true,
          render: (value: number) => formatAmount(value),
        },
        {
          title: '采购数量',
          dataIndex: 'totalPurchaseQuantity',
          key: 'totalPurchaseQuantity',
          width: 100,
          sorter: true,
          render: (value: number) => value?.toLocaleString() || '-',
        },
        {
          title: '平均单价',
          dataIndex: 'averageUnitPrice',
          key: 'averageUnitPrice',
          width: 100,
          sorter: true,
          render: (value: number) => formatAmount(value),
        },
      ],
    },
    {
      title: '质量指标',
      children: [
        {
          title: '瑕疵率',
          dataIndex: 'defectRate',
          key: 'defectRate',
          width: 100,
          sorter: true,
          render: (value: number) => (
            <span style={{ color: value > 0.05 ? '#ff4d4f' : '#52c41a' }}>
              {formatPercent(value)}
            </span>
          ),
        },
        {
          title: '退货率',
          dataIndex: 'returnRate',
          key: 'returnRate',
          width: 100,
          sorter: true,
          render: (value: number) => (
            <span style={{ color: value > 0.03 ? '#ff4d4f' : '#52c41a' }}>
              {formatPercent(value)}
            </span>
          ),
        },
        {
          title: '质量评分',
          dataIndex: 'qualityScore',
          key: 'qualityScore',
          width: 120,
          sorter: true,
          render: (value: number) => (
            <Progress
              percent={value}
              size="small"
              strokeColor={getScoreColor(value)}
              format={(percent) => `${percent}分`}
            />
          ),
        },
      ],
    },
    {
      title: '服务指标',
      children: [
        {
          title: '准时交付率',
          dataIndex: 'onTimeDeliveryRate',
          key: 'onTimeDeliveryRate',
          width: 120,
          sorter: true,
          render: (value: number) => (
            <span style={{ color: value > 0.9 ? '#52c41a' : value > 0.8 ? '#faad14' : '#ff4d4f' }}>
              {formatPercent(value)}
            </span>
          ),
        },
        {
          title: '响应速度',
          dataIndex: 'responseSpeedScore',
          key: 'responseSpeedScore',
          width: 100,
          sorter: true,
          render: (value: number) => (
            <span style={{ color: getScoreColor(value) }}>{formatNumber(value, 0)}分</span>
          ),
        },
        {
          title: '服务态度',
          dataIndex: 'serviceAttitudeScore',
          key: 'serviceAttitudeScore',
          width: 100,
          sorter: true,
          render: (value: number) => (
            <span style={{ color: getScoreColor(value) }}>{formatNumber(value, 0)}分</span>
          ),
        },
      ],
    },
    {
      title: '财务状况',
      children: [
        {
          title: '信用等级',
          dataIndex: 'creditLevel',
          key: 'creditLevel',
          width: 100,
          sorter: true,
          render: (level: string) => (
            <Tag color={getCreditLevelColor(level)}>
              {CreditLevelOptions.find((opt) => opt.value === level)?.label || level}
            </Tag>
          ),
        },
        {
          title: '未结算金额',
          dataIndex: 'unsettledAmount',
          key: 'unsettledAmount',
          width: 120,
          sorter: true,
          render: (value: number) => (
            <span style={{ color: value > 0 ? '#ff4d4f' : '#52c41a' }}>{formatAmount(value)}</span>
          ),
        },
      ],
    },
    {
      title: '综合评分',
      dataIndex: 'overallScore',
      key: 'overallScore',
      width: 120,
      sorter: true,
      render: (value: number) => (
        <Progress
          percent={value}
          size="small"
          strokeColor={getScoreColor(value)}
          format={(percent) => `${percent}分`}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看档案详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record.supplierCode)}
            >
              档案
            </Button>
          </Tooltip>
          <Tooltip title="查看汇总详情">
            <Button
              type="link"
              size="small"
              icon={<BarChartOutlined />}
              onClick={() => handleViewSummary(record.supplierCode)}
            >
              汇总
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16} justify="space-between" align="middle">
          <Col flex="auto">
            <Space>
              <Search
                placeholder="搜索供应商编码或名称"
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                style={{ width: 300 }}
              />
              <Select
                placeholder="信用等级"
                allowClear
                size="large"
                style={{ width: 120 }}
                value={creditLevel || undefined}
                onChange={handleCreditLevelChange}
              >
                {CreditLevelOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    <Tag color={option.color} style={{ margin: 0 }}>
                      {option.label}
                    </Tag>
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Tooltip title="导出汇总数据">
                <Button
                  type="default"
                  icon={<FileExcelOutlined />}
                  onClick={handleExportExcel}
                  loading={exportLoading}
                >
                  导出Excel
                </Button>
              </Tooltip>
              <Button
                type="default"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Table
        columns={columns}
        dataSource={archives}
        rowKey="supplierCode"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        onChange={handleTableChange}
        scroll={{ x: 1800, y: 600 }}
        size="small"
        bordered
      />
    </Card>
  );
};

export default SupplierArchivesSummaryList;
