import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  Upload,
  Tooltip,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DownloadOutlined,
  UploadOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import {
  getSupplierList,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  getSupplierDetail,
  exportSupplierExcel,
  downloadImportTemplate,
  importSupplierExcel,
} from '@/api/SupplierApi';
import type { SupplierListProps, AddSupplierParams, UpdateSupplierParams } from '@/types/supplier';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './SupplierManagement.module.css';

const { Search } = Input;

interface SupplierFormData extends AddSupplierParams {}

const SupplierManagement: React.FC = () => {
  const [suppliers, setSuppliers] = useState<SupplierListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState<SupplierListProps | null>(null);
  const [form] = Form.useForm<SupplierFormData>();

  // 获取供应商列表
  const fetchSuppliers = async (page = 1, pageSize = 10, search = '') => {
    setLoading(true);
    try {
      const response = await getSupplierList({
        page,
        pageSize,
        search: search.trim() || undefined,
      });

      if (response.code === 200) {
        setSuppliers(response.data.suppliers);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取供应商列表失败');
      }
    } catch (error: any) {
      logError('获取供应商列表', error);
      message.error(getErrorMessage(error, '获取供应商列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchSuppliers();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
    fetchSuppliers(1, pagination.pageSize, value);
  };

  // 分页处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));
    fetchSuppliers(current, pageSize, searchText);
  };

  // 刷新列表
  const handleRefresh = () => {
    fetchSuppliers(pagination.current, pagination.pageSize, searchText);
  };

  // 新增供应商
  const handleAdd = () => {
    setEditingSupplier(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑供应商
  const handleEdit = async (supplier: SupplierListProps) => {
    try {
      const response = await getSupplierDetail(supplier.code);
      if (response.code === 200) {
        setEditingSupplier(supplier);
        form.setFieldsValue({
          code: response.data.code,
          name: response.data.name,
          address: response.data.address || '',
          contactName: response.data.contactName || '',
          contactPhone: response.data.contactPhone || '',
        });
        setModalVisible(true);
      } else {
        message.error(response.message || '获取供应商详情失败');
      }
    } catch (error: any) {
      logError('获取供应商详情', error);
      message.error(getErrorMessage(error, '获取供应商详情失败'));
    }
  };

  // 删除供应商
  const handleDelete = async (supplierCode: string) => {
    try {
      const response = await deleteSupplier(supplierCode);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除供应商', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 保存供应商
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      let response;

      if (editingSupplier) {
        // 编辑模式
        const updateData: UpdateSupplierParams = {
          name: values.name,
          address: values.address,
          contactName: values.contactName,
          contactPhone: values.contactPhone,
        };
        response = await updateSupplier(editingSupplier.code, updateData);
      } else {
        // 新增模式
        response = await createSupplier(values);
      }

      const result = handleApiResponse(
        response,
        editingSupplier ? '更新成功' : '创建成功',
        editingSupplier ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError(editingSupplier ? '更新供应商' : '创建供应商', error);
      message.error(getErrorMessage(error, editingSupplier ? '更新失败' : '创建失败'));
    }
  };

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      const blob = await downloadImportTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '供应商导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('模板下载成功');
    } catch (error: any) {
      logError('下载导入模板', error);
      message.error(getErrorMessage(error, '下载模板失败'));
    }
  };

  // 导出Excel
  const handleExport = async () => {
    try {
      const blob = await exportSupplierExcel();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `供应商列表_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出供应商列表', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 导入Excel
  const handleImport = (file: File) => {
    // 验证文件类型
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');

    if (!isExcel) {
      message.error('只能上传Excel文件(.xlsx, .xls)');
      return false;
    }

    // 验证文件大小 (限制为10MB)
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB');
      return false;
    }

    const importFile = async () => {
      const hide = message.loading('正在导入，请稍候...', 0);
      try {
        const response = await importSupplierExcel(file);
        const result = handleApiResponse(response, '导入成功', '导入失败');

        if (result.success) {
          message.success(result.message);
          handleRefresh(); // 刷新列表
        } else {
          message.error(result.message);
        }
      } catch (error: any) {
        logError('导入供应商', error);
        message.error(getErrorMessage(error, '导入失败'));
      } finally {
        hide();
      }
    };

    importFile();
    return false; // 阻止默认上传行为
  };

  // 表格列定义
  const columns: ColumnsType<SupplierListProps> = [
    {
      title: '供应商编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      fixed: 'left',
    },
    {
      title: '供应商名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
      key: 'contactName',
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'contactPhone',
      key: 'contactPhone',
      width: 140,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 250,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text: string) => (text ? new Date(text).toLocaleDateString() : '-'),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个供应商吗？"
            onConfirm={() => handleDelete(record.code)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <Search
                placeholder="搜索供应商编码或名称"
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
                style={{ maxWidth: 400 }}
              />
            </Col>
            <Col>
              <Space>
                <Tooltip title="下载供应商导入模板">
                  <Button
                    type="default"
                    icon={<DownloadOutlined />}
                    onClick={handleDownloadTemplate}
                  >
                    下载模板
                  </Button>
                </Tooltip>
                <Tooltip title="导入Excel文件批量添加供应商">
                  <Upload accept=".xlsx,.xls" showUploadList={false} beforeUpload={handleImport}>
                    <Button type="default" icon={<UploadOutlined />}>
                      导入
                    </Button>
                  </Upload>
                </Tooltip>
                <Tooltip title="导出当前供应商列表为Excel文件">
                  <Button type="default" icon={<ExportOutlined />} onClick={handleExport}>
                    导出
                  </Button>
                </Tooltip>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增供应商
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={suppliers}
          rowKey="code"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 供应商编辑模态框 */}
      <Modal
        title={editingSupplier ? '编辑供应商' : '新增供应商'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="供应商编码"
                rules={[
                  { required: true, message: '请输入供应商编码' },
                  { min: 2, message: '供应商编码至少2个字符' },
                  { max: 20, message: '供应商编码至多20个字符' },
                ]}
              >
                <Input placeholder="请输入供应商编码" disabled={!!editingSupplier} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="供应商名称"
                rules={[{ required: true, message: '请输入供应商名称' }]}
              >
                <Input placeholder="请输入供应商名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="contactName" label="联系人">
                <Input placeholder="请输入联系人" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="contactPhone" label="联系电话">
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="address" label="地址">
            <Input.TextArea placeholder="请输入地址" rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SupplierManagement;
