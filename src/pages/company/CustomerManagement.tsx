import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  Upload,
  Tooltip,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DownloadOutlined,
  UploadOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import {
  getCustomerList,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerDetail,
  exportCustomerExcel,
  downloadCustomerImportTemplate,
  importCustomerExcel,
} from '@/api/CustomerApi';
import type {
  CustomersListProps,
  AddCustomerParams,
  UpdateCustomerParams,
  GetCustomerListParams,
} from '@/types/customers';
import { UserSearchSelector, ProvinceSelector } from '@/components';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { generateCustomerPinyinCode } from '@/utils';
import styles from './CustomerManagement.module.css';

const { Search } = Input;

interface CustomerFormData extends AddCustomerParams {}

const CustomerManagement: React.FC = () => {
  const [customers, setCustomers] = useState<CustomersListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedProvinceCode, setSelectedProvinceCode] = useState<number | undefined>();
  const [selectedManagerCode, setSelectedManagerCode] = useState<string | undefined>();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<CustomersListProps | null>(null);
  const [form] = Form.useForm<CustomerFormData>();

  // 获取客户列表
  const fetchCustomers = async (
    page = 1,
    pageSize = 10,
    search = '',
    provinceCode?: number,
    managerCode?: string,
  ) => {
    setLoading(true);
    try {
      // 判断搜索内容是否为拼音码（纯字母，大小写都可以）
      const searchTerm = search.trim();
      const isPinyinCode = /^[a-zA-Z]+$/.test(searchTerm);

      const params: GetCustomerListParams = {
        page,
        pageSize,
        provinceCode: provinceCode ? provinceCode.toString() : null,
        search: isPinyinCode ? '' : searchTerm, // 如果是拼音码，search 为空
        pinyinCode: isPinyinCode ? searchTerm.toUpperCase() : null, // 如果是拼音码，转为大写后使用 pinyinCode 参数
        managerCode: managerCode || null, // 负责人筛选
      };

      const response = await getCustomerList(params);

      if (response.code === 200) {
        setCustomers(response.data.customers);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取客户列表失败');
      }
    } catch (error: any) {
      logError('获取客户列表', error);
      message.error(getErrorMessage(error, '获取客户列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchCustomers();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setPagination((prev) => ({ ...prev, current: 1 }));
    fetchCustomers(1, pagination.pageSize, value, selectedProvinceCode, selectedManagerCode);
  };

  // 省份筛选处理
  const handleProvinceChange = (provinceCode: number | undefined) => {
    setSelectedProvinceCode(provinceCode);
    setPagination((prev) => ({ ...prev, current: 1 }));
    fetchCustomers(1, pagination.pageSize, searchText, provinceCode, selectedManagerCode);
  };

  // 负责人筛选处理
  const handleManagerChange = (managerCode: string, _user: any) => {
    setSelectedManagerCode(managerCode);
    setPagination((prev) => ({ ...prev, current: 1 }));
    fetchCustomers(1, pagination.pageSize, searchText, selectedProvinceCode, managerCode);
  };

  // 分页处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));
    fetchCustomers(current, pageSize, searchText, selectedProvinceCode, selectedManagerCode);
  };

  // 刷新列表
  const handleRefresh = () => {
    fetchCustomers(
      pagination.current,
      pagination.pageSize,
      searchText,
      selectedProvinceCode,
      selectedManagerCode,
    );
  };

  // 新增客户
  const handleAdd = () => {
    setEditingCustomer(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 编辑客户
  const handleEdit = async (customer: CustomersListProps) => {
    try {
      const response = await getCustomerDetail(customer.code);
      if (response.code === 200) {
        setEditingCustomer(customer);
        form.setFieldsValue({
          name: response.data.name,
          pinyinCode: response.data.pinyinCode || '',
          phone: response.data.phone || '',
          address: response.data.address || '',
          managerCode: response.data.managerCode || '',
          provinceCode: response.data.provinceCode || undefined,
          remarks: response.data.remarks || '',
        });
        setModalVisible(true);
      } else {
        message.error(response.message || '获取客户详情失败');
      }
    } catch (error: any) {
      logError('获取客户详情', error);
      message.error(getErrorMessage(error, '获取客户详情失败'));
    }
  };

  // 删除客户
  const handleDelete = async (customerCode: string) => {
    try {
      const response = await deleteCustomer(customerCode);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除客户', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 保存客户
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      let response;

      if (editingCustomer) {
        // 编辑模式
        const updateData: UpdateCustomerParams = {
          name: values.name,
          phone: values.phone,
          address: values.address,
          managerCode: values.managerCode,
          provinceCode: values.provinceCode,
          remarks: values.remarks || null,
        };
        response = await updateCustomer(editingCustomer.code, updateData);
      } else {
        // 新增模式 - 自动生成拼音码
        const customerData: AddCustomerParams = {
          ...values,
          pinyinCode: values.pinyinCode || generateCustomerPinyinCode(values.name),
          remarks: values.remarks || null,
        };
        response = await createCustomer(customerData);
      }

      const result = handleApiResponse(
        response,
        editingCustomer ? '更新成功' : '创建成功',
        editingCustomer ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError(editingCustomer ? '更新客户' : '创建客户', error);
      message.error(getErrorMessage(error, editingCustomer ? '更新失败' : '创建失败'));
    }
  };

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      const blob = await downloadCustomerImportTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '客户导入模板.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('模板下载成功');
    } catch (error: any) {
      logError('下载导入模板', error);
      message.error(getErrorMessage(error, '下载模板失败'));
    }
  };

  // 导出Excel
  const handleExport = async () => {
    try {
      const blob = await exportCustomerExcel();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `客户列表_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出客户列表', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 导入Excel
  const handleImport = (file: File) => {
    // 验证文件类型
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');

    if (!isExcel) {
      message.error('只能上传Excel文件(.xlsx, .xls)');
      return false;
    }

    // 验证文件大小 (限制为10MB)
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB');
      return false;
    }

    const importFile = async () => {
      const hide = message.loading('正在导入，请稍候...', 0);
      try {
        const response = await importCustomerExcel(file);
        const result = handleApiResponse(response, '导入成功', '导入失败');

        if (result.success) {
          message.success(result.message);
          handleRefresh(); // 刷新列表
        } else {
          message.error(result.message);
        }
      } catch (error: any) {
        logError('导入客户', error);
        message.error(getErrorMessage(error, '导入失败'));
      } finally {
        hide();
      }
    };

    importFile();
    return false; // 阻止默认上传行为
  };

  // 表格列定义
  const columns: ColumnsType<CustomersListProps> = [
    {
      title: '客户名称',
      dataIndex: 'name',
      key: 'name',
      width: 100,
    },
    {
      title: '拼音码',
      dataIndex: 'pinyinCode',
      key: 'pinyinCode',
      width: 80,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 100,
    },
    {
      title: '省份',
      dataIndex: 'provinceName',
      key: 'provinceName',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 250,
      ellipsis: true,
    },
    {
      title: '负责人',
      dataIndex: 'managerName',
      key: 'managerName',
      width: 150,
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>{text || '-'}</div>
      ),
    },

    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 300,
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个客户吗？"
            onConfirm={() => handleDelete(record.code)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.toolbar}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col flex="auto">
              <Row gutter={12} align="middle">
                <Col>
                  <Search
                    placeholder="搜索客户编码、名称或拼音码"
                    allowClear
                    enterButton={<SearchOutlined />}
                    size="large"
                    onSearch={handleSearch}
                    style={{ width: 400 }}
                  />
                </Col>
                <Col>
                  <ProvinceSelector
                    value={selectedProvinceCode}
                    onChange={handleProvinceChange}
                    placeholder="筛选省份"
                    allowClear
                    style={{ width: 200, height: 40 }}
                  />
                </Col>
                <Col>
                  <UserSearchSelector
                    value={selectedManagerCode}
                    onChange={handleManagerChange}
                    placeholder="筛选负责人"
                    allowClear
                    style={{ width: 200, height: 40 }}
                  />
                </Col>
              </Row>
            </Col>
            <Col>
              <Space>
                <Tooltip title="下载客户导入模板">
                  <Button
                    type="default"
                    icon={<DownloadOutlined />}
                    onClick={handleDownloadTemplate}
                  >
                    下载模板
                  </Button>
                </Tooltip>
                <Tooltip title="导入Excel文件批量添加客户">
                  <Upload accept=".xlsx,.xls" showUploadList={false} beforeUpload={handleImport}>
                    <Button type="default" icon={<UploadOutlined />}>
                      导入
                    </Button>
                  </Upload>
                </Tooltip>
                <Tooltip title="导出当前客户列表为Excel文件">
                  <Button type="default" icon={<ExportOutlined />} onClick={handleExport}>
                    导出
                  </Button>
                </Tooltip>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  新增客户
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={customers}
          rowKey="code"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1640 }}
        />
      </Card>

      {/* 客户编辑模态框 */}
      <Modal
        title={editingCustomer ? '编辑客户' : '新增客户'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="客户名称"
                rules={[{ required: true, message: '请输入客户名称' }]}
              >
                <Input
                  placeholder="请输入客户名称"
                  onChange={(e) => {
                    // 新增时自动生成拼音码
                    if (!editingCustomer && e.target.value) {
                      form.setFieldValue('pinyinCode', generateCustomerPinyinCode(e.target.value));
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="provinceCode"
                label="省份"
                rules={[{ required: true, message: '请选择省份' }]}
              >
                <ProvinceSelector placeholder="请选择省份" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="pinyinCode"
                label="拼音码"
                rules={[{ required: !editingCustomer, message: '请输入拼音码' }]}
              >
                <Input placeholder="请输入拼音码（新增时自动生成）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="phone" label="联系电话">
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={16}>
              <Form.Item name="managerCode" label="负责人">
                <UserSearchSelector placeholder="请选择负责人" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="address" label="地址">
            <Input.TextArea placeholder="请输入地址" rows={3} />
          </Form.Item>

          <Form.Item name="remarks" label="备注">
            <Input.TextArea placeholder="请输入备注（选填）" rows={2} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CustomerManagement;
