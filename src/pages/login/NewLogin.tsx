import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Typography, Spin, message } from 'antd';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import useStyles from './NewLoginStyles';
import { MANAGER_TITLE } from '@/utils';
import type { LoginParams } from '@/types/auth';
import { login } from '@/api/auth';
import { saveToken, saveUser, isAuthenticated } from '@/utils/tokenManager';
import useAuthStore from '@/store/authStore';

const { Title } = Typography;

const NewLogin: React.FC = () => {
  const { styles } = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { setAuth } = useAuthStore();

  // 检查用户是否已登录，如果已登录则跳转到首页
  useEffect(() => {
    if (isAuthenticated()) {
      const from = location.state?.from?.pathname || '/';
      navigate(from, { replace: true });
    }
  }, [navigate, location]);

  // 处理登录
  const handleLogin = async (values: LoginParams) => {
    try {
      setLoading(true);

      const response = await login(values);

      if (response.code === 200) {
        // 登录成功，保存token和用户信息到localStorage
        saveToken(response.data.accessToken);
        saveUser(response.data.user);

        // 同时保存到zustand store
        setAuth(response.data.accessToken, response.data.user);

        message.success('登录成功');

        // 跳转到首页
        setTimeout(() => {
          navigate('/');
        }, 100);
      } else {
        message.error(response.message || '登录失败，请检查用户名和密码');
      }
    } catch (error: any) {
      console.error('登录失败:', error);
      message.error(error.response?.data?.message || '登录失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.loginContainer}>
      {/* 左侧蓝色背景区域 */}
      <div className={styles.leftPanel}>
        <div className={styles.brandContent}>
          <Title className={styles.brandTitle}>
            VASA ENTERPRISE
            <br />
            MANAGEMENT SYSTEM
          </Title>
        </div>
      </div>

      {/* 右侧登录表单区域 */}
      <div className={styles.rightPanel}>
        <div className={styles.loginForm}>
          <div className={styles.formHeader}>
            <Title level={3} className={styles.logoTitle}>
              VASA
            </Title>
            <Title level={2} className={styles.welcomeTitle}>
              欢迎登录
            </Title>
          </div>

          <Spin spinning={loading}>
            <Form form={form} name="login" onFinish={handleLogin} size="large" layout="vertical">
              <Form.Item name="userCode" rules={[{ required: true, message: '请输入用户编码' }]}>
                <Input
                  prefix={<UserOutlined className={styles.inputIcon} />}
                  placeholder="用户编码"
                  className={styles.inputField}
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 4, message: '密码至少4个字符' },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined className={styles.inputIcon} />}
                  placeholder="密码"
                  className={styles.inputField}
                />
              </Form.Item>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  className={styles.loginButton}
                  loading={loading}
                  block
                >
                  登录
                </Button>
              </Form.Item>
            </Form>
          </Spin>

          <div className={styles.footer}>
            <p>
              Copyright © {new Date().getFullYear()} {MANAGER_TITLE}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewLogin;
