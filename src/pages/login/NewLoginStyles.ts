import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => ({
  loginContainer: {
    display: 'flex',
    minHeight: '100vh',
    overflow: 'hidden',
  },

  leftPanel: {
    flex: 1,
    background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 50%, #0050b3 100%)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden',
  },

  brandContent: {
    textAlign: 'center',
    zIndex: 1,
    position: 'relative',
    padding: '40px',
  },

  brandTitle: {
    color: '#ffffff',
    fontSize: '48px',
    fontWeight: '300',
    letterSpacing: '2px',
    lineHeight: '1.2',
    textShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
    margin: 0,

    '@media (max-width: 1200px)': {
      fontSize: '40px',
    },

    '@media (max-width: 992px)': {
      fontSize: '36px',
    },
  },

  rightPanel: {
    width: '480px',
    backgroundColor: '#ffffff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '40px',

    '@media (max-width: 768px)': {
      width: '100%',
      padding: '20px',
    },
  },

  loginForm: {
    width: '100%',
    maxWidth: '360px',
  },

  formHeader: {
    textAlign: 'center',
    marginBottom: '40px',
  },

  logoTitle: {
    color: '#1890ff',
    fontSize: '32px',
    fontWeight: 'bold',
    margin: '0 0 16px 0',
    letterSpacing: '1px',
  },

  welcomeTitle: {
    color: '#262626',
    fontSize: '24px',
    fontWeight: '500',
    margin: 0,
  },

  inputField: {
    height: '48px',
    borderRadius: '6px',
    fontSize: '16px',

    '&:focus, &:hover': {
      borderColor: '#1890ff',
      boxShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
    },
  },

  inputIcon: {
    color: '#8c8c8c',
    fontSize: '16px',
  },

  rememberCheckbox: {
    color: '#8c8c8c',
    fontSize: '14px',
  },

  loginButton: {
    height: '48px',
    borderRadius: '6px',
    fontSize: '16px',
    fontWeight: '500',
    marginTop: '16px',
    background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
    border: 'none',

    '&:hover': {
      background: 'linear-gradient(135deg, #40a9ff 0%, #1890ff 100%)',
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)',
    },

    '&:active': {
      transform: 'translateY(0)',
    },
  },

  footer: {
    textAlign: 'center',
    marginTop: '40px',
    color: '#8c8c8c',
    fontSize: '12px',

    p: {
      margin: 0,
    },
  },
}));

export default useStyles;
