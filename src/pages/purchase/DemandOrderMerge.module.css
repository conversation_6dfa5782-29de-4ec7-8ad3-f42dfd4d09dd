.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 0;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title {
  margin: 0;
  color: #262626;
  font-size: 20px;
  font-weight: 600;
}

.form {
  margin-bottom: 24px;
}

.summarySection {
  margin-bottom: 24px;
}

.summarySection .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table {
  margin-bottom: 24px;
}

.tableTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #262626;
}

.orderNumber {
  color: #1890ff;
  font-weight: 500;
  font-family: monospace;
}

.personCode {
  font-size: 12px;
  color: #8c8c8c;
  font-family: monospace;
}

.amount {
  color: #f5222d;
  font-weight: 600;
}

.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: #e6f7ff;
  color: #1890ff;
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .title {
    font-size: 18px;
  }
  
  .summarySection .ant-col {
    margin-bottom: 8px;
  }
  
  .footer {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 12px;
  }
  
  .header {
    gap: 8px;
  }
  
  .title {
    font-size: 16px;
  }
  
  .footer .ant-space {
    width: 100%;
    justify-content: center;
  }
  
  .footer .ant-btn {
    flex: 1;
  }
}
