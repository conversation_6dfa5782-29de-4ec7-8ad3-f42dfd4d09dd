.container {
  padding: 2px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

/* 统计信息区域 */
.statsSection {
  margin-bottom: 2px;
  padding: 12px;
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  border-radius: 6px;
  border: 1px solid #e8f4fd;
}

.statCard {
  text-align: center;
  padding: 8px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

/* 搜索区域 */
.searchSection {
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8f0fe;
  overflow: hidden;
}

.searchHeader {
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f4ff 100%);
  border-bottom: 1px solid #d6e4ff;
  display: flex;
  align-items: center;
}

.searchTitle {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.searchContent {
  padding: 12px;
}

.searchItem {
  margin-bottom: 4px;
  text-align: center;
}

.searchLabel {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  text-align: left;
}

.searchInputWrapper {
  min-width: 180px;
  width: 100%;
}

.searchInputWrapper .ant-select,
.searchInputWrapper .ant-input {
  min-width: 180px;
  width: 100%;
}

.searchActions {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding-top: 4px;
}

/* 工具栏 */
.toolbar {
  margin-bottom: 8px;
}

.toolbar .ant-row {
  align-items: center;
}

.toolbar .ant-input-search {
  max-width: 300px;
}

.toolbar .ant-btn {
  height: 32px;
}

/* 统计卡片样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 2px;
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #f0f5ff;
  font-weight: 600;
  color: #262626;
  padding: 6px 8px;
  font-size: 13px;
}

.ant-table-tbody > tr > td {
  padding: 4px 8px;
  font-size: 13px;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #e6f7ff;
}

.ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background-color: #bae7ff;
}

/* 状态标签样式 */
.statusTag {
  font-weight: 500;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 按钮样式 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.ant-btn-danger {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.ant-btn-danger:hover,
.ant-btn-danger:focus {
  color: #ff7875;
  border-color: #ff7875;
}

/* 紧凑型卡片样式 */
.ant-card {
  border-radius: 6px;
}

.ant-card-body {
  padding: 12px;
}

/* 紧凑型分页样式 */
.ant-pagination-small .ant-pagination-item {
  min-width: 24px;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
}

.ant-pagination-small .ant-pagination-prev,
.ant-pagination-small .ant-pagination-next {
  min-width: 24px;
  height: 24px;
  line-height: 22px;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-number,
.ant-select-selector,
.ant-picker {
  border-radius: 4px;
}

.ant-input:focus,
.ant-input-focused,
.ant-input-number:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector,
.ant-picker:focus,
.ant-picker-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 8px;
  }

  .statsSection {
    padding: 8px;
  }

  .searchActions {
    justify-content: center;
    padding-top: 8px;
  }

  .searchInputWrapper {
    min-width: 160px;
  }

  .toolbar .ant-row {
    flex-direction: column;
    gap: 8px;
  }

  .toolbar .ant-input-search {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 6px;
  }

  .statsSection {
    padding: 6px;
  }

  .searchContent {
    padding: 8px;
  }

  .searchHeader {
    padding: 6px 8px;
  }

  .searchActions {
    padding-top: 6px;
  }

  .searchActions .ant-space {
    width: 100%;
    justify-content: center;
  }

  .searchInputWrapper {
    min-width: 140px;
  }

  .searchInputWrapper .ant-select,
  .searchInputWrapper .ant-input {
    min-width: 140px;
  }

  .ant-statistic-content {
    font-size: 18px;
  }

  .ant-table-scroll {
    overflow-x: auto;
  }
}
