import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Table,
  Button,
  Space,
  message,
  Tag,
  Tabs,
  Modal,
  Form,
  InputNumber,
} from 'antd';
import { ArrowLeftOutlined, CheckOutlined, InboxOutlined, DeleteOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  getProductPurchaseOrderDetail,
  confirmPurchaseOrder,
  receiveOrder,
  cancelPurchaseOrder,
} from '@/api/ProductPurchaseOrderApi';
import type {
  ProductPurchaseOrder,
  ProductPurchaseOrderDetail as OrderDetail,
  DemandSource,
  ReceiveOrderParams,
} from '@/types/productPurchaseOrder';
import { PURCHASE_ORDER_STATUS_CONFIG } from '@/types/productPurchaseOrder';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { getUser } from '@/utils/tokenManager';
import styles from './ProductPurchaseOrderDetail.module.css';

const { TabPane } = Tabs;

const ProductPurchaseOrderDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const currentUser = getUser();
  const [loading, setLoading] = useState(false);
  const [order, setOrder] = useState<ProductPurchaseOrder | null>(null);
  const [demandSources, setDemandSources] = useState<DemandSource[]>([]);
  const [receiveModalVisible, setReceiveModalVisible] = useState(false);
  const [receiveForm] = Form.useForm();

  // 获取订单详情
  const fetchOrderDetail = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await getProductPurchaseOrderDetail(id);
      const result = handleApiResponse(response, '', '获取订单详情失败');

      if (result.success && response.data) {
        setOrder(response.data);
        setDemandSources(response.data.demandSources || []);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取订单详情', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchOrderDetail();
  }, [id]);

  // 返回列表
  const handleBack = () => {
    navigate('/purchase/product-orders');
  };

  // 确认订单
  const handleConfirm = async () => {
    if (!order || !currentUser?.code) return;

    try {
      const response = await confirmPurchaseOrder(order.id, {
        confirmedByUserCode: currentUser.code,
      });
      const result = handleApiResponse(response, '订单确认成功', '订单确认失败');

      if (result.success) {
        message.success(result.message);
        fetchOrderDetail();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('确认订单', error);
      message.error(getErrorMessage(error));
    }
  };

  // 取消订单
  const handleCancel = async () => {
    if (!order) return;

    try {
      const response = await cancelPurchaseOrder(order.id);
      const result = handleApiResponse(response, '订单取消成功', '订单取消失败');

      if (result.success) {
        message.success(result.message);
        fetchOrderDetail();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('取消订单', error);
      message.error(getErrorMessage(error));
    }
  };

  // 收货确认
  const handleReceive = () => {
    if (!order?.details) return;

    // 初始化收货表单数据
    const initialValues = order.details.reduce(
      (acc, detail) => {
        acc[detail.id] = detail.quantity - (detail.receivedQuantity || 0);
        return acc;
      },
      {} as Record<string, number>,
    );

    receiveForm.setFieldsValue(initialValues);
    setReceiveModalVisible(true);
  };

  // 提交收货
  const handleReceiveSubmit = async (values: any) => {
    if (!order || !currentUser?.code) return;

    const receiveDetails = Object.entries(values)
      .filter(([_, quantity]) => (quantity as number) > 0)
      .map(([detailId, quantity]) => ({
        detailId,
        receivedQuantity: quantity as number,
      }));

    if (receiveDetails.length === 0) {
      message.warning('请输入收货数量');
      return;
    }

    try {
      const params: ReceiveOrderParams = {
        receivedByUserCode: currentUser.code,
        receiveDetails,
      };

      const response = await receiveOrder(order.id, params);
      const result = handleApiResponse(response, '收货确认成功', '收货确认失败');

      if (result.success) {
        message.success(result.message);
        setReceiveModalVisible(false);
        fetchOrderDetail();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('收货确认', error);
      message.error(getErrorMessage(error));
    }
  };

  if (!order) {
    return (
      <div className={styles.container}>
        <Card loading={loading}>
          <div className={styles.emptyState}>{loading ? '加载中...' : '订单不存在'}</div>
        </Card>
      </div>
    );
  }

  // 订单详情列定义
  const detailColumns: ColumnsType<OrderDetail> = [
    {
      title: '商品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 150,
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 200,
    },
    {
      title: '订购数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (quantity: number) => <span className={styles.quantity}>{quantity}</span>,
    },
    {
      title: '已收货数量',
      dataIndex: 'receivedQuantity',
      key: 'receivedQuantity',
      width: 120,
      render: (received: number = 0) => <span className={styles.received}>{received}</span>,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      render: (price: number) => <span>¥{price?.toFixed(2) || '0.00'}</span>,
    },
    {
      title: '总价',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 120,
      render: (price: number) => (
        <span className={styles.totalPrice}>¥{price?.toFixed(2) || '0.00'}</span>
      ),
    },
  ];

  // 需求来源列定义
  const sourceColumns: ColumnsType<DemandSource> = [
    {
      title: '需求订单号',
      dataIndex: 'demandOrderNumber',
      key: 'demandOrderNumber',
      width: 150,
    },
    {
      title: '销售人员',
      dataIndex: 'salesPersonCode',
      key: 'salesPersonCode',
      width: 120,
    },
    {
      title: '需求日期',
      dataIndex: 'demandDate',
      key: 'demandDate',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 100,
    },
    {
      title: '金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => <span>¥{amount?.toFixed(2) || '0.00'}</span>,
    },
  ];

  const statusConfig = PURCHASE_ORDER_STATUS_CONFIG[order.status];

  return (
    <div style={{ padding: '16px' }}>
      <Card>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '16px',
          }}
        >
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              返回列表
            </Button>
            <h2 style={{ margin: 0 }}>采购订单详情</h2>
            <Tag color={statusConfig.color}>{statusConfig.text}</Tag>
          </Space>
          <Space>
            {order.status === 'draft' && (
              <>
                <Button type="primary" icon={<CheckOutlined />} onClick={handleConfirm}>
                  确认订单
                </Button>
                <Button danger icon={<DeleteOutlined />} onClick={handleCancel}>
                  取消订单
                </Button>
              </>
            )}
            {(order.status === 'shipped' || order.status === 'confirmed') && (
              <Button type="primary" icon={<InboxOutlined />} onClick={handleReceive}>
                收货确认
              </Button>
            )}
          </Space>
        </div>

        <Tabs defaultActiveKey="basic">
          <TabPane tab="基本信息" key="basic">
            <Descriptions
              title="订单信息"
              bordered
              column={{ xs: 1, sm: 2 }}
              style={{ marginBottom: '16px' }}
            >
              <Descriptions.Item label="订单编号">{order.orderNumber}</Descriptions.Item>
              <Descriptions.Item label="供应商">
                {order.supplier?.name || order.supplierCode}
              </Descriptions.Item>
              <Descriptions.Item label="订单日期">
                {dayjs(order.orderDate).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="预计交货日期">
                {order.expectedDeliveryDate
                  ? dayjs(order.expectedDeliveryDate).format('YYYY-MM-DD')
                  : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="总金额">
                ¥{order.totalAmount?.toFixed(2) || '0.00'}
              </Descriptions.Item>
              <Descriptions.Item label="总数量">{order.totalQuantity}</Descriptions.Item>
              <Descriptions.Item label="创建人">
                {order.createdByUser?.nickname ||
                  order.createdByUser?.name ||
                  order.createdByUserCode}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dayjs(order.createdAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              {order.confirmedByUserCode && (
                <>
                  <Descriptions.Item label="确认人">{order.confirmedByUserCode}</Descriptions.Item>
                  <Descriptions.Item label="确认时间">
                    {order.confirmedAt
                      ? dayjs(order.confirmedAt).format('YYYY-MM-DD HH:mm:ss')
                      : '-'}
                  </Descriptions.Item>
                </>
              )}
            </Descriptions>
          </TabPane>

          <TabPane tab="订单详情" key="details">
            <Table
              columns={detailColumns}
              dataSource={order.details || []}
              rowKey="id"
              pagination={false}
            />
          </TabPane>

          {demandSources.length > 0 && (
            <TabPane tab="需求来源" key="sources">
              <Table
                columns={sourceColumns}
                dataSource={demandSources}
                rowKey="demandOrderId"
                pagination={false}
              />
            </TabPane>
          )}
        </Tabs>
      </Card>

      {/* 收货确认弹窗 */}
      <Modal
        title="收货确认"
        open={receiveModalVisible}
        onCancel={() => setReceiveModalVisible(false)}
        onOk={() => receiveForm.submit()}
        width={800}
        className={styles.receiveModal}
      >
        <Form form={receiveForm} onFinish={handleReceiveSubmit} layout="vertical">
          <Table dataSource={order.details || []} rowKey="id" pagination={false} size="small">
            <Table.Column title="商品编码" dataIndex="productCode" key="productCode" />
            <Table.Column title="商品名称" dataIndex="productName" key="productName" />
            <Table.Column title="订购数量" dataIndex="quantity" key="quantity" />
            <Table.Column
              title="已收货"
              dataIndex="receivedQuantity"
              key="receivedQuantity"
              render={(received = 0) => received}
            />
            <Table.Column
              title="本次收货"
              key="receiveQuantity"
              render={(_, record: OrderDetail) => (
                <Form.Item
                  name={record.id}
                  style={{ margin: 0 }}
                  rules={[
                    {
                      type: 'number',
                      min: 0,
                      max: record.quantity - (record.receivedQuantity || 0),
                      message: `收货数量不能超过 ${record.quantity - (record.receivedQuantity || 0)}`,
                    },
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={record.quantity - (record.receivedQuantity || 0)}
                    style={{ width: '100%' }}
                    placeholder="收货数量"
                  />
                </Form.Item>
              )}
            />
          </Table>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductPurchaseOrderDetail;
