import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Popconfirm,
  Select,
  DatePicker,
  Tag,
  Row,
  Col,
  Statistic,
} from 'antd';
import {
  PlusOutlined,
  EyeOutlined,
  CheckOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CopyOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import {
  getProductPurchaseOrderList,
  confirmPurchaseOrder,
  cancelPurchaseOrder,
} from '@/api/ProductPurchaseOrderApi';
import type {
  ProductPurchaseOrder,
  ProductPurchaseOrderStatus,
  GetProductPurchaseOrdersParams,
} from '@/types/productPurchaseOrder';
import { PURCHASE_ORDER_STATUS_CONFIG } from '@/types/productPurchaseOrder';
import { SupplierSearchSelector } from '@/components';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { getUser } from '@/utils/tokenManager';
import styles from './ProductPurchaseOrderManagement.module.css';

const { Search } = Input;
const { RangePicker } = DatePicker;

const ProductPurchaseOrderManagement: React.FC = () => {
  const navigate = useNavigate();
  const currentUser = getUser();
  const [orders, setOrders] = useState<ProductPurchaseOrder[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [supplierCode, setSupplierCode] = useState<string>('');
  const [status, setStatus] = useState<ProductPurchaseOrderStatus | ''>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalQuantity, setTotalQuantity] = useState(0);

  // 获取采购订单列表
  const fetchOrders = async (
    page = 1,
    pageSize = 20,
    search = '',
    supplier = '',
    orderStatus = '',
    startDate = '',
    endDate = '',
  ) => {
    setLoading(true);
    try {
      const params: GetProductPurchaseOrdersParams = {
        page,
        pageSize,
        orderNumber: search || undefined,
        supplierCode: supplier || undefined,
        status: (orderStatus as ProductPurchaseOrderStatus) || undefined,
        orderDateStart: startDate || undefined,
        orderDateEnd: endDate || undefined,
        sortBy: 'createdAt',
        sortOrder: 'DESC',
      };

      const response = await getProductPurchaseOrderList(params);
      const result = handleApiResponse(response, '', '获取采购订单列表失败');

      if (result.success && response.data) {
        const orderList = response.data.data || [];
        setOrders(orderList);
        // 计算统计信息
        setTotalAmount(orderList.reduce((sum, order) => sum + (order.totalAmount || 0), 0));
        setTotalQuantity(orderList.reduce((sum, order) => sum + (order.totalQuantity || 0), 0));
        setPagination({
          current: page,
          pageSize,
          total: response.data.total || 0,
        });
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取采购订单列表', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchOrders();
  }, []);

  // 搜索处理
  const handleSearch = (value?: string) => {
    const searchValue = value !== undefined ? value : searchText;
    setSearchText(searchValue);
    const [startDate, endDate] = dateRange
      ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')]
      : ['', ''];
    fetchOrders(1, pagination.pageSize, searchValue, supplierCode, status, startDate, endDate);
  };

  // 刷新数据
  const handleRefresh = () => {
    const [startDate, endDate] = dateRange
      ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')]
      : ['', ''];
    fetchOrders(
      pagination.current,
      pagination.pageSize,
      searchText,
      supplierCode,
      status,
      startDate,
      endDate,
    );
  };

  // 分页变化处理
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));
    const [startDate, endDate] = dateRange
      ? [dateRange[0].format('YYYY-MM-DD'), dateRange[1].format('YYYY-MM-DD')]
      : ['', ''];
    fetchOrders(current, pageSize, searchText, supplierCode, status, startDate, endDate);
  };

  // 新增采购订单（跳转到合并需求订单页面）
  const handleAdd = () => {
    navigate('/purchase/product-orders/merge');
  };

  // 查看订单详情
  const handleView = (order: ProductPurchaseOrder) => {
    navigate(`/purchase/product-orders/view/${order.id}`);
  };

  // 确认订单
  const handleConfirm = async (order: ProductPurchaseOrder) => {
    if (!currentUser?.code) {
      message.error('用户信息获取失败');
      return;
    }

    try {
      const response = await confirmPurchaseOrder(order.id, {
        confirmedByUserCode: currentUser.code,
      });
      const result = handleApiResponse(response, '订单确认成功', '订单确认失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('确认订单', error);
      message.error(getErrorMessage(error));
    }
  };

  // 取消订单
  const handleCancel = async (order: ProductPurchaseOrder) => {
    try {
      const response = await cancelPurchaseOrder(order.id);
      const result = handleApiResponse(response, '订单取消成功', '订单取消失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('取消订单', error);
      message.error(getErrorMessage(error));
    }
  };

  // 复制到剪贴板
  const handleCopy = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success(`${label}已复制到剪贴板`);
    } catch (error) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      textArea.select();
      try {
        document.execCommand('copy');
        message.success(`${label}已复制到剪贴板`);
      } catch (err) {
        message.error('复制失败');
      }
      document.body.removeChild(textArea);
    }
  };

  // 重置搜索
  const handleReset = () => {
    setSearchText('');
    setSupplierCode('');
    setStatus('');
    setDateRange(null);
    setPagination((prev) => ({ ...prev, current: 1 }));
    fetchOrders(1, pagination.pageSize);
  };

  // 表格行选择配置
  const rowSelection: TableRowSelection<ProductPurchaseOrder> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // 状态选项
  const statusOptions = [
    { label: '全部状态', value: '' },
    ...Object.entries(PURCHASE_ORDER_STATUS_CONFIG).map(([key, config]) => ({
      label: config.text,
      value: key,
    })),
  ];

  // 表格列定义
  const columns: ColumnsType<ProductPurchaseOrder> = [
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 180,
      fixed: 'left',
      render: (orderNumber: string) => (
        <div style={{ fontSize: '13px', display: 'flex', alignItems: 'center' }}>
          <div style={{ fontWeight: 500 }}>{orderNumber}</div>
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleCopy(orderNumber, '订单编号')}
            style={{ fontSize: '12px', padding: '0 4px', marginTop: '2px' }}
          />
        </div>
      ),
    },
    {
      title: '供应商',
      key: 'supplier',
      width: 120,
      ellipsis: true,
      render: (_, record) => (
        <span style={{ fontSize: '13px' }}>{record.supplier?.name || record.supplierCode}</span>
      ),
    },
    {
      title: '金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 100,
      render: (amount: number) => (
        <Tag color="green" style={{ fontSize: '12px' }}>
          ¥{amount?.toFixed(0)}
        </Tag>
      ),
      sorter: (a, b) => (a.totalAmount || 0) - (b.totalAmount || 0),
    },
    {
      title: '数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 80,
      render: (quantity: number) => (
        <Tag color="blue" style={{ fontSize: '12px' }}>
          {quantity}
        </Tag>
      ),
      sorter: (a, b) => (a.totalQuantity || 0) - (b.totalQuantity || 0),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: ProductPurchaseOrderStatus) => {
        const config = PURCHASE_ORDER_STATUS_CONFIG[status];
        return (
          <Tag color={config.color} className={styles.statusTag}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '订单日期',
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: 120,
      render: (date: string) => (
        <span style={{ fontSize: '13px' }}>{dayjs(date).format('YYYY-MM-DD')}</span>
      ),
      sorter: (a, b) => dayjs(a.orderDate).unix() - dayjs(b.orderDate).unix(),
    },
    {
      title: '创建人',
      key: 'creator',
      width: 100,
      ellipsis: true,
      render: (_, record) => (
        <span style={{ fontSize: '13px' }}>
          {record.createdByUser?.nickname || record.createdByUser?.name || record.createdByUserCode}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            style={{ fontSize: '12px', padding: '0 4px' }}
          >
            查看
          </Button>
          {record.status === 'draft' && (
            <>
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleConfirm(record)}
                style={{ fontSize: '12px', padding: '0 4px' }}
              >
                确认
              </Button>
              <Popconfirm
                title="确定取消？"
                onConfirm={() => handleCancel(record)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  style={{ fontSize: '12px', padding: '0 4px' }}
                >
                  取消
                </Button>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Card>
        {/* 统计信息 */}
        <Row gutter={8} style={{ marginBottom: 8 }}>
          <Col span={6}>
            <Statistic
              title="总金额"
              value={totalAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="订单总数"
              value={pagination.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic title="总数量" value={totalQuantity} valueStyle={{ color: '#722ed1' }} />
          </Col>
          <Col span={6}>
            <Statistic
              title="已选择"
              value={selectedRowKeys.length}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Col>
        </Row>

        {/* 搜索过滤区域 */}
        <div className={styles.searchSection}>
          <div className={styles.searchContent}>
            <Row gutter={[8, 8]}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>订单编号</label>
                  <div className={styles.searchInputWrapper}>
                    <Input
                      placeholder="搜索订单编号"
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      allowClear
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>供应商</label>
                  <div className={styles.searchInputWrapper}>
                    <SupplierSearchSelector
                      value={supplierCode}
                      onChange={setSupplierCode}
                      placeholder="选择供应商"
                      allowClear
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>订单状态</label>
                  <div className={styles.searchInputWrapper}>
                    <Select
                      value={status}
                      onChange={setStatus}
                      placeholder="选择状态"
                      allowClear
                      options={statusOptions}
                    />
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div className={styles.searchItem}>
                  <label className={styles.searchLabel}>订单日期</label>
                  <div className={styles.searchInputWrapper}>
                    <RangePicker
                      value={dateRange}
                      onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
                      format="YYYY-MM-DD"
                      placeholder={['开始日期', '结束日期']}
                    />
                  </div>
                </div>
              </Col>
            </Row>
            <div className={styles.searchActions}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={() => handleSearch()}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </div>
          </div>
        </div>

        {/* 工具栏 */}
        <div className={styles.toolbar}>
          <Row gutter={8} justify="space-between" align="middle">
            <Col flex="auto">
              <Space size="middle">
                <Search
                  placeholder="快速搜索订单编号"
                  allowClear
                  enterButton={<SearchOutlined />}
                  size="middle"
                  onSearch={(value) => {
                    setSearchText(value);
                    handleSearch(value);
                  }}
                  style={{ width: 250 }}
                />
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  合并订单
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          size="small"
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            size: 'small',
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default ProductPurchaseOrderManagement;
