import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  message,
  DatePicker,
  Table,
  Row,
  Col,
  Statistic,
  Divider,
} from 'antd';
import { SaveOutlined, ArrowLeftOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { mergeDemandOrders } from '@/api/ProductPurchaseOrderApi';
import { getMergeableDemandOrders } from '@/api/SalesDemandOrderApi';
import type { MergeDemandOrdersParams } from '@/types/productPurchaseOrder';
import type { SalesDemandOrder } from '@/types/salesDemand';
import { SupplierSearchSelector } from '@/components';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { getUser } from '@/utils/tokenManager';
import styles from './DemandOrderMerge.module.css';

const { TextArea } = Input;

const DemandOrderMerge: React.FC = () => {
  const navigate = useNavigate();
  const currentUser = getUser();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [loadingOrders, setLoadingOrders] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedOrders, setSelectedOrders] = useState<SalesDemandOrder[]>([]);
  const [demandOrders, setDemandOrders] = useState<SalesDemandOrder[]>([]);
  const [, setSupplierCode] = useState<string>('');

  // 获取可合并的需求订单
  const fetchMergeableOrders = async (supplier?: string) => {
    setLoadingOrders(true);
    try {
      const response = await getMergeableDemandOrders(
        supplier ? { supplierCode: supplier } : undefined,
      );
      if (response.code === 200) {
        setDemandOrders(response.data);
      } else {
        message.error('获取需求订单失败');
        setDemandOrders([]);
      }
    } catch (error) {
      logError('获取可合并需求订单', error);
      message.error(getErrorMessage(error));
      setDemandOrders([]);
    } finally {
      setLoadingOrders(false);
    }
  };

  // 初始加载
  React.useEffect(() => {
    fetchMergeableOrders();
  }, []);

  // 供应商变化时重新加载订单
  const handleSupplierChange = (value: string) => {
    setSupplierCode(value);
    setSelectedRowKeys([]);
    setSelectedOrders([]);
    if (value) {
      fetchMergeableOrders(value);
    } else {
      fetchMergeableOrders();
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/purchase/product-orders');
  };

  // 提交合并
  const handleSubmit = async (values: any) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要合并的需求订单');
      return;
    }

    if (!values.supplierCode) {
      message.warning('请选择供应商');
      return;
    }

    if (!currentUser?.code) {
      message.error('用户信息获取失败');
      return;
    }

    setLoading(true);
    try {
      const params: MergeDemandOrdersParams = {
        demandOrderIds: selectedRowKeys,
        supplierCode: values.supplierCode,
        expectedDeliveryDate: values.expectedDeliveryDate?.format('YYYY-MM-DD'),
        createdByUserCode: currentUser.code,
      };

      const response = await mergeDemandOrders(params);
      const result = handleApiResponse(response, '合并成功', '合并失败');

      if (result.success) {
        message.success(result.message);
        navigate('/purchase/product-orders');
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('合并需求订单', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 选择变化处理
  const handleSelectionChange = (
    newSelectedRowKeys: React.Key[],
    newSelectedRows: SalesDemandOrder[],
  ) => {
    setSelectedRowKeys(newSelectedRowKeys as string[]);
    setSelectedOrders(newSelectedRows);
  };

  // 计算汇总数据
  const summaryData = {
    totalOrders: selectedOrders.length,
    totalQuantity: selectedOrders.reduce((sum, order) => sum + order.totalQuantity, 0),
    totalAmount: selectedOrders.reduce((sum, order) => sum + order.estimatedTotalAmount, 0),
    totalProducts: selectedOrders.reduce((sum, order) => sum + (order.details?.length || 0), 0),
  };

  // 表格列定义
  const columns: ColumnsType<SalesDemandOrder> = [
    {
      title: '需求订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 150,
      render: (text: string) => <span className={styles.orderNumber}>{text}</span>,
    },
    {
      title: '销售人员',
      key: 'salesperson',
      width: 120,
      render: (_, record) => (
        <div>
          <div>{record.salesPerson?.nickname || record.salesPersonCode}</div>
          <div className={styles.personCode}>{record.salesPersonCode}</div>
        </div>
      ),
    },
    {
      title: '需求日期',
      dataIndex: 'demandDate',
      key: 'demandDate',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '商品数量',
      key: 'productCount',
      width: 100,
      render: (_, record) => <span>{record.details?.length || 0} 种</span>,
    },
    {
      title: '总数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 100,
    },
    {
      title: '总金额',
      dataIndex: 'estimatedTotalAmount',
      key: 'estimatedTotalAmount',
      width: 120,
      render: (amount: number) => <span className={styles.amount}>¥{amount.toFixed(2)}</span>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <span className={styles.status}>{status === 'approved' ? '已审核' : '其他'}</span>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: handleSelectionChange,
    getCheckboxProps: (record: SalesDemandOrder) => ({
      disabled: record.status !== 'approved',
    }),
  };

  return (
    <div className={styles.container}>
      <Card>
        <div className={styles.header}>
          <Button icon={<ArrowLeftOutlined />} onClick={handleBack} className={styles.backButton}>
            返回列表
          </Button>
          <h2 className={styles.title}>合并需求订单</h2>
        </div>

        <Divider />

        <Form form={form} layout="vertical" onFinish={handleSubmit} className={styles.form}>
          <Row gutter={24}>
            <Col xs={24} sm={12} lg={8}>
              <Form.Item
                label="供应商"
                name="supplierCode"
                rules={[{ required: true, message: '请选择供应商' }]}
              >
                <SupplierSearchSelector
                  placeholder="请选择供应商"
                  style={{ width: '100%' }}
                  onChange={handleSupplierChange}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <Form.Item label="预计交货日期" name="expectedDeliveryDate">
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="请选择预计交货日期"
                  disabledDate={(current) => current && current < dayjs().startOf('day')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="备注" name="remarks">
            <TextArea rows={3} placeholder="请输入备注信息" maxLength={500} showCount />
          </Form.Item>
        </Form>

        <Divider />

        {/* 汇总信息 */}
        {selectedOrders.length > 0 && (
          <>
            <Row gutter={16} className={styles.summarySection}>
              <Col xs={12} sm={6}>
                <Card size="small">
                  <Statistic
                    title="选中订单"
                    value={summaryData.totalOrders}
                    suffix="个"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col xs={12} sm={6}>
                <Card size="small">
                  <Statistic
                    title="商品种类"
                    value={summaryData.totalProducts}
                    suffix="种"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col xs={12} sm={6}>
                <Card size="small">
                  <Statistic
                    title="总数量"
                    value={summaryData.totalQuantity}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Card>
              </Col>
              <Col xs={12} sm={6}>
                <Card size="small">
                  <Statistic
                    title="总金额"
                    value={summaryData.totalAmount}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#f5222d' }}
                  />
                </Card>
              </Col>
            </Row>
            <Divider />
          </>
        )}

        {/* 需求订单列表 */}
        <Table
          columns={columns}
          dataSource={demandOrders}
          rowKey="id"
          rowSelection={rowSelection}
          pagination={false}
          loading={loadingOrders}
          className={styles.table}
          title={() => (
            <div className={styles.tableTitle}>
              <ShoppingCartOutlined />
              <span>待合并的需求订单</span>
            </div>
          )}
        />

        <div className={styles.footer}>
          <Space size="middle">
            <Button onClick={handleBack}>取消</Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={() => form.submit()}
              loading={loading}
              disabled={selectedRowKeys.length === 0}
            >
              确认合并
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default DemandOrderMerge;
