import React, { useState, useEffect } from 'react';
import { Button, Space, message } from 'antd';
import { ReloadOutlined, TableOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { getProductPurchaseOrderList } from '@/api/ProductPurchaseOrderApi';
import type {
  ProductPurchaseOrder,
  GetProductPurchaseOrdersParams,
} from '@/types/productPurchaseOrder';
import { StatusKanban } from '@/components';

const ProductPurchaseOrderKanban: React.FC = () => {
  const navigate = useNavigate();
  const [orders, setOrders] = useState<ProductPurchaseOrder[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取订单数据
  const fetchOrders = async () => {
    setLoading(true);
    try {
      const params: GetProductPurchaseOrdersParams = {
        page: 1,
        pageSize: 100,
      };

      const response = await getProductPurchaseOrderList(params);
      if (response.code === 200 && response.data) {
        const orderList = response.data.data || [];
        setOrders(orderList);
      }
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  // 看板列配置
  const kanbanColumns = [
    {
      key: 'draft',
      title: '待确认',
      color: '#faad14',
      count: orders.filter((o) => o.status === 'draft').length,
    },
    {
      key: 'confirmed',
      title: '已确认',
      color: '#1890ff',
      count: orders.filter((o) => o.status === 'confirmed').length,
    },
    {
      key: 'partial_received',
      title: '部分收货',
      color: '#722ed1',
      count: orders.filter((o) => o.status === 'partial_received').length,
    },
    {
      key: 'completed',
      title: '已完成',
      color: '#52c41a',
      count: orders.filter((o) => o.status === 'completed').length,
    },
  ];

  // 转换订单数据为看板项目
  const kanbanItems = orders.map((order) => ({
    id: order.id,
    title: order.orderNumber,
    subtitle: order.supplier?.name || order.supplierCode,
    amount: order.totalAmount,
    date: dayjs(order.orderDate).format('MM-DD'),
    status: order.status,
    priority: 'normal' as const,
  }));

  const handleItemClick = (item: any) => {
    const order = orders.find((o) => o.id === item.id);
    if (order) {
      navigate(`/purchase/product-orders/view/${order.id}`);
    }
  };

  const handleItemEdit = (item: any) => {
    const order = orders.find((o) => o.id === item.id);
    if (order && order.status === 'draft') {
      navigate(`/purchase/product-orders/edit/${order.id}`);
    }
  };

  return (
    <div style={{ padding: '16px' }}>
      {/* 工具栏 */}
      <div
        style={{
          marginBottom: '16px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Button icon={<TableOutlined />} onClick={() => navigate('/purchase/product-orders')}>
          表格视图
        </Button>
        <Button icon={<ReloadOutlined />} onClick={fetchOrders} loading={loading}>
          刷新
        </Button>
      </div>

      {/* 看板视图 */}
      <StatusKanban
        columns={kanbanColumns}
        items={kanbanItems}
        onItemClick={handleItemClick}
        onItemEdit={handleItemEdit}
        loading={loading}
      />
    </div>
  );
};

export default ProductPurchaseOrderKanban;
