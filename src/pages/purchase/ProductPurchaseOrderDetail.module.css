/* 简化的详情页面样式 */
.emptyState {
  text-align: center;
  padding: 60px 0;
  color: #8c8c8c;
  font-size: 16px;
}

/* 表格数据样式 */
.quantity {
  color: #1890ff;
  font-weight: 500;
}

.received {
  color: #52c41a;
  font-weight: 500;
}

.totalPrice {
  color: #f5222d;
  font-weight: 600;
}

/* 收货弹窗 */
.receiveModal .ant-modal-body {
  padding: 16px;
}

.receiveModal .ant-table {
  margin-bottom: 0;
}

/* 统计卡片 */
.container .ant-card-small .ant-card-body {
  padding: 16px;
}

.container .ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
  }

  .titleSection {
    width: 100%;
    justify-content: space-between;
  }

  .actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .header {
    gap: 12px;
  }

  .title {
    font-size: 18px;
  }

  .titleSection {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .actions {
    justify-content: center;
  }

  .descriptions {
    font-size: 14px;
  }

  .receiveModal {
    width: 95% !important;
    max-width: none;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 8px;
  }

  .title {
    font-size: 16px;
  }

  .actions .ant-space {
    width: 100%;
    justify-content: center;
  }

  .actions .ant-btn {
    flex: 1;
  }

  .tabs .ant-tabs-tab {
    font-size: 14px;
    padding: 8px 12px;
  }
}
