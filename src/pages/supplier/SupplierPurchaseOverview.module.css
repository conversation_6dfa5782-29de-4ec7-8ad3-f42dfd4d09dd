.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.filterCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summaryCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.summaryCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.table {
  background: white;
  border-radius: 8px;
}

/* 供应商信息单元格 */
.supplierInfoCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.supplierName {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.supplierCode {
  font-size: 12px;
  color: #1890ff;
  font-family: monospace;
}

.lastOrderDate {
  font-size: 12px;
  color: #8c8c8c;
}

/* 订单统计单元格 */
.orderStatsCell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.statItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.statLabel {
  color: #8c8c8c;
}

.statValue {
  font-weight: 500;
  color: #262626;
}

/* 金额显示 */
.totalAmount {
  font-weight: 600;
  color: #f5222d;
  font-size: 14px;
}

.avgAmount {
  font-weight: 500;
  color: #1890ff;
  font-size: 14px;
}

/* 交货率单元格 */
.deliveryRateCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rateText {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

/* 热门商品单元格 */
.topProductsCell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.productItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
}

.productItem:last-child {
  border-bottom: none;
}

.productName {
  color: #262626;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.productAmount {
  color: #f5222d;
  font-weight: 500;
  flex-shrink: 0;
}

.moreProducts {
  font-size: 12px;
  color: #8c8c8c;
  text-align: center;
  padding: 2px 0;
  font-style: italic;
}

/* 操作按钮 */
.actionButton {
  padding: 4px;
  width: 28px;
  height: 28px;
  min-width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.actionButton:hover {
  background-color: #f0f0f0;
}

/* 统计卡片样式 */
.summaryCard .ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.summaryCard .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 快速导航样式 */
.linkCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.linkCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.linkContent {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.linkIcon {
  font-size: 32px;
  color: #1890ff;
  flex-shrink: 0;
}

.linkTitle {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.linkDescription {
  font-size: 14px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .filterCard .ant-row {
    flex-direction: column;
    gap: 12px;
  }

  .supplierInfoCell,
  .orderStatsCell,
  .topProductsCell {
    min-width: 120px;
  }

  .summaryCard .ant-statistic-content {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  .filterCard .ant-space {
    flex-direction: column;
    width: 100%;
  }

  .filterCard .ant-space > * {
    width: 100%;
  }
}
