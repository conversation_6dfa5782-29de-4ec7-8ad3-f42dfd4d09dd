.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.headerCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.titleIcon {
  color: #1890ff;
  font-size: 20px;
}

.title {
  margin: 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.infoCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.statisticCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.statisticCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.chartCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table {
  background: white;
  border-radius: 8px;
}

.emptyState {
  text-align: center;
  padding: 60px 0;
  color: #8c8c8c;
  font-size: 16px;
}

/* 人员编码 */
.personCode {
  font-size: 12px;
  color: #8c8c8c;
  font-family: monospace;
}

/* 贡献率单元格 */
.contributionCell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contributionText {
  font-size: 12px;
  font-weight: 500;
  min-width: 40px;
}

/* 统计卡片样式 */
.statisticCard .ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.statisticCard .ant-statistic-content {
  font-weight: 600;
}

/* 描述列表样式 */
.infoCard .ant-descriptions-item-label {
  font-weight: 500;
  color: #595959;
}

.infoCard .ant-descriptions-item-content {
  color: #262626;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
  
  .headerCard .ant-row {
    flex-direction: column;
    gap: 16px;
  }
  
  .titleSection {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .title {
    font-size: 16px;
  }
  
  .titleIcon {
    font-size: 18px;
  }
  
  .statisticCard .ant-statistic-content {
    font-size: 16px !important;
  }
  
  .contributionCell {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 576px) {
  .headerCard .ant-space {
    flex-direction: column;
    width: 100%;
  }
  
  .headerCard .ant-space > * {
    width: 100%;
  }
  
  .titleSection {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .tabs .ant-tabs-tab {
    font-size: 14px;
    padding: 8px 12px;
  }
}
