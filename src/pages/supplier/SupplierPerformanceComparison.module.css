.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.headerCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 8px;
}

.titleIcon {
  color: #1890ff;
  font-size: 20px;
}

.title {
  margin: 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.selectedCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selectedSection {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.selectedLabel {
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
}

.selectedSupplier {
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 16px;
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.selectedSupplier:hover {
  background-color: #bae7ff;
  border-color: #69c0ff;
}

.chartCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 400px;
}

.table {
  background: white;
  border-radius: 8px;
}

/* 供应商信息单元格 */
.supplierInfoCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.supplierName {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.supplierCode {
  font-size: 12px;
  color: #1890ff;
  font-family: monospace;
}

.removeButton {
  padding: 0;
  height: auto;
  font-size: 12px;
  color: #ff4d4f;
}

/* 金额显示 */
.totalAmount {
  font-weight: 600;
  color: #f5222d;
  font-size: 14px;
}

/* 雷达图容器 */
.radarContainer {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radarPlaceholder {
  text-align: center;
  color: #8c8c8c;
}

.radarIcon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.radarDesc {
  font-size: 12px;
  color: #bfbfbf;
  margin-top: 8px;
}

/* 指标对比 */
.metricsComparison {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 320px;
  overflow-y: auto;
}

.metricItem {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.metricHeader {
  margin-bottom: 8px;
}

.metricSupplier {
  font-weight: 600;
  color: #262626;
}

.metricValues {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.metricValue {
  display: flex;
  align-items: center;
  gap: 4px;
}

.metricLabel {
  font-size: 12px;
  color: #8c8c8c;
}

/* 绩效评级样式 */
.excellent {
  color: #52c41a;
  font-weight: 600;
}

.good {
  color: #1890ff;
  font-weight: 500;
}

.average {
  color: #faad14;
  font-weight: 500;
}

.poor {
  color: #ff4d4f;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
  
  .headerCard .ant-row {
    flex-direction: column;
    gap: 16px;
  }
  
  .selectedSection {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .title {
    font-size: 16px;
  }
  
  .titleIcon {
    font-size: 18px;
  }
  
  .chartCard {
    height: 300px;
  }
  
  .radarContainer {
    height: 240px;
  }
  
  .radarIcon {
    font-size: 36px;
  }
  
  .metricsComparison {
    height: 240px;
  }
  
  .metricValues {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 576px) {
  .headerCard .ant-space {
    flex-direction: column;
    width: 100%;
  }
  
  .headerCard .ant-space > * {
    width: 100%;
  }
  
  .selectedSection .ant-space {
    width: 100%;
  }
}
