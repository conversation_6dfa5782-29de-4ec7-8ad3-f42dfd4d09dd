import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Button,
  Space,
  message,
  DatePicker,
  Statistic,
  Progress,
  Tag,
} from 'antd';
import {
  EyeOutlined,
  ReloadOutlined,
  TrophyOutlined,
  BarChartOutlined,
  ShoppingCartOutlined,
  LineChartOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { getSupplierOverview } from '@/api/SupplierPurchaseOverviewApi';
import type { SupplierOverviewItem, GetSupplierOverviewParams } from '@/types/supplierPurchase';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import styles from './SupplierPurchaseOverview.module.css';

const { RangePicker } = DatePicker;

const SupplierPurchaseOverview: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState<SupplierOverviewItem[]>([]);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);

  // 获取供应商概览数据
  const fetchSupplierOverview = async () => {
    setLoading(true);
    try {
      const params: GetSupplierOverviewParams = {
        startDate: dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: dateRange?.[1]?.format('YYYY-MM-DD'),
      };

      const response = await getSupplierOverview(params);
      const result = handleApiResponse(response, '', '获取供应商概览失败');

      if (result.success && response.data) {
        setSuppliers(response.data);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取供应商概览', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchSupplierOverview();
  }, []);

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  // 应用筛选
  const handleApplyFilter = () => {
    fetchSupplierOverview();
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchSupplierOverview();
  };

  // 查看供应商详情
  const handleViewDetail = (supplier: SupplierOverviewItem) => {
    navigate(`/supplier/purchase-overview/${supplier.supplierCode}`);
  };

  // 查看排行榜
  const handleViewRanking = () => {
    navigate('/supplier/purchase-ranking');
  };

  // 查看绩效对比
  const handleViewComparison = () => {
    navigate('/supplier/performance-comparison');
  };

  // 计算汇总统计
  const summaryStats = {
    totalSuppliers: suppliers.length,
    totalOrders: suppliers.reduce((sum, item) => sum + item.totalOrders, 0),
    totalAmount: suppliers.reduce((sum, item) => sum + item.totalAmount, 0),
    avgDeliveryRate:
      suppliers.length > 0
        ? suppliers.reduce((sum, item) => sum + item.onTimeDeliveryRate, 0) / suppliers.length
        : 0,
  };

  // 表格列定义
  const columns: ColumnsType<SupplierOverviewItem> = [
    {
      title: '供应商信息',
      key: 'supplierInfo',
      width: 200,
      fixed: 'left',
      render: (_, record) => (
        <div className={styles.supplierInfoCell}>
          <div className={styles.supplierName}>{record.supplierName}</div>
          <div className={styles.supplierCode}>{record.supplierCode}</div>
          <div className={styles.lastOrderDate}>
            最后订单: {dayjs(record.lastOrderDate).format('YYYY-MM-DD')}
          </div>
        </div>
      ),
    },
    {
      title: '订单统计',
      key: 'orderStats',
      width: 150,
      render: (_, record) => (
        <div className={styles.orderStatsCell}>
          <div className={styles.statItem}>
            <span className={styles.statLabel}>总订单:</span>
            <span className={styles.statValue}>{record.totalOrders}</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statLabel}>待处理:</span>
            <span className={styles.statValue}>{record.pendingOrders}</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statLabel}>已完成:</span>
            <span className={styles.statValue}>{record.completedOrders}</span>
          </div>
        </div>
      ),
    },
    {
      title: '采购金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      sorter: (a, b) => a.totalAmount - b.totalAmount,
      render: (amount: number) => <span className={styles.totalAmount}>¥{amount.toFixed(2)}</span>,
    },
    {
      title: '平均订单金额',
      dataIndex: 'avgOrderAmount',
      key: 'avgOrderAmount',
      width: 120,
      sorter: (a, b) => a.avgOrderAmount - b.avgOrderAmount,
      render: (amount: number) => <span className={styles.avgAmount}>¥{amount.toFixed(2)}</span>,
    },
    {
      title: '按时交货率',
      dataIndex: 'onTimeDeliveryRate',
      key: 'onTimeDeliveryRate',
      width: 120,
      sorter: (a, b) => a.onTimeDeliveryRate - b.onTimeDeliveryRate,
      render: (rate: number) => (
        <div className={styles.deliveryRateCell}>
          <Progress
            percent={rate}
            size="small"
            strokeColor={rate >= 90 ? '#52c41a' : rate >= 70 ? '#faad14' : '#ff4d4f'}
            showInfo={false}
          />
          <span className={styles.rateText}>{rate.toFixed(1)}%</span>
        </div>
      ),
    },
    {
      title: '有效合同',
      dataIndex: 'activeContracts',
      key: 'activeContracts',
      width: 100,
      render: (count: number) => <Tag color={count > 0 ? 'green' : 'default'}>{count} 个</Tag>,
    },
    {
      title: '热门商品',
      key: 'topProducts',
      width: 200,
      render: (_, record) => (
        <div className={styles.topProductsCell}>
          {record.topProducts.slice(0, 3).map((product) => (
            <div key={product.productCode} className={styles.productItem}>
              <span className={styles.productName}>{product.productName}</span>
              <span className={styles.productAmount}>¥{product.totalAmount.toFixed(0)}</span>
            </div>
          ))}
          {record.topProducts.length > 3 && (
            <div className={styles.moreProducts}>+{record.topProducts.length - 3} 更多</div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record)}
          className={styles.actionButton}
          title="查看详情"
        />
      ),
    },
  ];

  return (
    <div className={styles.container}>
      {/* 筛选和操作栏 */}
      <Card className={styles.filterCard}>
        <Row gutter={16} justify="space-between" align="middle">
          <Col>
            <Space size="middle">
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                placeholder={['开始日期', '结束日期']}
                style={{ width: 240 }}
              />
              <Button type="primary" onClick={handleApplyFilter} loading={loading}>
                查询
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
                刷新
              </Button>
            </Space>
          </Col>
          <Col>
            <Space size="middle">
              <Button icon={<TrophyOutlined />} onClick={handleViewRanking} type="default">
                排行榜
              </Button>
              <Button icon={<BarChartOutlined />} onClick={handleViewComparison} type="default">
                绩效对比
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 汇总统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card className={styles.summaryCard}>
            <Statistic
              title="供应商总数"
              value={summaryStats.totalSuppliers}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className={styles.summaryCard}>
            <Statistic
              title="总订单数"
              value={summaryStats.totalOrders}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className={styles.summaryCard}>
            <Statistic
              title="总采购金额"
              value={summaryStats.totalAmount}
              precision={2}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className={styles.summaryCard}>
            <Statistic
              title="平均交货率"
              value={summaryStats.avgDeliveryRate}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 供应商列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={suppliers}
          rowKey="supplierCode"
          loading={loading}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
          className={styles.table}
        />
      </Card>

      {/* 快速导航 */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} sm={12}>
          <Card
            className={styles.linkCard}
            hoverable
            onClick={() => navigate('/supplier/purchase-ranking')}
          >
            <div className={styles.linkContent}>
              <TrophyOutlined className={styles.linkIcon} />
              <div>
                <div className={styles.linkTitle}>采购排行榜</div>
                <div className={styles.linkDescription}>查看供应商采购排行榜</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12}>
          <Card
            className={styles.linkCard}
            hoverable
            onClick={() => navigate('/supplier/performance-comparison')}
          >
            <div className={styles.linkContent}>
              <LineChartOutlined className={styles.linkIcon} />
              <div>
                <div className={styles.linkTitle}>绩效对比</div>
                <div className={styles.linkDescription}>对比多个供应商的绩效表现</div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SupplierPurchaseOverview;
