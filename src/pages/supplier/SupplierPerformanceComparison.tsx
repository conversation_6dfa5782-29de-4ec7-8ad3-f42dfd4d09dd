import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Table, Button, Space, message, Spin, Empty } from 'antd';
import {
  ArrowLeftOutlined,
  ReloadOutlined,
  BarChartOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import { getSupplierPerformanceComparison } from '@/api/SupplierPurchaseOverviewApi';
import type {
  SupplierPerformanceItem,
  GetSupplierPerformanceParams,
} from '@/types/supplierPurchase';
import { SupplierSearchSelector } from '@/components';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { formatAmountSimple } from '@/utils/formatUtils';
import styles from './SupplierPerformanceComparison.module.css';

const SupplierPerformanceComparison: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [performanceData, setPerformanceData] = useState<SupplierPerformanceItem[]>([]);
  const [selectedSuppliers, setSelectedSuppliers] = useState<string[]>([]);

  // 获取绩效对比数据
  const fetchPerformanceData = async () => {
    if (selectedSuppliers.length === 0) {
      setPerformanceData([]);
      return;
    }

    setLoading(true);
    try {
      const params: GetSupplierPerformanceParams = {
        supplierCodes: selectedSuppliers.join(','),
      };

      const response = await getSupplierPerformanceComparison(params);
      const result = handleApiResponse(response, '', '获取供应商绩效对比失败');

      if (result.success && response.data) {
        setPerformanceData(response.data);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取供应商绩效对比', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchPerformanceData();
  }, [selectedSuppliers]);

  // 返回概览页面
  const handleBack = () => {
    navigate('/supplier/purchase-overview');
  };

  // 添加供应商
  const handleAddSupplier = (supplierCode: string) => {
    if (supplierCode && !selectedSuppliers.includes(supplierCode)) {
      if (selectedSuppliers.length >= 6) {
        message.warning('最多只能对比6个供应商');
        return;
      }
      setSelectedSuppliers([...selectedSuppliers, supplierCode]);
    }
  };

  // 移除供应商
  const handleRemoveSupplier = (supplierCode: string) => {
    setSelectedSuppliers(selectedSuppliers.filter((code) => code !== supplierCode));
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchPerformanceData();
  };

  // 查看供应商详情
  const handleViewDetail = (supplier: SupplierPerformanceItem) => {
    navigate(`/supplier/purchase-overview/${supplier.supplierCode}`);
  };

  // 表格列定义
  const columns: ColumnsType<SupplierPerformanceItem> = [
    {
      title: '供应商信息',
      key: 'supplierInfo',
      width: 200,
      fixed: 'left',
      render: (_, record) => (
        <div className={styles.supplierInfoCell}>
          <div className={styles.supplierName}>{record.supplierName}</div>
          <div className={styles.supplierCode}>{record.supplierCode}</div>
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleRemoveSupplier(record.supplierCode)}
            className={styles.removeButton}
          >
            移除
          </Button>
        </div>
      ),
    },
    {
      title: '订单数',
      dataIndex: 'totalOrders',
      key: 'totalOrders',
      width: 100,
      sorter: (a, b) => a.totalOrders - b.totalOrders,
    },
    {
      title: '采购金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      sorter: (a, b) => a.totalAmount - b.totalAmount,
      render: (amount: number) => (
        <span className={styles.totalAmount}>{formatAmountSimple(amount)}</span>
      ),
    },
    {
      title: '采购数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 100,
      sorter: (a, b) => a.totalQuantity - b.totalQuantity,
    },
    {
      title: '平均订单金额',
      dataIndex: 'avgOrderAmount',
      key: 'avgOrderAmount',
      width: 130,
      sorter: (a, b) => a.avgOrderAmount - b.avgOrderAmount,
      render: (amount: number) => formatAmountSimple(amount),
    },
    {
      title: '平均交货天数',
      dataIndex: 'avgDeliveryDays',
      key: 'avgDeliveryDays',
      width: 120,
      sorter: (a, b) => a.avgDeliveryDays - b.avgDeliveryDays,
      render: (days: number) => (
        <span className={getDaysClassName(days)}>{days.toFixed(1)} 天</span>
      ),
    },
    {
      title: '按时交货率',
      dataIndex: 'onTimeDeliveryRate',
      key: 'onTimeDeliveryRate',
      width: 120,
      sorter: (a, b) => a.onTimeDeliveryRate - b.onTimeDeliveryRate,
      render: (rate: number) => <span className={getRateClassName(rate)}>{rate.toFixed(1)}%</span>,
    },
    {
      title: '质量评分',
      dataIndex: 'qualityScore',
      key: 'qualityScore',
      width: 100,
      sorter: (a, b) => a.qualityScore - b.qualityScore,
      render: (score: number) => (
        <span className={getScoreClassName(score)}>{score.toFixed(1)}</span>
      ),
    },
    {
      title: '合作月数',
      dataIndex: 'cooperationMonths',
      key: 'cooperationMonths',
      width: 100,
      sorter: (a, b) => a.cooperationMonths - b.cooperationMonths,
      render: (months: number) => `${months} 个月`,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Button type="link" size="small" onClick={() => handleViewDetail(record)}>
          详情
        </Button>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      {/* 头部操作栏 */}
      <Card className={styles.headerCard}>
        <Row gutter={16} justify="space-between" align="middle">
          <Col>
            <Space size="middle">
              <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
                返回概览
              </Button>
              <div className={styles.titleSection}>
                <BarChartOutlined className={styles.titleIcon} />
                <h2 className={styles.title}>供应商绩效对比</h2>
              </div>
            </Space>
          </Col>
          <Col>
            <Space size="middle">
              <SupplierSearchSelector
                placeholder="选择供应商进行对比"
                style={{ width: 200 }}
                onChange={handleAddSupplier}
                value={undefined}
              />
              <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 已选择的供应商 */}
      {selectedSuppliers.length > 0 && (
        <Card className={styles.selectedCard}>
          <div className={styles.selectedSection}>
            <span className={styles.selectedLabel}>
              已选择供应商 ({selectedSuppliers.length}/6):
            </span>
            <Space wrap>
              {selectedSuppliers.map((code) => (
                <Button
                  key={code}
                  size="small"
                  type="default"
                  onClick={() => handleRemoveSupplier(code)}
                  className={styles.selectedSupplier}
                >
                  {code} <DeleteOutlined />
                </Button>
              ))}
            </Space>
          </div>
        </Card>
      )}

      <Spin spinning={loading}>
        {performanceData.length === 0 ? (
          <Card>
            <Empty description="请选择供应商进行绩效对比" image={Empty.PRESENTED_IMAGE_SIMPLE} />
          </Card>
        ) : (
          <Row gutter={[16, 16]}>
            {/* 雷达图对比 */}
            <Col xs={24} lg={12}>
              <Card title="综合绩效雷达图" className={styles.chartCard}>
                <div className={styles.radarContainer}>
                  {/* 这里应该使用实际的雷达图组件 */}
                  <div className={styles.radarPlaceholder}>
                    <BarChartOutlined className={styles.radarIcon} />
                    <p>雷达图对比</p>
                    <p className={styles.radarDesc}>
                      对比维度：采购金额、订单数量、交货及时性、质量评分、合作稳定性
                    </p>
                  </div>
                </div>
              </Card>
            </Col>

            {/* 关键指标对比 */}
            <Col xs={24} lg={12}>
              <Card title="关键指标对比" className={styles.chartCard}>
                <div className={styles.metricsComparison}>
                  {performanceData.map((supplier, index) => (
                    <div key={supplier.supplierCode} className={styles.metricItem}>
                      <div className={styles.metricHeader}>
                        <span className={styles.metricSupplier}>{supplier.supplierName}</span>
                      </div>
                      <div className={styles.metricValues}>
                        <div className={styles.metricValue}>
                          <span className={styles.metricLabel}>交货率:</span>
                          <span className={getRateClassName(supplier.onTimeDeliveryRate)}>
                            {supplier.onTimeDeliveryRate.toFixed(1)}%
                          </span>
                        </div>
                        <div className={styles.metricValue}>
                          <span className={styles.metricLabel}>质量分:</span>
                          <span className={getScoreClassName(supplier.qualityScore)}>
                            {supplier.qualityScore.toFixed(1)}
                          </span>
                        </div>
                        <div className={styles.metricValue}>
                          <span className={styles.metricLabel}>交货天数:</span>
                          <span className={getDaysClassName(supplier.avgDeliveryDays)}>
                            {supplier.avgDeliveryDays.toFixed(1)}天
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>

            {/* 详细对比表格 */}
            <Col xs={24}>
              <Card title="详细数据对比">
                <Table
                  columns={columns}
                  dataSource={performanceData}
                  rowKey="supplierCode"
                  pagination={false}
                  scroll={{ x: 1200 }}
                  className={styles.table}
                />
              </Card>
            </Col>
          </Row>
        )}
      </Spin>
    </div>
  );
};

// 获取交货天数样式类名
const getDaysClassName = (days: number): string => {
  if (days <= 7) return 'excellent';
  if (days <= 15) return 'good';
  if (days <= 30) return 'average';
  return 'poor';
};

// 获取交货率样式类名
const getRateClassName = (rate: number): string => {
  if (rate >= 95) return 'excellent';
  if (rate >= 85) return 'good';
  if (rate >= 70) return 'average';
  return 'poor';
};

// 获取质量评分样式类名
const getScoreClassName = (score: number): string => {
  if (score >= 9) return 'excellent';
  if (score >= 8) return 'good';
  if (score >= 7) return 'average';
  return 'poor';
};

export default SupplierPerformanceComparison;
