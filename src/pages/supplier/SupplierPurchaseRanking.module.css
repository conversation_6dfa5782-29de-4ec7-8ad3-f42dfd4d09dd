.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.headerCard {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 8px;
}

.titleIcon {
  color: #faad14;
  font-size: 20px;
}

.title {
  margin: 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.summaryCard {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.summaryCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.table {
  background: white;
  border-radius: 8px;
}

/* 排名单元格 */
.rankCell {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-weight: bold;
  font-size: 16px;
}

.goldIcon {
  color: #ffd700;
  font-size: 24px;
}

.silverIcon {
  color: #c0c0c0;
  font-size: 22px;
}

.bronzeIcon {
  color: #cd7f32;
  font-size: 20px;
}

.rankNumber {
  color: #262626;
  font-weight: 600;
}

/* 排名行样式 */
.topRank {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2e6 100%);
  border-left: 4px solid #faad14;
}

.highRank {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9e6 100%);
  border-left: 4px solid #52c41a;
}

.normalRank {
  background: white;
}

/* 供应商信息单元格 */
.supplierInfoCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.supplierName {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.supplierCode {
  font-size: 12px;
  color: #1890ff;
  font-family: monospace;
}

/* 金额显示 */
.totalAmount {
  font-weight: 600;
  color: #f5222d;
  font-size: 14px;
}

/* 交货率单元格 */
.deliveryRateCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rateText {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

/* 统计卡片样式 */
.summaryCard .ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.summaryCard .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 表格行悬停效果 */
.table .ant-table-tbody > tr:hover > td {
  background: #e6f7ff !important;
}

.table .topRank:hover > td {
  background: #fff1b8 !important;
}

.table .highRank:hover > td {
  background: #d9f7be !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
  
  .headerCard .ant-row {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .title {
    font-size: 16px;
  }
  
  .titleIcon {
    font-size: 18px;
  }
  
  .summaryCard .ant-statistic-content {
    font-size: 20px;
  }
  
  .rankCell {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .goldIcon {
    font-size: 20px;
  }
  
  .silverIcon {
    font-size: 18px;
  }
  
  .bronzeIcon {
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .headerCard .ant-space {
    flex-direction: column;
    width: 100%;
  }
  
  .headerCard .ant-space > * {
    width: 100%;
  }
}
