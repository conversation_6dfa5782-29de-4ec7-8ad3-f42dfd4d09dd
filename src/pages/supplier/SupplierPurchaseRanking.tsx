import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Button,
  Space,
  message,
  DatePicker,
  InputNumber,
  Statistic,
  Progress,
  Tag,
} from 'antd';
import {
  ArrowLeftOutlined,
  ReloadOutlined,
  TrophyOutlined,
  CrownOutlined,
  GiftOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { getSupplierRanking } from '@/api/SupplierPurchaseOverviewApi';
import type { SupplierRankingItem, GetSupplierRankingParams } from '@/types/supplierPurchase';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { formatAmountSimple } from '@/utils/formatUtils';
import styles from './SupplierPurchaseRanking.module.css';

const { RangePicker } = DatePicker;

const SupplierPurchaseRanking: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [rankings, setRankings] = useState<SupplierRankingItem[]>([]);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [limit, setLimit] = useState<number>(20);

  // 获取排行榜数据
  const fetchRanking = async () => {
    setLoading(true);
    try {
      const params: GetSupplierRankingParams = {
        startDate: dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: dateRange?.[1]?.format('YYYY-MM-DD'),
        limit,
      };

      const response = await getSupplierRanking(params);
      const result = handleApiResponse(response, '', '获取供应商排行榜失败');

      if (result.success && response.data) {
        setRankings(response.data);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取供应商排行榜', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchRanking();
  }, []);

  // 返回概览页面
  const handleBack = () => {
    navigate('/supplier/purchase-overview');
  };

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  // 限制数量变化处理
  const handleLimitChange = (value: number | null) => {
    if (value && value > 0) {
      setLimit(value);
    }
  };

  // 应用筛选
  const handleApplyFilter = () => {
    fetchRanking();
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchRanking();
  };

  // 查看供应商详情
  const handleViewDetail = (supplier: SupplierRankingItem) => {
    navigate(`/supplier/purchase-overview/${supplier.supplierCode}`);
  };

  // 获取排名图标
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <CrownOutlined className={styles.goldIcon} />;
      case 2:
        return <TrophyOutlined className={styles.silverIcon} />;
      case 3:
        return <GiftOutlined className={styles.bronzeIcon} />;
      default:
        return <span className={styles.rankNumber}>{rank}</span>;
    }
  };

  // 获取排名样式
  const getRankClassName = (rank: number) => {
    if (rank <= 3) return styles.topRank;
    if (rank <= 10) return styles.highRank;
    return styles.normalRank;
  };

  // 计算汇总统计
  const summaryStats = {
    totalSuppliers: rankings.length,
    totalAmount: rankings.reduce((sum, item) => sum + (item.totalAmount || 0), 0),
    totalOrders: rankings.reduce((sum, item) => sum + (item.totalOrders || 0), 0),
    avgDeliveryRate:
      rankings.length > 0
        ? rankings.reduce((sum, item) => sum + (item.onTimeDeliveryRate || 0), 0) / rankings.length
        : 0,
  };

  // 表格列定义
  const columns: ColumnsType<SupplierRankingItem> = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 80,
      fixed: 'left',
      render: (rank: number) => (
        <div className={`${styles.rankCell} ${getRankClassName(rank)}`}>{getRankIcon(rank)}</div>
      ),
    },
    {
      title: '供应商信息',
      key: 'supplierInfo',
      width: 200,
      render: (_, record) => (
        <div className={styles.supplierInfoCell}>
          <div className={styles.supplierName}>{record.supplierName}</div>
          <div className={styles.supplierCode}>{record.supplierCode}</div>
        </div>
      ),
    },
    {
      title: '采购金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 150,
      sorter: (a, b) => (a.totalAmount || 0) - (b.totalAmount || 0),
      render: (amount: number) => (
        <span className={styles.totalAmount}>{formatAmountSimple(amount || 0)}</span>
      ),
    },
    {
      title: '订单数',
      dataIndex: 'totalOrders',
      key: 'totalOrders',
      width: 100,
      sorter: (a, b) => (a.totalOrders || 0) - (b.totalOrders || 0),
    },
    {
      title: '采购数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 120,
      sorter: (a, b) => (a.totalQuantity || 0) - (b.totalQuantity || 0),
    },
    {
      title: '平均订单金额',
      dataIndex: 'avgOrderAmount',
      key: 'avgOrderAmount',
      width: 130,
      sorter: (a, b) => (a.avgOrderAmount || 0) - (b.avgOrderAmount || 0),
      render: (amount: number) => formatAmountSimple(amount || 0),
    },
    {
      title: '按时交货率',
      dataIndex: 'onTimeDeliveryRate',
      key: 'onTimeDeliveryRate',
      width: 120,
      sorter: (a, b) => (a.onTimeDeliveryRate || 0) - (b.onTimeDeliveryRate || 0),
      render: (rate: number) => {
        const safeRate = rate || 0;
        return (
          <div className={styles.deliveryRateCell}>
            <Progress
              percent={safeRate}
              size="small"
              strokeColor={safeRate >= 90 ? '#52c41a' : safeRate >= 70 ? '#faad14' : '#ff4d4f'}
              showInfo={false}
            />
            <span className={styles.rateText}>{safeRate.toFixed(1)}%</span>
          </div>
        );
      },
    },
    {
      title: '增长率',
      dataIndex: 'growthRate',
      key: 'growthRate',
      width: 100,
      sorter: (a, b) => (a.growthRate || 0) - (b.growthRate || 0),
      render: (rate: number) => {
        const safeRate = rate || 0;
        return (
          <Tag color={safeRate > 0 ? 'green' : safeRate < 0 ? 'red' : 'default'}>
            {safeRate > 0 ? '+' : ''}
            {safeRate.toFixed(1)}%
          </Tag>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Button type="link" size="small" onClick={() => handleViewDetail(record)}>
          详情
        </Button>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      {/* 头部操作栏 */}
      <Card className={styles.headerCard}>
        <Row gutter={16} justify="space-between" align="middle">
          <Col>
            <Space size="middle">
              <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
                返回概览
              </Button>
              <div className={styles.titleSection}>
                <TrophyOutlined className={styles.titleIcon} />
                <h2 className={styles.title}>供应商采购排行榜</h2>
              </div>
            </Space>
          </Col>
          <Col>
            <Space size="middle">
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                placeholder={['开始日期', '结束日期']}
                style={{ width: 240 }}
              />
              <InputNumber
                value={limit}
                onChange={handleLimitChange}
                min={5}
                max={100}
                placeholder="显示数量"
                style={{ width: 100 }}
              />
              <Button type="primary" onClick={handleApplyFilter} loading={loading}>
                查询
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 汇总统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card className={styles.summaryCard}>
            <Statistic
              title="参与排名供应商"
              value={summaryStats.totalSuppliers}
              suffix="家"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className={styles.summaryCard}>
            <Statistic
              title="总采购金额"
              value={summaryStats.totalAmount}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className={styles.summaryCard}>
            <Statistic
              title="总订单数"
              value={summaryStats.totalOrders}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className={styles.summaryCard}>
            <Statistic
              title="平均交货率"
              value={summaryStats.avgDeliveryRate}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 排行榜表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={rankings}
          rowKey="supplierCode"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
          className={styles.table}
          rowClassName={(record) => getRankClassName(record.rank)}
        />
      </Card>
    </div>
  );
};

export default SupplierPurchaseRanking;
