import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Descriptions,
  Table,
  Button,
  Space,
  message,
  Statistic,
  Tabs,
  Tag,
  Progress,
} from 'antd';
import { ArrowLeftOutlined, ReloadOutlined, UserOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { getSupplierPurchaseDetail } from '@/api/SupplierPurchaseOverviewApi';
import { Line, Pie } from '@/components/charts';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { formatAmountSimple } from '@/utils/formatUtils';
import styles from './SupplierPurchaseDetail.module.css';

interface SupplierDetail {
  supplier: {
    code: string;
    name: string;
    address: string;
    contactName: string;
    contactPhone: string;
    createdAt: string;
    updatedAt: string;
  };
  summary: {
    totalOrders: number;
    totalAmount: number;
    totalQuantity: number;
    avgOrderAmount: number;
    avgDeliveryDays: number;
    onTimeDeliveryRate: number;
  };
  ordersByStatus: { [status: string]: number };
  monthlyTrend: Array<{
    month: string;
    orderCount: number;
    totalAmount: number;
    avgDeliveryDays: number;
  }>;
  topProducts: Array<{
    productCode: string;
    productName: string;
    brandCode: string;
    categoryCode: string;
    totalQuantity: number;
    totalAmount: number;
    avgPrice: number;
    orderCount: number;
  }>;
  demandSources: Array<{
    salesPersonCode: string;
    salesPersonName: string;
    demandOrderCount: number;
    totalDemandQuantity: number;
    contributionRate: number;
  }>;
  recentOrders: Array<{
    id: string;
    orderNumber: string;
    orderDate: string;
    totalAmount: number;
    totalQuantity: number;
    status: string;
    createdByUserCode: string;
  }>;
}

const SupplierPurchaseDetail: React.FC = () => {
  const navigate = useNavigate();
  const { supplierCode } = useParams<{ supplierCode: string }>();
  const [loading, setLoading] = useState(false);
  const [detail, setDetail] = useState<SupplierDetail | null>(null);

  // 获取供应商详情
  const fetchSupplierDetail = async () => {
    if (!supplierCode) return;

    setLoading(true);
    try {
      const response = await getSupplierPurchaseDetail(supplierCode);
      const result = handleApiResponse(response, '', '获取供应商详情失败');

      if (result.success && response.data) {
        setDetail(response.data);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      logError('获取供应商详情', error);
      message.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchSupplierDetail();
  }, [supplierCode]);

  // 返回概览页面
  const handleBack = () => {
    navigate('/supplier/purchase-overview');
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchSupplierDetail();
  };

  if (!detail) {
    return (
      <div className={styles.container}>
        <Card loading={loading}>
          <div className={styles.emptyState}>{loading ? '加载中...' : '供应商信息不存在'}</div>
        </Card>
      </div>
    );
  }

  // 热门商品表格列定义
  const productColumns: ColumnsType<any> = [
    {
      title: '商品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 200,
    },
    {
      title: '品牌',
      dataIndex: 'brandCode',
      key: 'brandCode',
      width: 100,
    },
    {
      title: '分类',
      dataIndex: 'categoryCode',
      key: 'categoryCode',
      width: 100,
    },
    {
      title: '采购数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 100,
      sorter: (a, b) => a.totalQuantity - b.totalQuantity,
    },
    {
      title: '采购金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      sorter: (a, b) => a.totalAmount - b.totalAmount,
      render: (amount: number) => formatAmountSimple(amount),
    },
    {
      title: '平均单价',
      dataIndex: 'avgPrice',
      key: 'avgPrice',
      width: 100,
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '订单次数',
      dataIndex: 'orderCount',
      key: 'orderCount',
      width: 100,
      sorter: (a, b) => a.orderCount - b.orderCount,
    },
  ];

  // 需求来源表格列定义
  const demandSourceColumns: ColumnsType<any> = [
    {
      title: '销售人员',
      key: 'salesperson',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.salesPersonName}</div>
          <div className={styles.personCode}>{record.salesPersonCode}</div>
        </div>
      ),
    },
    {
      title: '需求订单数',
      dataIndex: 'demandOrderCount',
      key: 'demandOrderCount',
      width: 120,
      sorter: (a, b) => a.demandOrderCount - b.demandOrderCount,
    },
    {
      title: '需求数量',
      dataIndex: 'totalDemandQuantity',
      key: 'totalDemandQuantity',
      width: 120,
      sorter: (a, b) => a.totalDemandQuantity - b.totalDemandQuantity,
    },
    {
      title: '贡献率',
      dataIndex: 'contributionRate',
      key: 'contributionRate',
      width: 120,
      sorter: (a, b) => a.contributionRate - b.contributionRate,
      render: (rate: number) => (
        <div className={styles.contributionCell}>
          <Progress percent={rate} size="small" showInfo={false} />
          <span className={styles.contributionText}>{rate.toFixed(1)}%</span>
        </div>
      ),
    },
  ];

  // 最近订单表格列定义
  const recentOrderColumns: ColumnsType<any> = [
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 150,
    },
    {
      title: '订单日期',
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '订单金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => formatAmountSimple(amount),
    },
    {
      title: '订单数量',
      dataIndex: 'totalQuantity',
      key: 'totalQuantity',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>,
    },
    {
      title: '创建人',
      dataIndex: 'createdByUserCode',
      key: 'createdByUserCode',
      width: 100,
    },
  ];

  // 状态分布数据
  const statusData = Object.entries(detail.ordersByStatus).map(([key, value]) => ({
    name: getStatusText(key),
    value,
  }));

  // 月度趋势数据
  const monthlyTrendData = detail.monthlyTrend.map((item) => ({
    month: item.month,
    订单数: item.orderCount,
    金额: item.totalAmount,
    交货天数: item.avgDeliveryDays,
  }));

  return (
    <div className={styles.container}>
      {/* 头部操作栏 */}
      <Card className={styles.headerCard}>
        <Row gutter={16} justify="space-between" align="middle">
          <Col>
            <Space size="middle">
              <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
                返回概览
              </Button>
              <div className={styles.titleSection}>
                <UserOutlined className={styles.titleIcon} />
                <h2 className={styles.title}>{detail.supplier.name}</h2>
                <Tag color="blue">{detail.supplier.code}</Tag>
              </div>
            </Space>
          </Col>
          <Col>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      <Tabs
        defaultActiveKey="overview"
        className={styles.tabs}
        items={[
          {
            key: 'overview',
            label: '概览信息',
            children: (
              <Row gutter={[16, 16]}>
                {/* 基本信息 */}
                <Col xs={24} lg={16}>
                  <Card title="基本信息" className={styles.infoCard}>
                    <Descriptions column={{ xs: 1, sm: 2 }} bordered>
                      <Descriptions.Item label="供应商编码">
                        {detail.supplier.code}
                      </Descriptions.Item>
                      <Descriptions.Item label="供应商名称">
                        {detail.supplier.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="联系人">
                        {detail.supplier.contactName}
                      </Descriptions.Item>
                      <Descriptions.Item label="联系电话">
                        {detail.supplier.contactPhone}
                      </Descriptions.Item>
                      <Descriptions.Item label="地址" span={2}>
                        {detail.supplier.address}
                      </Descriptions.Item>
                      <Descriptions.Item label="创建时间">
                        {dayjs(detail.supplier.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                      </Descriptions.Item>
                      <Descriptions.Item label="更新时间">
                        {dayjs(detail.supplier.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                      </Descriptions.Item>
                    </Descriptions>
                  </Card>
                </Col>

                {/* 关键指标 */}
                <Col xs={24} lg={8}>
                  <Row gutter={[16, 16]}>
                    <Col span={24}>
                      <Card className={styles.statisticCard}>
                        <Statistic
                          title="总采购金额"
                          value={detail.summary.totalAmount}
                          precision={2}
                          prefix="¥"
                          valueStyle={{ color: '#f5222d', fontSize: '20px' }}
                        />
                      </Card>
                    </Col>
                    <Col span={24}>
                      <Card className={styles.statisticCard}>
                        <Statistic
                          title="总订单数"
                          value={detail.summary.totalOrders}
                          valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                        />
                      </Card>
                    </Col>
                    <Col span={24}>
                      <Card className={styles.statisticCard}>
                        <Statistic
                          title="按时交货率"
                          value={detail.summary.onTimeDeliveryRate}
                          suffix="%"
                          precision={1}
                          valueStyle={{
                            color: detail.summary.onTimeDeliveryRate >= 90 ? '#52c41a' : '#faad14',
                            fontSize: '18px',
                          }}
                        />
                      </Card>
                    </Col>
                  </Row>
                </Col>
              </Row>
            ),
          },
          {
            key: 'analytics',
            label: '数据分析',
            children: (
              <Row gutter={[16, 16]}>
                <Col xs={24} lg={12}>
                  <Card title="订单状态分布" className={styles.chartCard}>
                    <Pie dataSource={statusData} height={300} />
                  </Card>
                </Col>
                <Col xs={24} lg={12}>
                  <Card title="月度趋势" className={styles.chartCard}>
                    <Line
                      dataSource={monthlyTrendData}
                      xField="month"
                      yField="订单数"
                      height={300}
                    />
                  </Card>
                </Col>
              </Row>
            ),
          },
          {
            key: 'products',
            label: '热门商品',
            children: (
              <Card>
                <Table
                  columns={productColumns}
                  dataSource={detail.topProducts}
                  rowKey="productCode"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                  }}
                  className={styles.table}
                />
              </Card>
            ),
          },
          {
            key: 'demand',
            label: '需求来源',
            children: (
              <Card>
                <Table
                  columns={demandSourceColumns}
                  dataSource={detail.demandSources}
                  rowKey="salesPersonCode"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                  }}
                  className={styles.table}
                />
              </Card>
            ),
          },
          {
            key: 'orders',
            label: '最近订单',
            children: (
              <Card>
                <Table
                  columns={recentOrderColumns}
                  dataSource={detail.recentOrders}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                  }}
                  className={styles.table}
                />
              </Card>
            ),
          },
        ]}
      />
    </div>
  );
};

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: { [key: string]: string } = {
    draft: 'default',
    confirmed: 'blue',
    producing: 'orange',
    shipped: 'purple',
    received: 'green',
    completed: 'green',
    cancelled: 'red',
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string): string => {
  const textMap: { [key: string]: string } = {
    draft: '草稿',
    confirmed: '已确认',
    producing: '生产中',
    shipped: '已发货',
    received: '已收货',
    completed: '已完成',
    cancelled: '已取消',
  };
  return textMap[status] || status;
};

export default SupplierPurchaseDetail;
