import React from 'react';
import { Select } from 'antd';

const { Option } = Select;

interface SizeSelectorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  sizes?: string[];
}

// 常用尺寸列表
const DEFAULT_SIZES = [
  'XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL',
  '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46',
  '26', '27', '28', '29', '30', '31', '32', '33', '34', '35',
  'F', // Free Size
];

const SizeSelector: React.FC<SizeSelectorProps> = ({
  value,
  onChange,
  placeholder = '请选择尺寸',
  disabled = false,
  allowClear = true,
  style,
  className,
  sizes = DEFAULT_SIZES,
}) => {
  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      showSearch
      filterOption={(input, option) =>
        (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
      }
    >
      {sizes.map((size) => (
        <Option key={size} value={size}>
          {size}
        </Option>
      ))}
    </Select>
  );
};

export default SizeSelector;
