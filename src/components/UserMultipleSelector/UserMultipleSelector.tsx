import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag } from 'antd';
import { debounce } from 'lodash-es';
import { getUserList } from '@/api/UserApi';
import type { UserProps } from '@/types/user';
import { logError } from '@/utils/errorHandler';

interface UserMultipleSelectorProps {
  value?: string[];
  onChange?: (value: string[], users: UserProps[]) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  maxTagCount?: number;
}

const UserMultipleSelector: React.FC<UserMultipleSelectorProps> = ({
  value = [],
  onChange,
  placeholder = '请搜索并选择用户',
  disabled = false,
  allowClear = true,
  style,
  className,
  maxTagCount = 3,
}) => {
  const [users, setUsers] = useState<UserProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [allUsers, setAllUsers] = useState<UserProps[]>([]); // 存储所有获取过的用户

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setUsers([]);
          return;
        }

        setLoading(true);
        try {
          const response = await getUserList({
            search: searchText.trim(),
            page: 0,
            pageSize: 0,
            // 不传入 page 和 pageSize，获取模糊匹配的所有结果
          });

          if (response.code === 200 && response.data?.users) {
            const newUsers = response.data.users;
            setUsers(newUsers);

            // 更新所有用户列表，避免重复
            setAllUsers((prev) => {
              const existingCodes = new Set(prev.map((u) => u.code));
              const uniqueNewUsers = newUsers.filter((u) => !existingCodes.has(u.code));
              return [...prev, ...uniqueNewUsers];
            });
          } else {
            setUsers([]);
          }
        } catch (error) {
          logError('搜索用户', error);
          setUsers([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [],
  );

  // 当搜索值变化时触发搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 处理选择
  const handleChange = (selectedValues: string[]) => {
    const selectedUsers = selectedValues
      .map((code) => allUsers.find((user) => user.code === code))
      .filter(Boolean) as UserProps[];

    onChange?.(selectedValues, selectedUsers);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setUsers([]);
    onChange?.([], []);
  };

  // 生成选项
  const options = users.map((user) => ({
    value: user.code,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div>
          <div style={{ fontWeight: 500 }}>{user.nickname}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
            {user.code}
            {user.isSuperAdmin && (
              <span style={{ color: '#f5222d', marginLeft: '4px' }}>(管理员)</span>
            )}
            {!user.isActive && (
              <span style={{ color: '#faad14', marginLeft: '4px' }}>(已禁用)</span>
            )}
          </div>
        </div>
      </div>
    ),
  }));

  // 自定义标签渲染
  const tagRender = (props: any) => {
    const { value, closable, onClose } = props;
    const user = allUsers.find((u) => u.code === value);

    return (
      <Tag color="blue" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
        {user?.nickname || value}
      </Tag>
    );
  };

  return (
    <Select
      mode="multiple"
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      maxTagCount={maxTagCount}
      maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
      tagRender={tagRender}
    />
  );
};

export default UserMultipleSelector;
