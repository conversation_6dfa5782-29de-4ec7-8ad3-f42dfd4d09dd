import { createStyles } from 'antd-style';
import { themeColors } from '@/theme';

const useStyles = createStyles(({ token }) => ({
  layoutContainer: {
    minHeight: '100vh',
  },
  sider: {
    boxShadow: '2px 0 6px rgba(0, 0, 0, 0.1)',
    zIndex: 1001, // 确保高于标签栏
    position: 'fixed',
    left: 0,
    top: 0,
    bottom: 0,
    height: '100vh',
    overflow: 'auto',
    backgroundColor: '#fff',
    borderRight: '1px solid #f0f0f0',
  },
  logo: {
    height: '32px',
    margin: '12px',
    color: 'white',
    fontSize: '18px',
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: '32px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    background: '#1890ff',
    borderRadius: '4px',
    transition: 'all 0.3s',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 'auto',
    zIndex: 1002, // 确保高于侧边栏和标签栏
    position: 'relative',
  },
  header: {
    padding: 0,
    background: 'white',
    boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)',
    position: 'sticky',
    top: 0,
    zIndex: 9,
    height: '48px',
    lineHeight: '48px',
    color: 'rgba(0, 0, 0, 0.85)',
    borderBottom: '1px solid #f0f0f0',
  },
  headerLeft: {
    display: 'flex',
    alignItems: 'center',
  },
  headerRight: {
    display: 'flex',
    alignItems: 'center',
    paddingRight: '24px',
  },
  trigger: {
    padding: '0',
    fontSize: '16px',
    lineHeight: '48px',
    cursor: 'pointer',
    transition: 'color 0.3s',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '48px',
    height: '48px',
    '&:hover': {
      color: '#1890ff',
    },
  },
  content: {
    margin: '16px',
    padding: '0',
    minHeight: 280,
    background: token.colorBgContainer,
    borderRadius: '4px',
    overflow: 'hidden', // 改为hidden，让内部容器处理滚动
  },
  footer: {
    textAlign: 'center',
    padding: '16px 50px',
    color: themeColors.textSecondary,
    backgroundColor: themeColors.background,
    borderTop: `1px solid ${themeColors.border}`,
  },
}));

export default useStyles;
