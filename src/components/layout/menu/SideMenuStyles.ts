import { createStyles } from 'antd-style';

const useSideMenuStyles = createStyles(() => ({
  menuContainer: {
    '.ant-menu': {
      '.ant-menu-submenu-title': {
        '&:hover': {
          color: '#147ffa', // 一级菜单悬停时为绿色
        },
      },
      '.ant-menu-submenu-selected > .ant-menu-submenu-title': {
        color: '#147ffa !important', // 一级菜单选中时为绿色
      },
      '.ant-menu-item': {
        '&:hover': {
          color: '#1890ff', // 二级菜单悬停时为蓝色
        },
        '&.ant-menu-item-selected': {
          color: '#1890ff', // 二级菜单选中时为蓝色
          backgroundColor: 'rgba(24, 144, 255, 0.1)', // 轻微的背景色
          '&::after': {
            position: 'absolute',
            top: 0,
            right: 0,
            bottom: 0,
            border: 'none',
            borderRight: '3px solid #1890ff', // 右侧边框高亮
            transform: 'scaleY(1)',
            opacity: 1,
            transition:
              'transform 0.15s cubic-bezier(0.215, 0.61, 0.355, 1), opacity 0.15s cubic-bezier(0.215, 0.61, 0.355, 1)',
            content: '""',
          },
        },
      },
    },
  },
}));

export default useSideMenuStyles;
