import React, { useState, useEffect } from 'react';
import { Layout, Menu } from 'antd';
import { useLocation } from 'react-router-dom';
import { BankOutlined } from '@ant-design/icons';
import { getMenuItems } from './MenuItems';
import { HOME_TITLE } from '@/utils';
import useStyles from '../BasicLayoutStyles';
import useSideMenuStyles from './SideMenuStyles';

const { Sider } = Layout;

interface SideMenuProps {
  collapsed: boolean;
}

const SideMenu: React.FC<SideMenuProps> = ({ collapsed }) => {
  const location = useLocation();
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const { styles } = useStyles();
  const { styles: menuStyles } = useSideMenuStyles();

  // 获取当前选中的菜单项
  const getSelectedKeys = (): string[] => {
    const path = location.pathname;
    const pathSegments = path.split('/').filter(Boolean);

    // 处理首页
    if (pathSegments.length === 0 || path === '/') {
      return ['home'];
    }

    if (pathSegments.length === 1) {
      return [pathSegments[0]];
    }

    // 对于二级路径，返回正确的菜单项 key
    if (pathSegments.length >= 2) {
      // 特殊处理零售退货路径
      if (pathSegments[0] === 'retail' && pathSegments[1] === 'return') {
        return ['retail-return'];
      }

      // 特殊处理零售出库路径
      if (pathSegments[0] === 'retail' && pathSegments[1] === 'out') {
        return ['retail-out'];
      }

      // 特殊处理商品品牌路径
      if (pathSegments[0] === 'product' && pathSegments[1] === 'brand') {
        return ['product-brand'];
      }

      // 特殊处理商品属性路径
      if (pathSegments[0] === 'product' && pathSegments[1] === 'attribute') {
        return ['product-attribute'];
      }

      // 特殊处理商品类别路径
      if (pathSegments[0] === 'product' && pathSegments[1] === 'category') {
        return ['product-category'];
      }

      // 特殊处理商品规格路径
      if (pathSegments[0] === 'product' && pathSegments[1] === 'specification') {
        return ['product-specification'];
      }

      // 特殊处理商品辅料路径
      if (pathSegments[0] === 'product' && pathSegments[1] === 'auxiliary') {
        return ['product-auxiliary'];
      }

      // 特殊处理商品档案路径
      if (pathSegments[0] === 'product' && pathSegments[1] === 'archive') {
        return ['product-archive'];
      }

      // 特殊处理系统用户管理路径
      if (pathSegments[0] === 'system' && pathSegments[1] === 'user') {
        return ['system-user'];
      }

      // 特殊处理系统备忘录路径
      if (pathSegments[0] === 'admin' && pathSegments[1] === 'memos') {
        return ['system-memo'];
      }

      // 特殊处理多单位路径
      if (pathSegments[0] === 'products' && pathSegments[1] === 'units') {
        return ['products-units'];
      }

      // 特殊处理多属性路径
      if (pathSegments[0] === 'products' && pathSegments[1] === 'props') {
        return ['products-props'];
      }

      // 特殊处理条码打印路径
      if (pathSegments[0] === 'products' && pathSegments[1] === 'code') {
        return ['products-code'];
      }

      // 特殊处理销售管理路径 - 使用斜杠格式匹配菜单项key
      if (pathSegments[0] === 'sales') {
        return [`${pathSegments[0]}/${pathSegments[1]}`];
      }

      // 特殊处理采购管理路径 - 使用斜杠格式匹配菜单项key
      if (pathSegments[0] === 'purchase') {
        return [`${pathSegments[0]}/${pathSegments[1]}`];
      }

      // 特殊处理统计分析路径 - 使用斜杠格式匹配菜单项key
      if (pathSegments[0] === 'statistics') {
        return [`${pathSegments[0]}/${pathSegments[1]}`];
      }

      // 特殊处理供应商分析路径 - 使用斜杠格式匹配菜单项key
      if (pathSegments[0] === 'supplier') {
        return [`${pathSegments[0]}/${pathSegments[1]}`];
      }

      // 其他路径使用连字符格式
      return [`${pathSegments[0]}-${pathSegments[1]}`];
    }

    return [];
  };

  // 获取当前展开的子菜单
  const getOpenKeys = (): string[] => {
    const path = location.pathname;
    const pathSegments = path.split('/').filter(Boolean);

    if (pathSegments.length === 0) {
      return [];
    }

    // 特殊处理商品管理路径
    if (pathSegments[0] === 'product') {
      return ['product'];
    }

    // 兼容旧的products路径
    if (pathSegments[0] === 'products') {
      return ['product']; // 统一使用product作为key
    }

    // 特殊处理系统管理路径
    if (pathSegments[0] === 'system' || pathSegments[0] === 'admin') {
      return ['system'];
    }

    // 特殊处理公司管理路径
    if (pathSegments[0] === 'company' || pathSegments[0] === 'customer') {
      return ['company'];
    }

    // 特殊处理销售管理路径
    if (pathSegments[0] === 'sales') {
      return ['sales'];
    }

    // 特殊处理采购管理路径
    if (pathSegments[0] === 'purchase') {
      return ['purchase'];
    }

    // 特殊处理财务管理路径
    if (pathSegments[0] === 'financial') {
      return ['financial'];
    }

    // 特殊处理统计分析路径
    if (pathSegments[0] === 'statistics') {
      return ['statistics'];
    }

    // 特殊处理供应商分析路径
    if (pathSegments[0] === 'supplier') {
      return ['supplier'];
    }

    return [pathSegments[0]];
  };

  // 处理菜单展开变化
  const handleOpenChange = (keys: string[]) => {
    // 获取最新点击的菜单项
    const latestOpenKey = keys.find((key) => openKeys.indexOf(key) === -1);

    if (latestOpenKey) {
      // 如果有新点击的菜单项，只展开该项
      setOpenKeys([latestOpenKey]);
    } else {
      // 如果没有新点击的菜单项，则全部收起
      setOpenKeys([]);
    }
  };

  // 初始化展开的菜单项
  useEffect(() => {
    const initialOpenKeys = getOpenKeys();
    setOpenKeys(initialOpenKeys);
  }, [location.pathname]);

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      className={styles.sider}
      width={150}
      collapsedWidth={80}
    >
      <div className={styles.logo}>
        <BankOutlined
          style={{
            fontSize: collapsed ? '16px' : '20px',
            marginRight: collapsed ? '0' : '8px',
            color: '#fff',
          }}
        />
        <span
          style={{
            color: '#fff',
            fontSize: collapsed ? '14px' : '18px',
            fontWeight: 'bold',
            whiteSpace: 'nowrap',
            display: collapsed ? 'none' : 'inline-block',
          }}
        >
          {HOME_TITLE}
        </span>
      </div>
      <div className={menuStyles.menuContainer}>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          openKeys={openKeys}
          onOpenChange={handleOpenChange}
          items={getMenuItems()}
        />
      </div>
    </Sider>
  );
};

export default SideMenu;
