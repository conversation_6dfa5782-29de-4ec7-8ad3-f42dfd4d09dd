import { Link } from 'react-router-dom';
import type { MenuProps } from 'antd';
import {
  DashboardOutlined,
  ShoppingCartOutlined,
  SettingOutlined,
  CalculatorOutlined,
  TagsOutlined,
  TeamOutlined,
  BarChartOutlined,
  UserOutlined,
  FileTextOutlined,
  ContainerOutlined,
} from '@ant-design/icons';
import { getUser } from '@/utils/tokenManager';

export type MenuItem = Required<MenuProps>['items'][number];

export const MENU_ICON_SIZE = '16px';
export const SWITCH_ICON_SIZE = '14px';

// 菜单项配置
export const getMenuItems = (): MenuItem[] => {
  // 获取当前用户信息
  const currentUser = getUser();
  const isSuperAdmin = currentUser?.isSuperAdmin;

  // 权限检查函数
  const hasRouteAccess = (route: string): boolean => {
    if (!currentUser) return false;
    if (isSuperAdmin) return true;

    // 确保路由格式一致，添加开头的斜杠
    const normalizedRoute = route.startsWith('/') ? route : `/${route}`;

    // 检查用户是否有该路由的任何权限
    const routePermissions = currentUser.routePermissions?.[normalizedRoute];
    if (!routePermissions) return false;

    return Object.values(routePermissions).some(Boolean);
  };

  // 构建菜单项数组
  const menuItems: MenuItem[] = [
    {
      key: 'home',
      icon: <DashboardOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
      label: <Link to="/">首页</Link>,
    },
  ];

  // 公司管理菜单
  const companyChildren = [
    hasRouteAccess('company/management') && {
      key: 'company-management',
      label: <Link to="/company/management">公司管理</Link>,
    },
    hasRouteAccess('company/supplier') && {
      key: 'company-supplier',
      label: <Link to="/company/supplier">供应管理</Link>,
    },
    hasRouteAccess('company/supplier-archives') && {
      key: 'company-supplier-archives',
      label: <Link to="/company/supplier-archives">供应商档案</Link>,
    },
    hasRouteAccess('company/customer') && {
      key: 'company-customer',
      label: <Link to="/company/customer">客户管理</Link>,
    },
    hasRouteAccess('customer/profiles') && {
      key: 'customer-profiles',
      label: <Link to="/customer/profiles">客户档案</Link>,
    },
  ].filter(Boolean) as MenuItem[];

  if (companyChildren.length > 0) {
    menuItems.push({
      key: 'company',
      icon: <TeamOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
      label: '公司',
      children: companyChildren,
    });
  }

  // 产品管理菜单
  const productChildren = [
    hasRouteAccess('product/category') && {
      key: 'product-category',
      label: <Link to="/product/category">类别管理</Link>,
    },
    hasRouteAccess('product/brand') && {
      key: 'product-brand',
      label: <Link to="/product/brand">品牌管理</Link>,
    },
    hasRouteAccess('product/color') && {
      key: 'product-color',
      label: <Link to="/product/color">颜色管理</Link>,
    },
    hasRouteAccess('product/auxiliary') && {
      key: 'product-auxiliary',
      label: <Link to="/product/auxiliary">辅料管理</Link>,
    },
    hasRouteAccess('product/sku') && {
      key: 'product-sku',
      label: <Link to="/product/sku">商品档案</Link>,
    },
    hasRouteAccess('product/skus-inventory') && {
      key: 'product-inventory',
      label: <Link to="/product/skus-inventory">库存明细</Link>,
    },
  ].filter(Boolean) as MenuItem[];

  if (productChildren.length > 0) {
    menuItems.push({
      key: 'product',
      icon: <TagsOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
      label: '商品',
      children: productChildren,
    });
  }

  // 销售管理菜单
  const salesChildren = [
    hasRouteAccess('sales/demand-orders') && {
      key: 'sales/demand-orders',
      label: <Link to="/sales/demand-orders">需求订单</Link>,
    },
    hasRouteAccess('sales/orders') && {
      key: 'sales/orders',
      label: <Link to="/sales/orders">销售订单</Link>,
    },
    hasRouteAccess('sales/returns') && {
      key: 'sales/returns',
      label: <Link to="/sales/returns">销售退货</Link>,
    },
  ].filter(Boolean) as MenuItem[];

  if (salesChildren.length > 0) {
    menuItems.push({
      key: 'sales',
      icon: <FileTextOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
      label: '销售',
      children: salesChildren,
    });
  }

  // 采购管理菜单
  const purchaseChildren = [
    hasRouteAccess('purchase/purchase-contracts') && {
      key: 'purchase/purchase-contracts',
      label: <Link to="/purchase/purchase-contracts">辅料采购</Link>,
    },
    hasRouteAccess('purchase/product-orders') && {
      key: 'purchase/product-orders',
      label: <Link to="/purchase/product-orders">商品采购</Link>,
    },
  ].filter(Boolean) as MenuItem[];

  if (purchaseChildren.length > 0) {
    menuItems.push({
      key: 'purchase',
      icon: <ShoppingCartOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
      label: '采购',
      children: purchaseChildren,
    });
  }

  // 合同管理菜单
  const contractChildren = [
    hasRouteAccess('commission/contracts') && {
      key: 'commission/contracts',
      label: <Link to="/commission/contracts">佣金发货合同</Link>,
    },
  ].filter(Boolean) as MenuItem[];

  if (contractChildren.length > 0) {
    menuItems.push({
      key: 'contract',
      icon: <ContainerOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
      label: '合同管理',
      children: contractChildren,
    });
  }

  // 财务管理菜单（会计）
  const financialChildren = [
    hasRouteAccess('financial/fixed-assets') && {
      key: 'financial-fixed-assets',
      label: <Link to="/financial/fixed-assets">固定资产</Link>,
    },
    hasRouteAccess('financial/operating-assets') && {
      key: 'financial-operating-assets',
      label: <Link to="/financial/operating-assets">运营资产</Link>,
    },
    hasRouteAccess('financial/rental-assets') && {
      key: 'financial-rental-assets',
      label: <Link to="/financial/rental-assets">租赁资产</Link>,
    },
    hasRouteAccess('financial/rd-costs') && {
      key: 'financial-rd-costs',
      label: <Link to="/financial/rd-costs">研发成本</Link>,
    },
    hasRouteAccess('financial/income-assets') && {
      key: 'financial-income-assets',
      label: <Link to="/financial/income-assets">收入资产</Link>,
    },
    hasRouteAccess('financial/expense-assets') && {
      key: 'financial-expense-assets',
      label: <Link to="/financial/expense-assets">支出资产</Link>,
    },
  ].filter(Boolean) as MenuItem[];

  if (financialChildren.length > 0) {
    menuItems.push({
      key: 'financial',
      icon: <CalculatorOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
      label: '会计',
      children: financialChildren,
    });
  }

  // 统计分析菜单
  const statisticsChildren = [
    hasRouteAccess('statistics/demand-purchase') && {
      key: 'statistics/demand-purchase',
      label: <Link to="/statistics/demand-purchase">综合统计</Link>,
    },
    hasRouteAccess('statistics/demand-purchase') && {
      key: 'statistics/demand-orders',
      label: <Link to="/statistics/demand-orders">需求统计</Link>,
    },
    hasRouteAccess('statistics/demand-purchase') && {
      key: 'statistics/purchase-orders',
      label: <Link to="/statistics/purchase-orders">采购统计</Link>,
    },
    hasRouteAccess('statistics/demand-purchase') && {
      key: 'statistics/product-demand',
      label: <Link to="/statistics/product-demand">商品统计</Link>,
    },
  ].filter(Boolean) as MenuItem[];

  if (statisticsChildren.length > 0) {
    menuItems.push({
      key: 'statistics',
      icon: <BarChartOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
      label: '数据分析',
      children: statisticsChildren,
    });
  }

  // 供应商分析菜单
  const supplierChildren = [
    hasRouteAccess('supplier/purchase-overview') && {
      key: 'supplier/purchase-overview',
      label: <Link to="/supplier/purchase-overview">采购概览</Link>,
    },
    hasRouteAccess('supplier/purchase-overview') && {
      key: 'supplier/purchase-ranking',
      label: <Link to="/supplier/purchase-ranking">采购排行</Link>,
    },
    hasRouteAccess('supplier/purchase-overview') && {
      key: 'supplier/performance-comparison',
      label: <Link to="/supplier/performance-comparison">绩效对比</Link>,
    },
  ].filter(Boolean) as MenuItem[];

  if (supplierChildren.length > 0) {
    menuItems.push({
      key: 'supplier',
      icon: <UserOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
      label: '供应商分析',
      children: supplierChildren,
    });
  }

  // 系统管理菜单（仅超级管理员可见）
  if (isSuperAdmin) {
    const systemChildren = [
      hasRouteAccess('system/users') && {
        key: 'system-users',
        label: <Link to="/system/users">用户管理</Link>,
      },
      hasRouteAccess('admin/memos') && {
        key: 'system-memo',
        label: <Link to="/admin/memos">备忘录</Link>,
      },
    ].filter(Boolean) as MenuItem[];

    if (systemChildren.length > 0) {
      menuItems.push({
        key: 'system',
        icon: <SettingOutlined style={{ fontSize: MENU_ICON_SIZE }} />,
        label: '系统',
        children: systemChildren,
      });
    }
  }

  return menuItems;
};
