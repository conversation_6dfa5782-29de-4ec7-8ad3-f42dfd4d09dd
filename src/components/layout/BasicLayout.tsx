import React, { useState } from 'react';
import { Layout } from 'antd';
import { Outlet } from 'react-router-dom';
import useStyles from '@/components/layout/BasicLayoutStyles';
import SideMenu from './menu/SideMenu';
import HeaderBar from './header/HeaderBar';
import FooterBar from './footer/FooterBar';

const { Content } = Layout;

const BasicLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const { styles } = useStyles();

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  return (
    <>
      <Layout className={styles.layoutContainer}>
        <SideMenu collapsed={collapsed} />
        <Layout
          style={{
            marginLeft: collapsed ? 60 : 140,
            transition: 'margin-left 0.2s',
            position: 'relative',
          }}
        >
          <HeaderBar collapsed={collapsed} toggleCollapsed={toggleCollapsed} />
          <Content className={styles.content} style={{ marginTop: 48 }}>
            <Outlet />
          </Content>
          <FooterBar />
        </Layout>
      </Layout>
    </>
  );
};

export default BasicLayout;
