import React from 'react';
import { Avatar, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { getUser } from '@/utils/tokenManager';
import { MENU_ICON_SIZE } from '../menu/MenuItems';
import LogoutButton from '@/components/auth/LogoutButton';

interface UserMenuProps {
  className?: string;
}

const UserMenu: React.FC<UserMenuProps> = ({ className }) => {
  // 获取用户信息
  const user = getUser();

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    // 处理其他菜单项点击
    if (key === 'profile') {
      // 跳转到个人信息页面
    } else if (key === 'settings') {
      // 跳转到账户设置页面
    }
  };

  // 用户菜单项
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'logout',
      label: <LogoutButton type="text">退出登录</LogoutButton>,
    },
  ];

  return (
    <Dropdown
      menu={{ items: userMenuItems, onClick: handleUserMenuClick }}
      placement="bottomRight"
      className={className}
    >
      <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
        <Avatar
          icon={<UserOutlined style={{ fontSize: MENU_ICON_SIZE }} />}
          src={user?.avatar}
          style={{ cursor: 'pointer' }}
        />
        {user && (
          <span style={{ marginLeft: 8, color: '#000' }}>
            {user.realName || user.nickname || user.userCode}
          </span>
        )}
      </div>
    </Dropdown>
  );
};

export default UserMenu;
