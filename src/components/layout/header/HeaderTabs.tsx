import React, { useEffect, useRef, useState } from 'react';
import { Tabs, Dropdown, Tooltip } from 'antd';
import { PushpinOutlined, PushpinFilled, CloseOutlined, EllipsisOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import useTabsStore, { Tab } from '@/store/tabsStore';
import { createStyles } from 'antd-style';

const useStyles = createStyles(() => ({
  headerTabs: {
    margin: 0,
    width: '100%',
    height: '100%',
    transition: 'width 0.2s',

    '.ant-tabs': {
      color: 'rgba(0, 0, 0, 0.85)',
      height: '100%',
    },

    '.ant-tabs-nav': {
      marginBottom: 0,
      height: '100%',
    },

    '.ant-tabs-nav-wrap': {
      height: '100%',
    },

    '.ant-tabs-nav-list': {
      height: '100%',
    },

    '.ant-tabs-tab': {
      padding: '0 16px',
      height: '48px',
      lineHeight: '48px',
      transition: 'all 0.3s',
      color: 'rgba(0, 0, 0, 0.65)',
      margin: '0 2px',
      borderRadius: '4px 4px 0 0',
      border: '1px solid transparent',

      '&:hover': {
        color: '#1890ff',
        backgroundColor: 'rgba(24, 144, 255, 0.04)',
        borderBottom: '1px solid #1890ff',
      },

      '&.ant-tabs-tab-active': {
        backgroundColor: 'white',
        borderLeft: '1px solid #f0f0f0',
        borderRight: '1px solid #f0f0f0',
        borderTop: '1px solid #f0f0f0',
        borderBottom: 'none',
        marginBottom: '0',
        position: 'relative',

        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: '0',
          left: '0',
          right: '0',
          height: '4px',
          background: 'linear-gradient(90deg, #1890ff, #40a9ff)',
          zIndex: 1,
          boxShadow: '0 1px 3px rgba(24, 144, 255, 0.6)',
        },

        '.ant-tabs-tab-btn': {
          color: '#1890ff',
          fontWeight: 600,
        },
      },
    },

    '.ant-tabs-nav-more': {
      padding: '0 16px',
      color: 'rgba(0, 0, 0, 0.65)',

      '&:hover': {
        color: '#1890ff',
      },
    },

    '.ant-tabs-ink-bar': {
      display: 'none', // 隐藏默认的 ink-bar，我们使用自定义的底部边框
    },
  },
  tabContent: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    maxWidth: '150px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    padding: '0 4px',
  },
  tabTitle: {
    marginRight: '8px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    fontSize: '14px',
    fontWeight: 'normal',
    letterSpacing: '0.2px',
  },
  tabActions: {
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    marginLeft: 'auto',
  },
  tabPin: {
    fontSize: '12px',
    cursor: 'pointer',
    color: 'rgba(0, 0, 0, 0.45)',
    transition: 'all 0.2s',
    transform: 'scale(1)',

    '&:hover': {
      color: '#1890ff',
      transform: 'scale(1.2)',
    },
  },
  tabClose: {
    fontSize: '12px',
    cursor: 'pointer',
    opacity: 0.6,
    color: 'rgba(0, 0, 0, 0.45)',
    transition: 'all 0.2s',
    borderRadius: '50%',
    width: '16px',
    height: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',

    '&:hover': {
      opacity: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.06)',
      color: 'rgba(0, 0, 0, 0.85)',
    },
  },
  moreButton: {
    padding: '8px',
    cursor: 'pointer',
    color: 'rgba(0, 0, 0, 0.65)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '32px',
    width: '32px',
    borderRadius: '50%',
    transition: 'all 0.2s',

    '&:hover': {
      color: '#1890ff',
      backgroundColor: 'rgba(24, 144, 255, 0.04)',
    },
  },
  dropdownMenu: {
    maxHeight: '400px',
    overflowY: 'auto',
    borderRadius: '8px',
    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.15)',
  },
  dropdownItem: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '10px 16px',
    transition: 'all 0.2s',
    borderRadius: '4px',
    margin: '2px 0',
    color: 'rgba(0, 0, 0, 0.85)',

    '&:hover': {
      backgroundColor: 'rgba(24, 144, 255, 0.06)',
    },

    '.item-title': {
      marginRight: '16px',
      fontWeight: 'normal',
      fontSize: '14px',
    },

    '.item-actions': {
      display: 'flex',
      gap: '12px',
      color: 'rgba(0, 0, 0, 0.45)',

      '& span:hover': {
        color: '#1890ff',
      },
    },
  },
}));

const HeaderTabs: React.FC = () => {
  const { styles } = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const tabsRef = useRef<HTMLDivElement>(null);

  const tabs = useTabsStore((state) => state.tabs);
  const activeTabId = useTabsStore((state) => state.activeTabId);
  const addTab = useTabsStore((state) => state.addTab);
  const removeTab = useTabsStore((state) => state.removeTab);
  const setActiveTab = useTabsStore((state) => state.setActiveTab);
  const pinTab = useTabsStore((state) => state.pinTab);
  const unpinTab = useTabsStore((state) => state.unpinTab);

  const [visibleTabs, setVisibleTabs] = useState<Tab[]>([]);
  const [overflowTabs, setOverflowTabs] = useState<Tab[]>([]);

  // 根据当前路径更新选项卡
  useEffect(() => {
    const currentPath = location.pathname;
    const existingTab = tabs.find((tab) => tab.path === currentPath);

    if (existingTab) {
      setActiveTab(existingTab.id);
    } else {
      // 这里可以根据路由匹配添加新选项卡
      // 实际项目中可能需要根据路由配置获取标题
      const title = getTabTitleFromPath(currentPath);
      if (title) {
        // 处理特殊路径，使子路径与父路径共用同一个标签
        let tabId;

        // 采购订单查看和编辑页面使用采购订单列表的标签
        if (
          currentPath.startsWith('/purchase/order/view/') ||
          currentPath.startsWith('/purchase/order/edit/')
        ) {
          tabId = 'purchase-order';
        }
        // 销售订单查看和编辑页面使用销售订单列表的标签
        else if (
          currentPath.startsWith('/sales/order/view/') ||
          currentPath.startsWith('/sales/order/edit/')
        ) {
          tabId = 'sales-order';
        } else {
          // 从路径生成一个稳定的ID
          const pathSegments = currentPath.split('/').filter(Boolean);
          tabId = pathSegments.join('-');
          if (tabId === '') {
            tabId = 'home';
          }
        }

        // 检查是否已存在相同ID的标签
        const existingTabWithId = tabs.find((tab) => tab.id === tabId);
        if (existingTabWithId) {
          // 如果已存在相同ID的标签，更新其路径并激活它
          setActiveTab(existingTabWithId.id);
        } else {
          // 否则添加新标签
          addTab({
            id: tabId,
            title,
            path: currentPath,
            closable: true,
          });
        }
      }
    }
  }, [location.pathname]);

  // 监听活动选项卡变化，确保路由同步更新
  useEffect(() => {
    if (activeTabId) {
      const activeTab = tabs.find((tab) => tab.id === activeTabId);
      if (activeTab && activeTab.path !== location.pathname) {
        navigate(activeTab.path);
      }
    }
  }, [activeTabId]);

  // 计算可见选项卡和溢出选项卡
  useEffect(() => {
    // 在实际项目中，这里应该根据容器宽度动态计算可见选项卡数量
    // 这里简化为固定显示最多 10 个选项卡
    if (tabs.length <= 10) {
      setVisibleTabs(tabs);
      setOverflowTabs([]);
    } else {
      // 确保固定的选项卡始终可见
      const pinnedTabs = tabs.filter((tab) => tab.isPinned);
      const unpinnedTabs = tabs.filter((tab) => !tab.isPinned);

      // 计算可以显示的非固定选项卡数量
      const availableSlots = 10 - pinnedTabs.length;

      if (availableSlots <= 0) {
        // 如果固定选项卡已经占满，只显示固定选项卡
        setVisibleTabs(pinnedTabs.slice(0, 10));
        setOverflowTabs([...pinnedTabs.slice(10), ...unpinnedTabs]);
      } else {
        // 显示所有固定选项卡和部分非固定选项卡
        const visibleUnpinnedTabs = unpinnedTabs.slice(-availableSlots);
        setVisibleTabs([...pinnedTabs, ...visibleUnpinnedTabs]);
        setOverflowTabs(unpinnedTabs.slice(0, unpinnedTabs.length - availableSlots));
      }
    }
  }, [tabs]);

  // 处理选项卡切换
  const handleTabChange = (activeKey: string) => {
    const tab = tabs.find((tab) => tab.id === activeKey);
    if (tab) {
      setActiveTab(tab.id);
      navigate(tab.path);
    }
  };

  // 处理选项卡关闭
  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();

    // 获取当前选项卡的索引和是否是活动选项卡
    const isActiveTab = activeTabId === tabId;
    const currentIndex = tabs.findIndex((tab) => tab.id === tabId);

    // 如果是活动选项卡，需要确定关闭后要导航到哪个选项卡
    if (isActiveTab) {
      // 确定下一个要激活的选项卡
      let nextTabId: string | null = null;

      if (currentIndex < tabs.length - 1) {
        // 如果不是最后一个选项卡，激活下一个选项卡
        nextTabId = tabs[currentIndex + 1].id;
      } else if (currentIndex > 0) {
        // 如果是最后一个选项卡，激活前一个选项卡
        nextTabId = tabs[currentIndex - 1].id;
      } else {
        // 如果只有一个选项卡，激活首页
        nextTabId = 'home';
      }

      // 先移除选项卡
      removeTab(tabId);

      // 然后导航到下一个选项卡
      if (nextTabId) {
        const nextTab = tabs.find((tab) => tab.id === nextTabId);
        if (nextTab) {
          navigate(nextTab.path);
        } else {
          // 如果找不到下一个选项卡（极少情况），导航到首页
          navigate('/');
        }
      } else {
        // 如果没有下一个选项卡，导航到首页
        navigate('/');
      }
    } else {
      // 如果不是活动选项卡，直接移除
      removeTab(tabId);
    }
  };

  // 处理选项卡固定/取消固定
  const handleTabPin = (e: React.MouseEvent, tabId: string, isPinned: boolean) => {
    e.stopPropagation();
    if (isPinned) {
      unpinTab(tabId);
    } else {
      pinTab(tabId);
    }
  };

  // 从路径获取选项卡标题
  const getTabTitleFromPath = (path: string): string => {
    // 处理子路径映射到父路径
    if (path.startsWith('/purchase/order/view/') || path.startsWith('/purchase/order/edit/')) {
      return '采购订单';
    }
    if (path.startsWith('/sales/order/view/') || path.startsWith('/sales/order/edit/')) {
      return '销售订单';
    }

    // 特殊处理一些路径
    const pathMap: Record<string, string> = {
      '/': '首页',
      // 系统管理
      '/system/users': '用户管理',
      '/system/users/add': '新增用户',
      '/system/users/edit': '编辑用户',
      '/system/users/view': '查看用户',
      // 采购管理
      '/purchase/purchase-contracts': '辅料采购',
      '/purchase/purchase-contracts/create': '新建采购合同',
      '/purchase/purchase-contracts/edit': '编辑采购合同',
      '/purchase/purchase-contracts/view': '查看采购合同',
      '/purchase/product-orders': '商品采购',
      '/purchase/product-orders/create': '新建采购订单',
      '/purchase/product-orders/edit': '编辑采购订单',
      '/purchase/product-orders/view': '查看采购订单',
      // 销售管理
      '/sales/demand-orders': '需求订单',
      '/sales/demand-orders/create': '新建需求订单',
      '/sales/demand-orders/edit': '编辑需求订单',
      '/sales/demand-orders/view': '查看需求订单',
      '/sales/orders': '销售订单',
      '/sales/orders/add': '新建销售订单',
      '/sales/orders/edit': '编辑销售订单',
      '/sales/orders/view': '查看销售订单',
      '/sales/returns': '销售退货',
      '/sales/returns/add': '新建退货单',
      '/sales/returns/edit': '编辑退货单',
      '/sales/returns/view': '查看退货单',
      // 公司管理
      '/company/management': '公司管理',
      '/company/supplier': '供应管理',
      '/company/supplier-archives': '供应商档案',
      '/company/customer': '客户管理',
      '/customer/profiles': '客户档案',
      '/customer/profiles/create': '新建客户档案',
      '/customer/profiles/edit': '编辑客户档案',
      '/customer/profiles/view': '查看客户档案',
      // 备忘录管理
      '/admin/memos': '备忘录管理',
      '/admin/memos/create': '新建备忘录',
      '/admin/memos/edit': '编辑备忘录',
      '/admin/memos/view': '查看备忘录',
      // 商品管理
      '/product/category': '类别管理',
      '/product/brand': '品牌管理',
      '/product/color': '颜色管理',
      '/product/auxiliary': '辅料管理',
      '/product/archive': '商品档案',
      '/product/archive/create': '新增商品',
      '/product/archive/edit': '编辑商品',
      '/product/archive/view': '商品详情',
      '/product/inventory': '库存明细',
      '/product/inventory/detail': '商品库存详情',
      // 统计分析
      '/statistics/demand-purchase': '综合统计',
      '/statistics/demand-orders': '需求统计',
      '/statistics/purchase-orders': '采购统计',
      '/statistics/product-demand': '商品统计',
      // 供应商分析
      '/supplier/purchase-overview': '采购概览',
      '/supplier/purchase-ranking': '采购排行',
      '/supplier/performance-comparison': '绩效对比',
      // 财务管理（会计）
      '/financial/fixed-assets': '固定资产 | Fixed Assets',
      '/financial/operating-assets': '运营资产 | Operating Assets',
      '/financial/rental-assets': '租赁资产 | Rental Assets',
      '/financial/rd-costs': '研发成本 | R&D Costs',
      '/financial/income-assets': '收入资产 | Income Assets',
      '/financial/expense-assets': '支出资产 | Expense Assets',
    };

    if (pathMap[path]) {
      return pathMap[path];
    }

    // 如果没有在映射表中找到，尝试从路径中提取名称
    const pathSegments = path.split('/').filter(Boolean);
    if (pathSegments.length > 0) {
      const lastSegment = pathSegments[pathSegments.length - 1];
      // 将路径片段转换为更友好的名称
      switch (lastSegment) {
        case 'dashboard':
          return '仪表盘';
        case 'user':
          return '用户管理';
        case 'profile':
          return '个人资料';
        case 'settings':
          return '系统设置';
        default:
          // 将驼峰或短横线命名转换为空格分隔的标题
          return (
            lastSegment
              .replace(/([A-Z])/g, ' $1') // 将驼峰转换为空格分隔
              .replace(/-/g, ' ') // 将短横线转换为空格
              .replace(/^\w/, (c) => c.toUpperCase()) || // 首字母大写
            '未知页面'
          );
      }
    }

    return '未知页面';
  };

  // 渲染选项卡内容
  const renderTabContent = (tab: Tab) => {
    return (
      <div className={styles.tabContent}>
        <span className={styles.tabTitle}>{tab.title}</span>
        <div className={styles.tabActions}>
          {tab.id !== 'home' && (
            <>
              <Tooltip title={tab.isPinned ? '取消固定' : '固定选项卡'}>
                <span
                  className={styles.tabPin}
                  onClick={(e) => handleTabPin(e, tab.id, tab.isPinned)}
                >
                  {tab.isPinned ? <PushpinFilled /> : <PushpinOutlined />}
                </span>
              </Tooltip>
              {tab.closable && (
                <Tooltip title="关闭选项卡">
                  <span className={styles.tabClose} onClick={(e) => handleTabClose(e, tab.id)}>
                    <CloseOutlined />
                  </span>
                </Tooltip>
              )}
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <div ref={tabsRef} className={styles.headerTabs}>
      <Tabs
        activeKey={activeTabId || undefined}
        onChange={handleTabChange}
        type="card"
        size="middle"
        animated={{ inkBar: true, tabPane: false }}
        items={visibleTabs.map((tab) => ({
          key: tab.id,
          label: renderTabContent(tab),
          closable: false, // 我们使用自定义关闭按钮
        }))}
        tabBarExtraContent={
          overflowTabs.length > 0 ? (
            <Dropdown
              menu={{
                items: overflowTabs.map((tab) => ({
                  key: tab.id,
                  onClick: () => handleTabChange(tab.id),
                  label: (
                    <div className={styles.dropdownItem}>
                      <span className="item-title">{tab.title}</span>
                      <div className="item-actions">
                        {tab.id !== 'home' && (
                          <>
                            <span
                              onClick={(e) => {
                                e.stopPropagation();
                                handleTabPin(e, tab.id, tab.isPinned);
                              }}
                            >
                              {tab.isPinned ? <PushpinFilled /> : <PushpinOutlined />}
                            </span>
                            {tab.closable && (
                              <span
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleTabClose(e, tab.id);
                                }}
                              >
                                <CloseOutlined />
                              </span>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  ),
                })),
              }}
              placement="bottomRight"
            >
              <span className={styles.moreButton}>
                <EllipsisOutlined />
              </span>
            </Dropdown>
          ) : null
        }
      />
    </div>
  );
};

export default HeaderTabs;
