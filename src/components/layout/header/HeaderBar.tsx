import React, { createElement } from 'react';
import { Layout, Button, Tooltip } from 'antd';
import { MenuFoldOutlined, MenuUnfoldOutlined, DashboardOutlined } from '@ant-design/icons';
import useStyles from '../BasicLayoutStyles';
import ThemeSwitch from '../ThemeSwitch';
import UserMenu from './UserMenu';
import HeaderTabs from './HeaderTabs';
import { createStyles } from 'antd-style';

const { Header } = Layout;

interface HeaderBarProps {
  collapsed: boolean;
  toggleCollapsed: () => void;
}

// 为 HeaderBar 创建额外的样式
const useHeaderStyles = createStyles(() => ({
  headerContainer: {
    display: 'flex',
    padding: 0,
    height: '48px',
    lineHeight: 'normal',
    backgroundColor: 'white',
    color: 'rgba(0, 0, 0, 0.85)',
    boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)',
    borderBottom: '1px solid #f0f0f0',
    position: 'fixed',
    top: 0,
    right: 0,
    zIndex: 1000,
  },
  headerLeft: {
    display: 'flex',
    alignItems: 'center',
    padding: '0 8px',
    width: '48px',
    justifyContent: 'center',
  },
  headerRight: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: 'auto',
    padding: '0 16px',
  },
  logo: {
    fontSize: '18px',
    fontWeight: 'bold',
    marginRight: '16px',
    color: 'white',
  },
  tabsContainer: {
    flex: 1,
    display: 'flex',
    alignItems: 'center',
    overflow: 'hidden',
    height: '100%',
  },
}));

const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, toggleCollapsed }) => {
  const { styles } = useStyles();
  const { styles: headerStyles } = useHeaderStyles();

  return (
    <Header
      className={headerStyles.headerContainer}
      style={{
        width: `calc(100% - ${collapsed ? 80 : 155}px)`,
        marginLeft: `${collapsed ? 80 : 155}px`,
        transition: 'all 0.2s',
      }}
    >
      <div className={headerStyles.headerLeft}>
        {createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {
          className: styles.trigger,
          onClick: toggleCollapsed,
          style: {
            color: 'rgba(0, 0, 0, 0.65)',
            fontSize: '16px',
            padding: '0',
            lineHeight: '48px',
          },
        })}
      </div>
      <div className={headerStyles.tabsContainer}>
        <HeaderTabs />
      </div>
      <div className={headerStyles.headerRight}>
        <UserMenu />
      </div>
    </Header>
  );
};

export default HeaderBar;
