import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag } from 'antd';
import { debounce } from 'lodash-es';
import { getBrandList } from '@/api/BrandApi';
import type { BrandListProps } from '@/types/brands';
import { logError } from '@/utils/errorHandler';

interface BrandSearchSelectorProps {
  value?: string;
  onChange?: (value: string, brand: BrandListProps | null) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const BrandSearchSelector: React.FC<BrandSearchSelectorProps> = ({
  value,
  onChange,
  placeholder = '请搜索品牌',
  disabled = false,
  allowClear = true,
  style,
  className,
}) => {
  const [brands, setBrands] = useState<BrandListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [currentBrand, setCurrentBrand] = useState<BrandListProps | null>(null);

  // 加载当前选中的品牌信息
  const loadCurrentBrand = async (brandCode: string) => {
    if (!brandCode) {
      setCurrentBrand(null);
      return;
    }

    try {
      setLoading(true);
      const response = await getBrandList({
        page: 0,
        pageSize: 0,
        search: brandCode,
      });

      if (response.code === 200 && response.data?.brands) {
        const brand = response.data.brands.find((b) => b.code === brandCode);
        if (brand) {
          setCurrentBrand(brand);
          // 将当前品牌添加到品牌列表中，确保它能被显示
          setBrands((prev) => {
            const exists = prev.some((b) => b.code === brandCode);
            return exists ? prev : [brand, ...prev];
          });
        }
      }
    } catch (error: any) {
      logError('加载品牌信息', error);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setBrands([]);
          return;
        }

        setLoading(true);
        try {
          const response = await getBrandList({
            page: 0,
            pageSize: 0,
            search: searchText.trim(),
          });

          if (response.code === 200 && response.data?.brands) {
            // 只显示启用的品牌
            const activeBrands = response.data.brands.filter((brand) => brand.isActive);
            setBrands(activeBrands);
          } else {
            setBrands([]);
          }
        } catch (error: any) {
          logError('搜索品牌', error);
          setBrands([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [],
  );

  // 当value变化时加载当前品牌信息
  useEffect(() => {
    if (value) {
      loadCurrentBrand(value);
    } else {
      setCurrentBrand(null);
    }
  }, [value]);

  // 当搜索值变化时触发搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 合并当前品牌和搜索结果，确保当前品牌始终显示
  const allBrands = React.useMemo(() => {
    const brandMap = new Map<string, BrandListProps>();

    // 先添加当前品牌
    if (currentBrand) {
      brandMap.set(currentBrand.code, currentBrand);
    }

    // 再添加搜索结果
    brands.forEach((brand) => {
      brandMap.set(brand.code, brand);
    });

    return Array.from(brandMap.values());
  }, [currentBrand, brands]);

  // 处理选择
  const handleChange = (selectedValue: string) => {
    const selectedBrand = allBrands.find((brand) => brand.code === selectedValue) || null;
    onChange?.(selectedValue, selectedBrand);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setBrands([]);
    onChange?.('', null);
  };

  // 生成选项
  const options = allBrands.map((brand) => ({
    value: brand.code,
    label: brand.name, // 简化为只显示品牌名称
    children: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ display: 'flex' }}>
          <div style={{ fontWeight: 500 }}>{brand.name}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c', display: 'flex', gap: '8px' }}>
            <span>{brand.code}</span>
            <Tag color="blue">预订: ¥{brand.orderPrice || 0}</Tag>
            <Tag color="green">补货: ¥{brand.restockPrice || 0}</Tag>
            <Tag color="orange">现货: ¥{brand.spotPrice || 0}</Tag>
          </div>
        </div>
      </div>
    ),
  }));

  return (
    <Select
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={{ minHeight: '40px', ...style }}
      className={className}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      optionLabelProp="label"
    />
  );
};

export default BrandSearchSelector;
