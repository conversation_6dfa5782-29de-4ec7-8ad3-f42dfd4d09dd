import React from 'react';
import SimpleImageUpload from './SimpleImageUpload';

interface SingleImageArrayAdapterProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  folder?: string;
  enablePaste?: boolean;
}

/**
 * 单图片上传适配器组件
 * 将 SimpleImageUpload（单个字符串）适配为字符串数组格式
 * 用于后端期望数组但前端只需要单选的场景
 */
const SingleImageArrayAdapter: React.FC<SingleImageArrayAdapterProps> = ({
  value = [],
  onChange,
  folder,
  enablePaste = true,
}) => {
  // 将数组的第一个元素作为单个值
  const singleValue = value.length > 0 ? value[0] : '';

  // 处理单个值变化，转换为数组格式
  const handleSingleChange = (newValue: string) => {
    if (onChange) {
      // 如果有值，创建包含单个元素的数组；如果没有值，创建空数组
      onChange(newValue ? [newValue] : []);
    }
  };

  return (
    <SimpleImageUpload
      value={singleValue}
      onChange={handleSingleChange}
      folder={folder}
      enablePaste={enablePaste}
    />
  );
};

export default SingleImageArrayAdapter;
