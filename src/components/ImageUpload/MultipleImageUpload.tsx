import React, { useState, useRef } from 'react';
import { message, Image, Button, Typography } from 'antd';
import { PlusOutlined, DeleteOutlined, LoadingOutlined } from '@ant-design/icons';
import { customUploadRequest } from '@/api/fileUpload';
import { UPLOAD_FOLDERS } from '@/api/fileUpload';

interface MultipleImageUploadProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  folder?: string;
  maxCount?: number; // 最大上传数量
  enablePaste?: boolean; // 是否启用粘贴上传
}

const { Text } = Typography;

/**
 * 多图片上传组件，基于 SimpleImageUpload 扩展
 * 支持点击上传、粘贴上传、拖拽上传
 */
const MultipleImageUpload: React.FC<MultipleImageUploadProps> = ({
  value = [],
  onChange,
  folder = UPLOAD_FOLDERS.PRODUCT,
  maxCount = 8,
  enablePaste = true,
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrls, setImageUrls] = useState<string[]>(value);
  const [isHovering, setIsHovering] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 通用的文件上传处理函数
  const uploadFile = async (file: File, source: 'click' | 'paste' = 'click') => {
    // 验证文件类型
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return;
    }

    // 验证文件大小 (5MB)
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片必须小于 5MB!');
      return;
    }

    // 检查数量限制
    if (imageUrls.length >= maxCount) {
      message.error(`最多只能上传 ${maxCount} 张图片!`);
      return;
    }

    try {
      setLoading(true);
      const sourceText = source === 'paste' ? '粘贴' : '点击';
      console.log(
        `开始${sourceText}上传图片:`,
        file.name || 'clipboard-image',
        '大小:',
        (file.size / 1024).toFixed(2) + 'KB',
      );

      // 使用自定义上传函数
      const result = await new Promise<{ url: string }>((resolve, reject) => {
        customUploadRequest(
          {
            file,
            onSuccess: (response: any) => {
              console.log('上传成功响应:', response);
              resolve(response);
            },
            onError: (error: any) => {
              console.error('上传失败:', error);
              reject(error);
            },
          },
          folder,
        );
      });

      // 检查返回的URL格式
      let newImageUrl = result.url;
      console.log('获取到的图片URL:', newImageUrl);

      // 如果URL不是完整的HTTP地址，添加域名前缀
      if (newImageUrl && !newImageUrl.startsWith('http')) {
        console.log('URL不是完整地址，原始URL:', newImageUrl);
        newImageUrl = `http://*************:3000${newImageUrl}`;
        console.log('修正后的URL:', newImageUrl);
      }

      // 验证图片URL是否可访问
      const img = new window.Image();
      img.onload = () => {
        console.log('图片加载成功:', newImageUrl);
        const newImageUrls = [...imageUrls, newImageUrl];
        setImageUrls(newImageUrls);

        // 调用 onChange 回调
        if (onChange) {
          onChange(newImageUrls);
        }

        const successText = source === 'paste' ? '粘贴上传成功!' : '上传成功!';
        message.success(successText);
      };

      img.onerror = () => {
        console.error('图片加载失败:', newImageUrl);
        message.error('图片上传成功但无法显示，请检查图片URL');

        // 即使图片无法显示，也要保存URL，可能是网络问题
        const newImageUrls = [...imageUrls, newImageUrl];
        setImageUrls(newImageUrls);
        if (onChange) {
          onChange(newImageUrls);
        }
      };

      img.src = newImageUrl;
    } catch (error) {
      console.error('上传失败:', error);
      const errorText = source === 'paste' ? '粘贴上传失败，请重试!' : '上传失败，请重试!';
      message.error(errorText);
    } finally {
      setLoading(false);
    }
  };

  // 处理文件选择（点击上传）
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // 检查数量限制
    if (imageUrls.length + files.length > maxCount) {
      message.error(`最多只能上传 ${maxCount} 张图片，当前已有 ${imageUrls.length} 张!`);
      return;
    }

    // 逐个上传文件
    for (const file of files) {
      await uploadFile(file, 'click');
    }

    // 清空 input 的值
    if (e.target) {
      e.target.value = '';
    }
  };

  // 处理粘贴事件
  const handlePaste = async (e: React.ClipboardEvent) => {
    if (!enablePaste) return;

    e.preventDefault();
    const items = Array.from(e.clipboardData.items);
    const imageItem = items.find((item) => item.type.startsWith('image/'));

    if (imageItem) {
      const file = imageItem.getAsFile();
      if (file) {
        await uploadFile(file, 'paste');
      }
    } else {
      message.info('剪贴板中没有图片，请先复制图片');
    }
  };

  // 删除图片
  const handleRemoveImage = (index: number) => {
    const newImageUrls = imageUrls.filter((_, i) => i !== index);
    setImageUrls(newImageUrls);
    if (onChange) {
      onChange(newImageUrls);
    }
  };

  // 处理按钮点击
  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 当 value 属性变化时，更新内部状态
  React.useEffect(() => {
    setImageUrls(value || []);
  }, [value]);

  return (
    <div
      ref={containerRef}
      tabIndex={0}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      onPaste={handlePaste}
      style={{
        outline: 'none',
        width: '100%',
      }}
    >
      {/* 已上传的图片列表 */}
      {imageUrls.length > 0 && (
        <div style={{ marginBottom: 16 }}>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
              gap: '12px',
              marginBottom: 12,
            }}
          >
            {imageUrls.map((url, index) => (
              <div
                key={index}
                style={{
                  position: 'relative',
                  border: '1px solid #d9d9d9',
                  borderRadius: 8,
                  overflow: 'hidden',
                  backgroundColor: '#fafafa',
                }}
              >
                <Image
                  src={url}
                  alt={`图片 ${index + 1}`}
                  style={{
                    width: '100%',
                    height: 120,
                    objectFit: 'cover',
                  }}
                  preview={{
                    mask: false,
                  }}
                />
                <Button
                  type="text"
                  danger
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => handleRemoveImage(index)}
                  style={{
                    position: 'absolute',
                    top: 4,
                    right: 4,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    border: '1px solid #ff4d4f',
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 上传区域 */}
      {imageUrls.length < maxCount && (
        <div
          style={{
            position: 'relative',
            width: '100%',
            minHeight: '120px',
            border: `2px dashed ${
              isDragOver ? '#1890ff' : isFocused ? '#1890ff' : isHovering ? '#40a9ff' : '#d9d9d9'
            }`,
            borderRadius: 8,
            backgroundColor: isDragOver
              ? '#e6f7ff'
              : isFocused
                ? '#f0f9ff'
                : isHovering
                  ? '#f0f9ff'
                  : '#fafafa',
            transition: 'all 0.3s',
            cursor: loading ? 'not-allowed' : 'pointer',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 16,
          }}
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          onClick={handleButtonClick}
          onDragOver={(e) => {
            e.preventDefault();
            setIsDragOver(true);
          }}
          onDragLeave={() => setIsDragOver(false)}
          onDrop={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsDragOver(false);
            const files = Array.from(e.dataTransfer.files);
            const imageFiles = files.filter((file) => file.type.startsWith('image/'));

            if (imageFiles.length > 0) {
              // 检查数量限制
              if (imageUrls.length + imageFiles.length > maxCount) {
                message.error(`最多只能上传 ${maxCount} 张图片，当前已有 ${imageUrls.length} 张!`);
                return;
              }

              // 逐个上传文件
              imageFiles.forEach((file) => uploadFile(file, 'click'));
            }
          }}
        >
          {/* 隐藏的文件输入 */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            style={{ display: 'none' }}
            disabled={loading}
            multiple
          />

          {loading ? (
            <div style={{ textAlign: 'center' }}>
              <LoadingOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
              <div style={{ color: '#666' }}>上传中...</div>
            </div>
          ) : (
            <div style={{ textAlign: 'center' }}>
              <PlusOutlined style={{ fontSize: 24, color: '#999', marginBottom: 8 }} />
              <div style={{ color: '#666', marginBottom: 4 }}>点击或拖拽上传图片</div>
              {enablePaste && (
                <Text type="secondary" style={{ fontSize: 12 }}>
                  您也可以复制图片后按 Ctrl+V 粘贴上传
                </Text>
              )}
              <div style={{ marginTop: 8 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  已上传 {imageUrls.length}/{maxCount} 张
                </Text>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MultipleImageUpload;
