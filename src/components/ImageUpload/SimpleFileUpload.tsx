import React, { useState, useId, useRef } from 'react';
import { message, Typography, Button } from 'antd';
import { LoadingOutlined, PaperClipOutlined, DeleteOutlined } from '@ant-design/icons';
import { uploadMultipleFiles } from '@/api/fileUpload';
import { UPLOAD_FOLDERS } from '@/api/fileUpload';

interface SimpleFileUploadProps {
  value?: string | string[];
  onChange?: (value: string | string[]) => void;
  folder?: string;
  accept?: string;
  maxSize?: number; // MB
  multiple?: boolean; // 是否支持多文件
  maxCount?: number; // 最大文件数量
}

const { Text } = Typography;

interface FileItem {
  url: string;
  name: string;
}

/**
 * 简单的文件上传组件
 * 支持各种文件类型上传
 */
const SimpleFileUpload: React.FC<SimpleFileUploadProps> = ({
  value,
  onChange,
  folder = UPLOAD_FOLDERS.DOCUMENT,
  accept = '*/*',
  maxSize = 10,
  multiple = false,
  maxCount = 5,
}) => {
  const [loading, setLoading] = useState(false);
  const [fileItems, setFileItems] = useState<FileItem[]>([]);
  const uniqueId = useId();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 初始化文件列表
  React.useEffect(() => {
    if (value) {
      if (typeof value === 'string') {
        // 单文件模式
        const urlParts = value.split('/');
        const fileName = urlParts[urlParts.length - 1] || '文件';
        setFileItems([{ url: value, name: fileName }]);
      } else if (Array.isArray(value)) {
        // 多文件模式
        const items = value.map((url) => {
          const urlParts = url.split('/');
          const fileName = urlParts[urlParts.length - 1] || '文件';
          return { url, name: fileName };
        });
        setFileItems(items);
      }
    } else {
      setFileItems([]);
    }
  }, [value]);

  // 文件上传处理函数
  const uploadFileHandler = async (files: File[]) => {
    // 验证文件数量
    if (!multiple && files.length > 1) {
      message.error('只能上传一个文件!');
      return;
    }

    if (multiple && fileItems.length + files.length > maxCount) {
      message.error(`最多只能上传 ${maxCount} 个文件!`);
      return;
    }

    // 验证文件大小
    for (const file of files) {
      const isLtMaxSize = file.size / 1024 / 1024 < maxSize;
      if (!isLtMaxSize) {
        message.error(`文件 ${file.name} 必须小于 ${maxSize}MB!`);
        return;
      }
    }

    try {
      setLoading(true);

      console.log('开始上传文件:', files.map((f) => f.name).join(', '));

      const response = await uploadMultipleFiles(files, folder);

      if (response.code === 200) {
        let newFileUrls = response.data.urls || [];
        console.log('获取到的文件URLs:', newFileUrls);

        // 如果URL不是完整的HTTP地址，添加域名前缀
        newFileUrls = newFileUrls.map((url: string) => {
          if (url && !url.startsWith('http')) {
            return `http://*************:3000${url}`;
          }
          return url;
        });

        // 创建新的文件项
        const newFileItems = files.map((file, index) => ({
          url: newFileUrls[index] || '',
          name: file.name,
        }));

        let updatedFileItems: FileItem[];
        if (multiple) {
          updatedFileItems = [...fileItems, ...newFileItems];
        } else {
          updatedFileItems = newFileItems;
        }

        setFileItems(updatedFileItems);

        // 调用 onChange 回调
        if (onChange) {
          if (multiple) {
            onChange(updatedFileItems.map((item) => item.url));
          } else {
            onChange(updatedFileItems[0]?.url || '');
          }
        }

        message.success(`文件上传成功! 共上传 ${files.length} 个文件`);
      } else {
        message.error(response.message || '文件上传失败');
      }
    } catch (error) {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试!');
    } finally {
      setLoading(false);
    }
  };

  // 处理文件选择
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    await uploadFileHandler(files);

    // 清空 input 的值
    if (e.target) {
      e.target.value = '';
    }
  };

  // 触发文件选择
  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 删除单个文件
  const handleRemoveFile = (index: number) => {
    const updatedFileItems = fileItems.filter((_, i) => i !== index);
    setFileItems(updatedFileItems);

    if (onChange) {
      if (multiple) {
        onChange(updatedFileItems.map((item) => item.url));
      } else {
        onChange(updatedFileItems[0]?.url || '');
      }
    }
  };

  // 删除所有文件
  const handleRemoveAll = () => {
    setFileItems([]);
    if (onChange) {
      onChange(multiple ? [] : '');
    }
  };

  return (
    <div
      style={{
        border: '1px dashed #d9d9d9',
        borderRadius: '8px',
        padding: '16px',
        textAlign: 'center',
        backgroundColor: '#fafafa',
        transition: 'all 0.3s ease',
        minHeight: '120px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileChange}
        style={{ display: 'none' }}
        id={uniqueId}
        disabled={loading}
      />

      {loading ? (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
          <LoadingOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          <Text style={{ color: '#666' }}>上传中...</Text>
        </div>
      ) : fileItems.length > 0 ? (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          {/* 文件列表 */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {fileItems.map((fileItem, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '8px 12px',
                  backgroundColor: '#fff',
                  border: '1px solid #d9d9d9',
                  borderRadius: '6px',
                }}
              >
                <PaperClipOutlined style={{ color: '#1890ff', fontSize: '16px' }} />
                <Text
                  style={{
                    flex: 1,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    fontSize: '14px',
                  }}
                  title={fileItem.name}
                >
                  {fileItem.name}
                </Text>
                <div style={{ display: 'flex', gap: '4px' }}>
                  <Button
                    size="small"
                    type="link"
                    onClick={() => window.open(fileItem.url, '_blank')}
                  >
                    下载
                  </Button>
                  <Button
                    size="small"
                    type="link"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveFile(index)}
                  />
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮 */}
          <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
            {multiple && fileItems.length < maxCount && (
              <Button onClick={handleButtonClick} disabled={loading}>
                继续添加文件
              </Button>
            )}
            {!multiple && (
              <Button onClick={handleButtonClick} disabled={loading}>
                重新选择文件
              </Button>
            )}
            <Button danger onClick={handleRemoveAll} disabled={loading}>
              清空所有文件
            </Button>
          </div>
        </div>
      ) : (
        <div
          style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '12px' }}
        >
          <PaperClipOutlined style={{ fontSize: '32px', color: '#bfbfbf' }} />
          <div
            style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '4px' }}
          >
            <Text style={{ fontSize: '16px', fontWeight: 600, color: '#333' }}>
              {multiple ? '点击上传文件' : '点击上传文件'}
            </Text>
            <Text style={{ fontSize: '12px', color: '#999' }}>
              {multiple
                ? `支持多文件上传，最多 ${maxCount} 个文件，每个文件最大 ${maxSize}MB`
                : `支持各种文件格式，最大 ${maxSize}MB`}
            </Text>
          </div>
          <Button type="primary" onClick={handleButtonClick} disabled={loading}>
            选择文件
          </Button>
        </div>
      )}
    </div>
  );
};

export default SimpleFileUpload;
