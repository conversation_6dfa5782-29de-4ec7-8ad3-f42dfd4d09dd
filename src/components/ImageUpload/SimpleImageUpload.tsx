import React, { useState, useId, useRef } from 'react';
import { message, Image, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { customUploadRequest } from '@/api/fileUpload';
import { UPLOAD_FOLDERS } from '@/api/fileUpload';

interface SimpleImageUploadProps {
  value?: string;
  onChange?: (value: string) => void;
  folder?: string;
  enablePaste?: boolean; // 是否启用粘贴上传
}

const { Text } = Typography;

/**
 * 简单的图片上传组件，支持点击上传和粘贴上传
 * 直接使用 input type="file" 和自定义上传函数
 */
const SimpleImageUpload: React.FC<SimpleImageUploadProps> = ({
  value,
  onChange,
  folder = UPLOAD_FOLDERS.PRODUCT,
  enablePaste = true,
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>(value || '');
  const [isHovering, setIsHovering] = useState(false); // 鼠标悬停状态
  const [isDragOver, setIsDragOver] = useState(false); // 拖拽悬停状态
  const [isFocused, setIsFocused] = useState(false); // 组件是否获得焦点
  const uniqueId = useId(); // 生成唯一ID
  const fileInputRef = useRef<HTMLInputElement>(null); // 引用input元素
  const containerRef = useRef<HTMLDivElement>(null); // 容器引用，用于焦点管理

  // 通用的文件上传处理函数
  const uploadFile = async (file: File, source: 'click' | 'paste' = 'click') => {
    // 验证文件类型
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return;
    }

    // 验证文件大小 (5MB)
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片必须小于 5MB!');
      return;
    }

    try {
      setLoading(true);
      const sourceText = source === 'paste' ? '粘贴' : '点击';
      console.log(
        `开始${sourceText}上传图片:`,
        file.name || 'clipboard-image',
        '大小:',
        (file.size / 1024).toFixed(2) + 'KB',
      );

      // 使用自定义上传函数
      const result = await new Promise<{ url: string }>((resolve, reject) => {
        customUploadRequest(
          {
            file,
            onSuccess: (response: any) => {
              console.log('上传成功响应:', response);
              resolve(response);
            },
            onError: (error: any) => {
              console.error('上传失败:', error);
              reject(error);
            },
          },
          folder,
        );
      });

      // 检查返回的URL格式
      let newImageUrl = result.url;
      console.log('获取到的图片URL:', newImageUrl);

      // 如果URL不是完整的HTTP地址，添加域名前缀
      if (newImageUrl && !newImageUrl.startsWith('http')) {
        console.log('URL不是完整地址，原始URL:', newImageUrl);
        // 添加后端服务器地址前缀
        newImageUrl = `http://43.138.236.92:3000${newImageUrl}`;
        console.log('修正后的URL:', newImageUrl);
      }

      // 验证图片URL是否可访问
      const img = new window.Image();
      img.onload = () => {
        console.log('图片加载成功:', newImageUrl);
        setImageUrl(newImageUrl);

        // 调用 onChange 回调
        if (onChange) {
          onChange(newImageUrl);
        }

        const successText = source === 'paste' ? '粘贴上传成功!' : '上传成功!';
        message.success(successText);
      };

      img.onerror = () => {
        console.error('图片加载失败:', newImageUrl);
        message.error('图片上传成功但无法显示，请检查图片URL');

        // 即使图片无法显示，也要保存URL，可能是网络问题
        setImageUrl(newImageUrl);
        if (onChange) {
          onChange(newImageUrl);
        }
      };

      img.src = newImageUrl;
    } catch (error) {
      console.error('上传失败:', error);
      const errorText = source === 'paste' ? '粘贴上传失败，请重试!' : '上传失败，请重试!';
      message.error(errorText);
    } finally {
      setLoading(false);
    }
  };

  // 处理文件选择（点击上传）
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    await uploadFile(file, 'click');

    // 清空 input 的值，以便可以再次选择同一个文件
    if (e.target) {
      e.target.value = '';
    }
  };

  // 处理粘贴事件
  const handlePaste = async (e: ClipboardEvent) => {
    // 只有当前组件获得焦点时才处理粘贴
    if (!enablePaste || loading || !isFocused) return;

    const items = e.clipboardData?.items;
    if (!items) return;

    // 查找图片文件
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.startsWith('image/')) {
        e.preventDefault(); // 阻止默认粘贴行为

        const file = item.getAsFile();
        if (file) {
          console.log(
            `组件 ${uniqueId} 检测到粘贴的图片:`,
            file.name || 'clipboard-image',
            file.type,
          );
          await uploadFile(file, 'paste');
        }
        break;
      }
    }
  };

  // 处理按钮点击，触发文件选择
  const handleButtonClick = React.useCallback(() => {
    if (loading) return; // 如果正在上传，直接返回

    if (fileInputRef.current) {
      console.log('触发文件选择');
      fileInputRef.current.click();
    }
  }, [loading]);

  // 处理单击 - 激活组件
  const handleSingleClick = React.useCallback(() => {
    if (loading || imageUrl) return;

    if (containerRef.current) {
      containerRef.current.focus();
    }
  }, [loading, imageUrl]);

  // 处理双击 - 触发上传
  const handleDoubleClick = React.useCallback(() => {
    if (loading || imageUrl) return;

    console.log('双击触发上传');
    handleButtonClick();
  }, [loading, imageUrl, handleButtonClick]);

  // 当 value 属性变化时，更新内部状态
  React.useEffect(() => {
    setImageUrl(value || '');
  }, [value]);

  // 设置粘贴事件监听
  React.useEffect(() => {
    if (!enablePaste) return;

    // 添加全局粘贴事件监听
    const handleGlobalPaste = (e: ClipboardEvent) => {
      handlePaste(e);
    };

    document.addEventListener('paste', handleGlobalPaste);

    return () => {
      document.removeEventListener('paste', handleGlobalPaste);
    };
  }, [enablePaste, loading, isFocused]); // 依赖焦点状态

  return (
    <div
      ref={containerRef}
      style={{
        width: '100%',
        position: 'relative',
        minWidth: '200px',
        maxWidth: '100%',
      }}
      tabIndex={0} // 使容器可以获得焦点
      onFocus={() => {
        setIsFocused(true);
      }}
      onBlur={() => {
        setIsFocused(false);
      }}
      onKeyDown={(e) => {
        // 支持空格键和回车键触发上传
        if ((e.key === ' ' || e.key === 'Enter') && !loading) {
          e.preventDefault();
          handleButtonClick();
        }
      }}
    >
      {/* 主上传区域 */}
      <div
        style={{
          position: 'relative',
          width: '100%',
          minHeight: imageUrl ? 'auto' : 'clamp(120px, 20vw, 180px)', // 自适应高度
          border: `2px dashed ${
            isDragOver
              ? '#1890ff'
              : isFocused
                ? '#1890ff'
                : isHovering && !imageUrl
                  ? '#40a9ff'
                  : '#d9d9d9'
          }`,
          borderRadius: 'clamp(6px, 1vw, 12px)', // 自适应圆角
          backgroundColor: imageUrl
            ? '#fafafa'
            : isDragOver
              ? '#e6f7ff'
              : isFocused
                ? '#f0f9ff'
                : isHovering
                  ? '#f0f9ff'
                  : '#fafafa',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)', // 更流畅的动画
          cursor: loading ? 'not-allowed' : 'pointer',
          overflow: 'hidden',
          transform:
            isHovering && !imageUrl ? 'translateY(-2px) scale(1.01)' : 'translateY(0) scale(1)',
          boxShadow:
            isHovering && !imageUrl
              ? '0 4px 20px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05)'
              : isDragOver
                ? '0 8px 30px rgba(24, 144, 255, 0.3), 0 4px 12px rgba(24, 144, 255, 0.15)'
                : isFocused
                  ? '0 0 0 3px rgba(24, 144, 255, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1)'
                  : '0 1px 3px rgba(0, 0, 0, 0.05)',
          outline: 'none', // 移除默认焦点轮廓
        }}
        onClick={handleSingleClick}
        onDoubleClick={handleDoubleClick}
        onMouseEnter={() => {
          setIsHovering(true);
        }}
        onMouseLeave={() => {
          setIsHovering(false);
        }}
        onDragOver={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsDragOver(true);
        }}
        onDragLeave={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsDragOver(false);
        }}
        onDrop={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsDragOver(false);
          const files = Array.from(e.dataTransfer.files);
          const imageFile = files.find((file) => file.type.startsWith('image/'));
          if (imageFile && !loading) {
            uploadFile(imageFile, 'click');
          }
        }}
      >
        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          style={{ display: 'none' }}
          id={uniqueId}
          disabled={loading}
        />

        {/* 图片预览 */}
        {imageUrl ? (
          <div
            style={{ position: 'relative', width: '100%' }}
            onClick={(e) => e.stopPropagation()} // 阻止图片区域的点击事件冒泡
          >
            <Image
              src={imageUrl}
              alt="上传的图片"
              style={{
                width: '100%',
                height: 'auto',
                maxHeight: '300px',
                objectFit: 'contain',
                borderRadius: '6px',
              }}
              preview={{
                mask: (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: '8px',
                      color: 'white',
                    }}
                  >
                    <div style={{ fontSize: '16px' }}>🔍</div>
                    <div>点击预览</div>
                  </div>
                ),
              }}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
              onError={() => {
                console.error('图片加载失败:', imageUrl);
              }}
            />

            {/* 更换图片按钮 */}
            <div
              style={{
                position: 'absolute',
                top: '12px',
                right: '12px',
                background: 'rgba(0, 0, 0, 0.6)',
                borderRadius: '6px',
                padding: '8px 12px',
                color: 'white',
                fontSize: '12px',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                opacity: 0.8,
                zIndex: 10, // 确保按钮在最上层
              }}
              onClick={(e) => {
                e.stopPropagation();
                if (!loading) handleButtonClick();
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '1';
                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.8)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '0.8';
                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.6)';
              }}
            >
              {loading ? '上传中...' : '更换图片'}
            </div>
          </div>
        ) : (
          /* 空状态上传区域 */
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '160px',
              padding: '20px',
              textAlign: 'center',
            }}
          >
            {loading ? (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '12px',
                }}
              >
                <LoadingOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
                <Text style={{ color: '#666', fontSize: '14px' }}>上传中...</Text>
              </div>
            ) : (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '16px',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: '8px',
                  }}
                >
                  <Text
                    style={{
                      fontSize: 'clamp(14px, 3vw, 18px)',
                      fontWeight: 600,
                      color: isDragOver ? '#1890ff' : isFocused ? '#1890ff' : '#333',
                      transition: 'all 0.3s ease',
                      lineHeight: 1.4,
                    }}
                  >
                    {isDragOver
                      ? '松开鼠标上传图片'
                      : isFocused
                        ? '已激活！可以粘贴图片了'
                        : '单击激活 • 双击上传'}
                  </Text>

                  <Text
                    style={{
                      fontSize: 'clamp(11px, 2vw, 14px)',
                      color: '#666',
                      textAlign: 'center',
                      lineHeight: 1.5,
                      maxWidth: '90%',
                    }}
                  >
                    {isDragOver
                      ? '支持 JPG、PNG、GIF 格式'
                      : isFocused
                        ? '按 Ctrl+V 粘贴图片，或拖拽文件到此处'
                        : '支持拖拽 • JPG、PNG、GIF • 最大 5MB'}
                  </Text>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleImageUpload;
