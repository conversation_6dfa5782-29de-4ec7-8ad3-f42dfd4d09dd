import React from 'react';
import { Select, Tag } from 'antd';
import type { ProductOrderInfo, SkuStock } from '@/types/product';

const { Option } = Select;

interface ProductSizeSelectorEnhancedProps {
  value?: string;
  onChange?: (sizeCode: string, skuStock?: SkuStock) => void;
  product?: ProductOrderInfo | null;
  selectedColorCode?: string;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
  showStockInfo?: boolean; // 是否显示库存信息
}

const ProductSizeSelectorEnhanced: React.FC<ProductSizeSelectorEnhancedProps> = ({
  value,
  onChange,
  product,
  selectedColorCode,
  placeholder = '请先选择颜色',
  disabled = false,
  style,
  className,
  showStockInfo = true,
}) => {
  // 获取选中颜色的可用尺码和库存信息
  const { availableSizes, skuStockMap } = React.useMemo(() => {
    if (!product || !selectedColorCode) return { availableSizes: [], skuStockMap: {} };
    
    const colorCombination = product.colorCombinations?.find(
      combo => combo.colorCode === selectedColorCode
    );
    
    if (!colorCombination) return { availableSizes: [], skuStockMap: {} };

    // 创建尺码到库存信息的映射
    const stockMap: Record<string, SkuStock> = {};
    colorCombination.skuStocks?.forEach(sku => {
      stockMap[sku.sizeCode] = sku;
    });

    return {
      availableSizes: colorCombination.sizes || [],
      skuStockMap: stockMap,
    };
  }, [product, selectedColorCode]);

  // 判断是否禁用
  const isDisabled = disabled || !product || !selectedColorCode || availableSizes.length === 0;

  // 更新placeholder
  const currentPlaceholder = !product 
    ? '请先选择商品' 
    : !selectedColorCode 
    ? '请先选择颜色' 
    : availableSizes.length === 0 
    ? '该颜色暂无尺码配置' 
    : '请选择尺码';

  // 尺码排序函数
  const sortSizes = (sizes: string[]) => {
    const sizeOrder = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'];
    
    return [...sizes].sort((a, b) => {
      // 如果都是标准尺码，按预定义顺序排序
      const aIndex = sizeOrder.indexOf(a);
      const bIndex = sizeOrder.indexOf(b);
      
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }
      
      // 如果都是数字，按数字大小排序
      const aNum = parseInt(a);
      const bNum = parseInt(b);
      if (!isNaN(aNum) && !isNaN(bNum)) {
        return aNum - bNum;
      }
      
      // 其他情况按字母顺序排序
      return a.localeCompare(b);
    });
  };

  // 获取库存状态颜色
  const getStockStatusColor = (stock: number) => {
    if (stock <= 0) return 'red';
    if (stock <= 5) return 'orange';
    return 'green';
  };

  // 处理选择变化
  const handleChange = (sizeCode: string) => {
    const skuStock = skuStockMap[sizeCode];
    onChange?.(sizeCode, skuStock);
  };

  const sortedSizes = sortSizes(availableSizes);

  return (
    <Select
      value={value}
      onChange={handleChange}
      placeholder={currentPlaceholder}
      disabled={isDisabled}
      style={style}
      className={className}
      allowClear
      showSearch
      filterOption={(input, option) =>
        (option?.children as any)?.props?.children?.some((child: any) => 
          typeof child === 'string' && child.toLowerCase().includes(input.toLowerCase())
        )
      }
    >
      {sortedSizes.map((size) => {
        const skuStock = skuStockMap[size];
        const actualStock = skuStock?.actualStock || 0;
        const reservedStock = skuStock?.reservedStock || 0;
        
        return (
          <Option 
            key={size} 
            value={size}
            disabled={showStockInfo && actualStock <= 0} // 无库存时禁用
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span style={{ 
                color: showStockInfo && actualStock <= 0 ? '#ccc' : undefined 
              }}>
                {size}
              </span>
              {showStockInfo && skuStock && (
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <Tag 
                    color={getStockStatusColor(actualStock)} 
                    size="small"
                    style={{ margin: 0, fontSize: '10px' }}
                  >
                    {actualStock}
                  </Tag>
                  {reservedStock > 0 && (
                    <Tag 
                      color="default" 
                      size="small"
                      style={{ margin: 0, fontSize: '10px' }}
                    >
                      预留{reservedStock}
                    </Tag>
                  )}
                </div>
              )}
            </div>
          </Option>
        );
      })}
    </Select>
  );
};

export default ProductSizeSelectorEnhanced;
