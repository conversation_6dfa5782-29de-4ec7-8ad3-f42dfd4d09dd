import React from 'react';
import { Select } from 'antd';
import type { ProductListItem } from '@/types/product';

const { Option } = Select;

interface ProductSizeSelectorProps {
  value?: string;
  onChange?: (sizeCode: string) => void;
  product?: ProductListItem | null;
  selectedColorCode?: string;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const ProductSizeSelector: React.FC<ProductSizeSelectorProps> = ({
  value,
  onChange,
  product,
  selectedColorCode,
  placeholder = '请先选择颜色',
  disabled = false,
  style,
  className,
}) => {
  // 获取选中颜色的可用尺码
  const availableSizes = React.useMemo(() => {
    if (!product || !selectedColorCode) return [];
    
    const colorCombination = product.colorSizeCombinations?.find(
      combo => combo.colorCode === selectedColorCode
    );
    
    return colorCombination?.sizes || [];
  }, [product, selectedColorCode]);

  // 判断是否禁用
  const isDisabled = disabled || !product || !selectedColorCode || availableSizes.length === 0;

  // 更新placeholder
  const currentPlaceholder = !product 
    ? '请先选择商品' 
    : !selectedColorCode 
    ? '请先选择颜色' 
    : availableSizes.length === 0 
    ? '该颜色暂无尺码配置' 
    : '请选择尺码';

  // 尺码排序函数
  const sortSizes = (sizes: string[]) => {
    const sizeOrder = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'];
    
    return [...sizes].sort((a, b) => {
      // 如果都是标准尺码，按预定义顺序排序
      const aIndex = sizeOrder.indexOf(a);
      const bIndex = sizeOrder.indexOf(b);
      
      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }
      
      // 如果都是数字，按数字大小排序
      const aNum = parseInt(a);
      const bNum = parseInt(b);
      if (!isNaN(aNum) && !isNaN(bNum)) {
        return aNum - bNum;
      }
      
      // 其他情况按字母顺序排序
      return a.localeCompare(b);
    });
  };

  const sortedSizes = sortSizes(availableSizes);

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={currentPlaceholder}
      disabled={isDisabled}
      style={style}
      className={className}
      allowClear
      showSearch
      filterOption={(input, option) =>
        (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
      }
    >
      {sortedSizes.map((size) => (
        <Option key={size} value={size}>
          {size}
        </Option>
      ))}
    </Select>
  );
};

export default ProductSizeSelector;
