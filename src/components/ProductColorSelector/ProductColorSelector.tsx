import React from 'react';
import { Select } from 'antd';
import type { ProductListItem } from '@/types/product';

const { Option } = Select;

interface ProductColorSelectorProps {
  value?: string;
  onChange?: (colorCode: string) => void;
  product?: ProductListItem | null;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const ProductColorSelector: React.FC<ProductColorSelectorProps> = ({
  value,
  onChange,
  product,
  placeholder = '请先选择商品',
  disabled = false,
  style,
  className,
}) => {
  // 获取商品的可用颜色
  const availableColors = product?.colorSizeCombinations || [];

  // 如果没有选择商品，显示禁用状态
  const isDisabled = disabled || !product || availableColors.length === 0;

  // 更新placeholder
  const currentPlaceholder = !product 
    ? '请先选择商品' 
    : availableColors.length === 0 
    ? '该商品暂无颜色配置' 
    : '请选择颜色';

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={currentPlaceholder}
      disabled={isDisabled}
      style={style}
      className={className}
      allowClear
      showSearch
      filterOption={(input, option) =>
        (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
      }
    >
      {availableColors.map((colorCombination) => (
        <Option key={colorCombination.colorCode} value={colorCombination.colorCode}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span style={{
              display: 'inline-block',
              width: '16px',
              height: '16px',
              borderRadius: '50%',
              backgroundColor: colorCombination.colorHex || '#ccc',
              border: '1px solid #d9d9d9'
            }} />
            <span>{colorCombination.colorName}</span>
            <span style={{ fontSize: '12px', color: '#8c8c8c' }}>
              ({colorCombination.colorCode})
            </span>
          </div>
        </Option>
      ))}
    </Select>
  );
};

export default ProductColorSelector;
