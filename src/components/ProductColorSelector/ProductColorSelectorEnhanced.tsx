import React from 'react';
import { Select, Tag } from 'antd';
import type { ProductOrderInfo } from '@/types/product';

const { Option } = Select;

interface ProductColorSelectorEnhancedProps {
  value?: string;
  onChange?: (colorCode: string) => void;
  product?: ProductOrderInfo | null;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
  showStockInfo?: boolean; // 是否显示库存信息
}

const ProductColorSelectorEnhanced: React.FC<ProductColorSelectorEnhancedProps> = ({
  value,
  onChange,
  product,
  placeholder = '请先选择商品',
  disabled = false,
  style,
  className,
  showStockInfo = true,
}) => {
  // 获取商品的可用颜色
  const availableColors = product?.colorCombinations || [];

  // 如果没有选择商品，显示禁用状态
  const isDisabled = disabled || !product || availableColors.length === 0;

  // 更新placeholder
  const currentPlaceholder = !product 
    ? '请先选择商品' 
    : availableColors.length === 0 
    ? '该商品暂无颜色配置' 
    : '请选择颜色';

  // 获取颜色的总库存
  const getColorTotalStock = (colorCombination: any) => {
    return colorCombination.skuStocks?.reduce((total: number, sku: any) => total + sku.actualStock, 0) || 0;
  };

  // 获取库存状态颜色
  const getStockStatusColor = (stock: number) => {
    if (stock <= 0) return 'red';
    if (stock <= 10) return 'orange';
    return 'green';
  };

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={currentPlaceholder}
      disabled={isDisabled}
      style={style}
      className={className}
      allowClear
      showSearch
      filterOption={(input, option) =>
        (option?.children as any)?.props?.children?.some((child: any) => 
          typeof child === 'string' && child.toLowerCase().includes(input.toLowerCase())
        )
      }
    >
      {availableColors.map((colorCombination) => {
        const totalStock = getColorTotalStock(colorCombination);
        
        return (
          <Option key={colorCombination.colorCode} value={colorCombination.colorCode}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{
                  display: 'inline-block',
                  width: '16px',
                  height: '16px',
                  borderRadius: '50%',
                  backgroundColor: '#ccc', // 可以根据需要添加颜色映射
                  border: '1px solid #d9d9d9'
                }} />
                <span>{colorCombination.colorName}</span>
                <span style={{ fontSize: '12px', color: '#8c8c8c' }}>
                  ({colorCombination.colorCode})
                </span>
              </div>
              {showStockInfo && (
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <Tag 
                    color={getStockStatusColor(totalStock)} 
                    size="small"
                    style={{ margin: 0, fontSize: '10px' }}
                  >
                    库存 {totalStock}
                  </Tag>
                  <span style={{ fontSize: '10px', color: '#999' }}>
                    {colorCombination.sizes.length}码
                  </span>
                </div>
              )}
            </div>
          </Option>
        );
      })}
    </Select>
  );
};

export default ProductColorSelectorEnhanced;
