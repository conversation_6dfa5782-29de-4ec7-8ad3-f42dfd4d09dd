import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag, Tooltip } from 'antd';
import { debounce } from 'lodash-es';
import { getBrandList } from '@/api/BrandApi';
import type { BrandListProps } from '@/types/brands';
import { logError } from '@/utils/errorHandler';

interface BrandMultipleSelectorProps {
  value?: string[];
  onChange?: (value: string[], brands: BrandListProps[]) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  maxTagCount?: number;
}

const BrandMultipleSelector: React.FC<BrandMultipleSelectorProps> = ({
  value = [],
  onChange,
  placeholder = '请搜索并选择品牌',
  disabled = false,
  allowClear = true,
  style,
  className,
  maxTagCount = 3,
}) => {
  const [brands, setBrands] = useState<BrandListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [allBrands, setAllBrands] = useState<BrandListProps[]>([]);

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setBrands([]);
          return;
        }

        setLoading(true);
        try {
          const response = await getBrandList({
            page: 0,
            pageSize: 0,
            search: searchText.trim(),
          });

          if (response.code === 200 && response.data?.brands) {
            // 只显示启用的品牌
            const activeBrands = response.data.brands.filter((brand) => brand.isActive);
            setBrands(activeBrands);

            // 更新所有品牌列表，避免重复
            setAllBrands((prev) => {
              const existingCodes = new Set(prev.map((b) => b.code));
              const uniqueNewBrands = activeBrands.filter((b) => !existingCodes.has(b.code));
              return [...prev, ...uniqueNewBrands];
            });
          } else {
            setBrands([]);
          }
        } catch (error) {
          logError('搜索品牌', error);
          setBrands([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [],
  );

  // 当搜索值变化时触发搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 处理选择
  const handleChange = (selectedValues: string[]) => {
    const selectedBrands = selectedValues
      .map((code) => allBrands.find((brand) => brand.code === code))
      .filter(Boolean) as BrandListProps[];

    onChange?.(selectedValues, selectedBrands);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setBrands([]);
    onChange?.([], []);
  };

  // 生成选项
  const options = brands.map((brand) => ({
    value: brand.code,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ display: 'flex' }}>
          <div style={{ fontWeight: 500 }}>{brand.name}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c', display: 'flex', gap: '8px' }}>
            <span>{brand.code}</span>
            <Tag color="blue">预订: ¥{brand.orderPrice}</Tag>
            <Tag color="green">补货: ¥{brand.restockPrice}</Tag>
            <Tag color="orange">现货: ¥{brand.spotPrice}</Tag>
          </div>
        </div>
      </div>
    ),
  }));

  return (
    <Select
      mode="multiple"
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      maxTagCount={maxTagCount}
      maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
    />
  );
};

export default BrandMultipleSelector;
