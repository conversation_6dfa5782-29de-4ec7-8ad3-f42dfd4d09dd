import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  Switch,
  Button,
  Space,
  Input,
  Modal,
  Form,
  Typography,
  message,
  Popconfirm,
  Checkbox,
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SettingOutlined,
  AppstoreAddOutlined,
} from '@ant-design/icons';
import RouteSelector from '@/components/RouteSelector';
import { extractAvailableRoutes } from '@/utils/routeExtractor';
import { routes } from '@/router/router';

const { Title, Text } = Typography;

interface Permission {
  read: boolean;
  create: boolean;
  delete: boolean;
  export: boolean;
  import: boolean;
  update: boolean;
}

interface PermissionEditorProps {
  value?: { [key: string]: Permission };
  onChange?: (value: { [key: string]: Permission }) => void;
  disabled?: boolean;
}

const defaultPermission: Permission = {
  read: false,
  create: false,
  delete: false,
  export: false,
  import: false,
  update: false,
};

const permissionLabels = {
  read: '查看',
  create: '创建',
  update: '更新',
  delete: '删除',
  export: '导出',
  import: '导入',
};

const PermissionEditor: React.FC<PermissionEditorProps> = ({
  value = {},
  onChange,
  disabled = false,
}) => {
  const [permissions, setPermissions] = useState<{ [key: string]: Permission }>(value);
  const [modalVisible, setModalVisible] = useState(false);
  const [routeSelectorVisible, setRouteSelectorVisible] = useState(false);
  const [newRoute, setNewRoute] = useState('');

  // 提取可用路由
  const availableRoutes = useMemo(() => extractAvailableRoutes(routes), []);

  useEffect(() => {
    setPermissions(value);
  }, [value]);

  const handlePermissionChange = (
    route: string,
    permissionKey: keyof Permission,
    checked: boolean,
  ) => {
    const newPermissions = {
      ...permissions,
      [route]: {
        ...permissions[route],
        [permissionKey]: checked,
      },
    };
    setPermissions(newPermissions);
    onChange?.(newPermissions);
  };

  const handleAddRoute = () => {
    if (!newRoute.trim()) {
      message.error('请输入路由路径');
      return;
    }

    if (permissions[newRoute]) {
      message.error('该路由已存在');
      return;
    }

    const newPermissions = {
      ...permissions,
      [newRoute]: { ...defaultPermission },
    };
    setPermissions(newPermissions);
    onChange?.(newPermissions);
    setNewRoute('');
    setModalVisible(false);
    message.success('路由添加成功');
  };

  const handleDeleteRoute = (route: string) => {
    const newPermissions = { ...permissions };
    delete newPermissions[route];
    setPermissions(newPermissions);
    onChange?.(newPermissions);
    message.success('路由删除成功');
  };

  // 处理路由选择器确认
  const handleRouteSelection = (selectedRoutes: string[]) => {
    const newPermissions = { ...permissions };

    // 为新选择的路由添加默认权限
    selectedRoutes.forEach((route) => {
      if (!newPermissions[route]) {
        newPermissions[route] = { ...defaultPermission };
      }
    });

    setPermissions(newPermissions);
    onChange?.(newPermissions);
    setRouteSelectorVisible(false);
    message.success(
      `已添加 ${selectedRoutes.filter((route) => !permissions[route]).length} 个路由权限`,
    );
  };

  const handleSelectAll = (route: string, checked: boolean) => {
    const newPermissions = {
      ...permissions,
      [route]: Object.keys(defaultPermission).reduce(
        (acc, key) => ({
          ...acc,
          [key]: checked,
        }),
        {} as Permission,
      ),
    };
    setPermissions(newPermissions);
    onChange?.(newPermissions);
  };

  const isAllSelected = (route: string) => {
    const routePermissions = permissions[route];
    if (!routePermissions) return false;
    return Object.values(routePermissions).every(Boolean);
  };

  const isPartialSelected = (route: string) => {
    const routePermissions = permissions[route];
    if (!routePermissions) return false;
    const values = Object.values(routePermissions);
    return values.some(Boolean) && !values.every(Boolean);
  };

  return (
    <div
      style={{
        backgroundColor: '#fafafa',
        padding: '16px',
        borderRadius: '8px',
        border: '1px solid #e8e8e8',
        width: '100%',
        minHeight: '400px',
      }}
    >
      <div
        style={{
          marginBottom: 16,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '12px 16px',
          backgroundColor: '#fff',
          borderRadius: '6px',
          border: '1px solid #e8e8e8',
        }}
      >
        <Title level={5} style={{ margin: 0 }}>
          <SettingOutlined /> 路由权限配置
        </Title>
        {!disabled && (
          <Space>
            <Button
              type="primary"
              size="small"
              icon={<AppstoreAddOutlined />}
              onClick={() => setRouteSelectorVisible(true)}
            >
              从路由列表选择
            </Button>
            <Button size="small" icon={<PlusOutlined />} onClick={() => setModalVisible(true)}>
              手动添加
            </Button>
          </Space>
        )}
      </div>

      <div
        style={{
          maxHeight: 500,
          overflowY: 'auto',
          backgroundColor: '#fff',
          borderRadius: '6px',
          padding: '8px',
          border: '1px solid #e8e8e8',
        }}
      >
        {Object.keys(permissions).length === 0 ? (
          <div
            style={{
              textAlign: 'center',
              padding: '40px 0',
              color: '#999',
              backgroundColor: '#fafafa',
              borderRadius: '4px',
              border: '1px dashed #d9d9d9',
            }}
          >
            暂无权限配置
          </div>
        ) : (
          Object.entries(permissions).map(([route, routePermissions]) => (
            <Card
              key={route}
              size="small"
              style={{
                marginBottom: 8,
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                border: '1px solid #e8e8e8',
                width: '100%',
              }}
              title={
                <div
                  style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                    <Text code style={{ fontSize: 13, color: '#1890ff', fontWeight: 500 }}>
                      {route}
                    </Text>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {availableRoutes.find((r) => r.path === route)?.displayName || route}
                    </Text>
                  </div>
                  <Space size="small">
                    <Checkbox
                      checked={isAllSelected(route)}
                      indeterminate={isPartialSelected(route)}
                      onChange={(e) => handleSelectAll(route, e.target.checked)}
                      disabled={disabled}
                      style={{ fontSize: 12 }}
                    >
                      全选
                    </Checkbox>
                    {!disabled && (
                      <Popconfirm
                        title="确定要删除这个路由权限吗？"
                        onConfirm={() => handleDeleteRoute(route)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          type="text"
                          size="small"
                          danger
                          icon={<DeleteOutlined />}
                          style={{ padding: '2px 4px' }}
                        />
                      </Popconfirm>
                    )}
                  </Space>
                </div>
              }
            >
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
                  gap: '12px',
                  padding: '8px 0',
                }}
              >
                {Object.entries(permissionLabels).map(([key, label]) => (
                  <div
                    key={key}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                      padding: '8px 16px',
                      borderRadius: '8px',
                      backgroundColor: routePermissions[key as keyof Permission]
                        ? '#f6ffed'
                        : '#fafafa',
                      border: `2px solid ${routePermissions[key as keyof Permission] ? '#52c41a' : '#d9d9d9'}`,
                      transition: 'all 0.3s ease',
                      cursor: disabled ? 'not-allowed' : 'pointer',
                      justifyContent: 'center',
                      minHeight: '40px',
                      boxShadow: routePermissions[key as keyof Permission]
                        ? '0 2px 4px rgba(82, 196, 26, 0.2)'
                        : '0 1px 2px rgba(0,0,0,0.1)',
                    }}
                    onClick={() =>
                      !disabled &&
                      handlePermissionChange(
                        route,
                        key as keyof Permission,
                        !routePermissions[key as keyof Permission],
                      )
                    }
                  >
                    <Switch
                      size="small"
                      checked={routePermissions[key as keyof Permission]}
                      onChange={(checked) =>
                        handlePermissionChange(route, key as keyof Permission, checked)
                      }
                      disabled={disabled}
                    />
                    <Text
                      style={{
                        fontSize: 13,
                        fontWeight: 500,
                        color: routePermissions[key as keyof Permission] ? '#52c41a' : '#666',
                      }}
                    >
                      {label}
                    </Text>
                  </div>
                ))}
              </div>
            </Card>
          ))
        )}
      </div>

      <Modal
        title="添加路由权限"
        open={modalVisible}
        onOk={handleAddRoute}
        onCancel={() => {
          setModalVisible(false);
          setNewRoute('');
        }}
        width={400}
      >
        <Form layout="vertical">
          <Form.Item label="路由路径" required help="例如：/users, /products, /orders">
            <Input
              placeholder="请输入路由路径，如 /users"
              value={newRoute}
              onChange={(e) => setNewRoute(e.target.value)}
              onPressEnter={handleAddRoute}
            />
          </Form.Item>
        </Form>
      </Modal>

      <RouteSelector
        visible={routeSelectorVisible}
        availableRoutes={availableRoutes}
        selectedRoutes={Object.keys(permissions)}
        onConfirm={handleRouteSelection}
        onCancel={() => setRouteSelectorVisible(false)}
      />
    </div>
  );
};

export default PermissionEditor;
