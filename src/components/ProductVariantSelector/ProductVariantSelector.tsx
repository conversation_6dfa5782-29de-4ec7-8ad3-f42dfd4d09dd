import React, { useState, useEffect } from 'react';
import { Card, Checkbox, InputNumber, Row, Col, Space, Tag, Empty, Divider } from 'antd';
import type { ProductListItem } from '@/types/product';

export interface ProductVariant {
  colorCode: string;
  colorName: string;
  colorHex?: string;
  sizeCode: string;
  skuCode: string;
  quantity: number;
}

interface ProductVariantSelectorProps {
  product?: ProductListItem | null;
  value?: ProductVariant[];
  onChange?: (variants: ProductVariant[]) => void;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const ProductVariantSelector: React.FC<ProductVariantSelectorProps> = ({
  product,
  value = [],
  onChange,
  disabled = false,
  style,
  className,
}) => {
  const [selectedVariants, setSelectedVariants] = useState<ProductVariant[]>(value);

  // 当外部value变化时更新内部状态
  useEffect(() => {
    setSelectedVariants(value);
  }, [value]);

  // 生成所有可能的颜色尺码组合
  const generateVariants = (): ProductVariant[] => {
    if (!product?.colorSizeCombinations) return [];

    const variants: ProductVariant[] = [];
    
    product.colorSizeCombinations.forEach((colorCombo) => {
      colorCombo.sizes.forEach((size) => {
        const skuCode = `${product.code}-${colorCombo.colorCode}-${size}`;
        variants.push({
          colorCode: colorCombo.colorCode,
          colorName: colorCombo.colorName,
          colorHex: colorCombo.colorHex,
          sizeCode: size,
          skuCode,
          quantity: 0,
        });
      });
    });

    return variants;
  };

  const allVariants = generateVariants();

  // 按颜色分组
  const variantsByColor = allVariants.reduce((acc, variant) => {
    if (!acc[variant.colorCode]) {
      acc[variant.colorCode] = {
        colorName: variant.colorName,
        colorHex: variant.colorHex,
        variants: [],
      };
    }
    acc[variant.colorCode].variants.push(variant);
    return acc;
  }, {} as Record<string, { colorName: string; colorHex?: string; variants: ProductVariant[] }>);

  // 检查某个变体是否被选中
  const isVariantSelected = (variant: ProductVariant): boolean => {
    return selectedVariants.some(
      (selected) =>
        selected.colorCode === variant.colorCode &&
        selected.sizeCode === variant.sizeCode
    );
  };

  // 获取某个变体的数量
  const getVariantQuantity = (variant: ProductVariant): number => {
    const selected = selectedVariants.find(
      (selected) =>
        selected.colorCode === variant.colorCode &&
        selected.sizeCode === variant.sizeCode
    );
    return selected?.quantity || 0;
  };

  // 处理变体选择/取消选择
  const handleVariantToggle = (variant: ProductVariant, checked: boolean) => {
    let newSelectedVariants: ProductVariant[];

    if (checked) {
      // 添加变体，默认数量为1
      newSelectedVariants = [
        ...selectedVariants.filter(
          (selected) =>
            !(selected.colorCode === variant.colorCode && selected.sizeCode === variant.sizeCode)
        ),
        { ...variant, quantity: 1 },
      ];
    } else {
      // 移除变体
      newSelectedVariants = selectedVariants.filter(
        (selected) =>
          !(selected.colorCode === variant.colorCode && selected.sizeCode === variant.sizeCode)
      );
    }

    setSelectedVariants(newSelectedVariants);
    onChange?.(newSelectedVariants);
  };

  // 处理数量变化
  const handleQuantityChange = (variant: ProductVariant, quantity: number) => {
    const newSelectedVariants = selectedVariants.map((selected) => {
      if (selected.colorCode === variant.colorCode && selected.sizeCode === variant.sizeCode) {
        return { ...selected, quantity: quantity || 0 };
      }
      return selected;
    });

    setSelectedVariants(newSelectedVariants);
    onChange?.(newSelectedVariants);
  };

  // 处理颜色全选/取消全选
  const handleColorToggleAll = (colorCode: string, checked: boolean) => {
    const colorVariants = variantsByColor[colorCode]?.variants || [];
    
    if (checked) {
      // 全选该颜色的所有尺码
      const newVariants = colorVariants.map(variant => ({ ...variant, quantity: 1 }));
      const otherVariants = selectedVariants.filter(
        selected => selected.colorCode !== colorCode
      );
      const newSelectedVariants = [...otherVariants, ...newVariants];
      setSelectedVariants(newSelectedVariants);
      onChange?.(newSelectedVariants);
    } else {
      // 取消选择该颜色的所有尺码
      const newSelectedVariants = selectedVariants.filter(
        selected => selected.colorCode !== colorCode
      );
      setSelectedVariants(newSelectedVariants);
      onChange?.(newSelectedVariants);
    }
  };

  // 检查某个颜色是否全选
  const isColorFullySelected = (colorCode: string): boolean => {
    const colorVariants = variantsByColor[colorCode]?.variants || [];
    return colorVariants.length > 0 && colorVariants.every(variant => isVariantSelected(variant));
  };

  // 检查某个颜色是否部分选择
  const isColorPartiallySelected = (colorCode: string): boolean => {
    const colorVariants = variantsByColor[colorCode]?.variants || [];
    const selectedCount = colorVariants.filter(variant => isVariantSelected(variant)).length;
    return selectedCount > 0 && selectedCount < colorVariants.length;
  };

  if (!product) {
    return (
      <div style={style} className={className}>
        <Empty description="请先选择商品" />
      </div>
    );
  }

  if (allVariants.length === 0) {
    return (
      <div style={style} className={className}>
        <Empty description="该商品暂无颜色尺码配置" />
      </div>
    );
  }

  return (
    <div style={style} className={className}>
      <Card size="small" title="选择颜色尺码组合">
        {Object.entries(variantsByColor).map(([colorCode, colorInfo]) => (
          <div key={colorCode} style={{ marginBottom: 16 }}>
            <div style={{ marginBottom: 8 }}>
              <Checkbox
                checked={isColorFullySelected(colorCode)}
                indeterminate={isColorPartiallySelected(colorCode)}
                onChange={(e) => handleColorToggleAll(colorCode, e.target.checked)}
                disabled={disabled}
              >
                <Space>
                  <span
                    style={{
                      display: 'inline-block',
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      backgroundColor: colorInfo.colorHex || '#ccc',
                      border: '1px solid #d9d9d9',
                    }}
                  />
                  <strong>{colorInfo.colorName}</strong>
                  <Tag color="blue">{colorCode}</Tag>
                </Space>
              </Checkbox>
            </div>
            
            <Row gutter={[8, 8]} style={{ marginLeft: 24 }}>
              {colorInfo.variants.map((variant) => (
                <Col key={`${variant.colorCode}-${variant.sizeCode}`} span={6}>
                  <Card size="small" style={{ textAlign: 'center' }}>
                    <Checkbox
                      checked={isVariantSelected(variant)}
                      onChange={(e) => handleVariantToggle(variant, e.target.checked)}
                      disabled={disabled}
                      style={{ marginBottom: 8 }}
                    >
                      <Tag>{variant.sizeCode}</Tag>
                    </Checkbox>
                    {isVariantSelected(variant) && (
                      <InputNumber
                        size="small"
                        min={1}
                        value={getVariantQuantity(variant)}
                        onChange={(value) => handleQuantityChange(variant, value || 0)}
                        disabled={disabled}
                        style={{ width: '100%' }}
                        placeholder="数量"
                      />
                    )}
                  </Card>
                </Col>
              ))}
            </Row>
            
            {Object.keys(variantsByColor).indexOf(colorCode) < Object.keys(variantsByColor).length - 1 && (
              <Divider style={{ margin: '16px 0' }} />
            )}
          </div>
        ))}
        
        {selectedVariants.length > 0 && (
          <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6ffed', borderRadius: 6 }}>
            <div style={{ marginBottom: 8, fontWeight: 'bold', color: '#52c41a' }}>
              已选择 {selectedVariants.length} 个变体，总数量：{selectedVariants.reduce((sum, v) => sum + v.quantity, 0)}
            </div>
            <Space wrap>
              {selectedVariants.map((variant) => (
                <Tag
                  key={`${variant.colorCode}-${variant.sizeCode}`}
                  color="green"
                >
                  {variant.colorName} - {variant.sizeCode} × {variant.quantity}
                </Tag>
              ))}
            </Space>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ProductVariantSelector;
