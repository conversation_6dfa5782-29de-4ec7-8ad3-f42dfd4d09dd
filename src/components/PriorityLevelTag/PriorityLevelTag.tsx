import React from 'react';
import { Tag } from 'antd';
import { PriorityLevel, PRIORITY_LEVEL_COLORS, PRIORITY_LEVEL_TEXT } from '@/types/salesDemand';

interface PriorityLevelTagProps {
  level: PriorityLevel;
  style?: React.CSSProperties;
  className?: string;
}

const PriorityLevelTag: React.FC<PriorityLevelTagProps> = ({ level, style, className }) => {
  return (
    <Tag color={PRIORITY_LEVEL_COLORS[level]} style={style} className={className}>
      {PRIORITY_LEVEL_TEXT[level]}
    </Tag>
  );
};

export default PriorityLevelTag;
