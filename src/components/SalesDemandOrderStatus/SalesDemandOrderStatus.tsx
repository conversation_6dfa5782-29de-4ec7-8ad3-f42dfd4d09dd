import React from 'react';
import { Tag } from 'antd';
import {
  SalesDemandOrderStatus,
  SALES_DEMAND_ORDER_STATUS_COLORS,
  SALES_DEMAND_ORDER_STATUS_TEXT,
} from '@/types/salesDemand';

interface SalesDemandOrderStatusProps {
  status: SalesDemandOrderStatus;
  style?: React.CSSProperties;
  className?: string;
}

const SalesDemandOrderStatusComponent: React.FC<SalesDemandOrderStatusProps> = ({
  status,
  style,
  className,
}) => {
  return (
    <Tag
      color={SALES_DEMAND_ORDER_STATUS_COLORS[status]}
      style={style}
      className={className}
    >
      {SALES_DEMAND_ORDER_STATUS_TEXT[status]}
    </Tag>
  );
};

export default SalesDemandOrderStatusComponent;
