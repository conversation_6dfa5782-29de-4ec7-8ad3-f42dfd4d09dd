import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag, InputNumber, Button, Space } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { debounce } from 'lodash-es';
import { getAccessoryList } from '@/api/AccessoryApi';
import type { AccessoryListProps } from '@/types/accessories';
import type { AccessoryQuantity } from '@/types/product';
import { logError } from '@/utils/errorHandler';

interface AccessoryQuantitySelectorProps {
  value?: AccessoryQuantity[];
  onChange?: (value: AccessoryQuantity[]) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const AccessoryQuantitySelector: React.FC<AccessoryQuantitySelectorProps> = ({
  value = [],
  onChange,
  placeholder = '请选择辅料',
  disabled = false,
  style,
  className,
}) => {
  const [accessories, setAccessories] = useState<AccessoryListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  // 获取辅料列表
  const fetchAccessories = async (search = '') => {
    setLoading(true);
    try {
      const response = await getAccessoryList({
        page: 0,
        pageSize: 0,
        nameSearch: search || undefined,
      });

      if (response.code === 200 && response.data) {
        setAccessories(response.data.accessories || []);
      }
    } catch (error) {
      logError('获取辅料列表', error);
      setAccessories([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedFetchAccessories = useMemo(
    () => debounce((search: string) => fetchAccessories(search), 300),
    [],
  );

  // 初始化数据
  useEffect(() => {
    fetchAccessories();
  }, []);

  // 搜索处理
  const handleSearch = (searchValue: string) => {
    setSearchText(searchValue);
    debouncedFetchAccessories(searchValue);
  };

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedFetchAccessories.cancel();
    };
  }, [debouncedFetchAccessories]);

  // 添加辅料
  const handleAddAccessory = (accessoryId: string) => {
    const existingIndex = value.findIndex((item) => item.accessoryId === accessoryId);
    if (existingIndex >= 0) {
      // 如果已存在，增加数量
      const newValue = [...value];
      newValue[existingIndex] = {
        ...newValue[existingIndex],
        quantity: newValue[existingIndex].quantity + 1,
      };
      onChange?.(newValue);
    } else {
      // 如果不存在，添加新项
      const newValue = [...value, { accessoryId, quantity: 1 }];
      onChange?.(newValue);
    }
    setSearchText('');
  };

  // 更新数量
  const handleQuantityChange = (accessoryId: string, quantity: number) => {
    if (quantity <= 0) {
      handleRemoveAccessory(accessoryId);
      return;
    }

    const newValue = value.map((item) =>
      item.accessoryId === accessoryId ? { ...item, quantity } : item,
    );
    onChange?.(newValue);
  };

  // 移除辅料
  const handleRemoveAccessory = (accessoryId: string) => {
    const newValue = value.filter((item) => item.accessoryId !== accessoryId);
    onChange?.(newValue);
  };

  // 生成选项（排除已选择的）
  const availableAccessories = accessories.filter(
    (accessory) => !value.some((item) => item.accessoryId === accessory.id),
  );

  const options = availableAccessories.map((accessory) => ({
    value: accessory.id,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ display: 'flex' }}>
          <div style={{ fontWeight: 500 }}>{accessory.name}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
            {accessory.articleNumber} | ¥{accessory.costPrice.toFixed(2)}
            {accessory.supplierName && (
              <span style={{ marginLeft: '8px', color: '#666' }}>
                供应商: {accessory.supplierName}
              </span>
            )}
          </div>
        </div>
      </div>
    ),
  }));

  // 获取辅料信息
  const getAccessoryInfo = (accessoryId: string) => {
    return accessories.find((acc) => acc.id === accessoryId);
  };

  return (
    <div style={style} className={className}>
      {/* 添加辅料选择器 */}
      <Select
        showSearch
        value={undefined}
        placeholder={placeholder}
        defaultActiveFirstOption={false}
        suffixIcon={loading ? <Spin size="small" /> : undefined}
        filterOption={false}
        onSearch={handleSearch}
        onChange={handleAddAccessory}
        notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
        options={options}
        disabled={disabled}
        style={{ width: '100%', marginBottom: value.length > 0 ? 16 : 0 }}
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      />

      {/* 已选择的辅料列表 */}
      {value.length > 0 && (
        <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 12 }}>
          <div style={{ marginBottom: 8, fontWeight: 500, color: '#666' }}>
            已选择辅料 ({value.length})
          </div>
          <Space direction="vertical" style={{ width: '100%' }}>
            {value.map((item) => {
              const accessoryInfo = getAccessoryInfo(item.accessoryId);
              return (
                <div
                  key={item.accessoryId}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '8px 12px',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                  }}
                >
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 500 }}>{accessoryInfo?.name || '未知辅料'}</div>
                    <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                      {accessoryInfo?.articleNumber} | ¥
                      {accessoryInfo?.costPrice.toFixed(2) || '0.00'}
                    </div>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <span style={{ fontSize: '12px', color: '#666' }}>数量:</span>
                    <InputNumber
                      size="small"
                      min={1}
                      value={item.quantity}
                      onChange={(quantity) => handleQuantityChange(item.accessoryId, quantity || 1)}
                      style={{ width: 80 }}
                      disabled={disabled}
                    />
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => handleRemoveAccessory(item.accessoryId)}
                      disabled={disabled}
                      danger
                    />
                  </div>
                </div>
              );
            })}
          </Space>
        </div>
      )}
    </div>
  );
};

export default AccessoryQuantitySelector;
