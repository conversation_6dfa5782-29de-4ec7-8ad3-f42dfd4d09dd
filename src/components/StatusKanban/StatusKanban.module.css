.kanban {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding: 16px 0;
  min-height: 400px;
}

.column {
  flex: 1;
  min-width: 280px;
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.columnHeader {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.columnTitle {
  font-weight: 500;
  font-size: 14px;
  color: #262626;
}

.columnContent {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 500px;
  overflow-y: auto;
}

.kanbanCard {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.kanbanCard:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.cardTitle {
  font-weight: 500;
  font-size: 13px;
  color: #262626;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.priorityDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.cardSubtitle {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.amount {
  font-size: 12px;
  font-weight: 500;
  color: #f5222d;
}

.date {
  font-size: 11px;
  color: #8c8c8c;
}

.emptyColumn {
  text-align: center;
  color: #8c8c8c;
  font-size: 12px;
  padding: 32px 16px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .kanban {
    flex-direction: column;
    gap: 12px;
  }
  
  .column {
    min-width: auto;
  }
  
  .columnContent {
    max-height: 300px;
  }
}
