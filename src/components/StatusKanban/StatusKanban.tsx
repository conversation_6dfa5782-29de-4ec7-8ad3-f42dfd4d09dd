import React from 'react';
import { Card, Badge, Tag, Space, Button } from 'antd';
import { EyeOutlined, EditOutlined } from '@ant-design/icons';
import styles from './StatusKanban.module.css';

interface StatusColumn {
  key: string;
  title: string;
  color: string;
  count: number;
}

interface StatusItem {
  id: string;
  title: string;
  subtitle?: string;
  amount?: number;
  date?: string;
  status: string;
  priority?: 'high' | 'normal' | 'low';
}

interface StatusKanbanProps {
  columns: StatusColumn[];
  items: StatusItem[];
  onItemClick?: (item: StatusItem) => void;
  onItemEdit?: (item: StatusItem) => void;
  loading?: boolean;
}

const StatusKanban: React.FC<StatusKanbanProps> = ({
  columns,
  items,
  onItemClick,
  onItemEdit,
  loading = false,
}) => {
  const getItemsByStatus = (status: string) => {
    return items.filter(item => item.status === status);
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high':
        return '#ff4d4f';
      case 'normal':
        return '#1890ff';
      case 'low':
        return '#52c41a';
      default:
        return '#d9d9d9';
    }
  };

  return (
    <div className={styles.kanban}>
      {columns.map(column => {
        const columnItems = getItemsByStatus(column.key);
        
        return (
          <div key={column.key} className={styles.column}>
            <div className={styles.columnHeader}>
              <Badge count={column.count} color={column.color}>
                <span className={styles.columnTitle}>{column.title}</span>
              </Badge>
            </div>
            
            <div className={styles.columnContent}>
              {columnItems.map(item => (
                <Card
                  key={item.id}
                  size="small"
                  className={styles.kanbanCard}
                  hoverable
                  onClick={() => onItemClick?.(item)}
                >
                  <div className={styles.cardHeader}>
                    <span className={styles.cardTitle}>{item.title}</span>
                    {item.priority && (
                      <div 
                        className={styles.priorityDot}
                        style={{ backgroundColor: getPriorityColor(item.priority) }}
                      />
                    )}
                  </div>
                  
                  {item.subtitle && (
                    <div className={styles.cardSubtitle}>{item.subtitle}</div>
                  )}
                  
                  <div className={styles.cardFooter}>
                    <div className={styles.cardInfo}>
                      {item.amount && (
                        <span className={styles.amount}>¥{item.amount.toFixed(2)}</span>
                      )}
                      {item.date && (
                        <span className={styles.date}>{item.date}</span>
                      )}
                    </div>
                    
                    <Space size="small">
                      <Button
                        type="text"
                        size="small"
                        icon={<EyeOutlined />}
                        onClick={(e) => {
                          e.stopPropagation();
                          onItemClick?.(item);
                        }}
                      />
                      {onItemEdit && (
                        <Button
                          type="text"
                          size="small"
                          icon={<EditOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            onItemEdit(item);
                          }}
                        />
                      )}
                    </Space>
                  </div>
                </Card>
              ))}
              
              {columnItems.length === 0 && (
                <div className={styles.emptyColumn}>
                  暂无数据
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default StatusKanban;
