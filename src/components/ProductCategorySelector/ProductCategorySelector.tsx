import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { debounce } from 'lodash-es';
import { getProductCategoryList } from '@/api/ProductCategoryApi';
import type { ProductCategoryListProps } from '@/types/productCategory';
import { logError } from '@/utils/errorHandler';

interface ProductCategorySelectorProps {
  value?: string;
  onChange?: (value: string, category: ProductCategoryListProps | null) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  showAll?: boolean; // 是否显示所有分类（不进行搜索过滤）
}

const ProductCategorySelector: React.FC<ProductCategorySelectorProps> = ({
  value,
  onChange,
  placeholder = '请选择商品分类',
  disabled = false,
  allowClear = true,
  style,
  className,
  showAll = false,
}) => {
  const [categories, setCategories] = useState<ProductCategoryListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        setLoading(true);
        try {
          const response = await getProductCategoryList({
            page: 0,
            pageSize: 0,
            search: showAll ? undefined : searchText.trim() || undefined,
          });

          if (response.code === 200 && response.data?.categories) {
            setCategories(response.data.categories);
          } else {
            setCategories([]);
          }
        } catch (error: any) {
          logError('搜索商品分类', error);
          setCategories([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [showAll],
  );

  // 初始化加载所有分类（当showAll为true时）
  useEffect(() => {
    if (showAll) {
      debouncedSearch('');
    }
  }, [showAll, debouncedSearch]);

  // 当搜索值变化时触发搜索
  useEffect(() => {
    if (!showAll) {
      debouncedSearch(searchValue);
    }
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch, showAll]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    if (!showAll) {
      setSearchValue(searchText);
    }
  };

  // 处理选择
  const handleChange = (selectedValue: string) => {
    const selectedCategory = categories.find((category) => category.code === selectedValue) || null;
    onChange?.(selectedValue, selectedCategory);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    if (!showAll) {
      setCategories([]);
    }
    onChange?.('', null);
  };

  // 生成选项
  const options = categories.map((category) => ({
    value: category.code,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ display: 'flex' }}>
          <div style={{ fontWeight: 500 }}>{category.name}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c', display: 'flex', gap: '8px' }}>
            <span>{category.code}</span>
            {category.sizes && category.sizes.length > 0 && (
              <span>尺寸: {category.sizes.join(', ')}</span>
            )}
          </div>
        </div>
      </div>
    ),
  }));

  return (
    <Select
      showSearch={!showAll}
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={showAll ? true : false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={{ minHeight: '40px', ...style }}
      className={className}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
    />
  );
};

export default ProductCategorySelector;
