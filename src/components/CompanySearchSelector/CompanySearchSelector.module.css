.companySearchSelector {
  width: 100%;
}

.optionContent {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.optionIcon {
  color: #1890ff;
  font-size: 16px;
  flex-shrink: 0;
}

.optionInfo {
  flex: 1;
  min-width: 0;
}

.optionName {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.optionDetails {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.optionDetails span {
  margin-left: 4px;
}

/* 下拉框样式 */
.companySearchSelector :global(.ant-select-dropdown) {
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.companySearchSelector :global(.ant-select-item-option) {
  padding: 8px 12px;
}

.companySearchSelector :global(.ant-select-item-option:hover) {
  background-color: #f5f5f5;
}

.companySearchSelector :global(.ant-select-item-option-selected) {
  background-color: #e6f7ff;
}

/* 加载状态 */
.companySearchSelector :global(.ant-spin) {
  color: #1890ff;
}

/* 空状态 */
.companySearchSelector :global(.ant-empty) {
  padding: 16px;
}

.companySearchSelector :global(.ant-empty-description) {
  color: #8c8c8c;
  font-size: 12px;
}
