import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { debounce } from 'lodash-es';
import { getCompanyList } from '@/api/CompanyApi';
import type { CompanyProps } from '@/types/company';
import { logError } from '@/utils/errorHandler';

interface CompanySearchSelectorProps {
  value?: string;
  onChange?: (value: string, company: CompanyProps | null) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const CompanySearchSelector: React.FC<CompanySearchSelectorProps> = ({
  value,
  onChange,
  placeholder = '请搜索公司',
  disabled = false,
  allowClear = true,
  style,
  className,
}) => {
  const [companies, setCompanies] = useState<CompanyProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setCompanies([]);
          return;
        }

        setLoading(true);
        try {
          const response = await getCompanyList({
            page: 0,
            pageSize: 0,
            search: searchText,
          });

          if (response.code === 200) {
            setCompanies(response.data.companies || []);
          } else {
            setCompanies([]);
          }
        } catch (error: any) {
          logError('搜索公司', error);
          setCompanies([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [],
  );

  // 当搜索值变化时执行搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 当value变化时，如果有值但没有对应的公司信息，则加载
  useEffect(() => {
    if (value && !companies.find((company) => company.code === value)) {
      // 如果有值但列表中没有对应公司，触发搜索
      setSearchValue(value);
    }
  }, [value, companies]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 处理选择
  const handleChange = (selectedValue: string) => {
    const selectedCompany = companies.find((company) => company.code === selectedValue) || null;
    onChange?.(selectedValue, selectedCompany);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setCompanies([]);
    onChange?.('', null);
  };

  // 生成选项
  const options = companies.map((company) => ({
    value: company.code,
    label: company.name, // 简化为只显示公司名称
    children: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div>
          <div style={{ fontWeight: 500 }}>{company.name}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
            {company.code}
            {company.managerName && (
              <span style={{ marginLeft: '4px' }}>• 负责人: {company.managerName}</span>
            )}
            {company.address && <span style={{ marginLeft: '4px' }}>• {company.address}</span>}
          </div>
        </div>
      </div>
    ),
  }));

  return (
    <Select
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      optionLabelProp="label"
    />
  );
};

export default CompanySearchSelector;
