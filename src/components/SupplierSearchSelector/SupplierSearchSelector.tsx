import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { debounce } from 'lodash-es';
import { getSupplierList, getSupplierDetail } from '@/api/SupplierApi';
import type { SupplierListProps } from '@/types/supplier';
import { logError } from '@/utils/errorHandler';

interface SupplierSearchSelectorProps {
  value?: string;
  onChange?: (value: string, supplier: SupplierListProps | null) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const SupplierSearchSelector: React.FC<SupplierSearchSelectorProps> = ({
  value,
  onChange,
  placeholder = '请搜索供应商',
  disabled = false,
  allowClear = true,
  style,
  className,
}) => {
  const [suppliers, setSuppliers] = useState<SupplierListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [currentSupplier, setCurrentSupplier] = useState<SupplierListProps | null>(null);

  // 加载当前供应商详情
  const loadCurrentSupplier = async (supplierCode: string) => {
    if (!supplierCode) {
      setCurrentSupplier(null);
      return;
    }

    try {
      const response = await getSupplierDetail(supplierCode);
      if (response.code === 200 && response.data) {
        setCurrentSupplier(response.data);
      } else {
        setCurrentSupplier(null);
      }
    } catch (error: any) {
      logError('获取供应商详情', error);
      setCurrentSupplier(null);
    }
  };

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setSuppliers([]);
          return;
        }

        setLoading(true);
        try {
          const response = await getSupplierList({
            page: 0,
            pageSize: 0,
            search: searchText.trim(),
          });

          if (response.code === 200 && response.data?.suppliers) {
            setSuppliers(response.data.suppliers);
          } else {
            setSuppliers([]);
          }
        } catch (error: any) {
          logError('搜索供应商', error);
          setSuppliers([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [],
  );

  // 当value变化时加载当前供应商详情
  useEffect(() => {
    if (value) {
      loadCurrentSupplier(value);
    } else {
      setCurrentSupplier(null);
    }
  }, [value]);

  // 当搜索值变化时触发搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 处理选择
  const handleChange = (selectedValue: string) => {
    const selectedSupplier =
      allSuppliers.find((supplier) => supplier.code === selectedValue) || null;
    onChange?.(selectedValue, selectedSupplier);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setSuppliers([]);
    onChange?.('', null);
  };

  // 生成选项
  const allSuppliers = useMemo(() => {
    const supplierMap = new Map<string, SupplierListProps>();

    // 添加搜索结果中的供应商
    suppliers.forEach((supplier) => {
      supplierMap.set(supplier.code, supplier);
    });

    // 确保当前选中的供应商也在选项中
    if (currentSupplier && !supplierMap.has(currentSupplier.code)) {
      supplierMap.set(currentSupplier.code, currentSupplier);
    }

    return Array.from(supplierMap.values());
  }, [suppliers, currentSupplier]);

  const options = allSuppliers.map((supplier) => ({
    value: supplier.code,
    label: supplier.name, // 简化为只显示供应商名称
    children: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ fontWeight: 500 }}>{supplier.name}</div>
        <div style={{ color: '#147ffa' }}>
          {supplier.code}
          {supplier.contactName && <span>• {supplier.contactName}</span>}
          {supplier.contactPhone && <span>• {supplier.contactPhone}</span>}
        </div>
      </div>
    ),
  }));

  return (
    <Select
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={{ minHeight: '40px', ...style }}
      className={className}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      optionLabelProp="label"
    />
  );
};

export default SupplierSearchSelector;
