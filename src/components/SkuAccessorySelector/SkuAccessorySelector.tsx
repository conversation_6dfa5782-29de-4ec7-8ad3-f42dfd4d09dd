import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, InputNumber, Button, Space, Card, Row, Col, Typography } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { debounce } from 'lodash-es';
import { getAccessoryList } from '@/api/AccessoryApi';
import type { AccessoryListProps } from '@/types/accessories';
import type { SkuAccessory } from '@/types/skus';
import { logError } from '@/utils/errorHandler';

const { Text } = Typography;

interface SkuAccessorySelectorProps {
  value?: SkuAccessory[];
  onChange?: (value: SkuAccessory[]) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const SkuAccessorySelector: React.FC<SkuAccessorySelectorProps> = ({
  value = [],
  onChange,
  placeholder = '请选择辅料',
  disabled = false,
  style,
  className,
}) => {
  const [accessories, setAccessories] = useState<AccessoryListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  // 获取辅料列表
  const fetchAccessories = async (search = '') => {
    setLoading(true);
    try {
      const response = await getAccessoryList({
        page: 0,
        pageSize: 0,
        nameSearch: search || undefined,
      });

      if (response.code === 200 && response.data) {
        setAccessories(response.data.accessories || []);
      }
    } catch (error) {
      logError('获取辅料列表', error);
      setAccessories([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedSearch = useMemo(
    () =>
      debounce((searchValue: string) => {
        fetchAccessories(searchValue);
      }, 300),
    [],
  );

  // 初始化加载
  useEffect(() => {
    fetchAccessories();
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // 处理搜索
  const handleSearch = (searchValue: string) => {
    setSearchText(searchValue);
    debouncedSearch(searchValue);
  };

  // 添加辅料
  const handleAdd = () => {
    const newAccessory: SkuAccessory = {
      accessoryCode: '',
      quantity: 1,
    };
    onChange?.([...value, newAccessory]);
  };

  // 删除辅料
  const handleRemove = (index: number) => {
    const newValue = value.filter((_, i) => i !== index);
    onChange?.(newValue);
  };

  // 更新辅料编码
  const handleAccessoryCodeChange = (index: number, accessoryCode: string) => {
    const newValue = [...value];
    newValue[index] = { ...newValue[index], accessoryCode };
    onChange?.(newValue);
  };

  // 更新数量
  const handleQuantityChange = (index: number, quantity: number | null) => {
    if (quantity === null || quantity < 0) return;
    const newValue = [...value];
    newValue[index] = { ...newValue[index], quantity };
    onChange?.(newValue);
  };

  // 获取可选的辅料选项（排除已选择的）
  const getAvailableOptions = (currentIndex: number) => {
    const selectedCodes = value
      .map((item, index) => (index !== currentIndex ? item.accessoryCode : null))
      .filter(Boolean);

    const filteredAccessories = accessories.filter(
      (accessory) =>
        accessory && accessory.articleNumber && !selectedCodes.includes(accessory.articleNumber),
    );

    return filteredAccessories.map((accessory) => ({
      value: accessory.articleNumber,
      label: `${accessory.name || '未知辅料'} (${accessory.articleNumber})`,
      searchValue: `${accessory.name || ''} ${accessory.articleNumber || ''} ${accessory.brandName || ''}`,
    }));
  };

  return (
    <div style={style} className={className}>
      <Space direction="vertical" style={{ width: '100%' }}>
        {value.map((item, index) => (
          <Card key={index} size="small" style={{ marginBottom: 8 }}>
            <Row gutter={16} align="middle">
              <Col flex="1">
                <Text strong>辅料选择</Text>
                <Select
                  showSearch
                  value={item.accessoryCode || undefined}
                  placeholder={placeholder}
                  style={{ width: '100%', marginTop: 4 }}
                  options={getAvailableOptions(index)}
                  onChange={(accessoryCode) => handleAccessoryCodeChange(index, accessoryCode)}
                  onSearch={handleSearch}
                  filterOption={false}
                  notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
                  disabled={disabled}
                  allowClear
                />
              </Col>
              <Col>
                <Text strong>数量</Text>
                <InputNumber
                  value={item.quantity}
                  onChange={(quantity) => handleQuantityChange(index, quantity)}
                  min={1}
                  style={{ width: 80, marginTop: 4 }}
                  disabled={disabled}
                />
              </Col>
              <Col>
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleRemove(index)}
                  disabled={disabled}
                  style={{ marginTop: 20 }}
                />
              </Col>
            </Row>
          </Card>
        ))}

        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          disabled={disabled}
          style={{ width: '100%' }}
        >
          添加辅料
        </Button>
      </Space>
    </div>
  );
};

export default SkuAccessorySelector;
