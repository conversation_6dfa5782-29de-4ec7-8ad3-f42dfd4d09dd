import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag } from 'antd';
import { debounce } from 'lodash-es';
import { getColorList } from '@/api/ColorApi';
import type { ColorListProps } from '@/types/color';
import { logError } from '@/utils/errorHandler';

interface ColorMultipleSelectorProps {
  value?: string[];
  onChange?: (value: string[], colors: ColorListProps[]) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  maxTagCount?: number;
  showAll?: boolean; // 是否显示所有颜色（不进行搜索过滤）
}

const ColorMultipleSelector: React.FC<ColorMultipleSelectorProps> = ({
  value = [],
  onChange,
  placeholder = '请搜索并选择颜色',
  disabled = false,
  allowClear = true,
  style,
  className,
  maxTagCount = 3,
  showAll = false,
}) => {
  const [colors, setColors] = useState<ColorListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [allColors, setAllColors] = useState<ColorListProps[]>([]);

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        setLoading(true);
        try {
          const response = await getColorList({
            page: 0,
            pageSize: 0,
            search: showAll ? undefined : searchText.trim() || undefined,
          });

          if (response.code === 200 && response.data?.colors) {
            setColors(response.data.colors);

            // 更新所有颜色列表，避免重复
            setAllColors((prev) => {
              const existingCodes = new Set(prev.map((c) => c.code));
              const uniqueNewColors = response.data.colors.filter(
                (c) => !existingCodes.has(c.code),
              );
              return [...prev, ...uniqueNewColors];
            });
          } else {
            setColors([]);
          }
        } catch (error) {
          logError('搜索颜色', error);
          setColors([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [showAll],
  );

  // 初始化加载所有颜色（当showAll为true时）
  useEffect(() => {
    if (showAll) {
      debouncedSearch('');
    }
  }, [showAll, debouncedSearch]);

  // 当搜索值变化时触发搜索
  useEffect(() => {
    if (!showAll) {
      debouncedSearch(searchValue);
    }
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch, showAll]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    if (!showAll) {
      setSearchValue(searchText);
    }
  };

  // 处理选择
  const handleChange = (selectedValues: string[]) => {
    const selectedColors = selectedValues
      .map((code) => allColors.find((color) => color.code === code))
      .filter(Boolean) as ColorListProps[];

    onChange?.(selectedValues, selectedColors);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    if (!showAll) {
      setColors([]);
    }
    onChange?.([], []);
  };

  // 生成选项
  const options = colors.map((color) => ({
    value: color.code,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', padding: '4px 0' }}>
        <div style={{ flex: 1 }}>
          <div
            style={{
              fontWeight: 500,
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              fontSize: '14px',
            }}
          >
            <span>{color.name}</span>
            <Tag color="blue" style={{ fontSize: '12px' }}>
              {color.code}
            </Tag>
          </div>
        </div>
      </div>
    ),
  }));

  // 自定义标签渲染
  const tagRender = (props: any) => {
    const { value, closable, onClose } = props;
    const color = allColors.find((item) => item.code === value);

    return (
      <Tag
        color="blue"
        closable={closable}
        onClose={onClose}
        style={{
          marginRight: 4,
          marginBottom: 4,
          padding: '4px 8px',
          fontSize: '13px',
          lineHeight: '20px',
          borderRadius: '4px',
        }}
      >
        {color?.name || value} ({value})
      </Tag>
    );
  };

  return (
    <Select
      mode="multiple"
      showSearch={!showAll}
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={showAll ? true : false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={{
        minHeight: '48px',
        height: '48px',
        fontSize: '14px',
        width: '100%',
        ...style,
      }}
      className={className}
      dropdownStyle={{
        maxHeight: 400,
        overflow: 'auto',
        fontSize: '14px',
      }}
      maxTagCount={maxTagCount}
      maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
      tagRender={tagRender}
    />
  );
};

export default ColorMultipleSelector;
