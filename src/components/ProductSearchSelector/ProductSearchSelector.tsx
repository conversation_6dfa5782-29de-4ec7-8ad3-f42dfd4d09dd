import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { debounce } from 'lodash-es';
import { getProductList, getProductDetail } from '@/api/ProductApi';
import type { ProductListItem, ProductListParams } from '@/types/product';
import { logError } from '@/utils/errorHandler';

interface ProductSearchSelectorProps {
  value?: string;
  onChange?: (value: string, product: ProductListItem | null) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  brandCode?: string;
  supplierCode?: string;
  categoryCode?: string;
}

const ProductSearchSelector: React.FC<ProductSearchSelectorProps> = ({
  value,
  onChange,
  placeholder = '请输入商品编码或名称搜索',
  disabled = false,
  allowClear = true,
  style,
  className,
  brandCode,
  supplierCode,
  categoryCode,
}) => {
  const [products, setProducts] = useState<ProductListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [currentProduct, setCurrentProduct] = useState<ProductListItem | null>(null);

  // 加载当前商品详情
  const loadCurrentProduct = async (productId: string) => {
    if (!productId) {
      setCurrentProduct(null);
      return;
    }

    try {
      const response = await getProductDetail(productId);
      if (response.code === 200 && response.data) {
        setCurrentProduct(response.data);
      } else {
        setCurrentProduct(null);
      }
    } catch (error: any) {
      logError('获取商品详情', error);
      setCurrentProduct(null);
    }
  };

  // 判断搜索文本是否为商品编码（纯字母数字组合）
  const isProductCode = (text: string): boolean => {
    return /^[A-Za-z0-9]+$/.test(text.trim());
  };

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setProducts([]);
          return;
        }

        setLoading(true);
        try {
          const trimmedText = searchText.trim();
          const params: ProductListParams = {
            page: 1,
            pageSize: 50,
            brandCode,
            supplierCode,
            categoryCode,
          };

          // 根据输入内容判断是搜索编码还是名称
          if (isProductCode(trimmedText)) {
            params.codeSearch = trimmedText;
          } else {
            params.search = trimmedText;
          }

          const response = await getProductList(params);

          if (response.code === 200 && response.data?.products) {
            setProducts(response.data.products);
          } else {
            setProducts([]);
          }
        } catch (error: any) {
          logError('搜索商品', error);
          setProducts([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [brandCode, supplierCode, categoryCode],
  );

  // 当value变化时加载当前商品详情
  useEffect(() => {
    if (value) {
      loadCurrentProduct(value);
    } else {
      setCurrentProduct(null);
    }
  }, [value]);

  // 当搜索值变化时触发搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 处理选择
  const handleChange = (selectedValue: string) => {
    const selectedProduct = allProducts.find((product) => product.id === selectedValue) || null;
    onChange?.(selectedValue, selectedProduct);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setProducts([]);
    onChange?.('', null);
  };

  // 生成选项
  const allProducts = useMemo(() => {
    const productMap = new Map<string, ProductListItem>();

    // 添加搜索结果中的商品
    products.forEach((product) => {
      productMap.set(product.id, product);
    });

    // 确保当前选中的商品也在选项中
    if (currentProduct && !productMap.has(currentProduct.id)) {
      productMap.set(currentProduct.id, currentProduct);
    }

    return Array.from(productMap.values());
  }, [products, currentProduct]);

  const options = allProducts.map((product) => ({
    value: product.id,
    label: (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span
            style={{
              fontWeight: 600,
              color: '#1890ff',
              backgroundColor: '#f0f8ff',
              padding: '2px 6px',
              borderRadius: '4px',
              fontSize: '12px',
            }}
          >
            {product.code}
          </span>
          <span style={{ fontWeight: 500, flex: 1 }}>{product.name}</span>
        </div>
        <div style={{ fontSize: '12px', color: '#8c8c8c', paddingLeft: '4px' }}>
          {product.brandName && <span>品牌: {product.brandName}</span>}
          {product.supplierName && (
            <span style={{ marginLeft: '12px' }}>供应商: {product.supplierName}</span>
          )}
          <span style={{ marginLeft: '12px', color: '#f5222d', fontWeight: 500 }}>
            零售价: ¥{product.retailPrice.toFixed(2)}
          </span>
        </div>
      </div>
    ),
  }));

  return (
    <Select
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
    />
  );
};

export default ProductSearchSelector;
