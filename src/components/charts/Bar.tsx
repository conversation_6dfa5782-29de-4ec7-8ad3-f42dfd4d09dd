import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import useStyles from './BarStyles';
import { ChartDataItem, BarProps } from './chartsInterface';

const Bar: React.FC<BarProps> = ({
  title,
  dataSource = [] as ChartDataItem[],
  height = 300,
  yaxisText,
}) => {
  const { styles } = useStyles();
  const chartRef = useRef<HTMLDivElement | null>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  useEffect(() => {
    if (chartInstance.current && dataSource.length > 0) {
      const xAxisData = dataSource.map((item) => item.x);
      const seriesData = dataSource.map((item) => item.y);

      const option: echarts.EChartsOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: xAxisData,
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: yaxisText || '',
            nameTextStyle: {
              padding: [0, 0, 0, 40],
            },
          },
        ],
        series: [
          {
            name: title,
            type: 'bar',
            barWidth: '60%',
            data: seriesData,
            itemStyle: {
              color: '#1890ff',
            },
          },
        ],
      };

      chartInstance.current.setOption(option);
    }
  }, [dataSource, title, yaxisText]);

  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div className={styles.barChartWrapper}>
      <div className={styles.chartTitle}>{title}</div>
      <div className={styles.chartContainer} ref={chartRef} style={{ height: height }} />
    </div>
  );
};

export default Bar;
