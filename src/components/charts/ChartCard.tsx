import { ChartCardProps } from './chartsInterface';
import useStyles from './ChartCardStyles';
import { Card } from 'antd';

const ChartCard: React.FC<ChartCardProps> = ({
  title,
  action,
  total,
  footer,
  contentHeight,
  children,
  loading,
  ...rest
}) => {
  const { styles } = useStyles();

  return (
    <div className={styles.chartCardWrapper}>
      <Card
        loading={loading}
        style={{ padding: '20px 24px 8px 24px' }}
        className={styles.chartCard}
        {...rest}
      >
        <div className={styles.meta}>
          <span>{title}</span>
          <span className={styles.action}>{action}</span>
        </div>
        <div className={styles.content} style={{ height: contentHeight || 'auto' }}>
          {children || <div className={styles.total}>{total}</div>}
        </div>
        {footer && <div className={styles.footer}>{footer}</div>}
      </Card>
    </div>
  );
};

export default ChartCard;
