import { ReactNode } from 'react';
import { CardProps } from 'antd';

export type ChartType = 'bar' | 'line' | 'pie';

export interface ChartDataItem {
  x: string;
  y: number;
}

export interface BarProps {
  title: string;
  dataSource?: ChartDataItem[];
  height?: number;
  yaxisText?: string;
}

export interface ChartProps {
  type: ChartType;
  child: React.ReactNode;
  dataSource?: ChartDataItem[];
  height?: number;
  yaxisText?: string;
}

export interface ChartCardProps extends CardProps {
  title?: ReactNode;
  action?: ReactNode;
  total?: ReactNode;
  footer?: ReactNode;
  contentHeight?: number;
  children?: ReactNode;
}
