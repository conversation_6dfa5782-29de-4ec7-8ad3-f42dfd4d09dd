import React from 'react';
import Bar from './Bar';
import Line from './Line';
import Pie from './Pie';
import { ChartProps } from './chartsInterface';

const Chart: React.FC<ChartProps> = ({ type, child, dataSource = [], height = 300, yaxisText }) => {
  switch (type) {
    case 'bar':
      return (
        <>
          {child}
          <Bar title="" dataSource={dataSource} height={height} yaxisText={yaxisText} />
        </>
      );
    case 'line':
      return (
        <>
          {child}
          <Line title="" dataSource={dataSource} height={height} yaxisText={yaxisText} />
        </>
      );
    case 'pie':
      return (
        <>
          {child}
          <Pie title="" dataSource={dataSource} height={height} />
        </>
      );
    default:
      return (
        <>
          {child}
          <Bar title="" dataSource={dataSource} height={height} yaxisText={yaxisText} />
        </>
      );
  }
};

export default Chart;
