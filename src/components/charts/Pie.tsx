import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import useStyles from './BarStyles';

export interface ChartDataItem {
  x: string;
  y: number;
}

interface PieProps {
  title: string;
  dataSource?: ChartDataItem[];
  height?: number;
}

const Pie: React.FC<PieProps> = ({ title, dataSource = [] as ChartDataItem[], height = 300 }) => {
  const { styles } = useStyles();
  const chartRef = useRef<HTMLDivElement | null>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  useEffect(() => {
    if (chartInstance.current && dataSource.length > 0) {
      const pieData = dataSource.map((item) => ({
        name: item.x,
        value: item.y,
      }));

      const option: echarts.EChartsOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: dataSource.map((item) => item.x),
        },
        series: [
          {
            name: title,
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: pieData,
          },
        ],
      };

      chartInstance.current.setOption(option);
    }
  }, [dataSource, title]);

  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div className={styles.barChartWrapper}>
      <div className={styles.chartTitle}>{title}</div>
      <div className={styles.chartContainer} ref={chartRef} style={{ height: height }} />
    </div>
  );
};

export default Pie;
