import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => ({
  chartCardWrapper: {
    position: 'relative',
  },
  chartCard: {
    position: 'relative',
  },
  meta: {
    position: 'relative',
    overflow: 'hidden',
    width: '100%',
    color: 'rgba(0, 0, 0, 0.45)',
    fontSize: '14px',
    lineHeight: '22px',
  },
  content: {
    position: 'relative',
    width: '100%',
    marginBottom: '12px',
  },
  total: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    wordBreak: 'break-all',
    whiteSpace: 'nowrap',
    color: token.colorText,
    marginTop: '4px',
    marginBottom: '0',
    fontSize: '30px',
    lineHeight: '38px',
    height: '38px',
  },
  action: {
    position: 'absolute',
    top: '4px',
    right: '0',
    lineHeight: '1',
    cursor: 'pointer',
  },
  footer: {
    marginTop: '8px',
    paddingTop: '9px',
    borderTop: `1px solid ${token.colorBorderSecondary}`,
  },
}));

export default useStyles;
