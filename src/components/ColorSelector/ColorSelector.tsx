import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag } from 'antd';
import { debounce } from 'lodash-es';
import { getColorList } from '@/api/ColorApi';
import type { ColorListProps } from '@/types/color';
import { logError } from '@/utils/errorHandler';

interface ColorSelectorProps {
  value?: string;
  onChange?: (value: string, color: ColorListProps | null) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  showAll?: boolean; // 是否显示所有颜色（不进行搜索过滤）
}

const ColorSelector: React.FC<ColorSelectorProps> = ({
  value,
  onChange,
  placeholder = '请选择颜色',
  disabled = false,
  allowClear = true,
  style,
  className,
  showAll = false,
}) => {
  const [colors, setColors] = useState<ColorListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        setLoading(true);
        try {
          const response = await getColorList({
            page: 0,
            pageSize: 0,
            search: showAll ? undefined : searchText.trim() || undefined,
          });

          if (response.code === 200 && response.data?.colors) {
            setColors(response.data.colors);
          } else {
            setColors([]);
          }
        } catch (error: any) {
          logError('搜索颜色', error);
          setColors([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [showAll],
  );

  // 初始化加载所有颜色（当showAll为true时）
  useEffect(() => {
    if (showAll) {
      debouncedSearch('');
    }
  }, [showAll, debouncedSearch]);

  // 当搜索值变化时触发搜索
  useEffect(() => {
    if (!showAll) {
      debouncedSearch(searchValue);
    }
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch, showAll]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    if (!showAll) {
      setSearchValue(searchText);
    }
  };

  // 处理选择
  const handleChange = (selectedValue: string) => {
    const selectedColor = colors.find((color) => color.code === selectedValue) || null;
    onChange?.(selectedValue, selectedColor);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    if (!showAll) {
      setColors([]);
    }
    onChange?.('', null);
  };

  // 生成选项
  const options = colors.map((color) => ({
    value: color.code,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ flex: 1 }}>
          <div style={{ fontWeight: 500, display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span>{color.name}</span>
            <Tag color="blue">{color.code}</Tag>
          </div>
        </div>
      </div>
    ),
  }));

  return (
    <Select
      showSearch={!showAll}
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={showAll ? true : false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={{ minHeight: '40px', ...style }}
      className={className}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
    />
  );
};

export default ColorSelector;
