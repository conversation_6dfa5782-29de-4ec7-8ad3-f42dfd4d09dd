import React, { useState } from 'react';
import {
  Card,
  Button,
  Space,
  message,
  Modal,
  Form,
  Tag,
  Popconfirm,
  Typography,
  Alert,
} from 'antd';
import {
  UserAddOutlined,
  UserDeleteOutlined,
  CrownOutlined,
  BankOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { setCompanyAdmin, removeCompanyAdmin } from '@/api/UserApi';
import CompanySearchSelector from '@/components/CompanySearchSelector/CompanySearchSelector';
import UserSearchSelector from '@/components/UserSearchSelector/UserSearchSelector';
import type { UserProps } from '@/types/user';
import { getErrorMessage, handleApiResponse, logError } from '@/utils/errorHandler';

const { Text } = Typography;

interface CompanyAdminManagerProps {
  user?: UserProps;
  onSuccess?: () => void;
}

const CompanyAdminManager: React.FC<CompanyAdminManagerProps> = ({ user, onSuccess }) => {
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [actionType, setActionType] = useState<'set' | 'remove'>('set');

  // 设置公司管理员
  const handleSetCompanyAdmin = async (values: { userCode: string; companyCode: string }) => {
    setLoading(true);
    try {
      const response = await setCompanyAdmin({
        userCode: values.userCode,
        companyCode: values.companyCode,
      });

      const result = handleApiResponse(response, '设置公司管理员成功', '设置公司管理员失败');

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        form.resetFields();
        onSuccess?.();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('设置公司管理员', error);
      message.error(getErrorMessage(error, '设置公司管理员失败'));
    } finally {
      setLoading(false);
    }
  };

  // 移除公司管理员
  const handleRemoveCompanyAdmin = async (userCode: string) => {
    setLoading(true);
    try {
      const response = await removeCompanyAdmin(userCode);

      const result = handleApiResponse(response, '移除公司管理员成功', '移除公司管理员失败');

      if (result.success) {
        message.success(result.message);
        onSuccess?.();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('移除公司管理员', error);
      message.error(getErrorMessage(error, '移除公司管理员失败'));
    } finally {
      setLoading(false);
    }
  };

  // 打开设置模态框
  const openSetModal = () => {
    setActionType('set');
    form.resetFields();
    if (user) {
      form.setFieldsValue({ userCode: user.code });
    }
    setModalVisible(true);
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      await handleSetCompanyAdmin(values);
    } catch (error: any) {
      if (error.errorFields) {
        message.error('请检查表单填写');
      }
    }
  };

  // 确认移除
  const confirmRemove = () => {
    if (user) {
      handleRemoveCompanyAdmin(user.code);
    }
  };

  return (
    <div>
      {/* 当前用户的管理员状态显示 */}
      {user && (
        <Card size="small" style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Space>
                <Text strong>{user.nickname}</Text>
                <Text type="secondary">({user.code})</Text>
                {user.isSuperAdmin && (
                  <Tag color="red" icon={<CrownOutlined />}>
                    超级管理员
                  </Tag>
                )}
                {user.isCompanyAdmin && (
                  <Tag color="blue" icon={<BankOutlined />}>
                    公司管理员 {user.companyCode && `(${user.companyCode})`}
                  </Tag>
                )}
                {!user.isSuperAdmin && !user.isCompanyAdmin && <Tag color="default">普通用户</Tag>}
              </Space>
            </div>
            <Space>
              {!user.isSuperAdmin && !user.isCompanyAdmin && (
                <Button
                  type="primary"
                  size="small"
                  icon={<UserAddOutlined />}
                  onClick={openSetModal}
                >
                  设为公司管理员
                </Button>
              )}
              {user.isCompanyAdmin && (
                <Popconfirm
                  title="确定要移除公司管理员权限吗？"
                  description="移除后该用户将失去公司管理权限"
                  onConfirm={confirmRemove}
                  okText="确定"
                  cancelText="取消"
                  icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                >
                  <Button danger size="small" icon={<UserDeleteOutlined />} loading={loading}>
                    移除管理员
                  </Button>
                </Popconfirm>
              )}
            </Space>
          </div>
        </Card>
      )}

      {/* 快速操作按钮 */}
      {!user && (
        <Card size="small">
          <Space>
            <Button type="primary" icon={<UserAddOutlined />} onClick={openSetModal}>
              设置公司管理员
            </Button>
          </Space>
        </Card>
      )}

      {/* 设置公司管理员模态框 */}
      <Modal
        title={
          <Space>
            <UserAddOutlined />
            设置公司管理员
          </Space>
        }
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
        width={600}
        destroyOnClose
      >
        <Alert
          message="设置说明"
          description="公司管理员将拥有该公司的管理权限，包括员工管理、数据查看等功能。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form form={form} layout="vertical" preserve={false}>
          <Form.Item
            name="userCode"
            label="选择用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <UserSearchSelector placeholder="请搜索并选择用户" disabled={!!user} />
          </Form.Item>

          <Form.Item
            name="companyCode"
            label="选择公司"
            rules={[{ required: true, message: '请选择公司' }]}
          >
            <CompanySearchSelector placeholder="请搜索并选择公司" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CompanyAdminManager;
