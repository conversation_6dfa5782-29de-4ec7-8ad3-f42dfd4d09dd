import React from 'react';
import { Select } from 'antd';
import { getAllProvinces } from '@/types/province';

interface ProvinceSelectorProps {
  value?: string | number;
  onChange?: (value: number) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const ProvinceSelector: React.FC<ProvinceSelectorProps> = ({
  value,
  onChange,
  placeholder = '请选择省份',
  disabled = false,
  allowClear = true,
  style,
  className,
}) => {
  // 获取所有省份数据
  const provinces = getAllProvinces();

  // 处理选择变化 - 直接传递省份编码给后端
  const handleChange = (selectedCode: number) => {
    onChange?.(selectedCode);
  };

  // 处理清空
  const handleClear = () => {
    onChange?.(undefined as any);
  };

  // 生成选项 - 显示中文名称，值是编码
  const options = provinces.map((province) => ({
    value: province.code, // 这是编码，传给后端
    label: province.name, // 这是中文名称，显示给用户
  }));

  // 确保value是数字类型
  const normalizedValue = value
    ? typeof value === 'string'
      ? parseInt(value, 10)
      : value
    : undefined;

  return (
    <Select
      value={normalizedValue}
      placeholder={placeholder}
      onChange={handleChange}
      onClear={handleClear}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      showSearch
      filterOption={(input, option) =>
        // 按中文名称搜索
        (option?.label ?? '').toString().includes(input)
      }
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
    />
  );
};

export default ProvinceSelector;
