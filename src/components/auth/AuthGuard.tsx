import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { isAuthenticated, refreshTokenExpiry } from '@/utils/tokenManager';
import useAuthStore from '@/store/authStore';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const location = useLocation();
  const [isChecking, setIsChecking] = useState(true);
  const { checkAuth, isAuthenticated: storeIsAuth } = useAuthStore();

  useEffect(() => {
    // 检查用户是否已登录并同步到store
    const performAuthCheck = () => {
      const auth = isAuthenticated();

      if (auth) {
        // 如果已登录，同步用户信息到store
        checkAuth();
        refreshTokenExpiry();
      }

      setIsChecking(false);
    };

    performAuthCheck();
  }, [checkAuth]);

  // 加载状态
  if (isChecking) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  // 未登录，重定向到登录页
  if (!storeIsAuth) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 已登录，渲染子组件
  return <>{children}</>;
};

export default AuthGuard;
