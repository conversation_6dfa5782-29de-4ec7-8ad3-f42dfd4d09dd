import React from 'react';
import { Card, Typography, Tag, Space, Divider } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import useAuthStore from '@/store/authStore';

const { Title, Text } = Typography;

/**
 * 权限测试组件 - 用于测试和展示当前用户的权限状态
 */
const PermissionTest: React.FC = () => {
  const { user, hasRoutePermission, canAccessRoute, getRoutePermissions } = useAuthStore();

  if (!user) {
    return (
      <Card title="权限测试">
        <Text type="secondary">用户未登录</Text>
      </Card>
    );
  }

  // 测试路由列表
  const testRoutes = [
    '/financial/fixed-assets',
    '/financial/operating-assets', 
    '/financial/rental-assets',
    '/financial/rd-costs',
    '/financial/income-assets',
    '/financial/expense-assets',
    '/system/users',
    '/admin/memos',
    '/product/brand',
    '/company/management',
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="用户权限测试" style={{ marginBottom: 16 }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Title level={4}>用户信息</Title>
            <Space>
              <Text strong>用户代码:</Text>
              <Text>{user.code}</Text>
            </Space>
            <br />
            <Space>
              <Text strong>用户昵称:</Text>
              <Text>{user.nickname}</Text>
            </Space>
            <br />
            <Space>
              <Text strong>超级管理员:</Text>
              <Tag color={user.isSuperAdmin ? 'green' : 'red'}>
                {user.isSuperAdmin ? '是' : '否'}
              </Tag>
            </Space>
          </div>

          <Divider />

          <div>
            <Title level={4}>路由权限测试</Title>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {testRoutes.map(route => {
                const canAccess = canAccessRoute(route);
                const permissions = getRoutePermissions(route);
                
                return (
                  <Card key={route} size="small" style={{ marginBottom: 8 }}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <Space>
                        <Text strong>{route}</Text>
                        {canAccess ? (
                          <Tag color="green" icon={<CheckCircleOutlined />}>
                            可访问
                          </Tag>
                        ) : (
                          <Tag color="red" icon={<CloseCircleOutlined />}>
                            无权限
                          </Tag>
                        )}
                      </Space>
                      
                      {permissions && (
                        <div>
                          <Text type="secondary">具体权限: </Text>
                          <Space wrap>
                            {Object.entries(permissions).map(([key, value]) => (
                              <Tag 
                                key={key} 
                                color={value ? 'green' : 'default'}
                                style={{ fontSize: '12px' }}
                              >
                                {key}: {value ? '✓' : '✗'}
                              </Tag>
                            ))}
                          </Space>
                        </div>
                      )}
                    </Space>
                  </Card>
                );
              })}
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default PermissionTest;
