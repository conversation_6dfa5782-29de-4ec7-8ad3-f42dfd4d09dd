import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Result, Button } from 'antd';
import { LockOutlined, HomeOutlined } from '@ant-design/icons';
import useAuthStore from '@/store/authStore';

interface RoutePermissionGuardProps {
  children: React.ReactNode;
  requiredRoute?: string; // 可选：指定需要检查的路由，默认使用当前路由
  requiredPermission?: 'read' | 'create' | 'update' | 'delete' | 'export' | 'import'; // 可选：指定需要的具体权限
}

/**
 * 路由权限守卫组件
 * 检查用户是否有访问当前路由的权限
 */
const RoutePermissionGuard: React.FC<RoutePermissionGuardProps> = ({
  children,
  requiredRoute,
  requiredPermission,
}) => {
  const location = useLocation();
  const { user, canAccessRoute, hasRoutePermission } = useAuthStore();

  // 确定要检查的路由
  const routeToCheck = requiredRoute || location.pathname;

  // 如果用户未登录，重定向到登录页
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // 超级管理员可以访问所有路由
  if (user.isSuperAdmin) {
    return <>{children}</>;
  }

  // 首页所有人都可以访问
  if (routeToCheck === '/' || routeToCheck === '/home') {
    return <>{children}</>;
  }

  // 检查权限 - 添加调试日志
  let hasPermission = false;

  console.log('RoutePermissionGuard - 检查路由权限:', {
    routeToCheck,
    requiredPermission,
    user: user ? { code: user.code, isSuperAdmin: user.isSuperAdmin } : null,
    routePermissions: user?.routePermissions,
  });

  if (requiredPermission) {
    // 如果指定了具体权限，检查该权限
    hasPermission = hasRoutePermission(routeToCheck, requiredPermission);
  } else {
    // 如果没有指定权限，检查是否可以访问该路由
    hasPermission = canAccessRoute(routeToCheck);
  }

  console.log('RoutePermissionGuard - 权限检查结果:', hasPermission);

  // 如果没有权限，显示权限不足页面
  if (!hasPermission) {
    return (
      <Result
        status="403"
        title="权限不足"
        subTitle={`抱歉，您没有权限访问此页面。${requiredPermission ? `需要 ${getPermissionName(requiredPermission)} 权限。` : ''}`}
        icon={<LockOutlined style={{ color: '#ff4d4f' }} />}
        extra={[
          <Button
            key="home"
            type="primary"
            icon={<HomeOutlined />}
            onClick={() => (window.location.href = '/')}
          >
            返回首页
          </Button>,
          <Button key="back" onClick={() => window.history.back()}>
            返回上一页
          </Button>,
        ]}
        style={{
          padding: '60px 24px',
          backgroundColor: '#fff',
          borderRadius: '8px',
          margin: '24px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        }}
      />
    );
  }

  // 有权限，渲染子组件
  return <>{children}</>;
};

// 权限名称映射
const getPermissionName = (permission: string): string => {
  const permissionNames: { [key: string]: string } = {
    read: '查看',
    create: '创建',
    update: '更新',
    delete: '删除',
    export: '导出',
    import: '导入',
  };
  return permissionNames[permission] || permission;
};

export default RoutePermissionGuard;
