import React from 'react';
import { Button, Dropdown, message } from 'antd';
import { LogoutOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { clearToken } from '@/utils/tokenManager';
import { logout } from '@/api/auth';

interface LogoutButtonProps {
  children?: React.ReactNode;
  type?: 'link' | 'text' | 'default' | 'primary' | 'dashed';
  danger?: boolean;
  className?: string;
}

/**
 * 登出按钮组件
 * 可以作为独立按钮使用，也可以作为下拉菜单项使用
 */
const LogoutButton: React.FC<LogoutButtonProps> = ({
  children,
  type = 'default',
  danger = false,
  className,
}) => {
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      // 调用后端登出接口
      await logout();
      
      // 清除本地存储的 token 和用户信息
      clearToken();
      
      message.success('已成功退出登录');
      
      // 跳转到登录页
      navigate('/login');
    } catch (error) {
      console.error('登出失败:', error);
      
      // 即使后端登出失败，也清除本地存储并跳转到登录页
      clearToken();
      navigate('/login');
    }
  };

  return (
    <Button
      type={type}
      danger={danger}
      icon={<LogoutOutlined />}
      onClick={handleLogout}
      className={className}
    >
      {children || '退出登录'}
    </Button>
  );
};

export default LogoutButton;
