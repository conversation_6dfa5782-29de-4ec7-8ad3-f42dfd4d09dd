import React from 'react';
import { Navigate } from 'react-router-dom';
import { Result, Button } from 'antd';
import { LockOutlined, HomeOutlined } from '@ant-design/icons';
import { getUser } from '@/utils/tokenManager';

interface SuperAdminGuardProps {
  children: React.ReactNode;
}

/**
 * 超级管理员权限守卫组件
 * 只有超级管理员才能访问被保护的页面
 */
const SuperAdminGuard: React.FC<SuperAdminGuardProps> = ({ children }) => {
  const user = getUser();

  // 如果用户未登录，重定向到登录页
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // 如果不是超级管理员，显示权限不足页面
  if (!user.isSuperAdmin) {
    return (
      <Result
        status="403"
        title="权限不足"
        subTitle="抱歉，您没有权限访问此页面。只有超级管理员才能访问用户管理功能。"
        icon={<LockOutlined style={{ color: '#ff4d4f' }} />}
        extra={
          <Button 
            type="primary" 
            icon={<HomeOutlined />}
            onClick={() => window.history.back()}
          >
            返回上一页
          </Button>
        }
        style={{
          padding: '60px 24px',
          backgroundColor: '#fff',
          borderRadius: '8px',
          margin: '24px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        }}
      />
    );
  }

  // 是超级管理员，渲染子组件
  return <>{children}</>;
};

export default SuperAdminGuard;
