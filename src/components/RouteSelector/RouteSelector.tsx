import React, { useState, useMemo } from 'react';
import { Modal, Input, Checkbox, Collapse, Space, Typography, Empty, Button, Divider } from 'antd';
import { SearchOutlined, FolderOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { RouteInfo, groupRoutesByCategory } from '@/utils/routeExtractor';

const { Search } = Input;
const { Panel } = Collapse;
const { Text } = Typography;

interface RouteSelectorProps {
  visible: boolean;
  availableRoutes: RouteInfo[];
  selectedRoutes: string[];
  onConfirm: (selectedRoutes: string[]) => void;
  onCancel: () => void;
}

const RouteSelector: React.FC<RouteSelectorProps> = ({
  visible,
  availableRoutes,
  selectedRoutes,
  onConfirm,
  onCancel,
}) => {
  const [searchText, setSearchText] = useState('');
  const [tempSelectedRoutes, setTempSelectedRoutes] = useState<string[]>(selectedRoutes);

  // 重置临时选择状态
  React.useEffect(() => {
    if (visible) {
      setTempSelectedRoutes(selectedRoutes);
      setSearchText('');
    }
  }, [visible, selectedRoutes]);

  // 过滤和分组路由
  const { filteredRoutes, groupedRoutes } = useMemo(() => {
    const filtered = availableRoutes.filter((route) => {
      if (!searchText) return true;
      const searchLower = searchText.toLowerCase();
      return (
        route.path.toLowerCase().includes(searchLower) ||
        route.displayName.toLowerCase().includes(searchLower) ||
        route.category.toLowerCase().includes(searchLower)
      );
    });

    const grouped = groupRoutesByCategory(filtered);
    return { filteredRoutes: filtered, groupedRoutes: grouped };
  }, [availableRoutes, searchText]);

  // 处理单个路由选择
  const handleRouteToggle = (routePath: string, checked: boolean) => {
    if (checked) {
      setTempSelectedRoutes((prev) => [...prev, routePath]);
    } else {
      setTempSelectedRoutes((prev) => prev.filter((path) => path !== routePath));
    }
  };

  // 处理分类全选/取消全选
  const handleCategoryToggle = (categoryRoutes: RouteInfo[], checked: boolean) => {
    const categoryPaths = categoryRoutes.map((route) => route.path);

    if (checked) {
      // 添加该分类下所有未选中的路由
      const newPaths = categoryPaths.filter((path) => !tempSelectedRoutes.includes(path));
      setTempSelectedRoutes((prev) => [...prev, ...newPaths]);
    } else {
      // 移除该分类下所有已选中的路由
      setTempSelectedRoutes((prev) => prev.filter((path) => !categoryPaths.includes(path)));
    }
  };

  // 检查分类选择状态
  const getCategoryCheckState = (categoryRoutes: RouteInfo[]) => {
    const categoryPaths = categoryRoutes.map((route) => route.path);
    const selectedCount = categoryPaths.filter((path) => tempSelectedRoutes.includes(path)).length;

    if (selectedCount === 0) return { checked: false, indeterminate: false };
    if (selectedCount === categoryPaths.length) return { checked: true, indeterminate: false };
    return { checked: false, indeterminate: true };
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allPaths = filteredRoutes.map((route) => route.path);
      const newPaths = allPaths.filter((path) => !tempSelectedRoutes.includes(path));
      setTempSelectedRoutes((prev) => [...prev, ...newPaths]);
    } else {
      const filteredPaths = filteredRoutes.map((route) => route.path);
      setTempSelectedRoutes((prev) => prev.filter((path) => !filteredPaths.includes(path)));
    }
  };

  // 检查全选状态
  const getAllCheckState = () => {
    const filteredPaths = filteredRoutes.map((route) => route.path);
    const selectedCount = filteredPaths.filter((path) => tempSelectedRoutes.includes(path)).length;

    if (selectedCount === 0) return { checked: false, indeterminate: false };
    if (selectedCount === filteredPaths.length) return { checked: true, indeterminate: false };
    return { checked: false, indeterminate: true };
  };

  const allCheckState = getAllCheckState();

  const handleConfirm = () => {
    onConfirm(tempSelectedRoutes);
  };

  const handleCancel = () => {
    setTempSelectedRoutes(selectedRoutes);
    setSearchText('');
    onCancel();
  };

  return (
    <Modal
      title="选择路由权限"
      open={visible}
      onOk={handleConfirm}
      onCancel={handleCancel}
      width={800}
      bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
      okText="确定"
      cancelText="取消"
    >
      <div style={{ marginBottom: 16 }}>
        <Search
          placeholder="搜索路由路径、名称或分类"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ marginBottom: 12 }}
          prefix={<SearchOutlined />}
        />

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Checkbox
              checked={allCheckState.checked}
              indeterminate={allCheckState.indeterminate}
              onChange={(e) => handleSelectAll(e.target.checked)}
            >
              全选当前结果
            </Checkbox>
            <Text type="secondary">已选择 {tempSelectedRoutes.length} 个路由</Text>
          </Space>

          <Space>
            <Button size="small" icon={<CheckOutlined />} onClick={() => handleSelectAll(true)}>
              全选
            </Button>
            <Button size="small" icon={<CloseOutlined />} onClick={() => handleSelectAll(false)}>
              清空
            </Button>
          </Space>
        </div>
      </div>

      <Divider style={{ margin: '12px 0' }} />

      {Object.keys(groupedRoutes).length === 0 ? (
        <Empty description="没有找到匹配的路由" />
      ) : (
        <Collapse defaultActiveKey={Object.keys(groupedRoutes)} ghost size="small">
          {Object.entries(groupedRoutes).map(([category, routes]) => {
            const categoryCheckState = getCategoryCheckState(routes);

            return (
              <Panel
                key={category}
                header={
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <Checkbox
                      checked={categoryCheckState.checked}
                      indeterminate={categoryCheckState.indeterminate}
                      onChange={(e) => handleCategoryToggle(routes, e.target.checked)}
                      onClick={(e) => e.stopPropagation()}
                    />
                    <FolderOutlined />
                    <Text strong>{category}</Text>
                    <Text type="secondary">({routes.length})</Text>
                  </div>
                }
              >
                <div style={{ paddingLeft: 24 }}>
                  {routes.map((route) => (
                    <div
                      key={route.path}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '4px 0',
                        borderBottom: '1px solid #f0f0f0',
                      }}
                    >
                      <Checkbox
                        checked={tempSelectedRoutes.includes(route.path)}
                        onChange={(e) => handleRouteToggle(route.path, e.target.checked)}
                        style={{ marginRight: 12 }}
                      />
                      <div style={{ display: 'flex', gap: '12px' }}>
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8,
                            color: '#147ffa',
                            fontWeight: 'bold',
                          }}
                        >
                          <span>{route.displayName}</span>
                        </div>
                        <Text code style={{ fontSize: 14, color: '#000' }}>
                          {route.path}
                        </Text>
                      </div>
                    </div>
                  ))}
                </div>
              </Panel>
            );
          })}
        </Collapse>
      )}
    </Modal>
  );
};

export default RouteSelector;
