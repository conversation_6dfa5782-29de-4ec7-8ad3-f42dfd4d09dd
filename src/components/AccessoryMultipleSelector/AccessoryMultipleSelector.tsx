import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag, InputNumber, Popover } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { debounce } from 'lodash-es';
import { getAccessoryList } from '@/api/AccessoryApi';
import type { AccessoryListProps, AccessorySelectOption } from '@/types/accessories';
import { logError } from '@/utils/errorHandler';

interface AccessoryMultipleSelectorProps {
  value?: AccessorySelectOption[];
  onChange?: (value: AccessorySelectOption[]) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  maxTagCount?: number;
}

const AccessoryMultipleSelector: React.FC<AccessoryMultipleSelectorProps> = ({
  value = [],
  onChange,
  placeholder = '请选择辅料',
  disabled = false,
  allowClear = true,
  style,
  className,
  maxTagCount = 3,
}) => {
  const [accessories, setAccessories] = useState<AccessoryListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [_, setSearchText] = useState('');

  // 获取辅料列表
  const fetchAccessories = async (search = '') => {
    setLoading(true);
    try {
      const response = await getAccessoryList({
        page: 0, // 当page为0时获取所有数据
        pageSize: 0, // 当pageSize为0时获取所有数据
        nameSearch: search || undefined,
      });

      if (response.code === 200 && response.data) {
        setAccessories(response.data.accessories || []);
      }
    } catch (error) {
      logError('获取辅料列表', error);
      setAccessories([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedFetchAccessories = useMemo(
    () => debounce((search: string) => fetchAccessories(search), 300),
    [],
  );

  // 初始化数据
  useEffect(() => {
    fetchAccessories();
  }, []);

  // 搜索处理
  const handleSearch = (searchValue: string) => {
    setSearchText(searchValue);
    debouncedFetchAccessories(searchValue);
  };

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedFetchAccessories.cancel();
    };
  }, [debouncedFetchAccessories]);

  // 选择变化处理
  const handleChange = (selectedValues: string[]) => {
    const selectedAccessories = selectedValues.map((articleNumber) => {
      const accessory = accessories.find((item) => item.articleNumber === articleNumber);
      // 检查是否已存在，如果存在则保留原有数量，否则默认为1
      const existingAccessory = value.find((item) => item.code === articleNumber);
      return {
        id: accessory?.id || '',
        code: accessory?.articleNumber || articleNumber,
        name: accessory?.name || '',
        price: accessory?.costPrice || 0,
        quantity: existingAccessory?.quantity || 1,
      };
    });
    onChange?.(selectedAccessories);
  };

  // 清除处理
  const handleClear = () => {
    onChange?.([]);
  };

  // 生成选项
  const options = accessories.map((accessory) => ({
    value: accessory.articleNumber,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', padding: '4px 0' }}>
        <div style={{ flex: 1 }}>
          <div style={{ fontWeight: 500, fontSize: '14px', marginBottom: '2px' }}>
            {accessory.name}
          </div>
          <div style={{ fontSize: '12px', color: '#8c8c8c', lineHeight: '16px' }}>
            {accessory.articleNumber} | ¥{accessory.costPrice.toFixed(2)}
            {accessory.supplierName && (
              <span style={{ marginLeft: '8px', color: '#666' }}>
                供应商: {accessory.supplierName}
              </span>
            )}
          </div>
        </div>
      </div>
    ),
  }));

  // 更新辅料数量
  const updateAccessoryQuantity = (code: string, newQuantity: number) => {
    const updatedAccessories = value.map((item) =>
      item.code === code ? { ...item, quantity: newQuantity } : item,
    );
    onChange?.(updatedAccessories);
  };

  // 自定义标签渲染
  const tagRender = (props: any) => {
    const { value: tagValue, closable, onClose } = props;
    const accessory = accessories.find((item) => item.articleNumber === tagValue);
    const selectedAccessory = value.find((item: AccessorySelectOption) => item.code === tagValue);
    const quantity = selectedAccessory?.quantity || 1;

    const quantityEditor = (
      <div style={{ padding: '8px' }}>
        <div style={{ marginBottom: '8px', fontWeight: 500 }}>{accessory?.name || tagValue}</div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span>数量:</span>
          <InputNumber
            size="small"
            min={1}
            value={quantity}
            onChange={(newQuantity) => updateAccessoryQuantity(tagValue, newQuantity || 1)}
            style={{ width: 80 }}
          />
        </div>
      </div>
    );

    return (
      <Popover content={quantityEditor} title="编辑数量" trigger="click" placement="top">
        <Tag
          color="blue"
          closable={closable}
          onClose={onClose}
          style={{
            marginRight: 4,
            marginBottom: 4,
            padding: '4px 8px',
            fontSize: '13px',
            lineHeight: '20px',
            borderRadius: '4px',
            cursor: 'pointer',
          }}
        >
          {accessory?.name || tagValue} (¥{accessory?.costPrice.toFixed(2) || '0.00'}) x{quantity}
          <EditOutlined style={{ marginLeft: 4, fontSize: '11px' }} />
        </Tag>
      </Popover>
    );
  };

  return (
    <Select
      mode="multiple"
      showSearch
      value={value.map((item) => item.code)}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={{
        minHeight: '48px',
        height: '48px',
        fontSize: '14px',
        width: '100%',
        ...style,
      }}
      className={className}
      dropdownStyle={{
        maxHeight: 400,
        overflow: 'auto',
        fontSize: '14px',
      }}
      maxTagCount={maxTagCount}
      maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
      tagRender={tagRender}
    />
  );
};

export default AccessoryMultipleSelector;
