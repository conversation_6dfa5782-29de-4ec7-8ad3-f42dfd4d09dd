import React from 'react';
import { InputNumber, InputNumberProps } from 'antd';

interface FormattedAmountInputProps extends Omit<InputNumberProps, 'formatter' | 'parser'> {
  showCurrency?: boolean;
  currency?: string;
}

/**
 * 格式化金额输入组件
 * 支持千分位分隔符的金额输入，用户输入时自动格式化
 */
const FormattedAmountInput: React.FC<FormattedAmountInputProps> = ({
  showCurrency = true,
  currency = '¥',
  precision = 2,
  min = 0,
  placeholder = '请输入金额',
  style = { width: '100%' },
  ...props
}) => {
  // 格式化显示：添加千分位分隔符
  const formatter = (value: string | number | undefined): string => {
    if (!value && value !== 0) return '';

    // 转换为数字
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    if (isNaN(numValue)) return '';

    // 使用toLocaleString添加千分位分隔符
    return numValue.toLocaleString('zh-CN', {
      minimumFractionDigits: 0,
      maximumFractionDigits: precision || 2,
    });
  };

  // 解析输入：移除千分位分隔符，返回纯数字
  const parser = (value: string | undefined): string => {
    if (!value) return '';

    // 移除所有非数字字符（除了小数点和负号）
    const cleanValue = value.replace(/[^\d.-]/g, '');

    return cleanValue;
  };

  return (
    <InputNumber
      {...props}
      precision={precision}
      min={min}
      placeholder={placeholder}
      style={style}
      formatter={formatter}
      parser={parser}
      addonBefore={showCurrency ? currency : undefined}
    />
  );
};

export default FormattedAmountInput;
