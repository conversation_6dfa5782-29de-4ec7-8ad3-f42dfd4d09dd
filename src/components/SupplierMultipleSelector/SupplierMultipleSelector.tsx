import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag } from 'antd';
import { debounce } from 'lodash-es';
import { getSupplierList } from '@/api/SupplierApi';
import type { SupplierListProps } from '@/types/supplier';
import { logError } from '@/utils/errorHandler';

interface SupplierMultipleSelectorProps {
  value?: string[];
  onChange?: (value: string[], suppliers: SupplierListProps[]) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  maxTagCount?: number;
}

const SupplierMultipleSelector: React.FC<SupplierMultipleSelectorProps> = ({
  value = [],
  onChange,
  placeholder = '请搜索并选择供应商',
  disabled = false,
  allowClear = true,
  style,
  className,
  maxTagCount = 3,
}) => {
  const [suppliers, setSuppliers] = useState<SupplierListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [allSuppliers, setAllSuppliers] = useState<SupplierListProps[]>([]);

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setSuppliers([]);
          return;
        }

        setLoading(true);
        try {
          const response = await getSupplierList({
            page: 0,
            pageSize: 0,
            search: searchText.trim(),
          });

          if (response.code === 200 && response.data?.suppliers) {
            const newSuppliers = response.data.suppliers;
            setSuppliers(newSuppliers);

            // 更新所有供应商列表，避免重复
            setAllSuppliers((prev) => {
              const existingCodes = new Set(prev.map((s) => s.code));
              const uniqueNewSuppliers = newSuppliers.filter((s) => !existingCodes.has(s.code));
              return [...prev, ...uniqueNewSuppliers];
            });
          } else {
            setSuppliers([]);
          }
        } catch (error) {
          logError('搜索供应商', error);
          setSuppliers([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [],
  );

  // 当搜索值变化时触发搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 处理选择
  const handleChange = (selectedValues: string[]) => {
    const selectedSuppliers = selectedValues
      .map((code) => allSuppliers.find((supplier) => supplier.code === code))
      .filter(Boolean) as SupplierListProps[];

    onChange?.(selectedValues, selectedSuppliers);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setSuppliers([]);
    onChange?.([], []);
  };

  // 生成选项
  const options = suppliers.map((supplier) => ({
    value: supplier.code,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div>
          <div style={{ fontWeight: 500 }}>{supplier.name}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
            {supplier.code}
            {supplier.contactName && (
              <span style={{ marginLeft: '4px' }}>• {supplier.contactName}</span>
            )}
            {supplier.contactPhone && (
              <span style={{ marginLeft: '4px' }}>• {supplier.contactPhone}</span>
            )}
          </div>
        </div>
      </div>
    ),
  }));

  // 自定义标签渲染
  const tagRender = (props: any) => {
    const { value, closable, onClose } = props;
    const supplier = allSuppliers.find((s) => s.code === value);

    return (
      <Tag color="green" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
        {supplier?.name || value}
      </Tag>
    );
  };

  return (
    <Select
      mode="multiple"
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      maxTagCount={maxTagCount}
      maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
      tagRender={tagRender}
    />
  );
};

export default SupplierMultipleSelector;
