import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag } from 'antd';
import { debounce } from 'lodash-es';
import { getAccessoryList } from '@/api/AccessoryApi';
import type { AccessoryListProps } from '@/types/accessories';
import { logError } from '@/utils/errorHandler';

interface SupplierAccessoryMultipleSelectorProps {
  value?: string[]; // 辅料ID数组 (UUID)
  onChange?: (value: string[], accessories: AccessoryListProps[]) => void;
  supplierCode?: string; // 供应商编码，用于过滤辅料
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  size?: 'small' | 'middle' | 'large';
  maxTagCount?: number;
}

const SupplierAccessoryMultipleSelector: React.FC<SupplierAccessoryMultipleSelectorProps> = ({
  value = [],
  onChange,
  supplierCode,
  placeholder = '请选择辅料',
  disabled = false,
  allowClear = true,
  style,
  className,
  size = 'middle',
  maxTagCount = 3,
}) => {
  const [accessories, setAccessories] = useState<AccessoryListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');

  // 获取辅料列表
  const fetchAccessories = async (search = '') => {
    if (!supplierCode) {
      setAccessories([]);
      return;
    }

    setLoading(true);
    try {
      const response = await getAccessoryList({
        page: 0, // 当page为0时获取所有数据
        pageSize: 0, // 当pageSize为0时获取所有数据
        nameSearch: search || undefined,
        supplierCodeSearch: supplierCode, // 根据供应商编码过滤
      });

      if (response.code === 200 && response.data) {
        setAccessories(response.data.accessories || []);
      }
    } catch (error) {
      logError('获取辅料列表', error);
      setAccessories([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedFetchAccessories = useMemo(
    () => debounce((search: string) => fetchAccessories(search), 300),
    [supplierCode],
  );

  // 当供应商编码变化时重新获取数据
  useEffect(() => {
    fetchAccessories();
    // 清空搜索文本
    setSearchText('');
  }, [supplierCode]);

  // 搜索处理
  const handleSearch = (searchValue: string) => {
    setSearchText(searchValue);
    debouncedFetchAccessories(searchValue);
  };

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedFetchAccessories.cancel();
    };
  }, [debouncedFetchAccessories]);

  // 选择变化处理
  const handleChange = (selectedValues: string[]) => {
    const selectedAccessories = accessories.filter((item) => selectedValues.includes(item.id));
    onChange?.(selectedValues, selectedAccessories);
  };

  // 清除处理
  const handleClear = () => {
    onChange?.([], []);
  };

  // 自定义标签渲染
  const tagRender = (props: any) => {
    const { label, value, closable, onClose } = props;
    const accessory = accessories.find((item) => item.id === value);

    return (
      <Tag
        color="blue"
        closable={closable}
        onClose={onClose}
        style={{ marginRight: 3, fontSize: size === 'small' ? '11px' : '12px' }}
      >
        {accessory ? `${accessory.name} (¥${accessory.costPrice.toFixed(2)})` : label}
      </Tag>
    );
  };

  // 生成选项
  const options = accessories.map((accessory) => ({
    value: accessory.id, // 使用UUID作为value
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ flex: 1 }}>
          <div style={{ fontWeight: 500, fontSize: size === 'small' ? '12px' : '14px' }}>
            {accessory.name}
          </div>
          <div
            style={{
              fontSize: size === 'small' ? '11px' : '12px',
              color: '#8c8c8c',
              lineHeight: 1.2,
            }}
          >
            {accessory.articleNumber} | ¥{accessory.costPrice.toFixed(2)}
            {accessory.color && (
              <span style={{ marginLeft: '8px', color: '#666' }}>颜色: {accessory.color}</span>
            )}
            {accessory.size && (
              <span style={{ marginLeft: '8px', color: '#666' }}>尺码: {accessory.size}</span>
            )}
          </div>
        </div>
      </div>
    ),
  }));

  // 如果没有选择供应商，显示提示
  if (!supplierCode) {
    return (
      <Select
        mode="multiple"
        placeholder="请先选择供应商"
        disabled={true}
        style={style}
        className={className}
        size={size}
      />
    );
  }

  return (
    <Select
      mode="multiple"
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      size={size}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      maxTagCount={maxTagCount}
      maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
      tagRender={tagRender}
    />
  );
};

export default SupplierAccessoryMultipleSelector;
