/* UserSearchSelector 组件样式 */
.userSearchSelector {
  width: 100%;
}

/* 选择器输入框样式 */
.userSearchSelector .ant-select-selector {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.userSearchSelector .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 选中项显示样式优化 */
.userSearchSelector .ant-select-selection-item {
  display: flex;
  align-items: center;
  padding: 2px 6px;
  background: rgba(24, 144, 255, 0.06);
  border: 1px solid rgba(24, 144, 255, 0.15);
  border-radius: 3px;
  font-size: 13px;
  line-height: 1.4;
  margin: 1px;
  max-width: calc(100% - 20px);
  transition: all 0.2s ease;
  color: #1890ff;
  font-weight: 500;
}

.userSearchSelector .ant-select-selection-item:hover {
  background: rgba(24, 144, 255, 0.1);
  border-color: rgba(24, 144, 255, 0.25);
}

/* 简化选中项内容显示 */
.userSearchSelector .ant-select-selection-item .ant-select-selection-item-content {
  display: flex;
  align-items: center;
  gap: 4px;
  overflow: hidden;
}

/* 在选中项中隐藏复杂内容，只显示用户名 */
.userSearchSelector .ant-select-selection-item .optionContent .optionIcon {
  display: none;
}

.userSearchSelector .ant-select-selection-item .optionContent .optionDetails {
  display: none;
}

.optionInfo {
  display: flex !important;
  gap: 4px !important;
}

.userSearchSelector .ant-select-selection-item .optionContent .optionName {
  font-weight: 500;
  color: #1890ff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 选中项内容样式 */
.selectedUserContent {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
  overflow: hidden;
}

.selectedUserIcon {
  color: #1890ff;
  font-size: 14px;
  flex-shrink: 0;
}

.selectedUserInfo {
  flex: 1;
  overflow: hidden;
}

.selectedUserName {
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selectedUserCode {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selectedUserBadge {
  font-size: 11px;
  margin-left: 4px;
  padding: 1px 4px;
  border-radius: 2px;
  font-weight: 500;
}

.selectedUserBadge.admin {
  color: #f5222d;
  background: rgba(245, 34, 45, 0.1);
}

.selectedUserBadge.disabled {
  color: #faad14;
  background: rgba(250, 173, 20, 0.1);
}

/* 下拉选项样式 */
.userSearchSelector .ant-select-dropdown {
  border-radius: 8px;
  box-shadow:
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.userSearchSelector .ant-select-item {
  padding: 8px 12px;
  border-radius: 4px;
  margin: 2px 4px;
  transition: all 0.2s ease;
}

.userSearchSelector .ant-select-item:hover {
  background-color: rgba(24, 144, 255, 0.06);
}

.userSearchSelector .ant-select-item-option-selected {
  background-color: rgba(24, 144, 255, 0.1);
  font-weight: 500;
}

/* 选项内容样式 */
.optionContent {
  display: flex;
  align-items: center;
  gap: 8px;
}

.optionIcon {
  color: #1890ff;
  font-size: 16px;
  flex-shrink: 0;
}

.optionInfo {
  flex: 1;
  min-width: 0;
}

.optionName {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.optionDetails {
  font-size: 12px;
  color: #8c8c8c;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.optionBadge {
  font-size: 11px;
  padding: 1px 4px;
  border-radius: 2px;
  font-weight: 500;
  margin-left: 4px;
}

.optionBadge.admin {
  color: #f5222d;
  background: rgba(245, 34, 45, 0.1);
}

.optionBadge.disabled {
  color: #faad14;
  background: rgba(250, 173, 20, 0.1);
}

/* 加载状态样式 */
.userSearchSelector .ant-select-selection-placeholder {
  color: #bfbfbf;
  font-style: italic;
}

/* 清除按钮样式 */
.userSearchSelector .ant-select-clear {
  background: rgba(0, 0, 0, 0.06);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.userSearchSelector .ant-select-clear:hover {
  background: rgba(0, 0, 0, 0.12);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selectedUserInfo {
    max-width: 150px;
  }

  .optionInfo {
    max-width: 200px;
  }
}
