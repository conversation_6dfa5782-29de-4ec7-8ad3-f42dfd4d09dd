import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { debounce } from 'lodash-es';
import { getUserList, getUserDetail } from '@/api/UserApi';
import type { UserProps } from '@/types/user';
import { logError } from '@/utils/errorHandler';
import styles from './UserSearchSelector.module.css';

interface UserSearchSelectorProps {
  value?: string;
  onChange?: (value: string, user: UserProps | null) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const UserSearchSelector: React.FC<UserSearchSelectorProps> = ({
  value,
  onChange,
  placeholder = '请搜索用户',
  disabled = false,
  allowClear = true,
  style,
  className,
}) => {
  const [users, setUsers] = useState<UserProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [currentUser, setCurrentUser] = useState<UserProps | null>(null);

  // 加载当前用户详情
  const loadCurrentUser = async (userCode: string) => {
    if (!userCode) {
      setCurrentUser(null);
      return;
    }

    try {
      const response = await getUserDetail(userCode);
      if (response.code === 200 && response.data) {
        setCurrentUser(response.data);
      } else {
        setCurrentUser(null);
      }
    } catch (error: any) {
      logError('获取用户详情', error);
      setCurrentUser(null);
    }
  };

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setUsers([]);
          return;
        }

        setLoading(true);
        try {
          const response = await getUserList({
            page: 0,
            pageSize: 0,
            search: searchText.trim(),
          });

          if (response.code === 200 && response.data?.users) {
            setUsers(response.data.users);
          } else {
            setUsers([]);
          }
        } catch (error: any) {
          logError('搜索用户', error);
          setUsers([]);
          // 可以选择是否显示错误提示
          // message.error(getErrorMessage(error, '搜索用户失败'));
        } finally {
          setLoading(false);
        }
      }, 300),
    [],
  );

  // 当value变化时加载当前用户详情
  useEffect(() => {
    if (value) {
      loadCurrentUser(value);
    } else {
      setCurrentUser(null);
    }
  }, [value]);

  // 当搜索值变化时触发搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 处理选择
  const handleChange = (selectedValue: string) => {
    const selectedUser = allUsers.find((user) => user.code === selectedValue) || null;
    onChange?.(selectedValue, selectedUser);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setUsers([]);
    onChange?.('', null);
  };

  // 生成选项
  const allUsers = useMemo(() => {
    const userMap = new Map<string, UserProps>();

    // 添加搜索结果中的用户
    users.forEach((user) => {
      userMap.set(user.code, user);
    });

    // 确保当前选中的用户也在选项中
    if (currentUser && !userMap.has(currentUser.code)) {
      userMap.set(currentUser.code, currentUser);
    }

    return Array.from(userMap.values());
  }, [users, currentUser]);

  const options = allUsers.map((user) => ({
    value: user.code,
    label: user.nickname, // 简化为只显示用户昵称
    children: (
      <div className={styles.optionContent}>
        <div className={styles.optionInfo}>
          <div className={styles.optionName}>{user.nickname}</div>
          <div className={styles.optionDetails}>{user.code}</div>
        </div>
      </div>
    ),
  }));

  return (
    <Select
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={`${styles.userSearchSelector} ${className || ''}`}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      optionLabelProp="label"
    />
  );
};

export default UserSearchSelector;
