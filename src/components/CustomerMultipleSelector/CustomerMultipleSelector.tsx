import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin, Tag } from 'antd';
import { debounce } from 'lodash-es';
import { getCustomerList } from '@/api/CustomerApi';
import type { CustomersListProps } from '@/types/customers';
import { logError } from '@/utils/errorHandler';

interface CustomerMultipleSelectorProps {
  value?: string[];
  onChange?: (value: string[], customers: CustomersListProps[]) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  maxTagCount?: number;
}

const CustomerMultipleSelector: React.FC<CustomerMultipleSelectorProps> = ({
  value = [],
  onChange,
  placeholder = '请搜索并选择客户',
  disabled = false,
  allowClear = true,
  style,
  className,
  maxTagCount = 3,
}) => {
  const [customers, setCustomers] = useState<CustomersListProps[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [allCustomers, setAllCustomers] = useState<CustomersListProps[]>([]);

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setCustomers([]);
          return;
        }

        setLoading(true);
        try {
          const response = await getCustomerList({
            page: 0,
            pageSize: 0,
            search: searchText.trim(),
          });

          if (response.code === 200 && response.data?.customers) {
            const newCustomers = response.data.customers;
            setCustomers(newCustomers);

            // 更新所有客户列表，避免重复
            setAllCustomers((prev) => {
              const existingCodes = new Set(prev.map((c) => c.code));
              const uniqueNewCustomers = newCustomers.filter((c) => !existingCodes.has(c.code));
              return [...prev, ...uniqueNewCustomers];
            });
          } else {
            setCustomers([]);
          }
        } catch (error) {
          logError('搜索客户', error);
          setCustomers([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [],
  );

  // 当搜索值变化时触发搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 处理选择
  const handleChange = (selectedValues: string[]) => {
    const selectedCustomers = selectedValues
      .map((code) => allCustomers.find((customer) => customer.code === code))
      .filter(Boolean) as CustomersListProps[];

    onChange?.(selectedValues, selectedCustomers);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setCustomers([]);
    onChange?.([], []);
  };

  // 合并当前搜索结果和已选择的客户
  const displayCustomers = useMemo(() => {
    const customerMap = new Map<string, CustomersListProps>();

    // 添加搜索结果
    customers.forEach((customer) => {
      customerMap.set(customer.code, customer);
    });

    // 添加已选择的客户（确保它们也在选项中显示）
    if (value && value.length > 0) {
      value.forEach((code) => {
        const customer = allCustomers.find((c) => c.code === code);
        if (customer && !customerMap.has(code)) {
          customerMap.set(code, customer);
        }
      });
    }

    return Array.from(customerMap.values());
  }, [customers, allCustomers, value]);

  // 生成选项
  const options = displayCustomers.map((customer) => ({
    value: customer.code,
    label: (
      <div style={{ padding: '4px 0' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: '2px',
          }}
        >
          <div style={{ fontWeight: 500, fontSize: '14px', color: '#262626' }}>{customer.name}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>{customer.pinyinCode}</div>
        </div>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '2px',
          }}
        >
          {customer.phone && (
            <div style={{ fontSize: '12px', color: '#595959' }}>📱 {customer.phone}</div>
          )}
          {customer.managerName && (
            <div style={{ fontSize: '12px', color: '#595959' }}>
              👤 负责人: {customer.managerName}
            </div>
          )}
          {customer.address && (
            <div
              style={{
                fontSize: '12px',
                color: '#8c8c8c',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '300px',
              }}
            >
              📍 {customer.address}
            </div>
          )}
        </div>
      </div>
    ),
  }));

  // 自定义标签渲染
  const tagRender = (props: any) => {
    const { value, closable, onClose } = props;
    const customer = allCustomers.find((c) => c.code === value);

    return (
      <Tag color="purple" closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
        {customer?.name || value}
      </Tag>
    );
  };

  return (
    <Select
      mode="multiple"
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      dropdownStyle={{
        maxHeight: 400,
        overflow: 'auto',
        padding: '4px 0',
      }}
      maxTagCount={maxTagCount}
      maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
      tagRender={tagRender}
      dropdownRender={(menu) => (
        <div
          style={{
            background: '#fff',
            borderRadius: '6px',
            boxShadow:
              '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
          }}
        >
          {menu}
        </div>
      )}
    />
  );
};

export default CustomerMultipleSelector;
