import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { debounce } from 'lodash-es';
import { getAccessoryList } from '@/api/AccessoryApi';
import type { AccessoryListProps } from '@/types/accessories';
import { logError } from '@/utils/errorHandler';

interface AccessorySelectorProps {
  value?: string; // 辅料ID (UUID)
  onChange?: (value: string, accessory?: AccessoryListProps) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

const AccessorySelector: React.FC<AccessorySelectorProps> = ({
  value,
  onChange,
  placeholder = '请选择辅料',
  disabled = false,
  allowClear = true,
  style,
  className,
  size = 'middle',
}) => {
  const [accessories, setAccessories] = useState<AccessoryListProps[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取辅料列表
  const fetchAccessories = async (search = '') => {
    setLoading(true);
    try {
      const response = await getAccessoryList({
        page: 0, // 当page为0时获取所有数据
        pageSize: 0, // 当pageSize为0时获取所有数据
        nameSearch: search || undefined,
      });

      if (response.code === 200 && response.data) {
        setAccessories(response.data.accessories || []);
      }
    } catch (error) {
      logError('获取辅料列表', error);
      setAccessories([]);
    } finally {
      setLoading(false);
    }
  };

  // 防抖搜索
  const debouncedFetchAccessories = useMemo(
    () => debounce((search: string) => fetchAccessories(search), 300),
    [],
  );

  // 初始化数据
  useEffect(() => {
    fetchAccessories();
  }, []);

  // 搜索处理
  const handleSearch = (searchValue: string) => {
    debouncedFetchAccessories(searchValue);
  };

  // 清理防抖函数
  useEffect(() => {
    return () => {
      debouncedFetchAccessories.cancel();
    };
  }, [debouncedFetchAccessories]);

  // 选择变化处理
  const handleChange = (selectedValue: string) => {
    const selectedAccessory = accessories.find((item) => item.id === selectedValue);
    onChange?.(selectedValue, selectedAccessory);
  };

  // 清除处理
  const handleClear = () => {
    onChange?.('');
  };

  // 生成选项
  const options = accessories.map((accessory) => ({
    value: accessory.id, // 使用UUID作为value
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ fontWeight: 500, fontSize: size === 'small' ? '12px' : '14px' }}>
            {accessory.name}
          </div>
          <div
            style={{
              fontSize: size === 'small' ? '11px' : '12px',
              color: '#8c8c8c',
              lineHeight: 1.2,
            }}
          >
            {accessory.articleNumber} | ¥{accessory.costPrice.toFixed(2)}
            {accessory.supplierName && (
              <span style={{ marginLeft: '8px', color: '#666' }}>
                供应商: {accessory.supplierName}
              </span>
            )}
          </div>
        </div>
      </div>
    ),
    searchValue: `${accessory.name} ${accessory.articleNumber} ${accessory.supplierName || ''}`,
  }));

  return (
    <Select
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={(input, option) => {
        const searchValue = option?.searchValue?.toLowerCase() || '';
        return searchValue.includes(input.toLowerCase());
      }}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      size={size}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
      optionLabelProp="label"
    />
  );
};

export default AccessorySelector;
