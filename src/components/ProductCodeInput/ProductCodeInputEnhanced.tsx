import React, { useState, useEffect, useRef } from 'react';
import { Input, Spin, message, Tooltip, Button, Space, Tag } from 'antd';
import { SearchOutlined, CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { getProductOrderInfo } from '@/api/ProductApi';
import type { ProductOrderInfo } from '@/types/product';
import { logError } from '@/utils/errorHandler';

interface ProductCodeInputEnhancedProps {
  value?: string;
  onChange?: (productCode: string, product: ProductOrderInfo | null) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
  onProductSelect?: (product: ProductOrderInfo | null) => void;
  showStockInfo?: boolean; // 是否显示库存信息
  showPriceInfo?: boolean; // 是否显示价格信息
}

const ProductCodeInputEnhanced: React.FC<ProductCodeInputEnhancedProps> = ({
  value,
  onChange,
  placeholder = '请输入商品编码',
  disabled = false,
  style,
  className,
  onProductSelect,
  showStockInfo = true,
  showPriceInfo = true,
}) => {
  const [loading, setLoading] = useState(false);
  const [validationStatus, setValidationStatus] = useState<'success' | 'error' | 'validating' | ''>(
    '',
  );
  const [currentProduct, setCurrentProduct] = useState<ProductOrderInfo | null>(null);
  const inputRef = useRef<any>(null);

  // 验证商品编码
  const validateProductCode = async (code: string) => {
    if (!code.trim()) {
      setValidationStatus('');
      setCurrentProduct(null);
      onProductSelect?.(null);
      return;
    }

    setLoading(true);
    setValidationStatus('validating');

    try {
      const response = await getProductOrderInfo(code.trim());

      if (response.code === 200 && response.data) {
        const product = response.data;
        setValidationStatus('success');
        setCurrentProduct(product);
        onProductSelect?.(product);
        // 通知父组件验证成功的商品
        onChange?.(code, product);
      } else {
        setValidationStatus('error');
        setCurrentProduct(null);
        onProductSelect?.(null);
      }
    } catch (error: any) {
      logError('验证商品编码', error);
      setValidationStatus('error');
      setCurrentProduct(null);
      onProductSelect?.(null);
    } finally {
      setLoading(false);
    }
  };

  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.toUpperCase(); // 商品编码通常是大写

    // 总是通知父组件输入值的变化
    onChange?.(newValue, null);

    // 如果输入为空，清空商品信息
    if (!newValue.trim()) {
      setValidationStatus('');
      setCurrentProduct(null);
      onProductSelect?.(null);
    } else {
      // 如果输入不为空且与当前商品编码不同，清空验证状态
      if (newValue !== currentProduct?.code) {
        setValidationStatus('');
        setCurrentProduct(null);
        onProductSelect?.(null);
      }
    }
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    if (value?.trim()) {
      validateProductCode(value.trim());
    } else {
      message.warning('请输入商品编码');
    }
  };

  // 处理回车键
  const handlePressEnter = () => {
    handleSearch();
  };

  // 当value从外部改变时，如果为空则清空状态
  useEffect(() => {
    if (!value) {
      setValidationStatus('');
      setCurrentProduct(null);
      onProductSelect?.(null);
    }
  }, [value]);

  // 获取输入框状态
  const getInputStatus = () => {
    if (validationStatus === 'error') return 'error';
    if (validationStatus === 'validating') return 'validating';
    return undefined;
  };

  // 获取库存状态颜色
  const getStockStatusColor = (stock: number) => {
    if (stock <= 0) return 'red';
    if (stock <= 10) return 'orange';
    return 'green';
  };

  return (
    <div style={style} className={className}>
      <Space.Compact style={{ width: '100%' }}>
        <Tooltip
          title={
            currentProduct
              ? `${currentProduct.name} - ${currentProduct.brandName}`
              : undefined
          }
          placement="top"
        >
          <Input
            ref={inputRef}
            value={value}
            onChange={handleChange}
            onPressEnter={handlePressEnter}
            placeholder={placeholder}
            disabled={disabled}
            status={getInputStatus()}
            suffix={loading ? <Spin size="small" /> : undefined}
            style={{
              borderColor:
                validationStatus === 'success'
                  ? '#52c41a'
                  : validationStatus === 'error'
                    ? '#ff4d4f'
                    : undefined,
            }}
          />
        </Tooltip>
        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={handleSearch}
          disabled={disabled || loading}
          loading={loading}
        >
          搜索
        </Button>
      </Space.Compact>

      {validationStatus === 'success' && currentProduct && (
        <div
          style={{
            fontSize: '12px',
            color: '#52c41a',
            marginTop: '4px',
            padding: '8px',
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '4px',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px', marginBottom: '4px' }}>
            <CheckCircleOutlined />
            <strong>{currentProduct.name}</strong>
            <Tag color="blue">{currentProduct.brandName}</Tag>
            <Tag color="purple">{currentProduct.supplierName}</Tag>
          </div>
          
          {showPriceInfo && (
            <div style={{ marginBottom: '4px' }}>
              <InfoCircleOutlined style={{ marginRight: '4px' }} />
              <span>价格: </span>
              <Tag color="green">零售 ¥{currentProduct.retailPrice.toFixed(2)}</Tag>
              <Tag color="blue">预订 ¥{currentProduct.preOrderPrice.toFixed(2)}</Tag>
              <Tag color="orange">补货 ¥{currentProduct.restockPrice.toFixed(2)}</Tag>
              <Tag color="red">现货 ¥{currentProduct.spotPrice.toFixed(2)}</Tag>
            </div>
          )}
          
          {showStockInfo && (
            <div>
              <InfoCircleOutlined style={{ marginRight: '4px' }} />
              <span>库存: </span>
              <Tag color={getStockStatusColor(currentProduct.totalAvailableStock)}>
                可用 {currentProduct.totalAvailableStock}
              </Tag>
              <Tag color="default">预留 {currentProduct.totalReservedStock}</Tag>
              <span style={{ fontSize: '11px', color: '#666', marginLeft: '8px' }}>
                {currentProduct.colorCombinations.length} 个颜色组合
              </span>
            </div>
          )}
        </div>
      )}

      {validationStatus === 'error' && (
        <div
          style={{
            fontSize: '12px',
            color: '#ff4d4f',
            marginTop: '4px',
            padding: '2px 4px',
            backgroundColor: '#fff2f0',
            border: '1px solid #ffccc7',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
          }}
        >
          <CloseCircleOutlined />
          商品编码不存在或无效
        </div>
      )}
    </div>
  );
};

export default ProductCodeInputEnhanced;
