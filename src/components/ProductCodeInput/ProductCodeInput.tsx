import React, { useState, useEffect, useRef } from 'react';
import { Input, Spin, message, Tooltip, Button, Space } from 'antd';
import { SearchOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { getProductList } from '@/api/ProductApi';
import type { ProductListItem, ProductListParams } from '@/types/product';
import { logError } from '@/utils/errorHandler';

interface ProductCodeInputProps {
  value?: string;
  onChange?: (productCode: string, product: ProductListItem | null) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
  brandCode?: string;
  supplierCode?: string;
  categoryCode?: string;
  onProductSelect?: (product: ProductListItem | null) => void;
}

const ProductCodeInput: React.FC<ProductCodeInputProps> = ({
  value,
  onChange,
  placeholder = '请输入商品编码',
  disabled = false,
  style,
  className,
  brandCode,
  supplierCode,
  categoryCode,
  onProductSelect,
}) => {
  const [loading, setLoading] = useState(false);
  const [validationStatus, setValidationStatus] = useState<'success' | 'error' | 'validating' | ''>(
    '',
  );
  const [currentProduct, setCurrentProduct] = useState<ProductListItem | null>(null);
  const inputRef = useRef<any>(null);

  // 验证商品编码
  const validateProductCode = async (code: string) => {
    if (!code.trim()) {
      setValidationStatus('');
      setCurrentProduct(null);
      onProductSelect?.(null);
      return;
    }

    setLoading(true);
    setValidationStatus('validating');

    try {
      const params: ProductListParams = {
        page: 1,
        pageSize: 1,
        codeSearch: code.trim(),
        brandCode,
        supplierCode,
        categoryCode,
      };

      const response = await getProductList(params);

      if (response.code === 200 && response.data?.products?.length > 0) {
        const product = response.data.products[0];
        // 精确匹配商品编码
        if (product.code.toLowerCase() === code.trim().toLowerCase()) {
          setValidationStatus('success');
          setCurrentProduct(product);
          onProductSelect?.(product);
          // 通知父组件验证成功的商品
          onChange?.(code, product);
        } else {
          setValidationStatus('error');
          setCurrentProduct(null);
          onProductSelect?.(null);
        }
      } else {
        setValidationStatus('error');
        setCurrentProduct(null);
        onProductSelect?.(null);
      }
    } catch (error: any) {
      logError('验证商品编码', error);
      setValidationStatus('error');
      setCurrentProduct(null);
      onProductSelect?.(null);
    } finally {
      setLoading(false);
    }
  };

  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.toUpperCase(); // 商品编码通常是大写

    // 总是通知父组件输入值的变化
    onChange?.(newValue, null);

    // 如果输入为空，清空商品信息
    if (!newValue.trim()) {
      setValidationStatus('');
      setCurrentProduct(null);
      onProductSelect?.(null);
    } else {
      // 如果输入不为空且与当前商品编码不同，清空验证状态
      if (newValue !== currentProduct?.code) {
        setValidationStatus('');
        setCurrentProduct(null);
        onProductSelect?.(null);
      }
    }
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    if (value?.trim()) {
      validateProductCode(value.trim());
    } else {
      message.warning('请输入商品编码');
    }
  };

  // 处理回车键
  const handlePressEnter = () => {
    handleSearch();
  };

  // 当value从外部改变时，如果为空则清空状态
  useEffect(() => {
    if (!value) {
      setValidationStatus('');
      setCurrentProduct(null);
      onProductSelect?.(null);
    }
  }, [value]);

  // 获取输入框状态
  const getInputStatus = () => {
    if (validationStatus === 'error') return 'error';
    if (validationStatus === 'validating') return 'validating';
    return undefined;
  };

  return (
    <div style={style} className={className}>
      <Space.Compact style={{ width: '100%' }}>
        <Tooltip
          title={
            currentProduct
              ? `${currentProduct.name} - ¥${currentProduct.retailPrice.toFixed(2)}`
              : undefined
          }
          placement="top"
        >
          <Input
            ref={inputRef}
            value={value}
            onChange={handleChange}
            onPressEnter={handlePressEnter}
            placeholder={placeholder}
            disabled={disabled}
            status={getInputStatus()}
            suffix={loading ? <Spin size="small" /> : undefined}
            style={{
              borderColor:
                validationStatus === 'success'
                  ? '#52c41a'
                  : validationStatus === 'error'
                    ? '#ff4d4f'
                    : undefined,
            }}
          />
        </Tooltip>
        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={handleSearch}
          disabled={disabled || loading}
          loading={loading}
        >
          搜索
        </Button>
      </Space.Compact>

      {validationStatus === 'success' && currentProduct && (
        <div
          style={{
            fontSize: '12px',
            color: '#52c41a',
            marginTop: '4px',
            padding: '2px 4px',
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
          }}
        >
          <CheckCircleOutlined />
          {currentProduct.name} - {currentProduct.brandName} - ¥
          {currentProduct.retailPrice.toFixed(2)}
        </div>
      )}

      {validationStatus === 'error' && (
        <div
          style={{
            fontSize: '12px',
            color: '#ff4d4f',
            marginTop: '4px',
            padding: '2px 4px',
            backgroundColor: '#fff2f0',
            border: '1px solid #ffccc7',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
          }}
        >
          <CloseCircleOutlined />
          商品编码不存在或无效
        </div>
      )}
    </div>
  );
};

export default ProductCodeInput;
