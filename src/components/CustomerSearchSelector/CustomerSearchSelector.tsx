import React, { useState, useEffect, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { debounce } from 'lodash-es';
import { searchCustomers, getCustomerDetail } from '@/api/CustomerApi';
import type { CustomerSearchItem } from '@/types/customers';
import { logError } from '@/utils/errorHandler';

interface CustomerSearchSelectorProps {
  value?: string;
  onChange?: (value: string, customer: CustomerSearchItem | null) => void;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const CustomerSearchSelector: React.FC<CustomerSearchSelectorProps> = ({
  value,
  onChange,
  placeholder = '请搜索客户名称、手机号或拼音码',
  disabled = false,
  allowClear = true,
  style,
  className,
}) => {
  const [customers, setCustomers] = useState<CustomerSearchItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [currentCustomer, setCurrentCustomer] = useState<CustomerSearchItem | null>(null);

  // 加载当前客户详情
  const loadCurrentCustomer = async (customerCode: string) => {
    if (!customerCode) {
      setCurrentCustomer(null);
      return;
    }

    try {
      const response = await getCustomerDetail(customerCode);
      if (response.code === 200 && response.data) {
        // 将详情数据转换为搜索项格式
        const customerSearchItem: CustomerSearchItem = {
          code: response.data.code,
          name: response.data.name,
          phone: response.data.phone || '',
          managerCode: response.data.managerCode,
          managerName: response.data.managerName,
        };
        setCurrentCustomer(customerSearchItem);
      } else {
        setCurrentCustomer(null);
      }
    } catch (error: any) {
      logError('获取客户详情', error);
      setCurrentCustomer(null);
    }
  };

  // 防抖搜索函数
  const debouncedSearch = useMemo(
    () =>
      debounce(async (searchText: string) => {
        if (!searchText.trim()) {
          setCustomers([]);
          return;
        }

        setLoading(true);
        try {
          const response = await searchCustomers(searchText.trim());

          if (response.code === 200 && response.data) {
            setCustomers(response.data);
          } else {
            setCustomers([]);
          }
        } catch (error: any) {
          logError('搜索客户', error);
          setCustomers([]);
        } finally {
          setLoading(false);
        }
      }, 300),
    [],
  );

  // 当value变化时加载当前客户详情
  useEffect(() => {
    if (value) {
      loadCurrentCustomer(value);
    } else {
      setCurrentCustomer(null);
    }
  }, [value]);

  // 当搜索值变化时触发搜索
  useEffect(() => {
    debouncedSearch(searchValue);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchValue, debouncedSearch]);

  // 处理搜索输入
  const handleSearch = (searchText: string) => {
    setSearchValue(searchText);
  };

  // 处理选择
  const handleChange = (selectedValue: string) => {
    const selectedCustomer =
      allCustomers.find((customer) => customer.code === selectedValue) || null;
    onChange?.(selectedValue, selectedCustomer);
  };

  // 处理清空
  const handleClear = () => {
    setSearchValue('');
    setCustomers([]);
    onChange?.('', null);
  };

  // 生成选项
  const allCustomers = useMemo(() => {
    const customerMap = new Map<string, CustomerSearchItem>();

    // 添加搜索结果中的客户
    customers.forEach((customer) => {
      customerMap.set(customer.code, customer);
    });

    // 确保当前选中的客户也在选项中
    if (currentCustomer && !customerMap.has(currentCustomer.code)) {
      customerMap.set(currentCustomer.code, currentCustomer);
    }

    return Array.from(customerMap.values());
  }, [customers, currentCustomer]);

  const options = allCustomers.map((customer) => ({
    value: customer.code,
    title: customer.name, // 用于选中后显示
    label: (
      <div style={{ padding: '4px 0' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: '2px',
          }}
        >
          <div style={{ fontWeight: 500, fontSize: '14px', color: '#262626' }}>{customer.name}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>{customer.code}</div>
        </div>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '2px',
          }}
        >
          {customer.phone && (
            <div style={{ fontSize: '12px', color: '#595959' }}>📱 {customer.phone}</div>
          )}
          {customer.managerName && (
            <div style={{ fontSize: '12px', color: '#595959' }}>
              👤 负责人: {customer.managerName}
            </div>
          )}
        </div>
      </div>
    ),
  }));

  return (
    <Select
      showSearch
      value={value}
      placeholder={placeholder}
      defaultActiveFirstOption={false}
      suffixIcon={loading ? <Spin size="small" /> : undefined}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      onClear={handleClear}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      options={options}
      disabled={disabled}
      allowClear={allowClear}
      style={style}
      className={className}
      dropdownStyle={{
        maxHeight: 400,
        overflow: 'auto',
        padding: '4px 0',
      }}
      optionLabelProp="title"
      dropdownRender={(menu) => (
        <div
          style={{
            background: '#fff',
            borderRadius: '6px',
            boxShadow:
              '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
          }}
        >
          {menu}
        </div>
      )}
    />
  );
};

export default CustomerSearchSelector;
