import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Tab {
  id: string;
  title: string;
  path: string;
  isPinned: boolean;
  closable: boolean;
}

interface TabsState {
  tabs: Tab[];
  activeTabId: string | null;
  addTab: (tab: Omit<Tab, 'isPinned' | 'id'> & { id?: string }) => void;
  removeTab: (id: string) => void;
  setActiveTab: (id: string) => void;
  pinTab: (id: string) => void;
  unpinTab: (id: string) => void;
  getTabById: (id: string) => Tab | undefined;
}

const MAX_TABS = 10;
const MAX_PINNED_TABS = 5;

const useTabsStore = create<TabsState>()(
  persist(
    (set, get) => ({
      tabs: [
        {
          id: 'home',
          title: '首页',
          path: '/',
          isPinned: true,
          closable: false,
        },
      ],
      activeTabId: 'home',

      addTab: (newTab) => {
        const { tabs } = get();
        const id = newTab.id || `tab-${Date.now()}`;

        // 检查是否已存在相同 path 的选项卡
        const existingTabIndex = tabs.findIndex((tab) => tab.path === newTab.path);
        if (existingTabIndex !== -1) {
          // 如果已存在，只需激活它
          set({ activeTabId: tabs[existingTabIndex].id });
          return;
        }

        // 创建新选项卡
        const tab: Tab = {
          id,
          title: newTab.title,
          path: newTab.path,
          isPinned: false,
          closable: newTab.closable !== false,
        };

        let newTabs = [...tabs, tab];

        // 如果选项卡总数超过最大值，移除最早的非固定选项卡
        if (newTabs.length > MAX_TABS) {
          const nonPinnedTabs = newTabs.filter((tab) => !tab.isPinned);
          if (nonPinnedTabs.length > 0) {
            const oldestNonPinnedTab = nonPinnedTabs[0];
            newTabs = newTabs.filter((tab) => tab.id !== oldestNonPinnedTab.id);
          }
        }

        set({ tabs: newTabs, activeTabId: id });
      },

      removeTab: (id) => {
        const { tabs, activeTabId } = get();
        const tabToRemove = tabs.find((tab) => tab.id === id);

        // 如果选项卡不存在或不可关闭，则返回
        if (!tabToRemove || !tabToRemove.closable) {
          return;
        }

        const newTabs = tabs.filter((tab) => tab.id !== id);

        // 如果关闭的是当前激活的选项卡，则激活下一个选项卡
        let newActiveTabId = activeTabId;
        if (activeTabId === id) {
          const index = tabs.findIndex((tab) => tab.id === id);
          if (index > 0 && index < tabs.length) {
            // 优先选择右侧选项卡，如果没有则选择左侧选项卡
            newActiveTabId = tabs[index + 1]?.id || tabs[index - 1]?.id;
          } else if (index === 0 && tabs.length > 1) {
            newActiveTabId = tabs[1].id;
          } else {
            newActiveTabId = null;
          }
        }

        set({ tabs: newTabs, activeTabId: newActiveTabId });
      },

      setActiveTab: (id) => {
        set({ activeTabId: id });
      },

      pinTab: (id) => {
        const { tabs } = get();
        const pinnedTabs = tabs.filter((tab) => tab.isPinned);

        // 如果已经达到最大固定选项卡数量，则返回
        if (pinnedTabs.length >= MAX_PINNED_TABS) {
          return;
        }

        const newTabs = tabs.map((tab) =>
          tab.id === id ? { ...tab, isPinned: true } : tab
        );

        // 重新排序，将固定选项卡放在前面
        const pinnedNewTabs = newTabs.filter((tab) => tab.isPinned);
        const unpinnedNewTabs = newTabs.filter((tab) => !tab.isPinned);
        
        set({ tabs: [...pinnedNewTabs, ...unpinnedNewTabs] });
      },

      unpinTab: (id) => {
        const { tabs } = get();
        const newTabs = tabs.map((tab) =>
          tab.id === id ? { ...tab, isPinned: false } : tab
        );
        set({ tabs: newTabs });
      },

      getTabById: (id) => {
        return get().tabs.find((tab) => tab.id === id);
      },
    }),
    {
      name: 'tabs-storage',
      partialize: (state) => ({ 
        tabs: state.tabs,
        activeTabId: state.activeTabId 
      }),
    }
  )
);

export default useTabsStore;
