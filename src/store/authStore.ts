import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { getUser, getToken, clearToken } from '@/utils/tokenManager';

// 权限类型定义
export interface Permission {
  read: boolean;
  create: boolean;
  delete: boolean;
  export: boolean;
  import: boolean;
  update: boolean;
}

// 用户信息类型
export interface User {
  code: string;
  nickname: string;
  routePermissions?: {
    [key: string]: Permission;
  };
  isSuperAdmin: boolean;
  isCompanyAdmin?: boolean;
  companyCode?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  bankAccountName?: string;
  bankAccountNumber?: string;
  isDeleted?: boolean;
  deletedAt?: string | null;
}

// 权限状态接口
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;

  // Actions
  setAuth: (token: string, user: User) => void;
  clearAuth: () => void;
  updateUser: (user: User) => void;
  checkAuth: () => void;

  // 权限检查方法
  hasRoutePermission: (route: string, permission?: keyof Permission) => boolean;
  hasAnyRoutePermission: (route: string) => boolean;
  canAccessRoute: (route: string) => boolean;
  getRoutePermissions: (route: string) => Permission | null;
}

// 路由权限映射 - 将前端路由映射到后端权限路由
const ROUTE_PERMISSION_MAP: { [key: string]: string } = {
  // 系统管理
  '/system/users': '/system/users',
  '/admin/memos': '/admin/memos',

  // 公司管理
  '/company/management': '/company/management',
  '/company/supplier': '/company/supplier',
  '/company/supplier-archives': '/company/supplier-archives',
  '/company/customer': '/company/customer',
  '/customer/profiles': '/customer/profiles',

  // 产品管理
  '/product/brand': '/product/brand',
  '/product/category': '/product/category',
  '/product/color': '/product/color',
  '/product/auxiliary': '/product/auxiliary',
  '/product/archive': '/product/archive',
  '/product/sku': '/product/sku',
  '/product/inventory': '/product/inventory',
  '/product/skus-inventory': '/product/skus-inventory',

  // 采购管理
  '/purchase/purchase-contracts': '/purchase/purchase-contracts',
  '/purchase/product-orders': '/purchase/product-orders',

  // 统计分析
  '/statistics/demand-purchase': '/statistics/demand-purchase',

  // 供应商管理
  '/supplier/purchase-overview': '/supplier/purchase-overview',

  // 财务管理（会计）
  '/financial/fixed-assets': '/financial/fixed-assets',
  '/financial/operating-assets': '/financial/operating-assets',
  '/financial/rental-assets': '/financial/rental-assets',
  '/financial/rd-costs': '/financial/rd-costs',
  '/financial/income-assets': '/financial/income-assets',
  '/financial/expense-assets': '/financial/expense-assets',
};

// 获取路由对应的权限键
const getPermissionKey = (route: string): string => {
  // 移除路由参数和查询参数，但保留完整的路径结构
  let cleanRoute = route.split('?')[0];

  // 移除路径参数（如 :id）
  cleanRoute = cleanRoute.replace(/\/:[^\/]+/g, '');

  // 确保路由以斜杠开头
  if (!cleanRoute.startsWith('/')) {
    cleanRoute = `/${cleanRoute}`;
  }

  console.log('getPermissionKey - 路由转换:', {
    originalRoute: route,
    cleanRoute,
    mappedRoute: ROUTE_PERMISSION_MAP[cleanRoute],
  });

  // 首先尝试从映射中获取
  if (ROUTE_PERMISSION_MAP[cleanRoute]) {
    return ROUTE_PERMISSION_MAP[cleanRoute];
  }

  // 如果映射中没有，直接返回清理后的路由（保持斜杠格式）
  // 用户权限数据中存储的是带斜杠的格式，所以这里也要保持一致
  return cleanRoute;
};

const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,

      setAuth: (token: string, user: User) => {
        set({
          token,
          user,
          isAuthenticated: true,
        });
      },

      clearAuth: () => {
        clearToken();
        set({
          token: null,
          user: null,
          isAuthenticated: false,
        });
      },

      updateUser: (user: User) => {
        set({ user });
      },

      checkAuth: () => {
        const token = getToken();
        const user = getUser();

        if (token && user) {
          set({
            token,
            user,
            isAuthenticated: true,
          });
        } else {
          set({
            token: null,
            user: null,
            isAuthenticated: false,
          });
        }
      },

      hasRoutePermission: (route: string, permission?: keyof Permission): boolean => {
        const { user } = get();

        // 如果用户未登录，返回 false
        if (!user) return false;

        // 超级管理员拥有所有权限
        if (user.isSuperAdmin) return true;

        // 获取权限键
        const permissionKey = getPermissionKey(route);
        const routePermissions = user.routePermissions?.[permissionKey];

        // 如果没有该路由的权限配置，返回 false
        if (!routePermissions) return false;

        // 如果指定了具体权限，检查该权限
        if (permission) {
          return routePermissions[permission] === true;
        }

        // 如果没有指定权限，检查是否有任何权限
        return Object.values(routePermissions).some(Boolean);
      },

      hasAnyRoutePermission: (route: string): boolean => {
        const { user } = get();

        if (!user) return false;
        if (user.isSuperAdmin) return true;

        const permissionKey = getPermissionKey(route);
        const routePermissions = user.routePermissions?.[permissionKey];

        console.log('authStore.hasAnyRoutePermission - 详细检查:', {
          route,
          permissionKey,
          routePermissions,
          allUserPermissions: user.routePermissions,
        });

        if (!routePermissions) {
          console.log('authStore.hasAnyRoutePermission - 没有找到路由权限配置');
          return false;
        }

        const hasAny = Object.values(routePermissions).some(Boolean);
        console.log('authStore.hasAnyRoutePermission - 是否有任何权限:', hasAny);
        return hasAny;
      },

      canAccessRoute: (route: string): boolean => {
        const { user } = get();

        console.log('authStore.canAccessRoute - 检查路由:', route, {
          user: user ? { code: user.code, isSuperAdmin: user.isSuperAdmin } : null,
          routePermissions: user?.routePermissions,
        });

        // 如果用户未登录，不能访问任何路由
        if (!user) {
          console.log('authStore.canAccessRoute - 用户未登录');
          return false;
        }

        // 超级管理员可以访问所有路由
        if (user.isSuperAdmin) {
          console.log('authStore.canAccessRoute - 超级管理员，允许访问');
          return true;
        }

        // 首页所有人都可以访问
        if (route === '/' || route === '/home') {
          console.log('authStore.canAccessRoute - 首页，允许访问');
          return true;
        }

        // 检查是否有该路由的任何权限
        const hasPermission = get().hasAnyRoutePermission(route);
        console.log('authStore.canAccessRoute - 权限检查结果:', hasPermission);
        return hasPermission;
      },

      getRoutePermissions: (route: string): Permission | null => {
        const { user } = get();

        if (!user) return null;

        const permissionKey = getPermissionKey(route);
        return user.routePermissions?.[permissionKey] || null;
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    },
  ),
);

export default useAuthStore;
