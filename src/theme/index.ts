import { theme } from 'antd';
import type { ThemeConfig } from 'antd';

// 定义主题颜色
export const themeColors = {
  // 主色调
  primary: '#1890ff', // 主色调 - 蓝色
  success: '#52c41a', // 成功色 - 绿色
  warning: '#faad14', // 警告色 - 黄色
  error: '#f5222d', // 错误色 - 红色
  link: '#1890ff', // 链接色 - 蓝色

  // 文本颜色
  heading: '#262626', // 标题色 - 深灰色
  text: '#595959', // 正文色 - 灰色
  textSecondary: '#8c8c8c', // 次要文字 - 浅灰色
  disabled: '#bfbfbf', // 失效色 - 浅灰色

  // 边框和背景
  border: '#d9d9d9', // 边框色 - 浅灰色
  background: '#f0f2f5', // 背景色 - 浅蓝灰色
  headerBackground: '#ffffff', // 头部背景 - 白色
  siderBackground: '#ffffff', // 侧边栏背景 - 白色
  cardBackground: '#ffffff', // 卡片背景 - 白色

  // 暗色主题颜色
  darkBackground: '#141414', // 暗色背景
  darkComponentBackground: '#1f1f1f', // 暗色组件背景
  darkBorder: '#434343', // 暗色边框
  darkText: 'rgba(255, 255, 255, 0.85)', // 暗色文本
  darkTextSecondary: 'rgba(255, 255, 255, 0.45)', // 暗色次要文本
};

// 定义主题配置
export const themeConfig: ThemeConfig = {
  token: {
    colorPrimary: themeColors.primary,
    colorSuccess: themeColors.success,
    colorWarning: themeColors.warning,
    colorError: themeColors.error,
    colorLink: themeColors.link,
    colorTextHeading: themeColors.heading,
    colorText: themeColors.text,
    colorTextSecondary: themeColors.textSecondary,
    colorTextDisabled: themeColors.disabled,
    colorBorder: themeColors.border,
    borderRadius: 4,
  },
  components: {
    Layout: {
      headerBg: themeColors.headerBackground,
      siderBg: themeColors.siderBackground,
    },
    Menu: {
      itemBg: themeColors.siderBackground,
      itemSelectedBg: 'rgba(24, 144, 255, 0.1)',
      itemHoverBg: 'rgba(24, 144, 255, 0.06)',
    },
    Card: {
      colorBgContainer: themeColors.cardBackground,
    },
    Table: {
      headerBg: '#f0f5ff', // 表头背景色 - 浅蓝色
    },
  },
  algorithm: theme.defaultAlgorithm, // 默认使用亮色主题算法
};

export default themeConfig;
