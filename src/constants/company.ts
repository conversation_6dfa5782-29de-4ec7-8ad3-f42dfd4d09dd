// 公司配置常量
export const COMPANY_CONFIG = {
  '01': {
    code: '01',
    name: '广州',
    fullName: '广州总公司',
  },
  '02': {
    code: '02',
    name: '杭州',
    fullName: '杭州分公司',
  },
  '03': {
    code: '03',
    name: '金宝',
    fullName: '金宝分公司',
  },
} as const;

// 公司编码类型
export type CompanyCode = keyof typeof COMPANY_CONFIG;

// 获取公司名称
export const getCompanyName = (companyCode: string): string => {
  return COMPANY_CONFIG[companyCode as CompanyCode]?.name || companyCode;
};

// 获取公司全名
export const getCompanyFullName = (companyCode: string): string => {
  return COMPANY_CONFIG[companyCode as CompanyCode]?.fullName || companyCode;
};

// 获取所有公司选项（用于下拉菜单）
export const getCompanyOptions = () => {
  return Object.values(COMPANY_CONFIG).map((company) => ({
    label: company.name,
    value: company.code,
  }));
};

// 获取所有公司选项（包含全名）
export const getCompanyFullNameOptions = () => {
  return Object.values(COMPANY_CONFIG).map((company) => ({
    label: company.fullName,
    value: company.code,
  }));
};
