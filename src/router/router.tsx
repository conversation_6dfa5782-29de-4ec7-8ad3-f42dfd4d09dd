import { createBrowserRouter, RouteObject } from 'react-router-dom';
import BasicLayout from '@/components/layout/BasicLayout';
import NewLogin from '@/pages/login/NewLogin';
import AuthGuard from '@/components/auth/AuthGuard';
import SuperAdminGuard from '@/components/auth/SuperAdminGuard';
import RoutePermissionGuard from '@/components/auth/RoutePermissionGuard';
import UserManagement from '@/pages/system/UserManagement';
import UserForm from '@/pages/system/UserForm';
import CompanyManagement from '@/pages/company/CompanyManagement';
import SupplierManagement from '@/pages/company/SupplierManagement';
import SupplierArchives from '@/pages/company/SupplierArchives';
import CustomerManagement from '@/pages/company/CustomerManagement';
import BrandManagement from '@/pages/product/BrandManagement';
import CategoryManagement from '@/pages/product/CategoryManagement';
import ColorManagement from '@/pages/product/ColorManagement';
import AccessoryManagement from '@/pages/product/AccessoryManagement';
import SkuManagement from '@/pages/product/SkuManagement';
import SkuForm from '@/pages/product/SkuForm';
import SkusInventoryManagement from '@/pages/product/SkusInventoryManagement';
import SkusInventoryForm from '@/pages/product/SkusInventoryForm';
import ProductInventoryDetail from '@/pages/product/ProductInventoryDetail';
import PurchaseContractsManagement from '@/pages/product/PurchaseContractsManagement';
import PurchaseContractDetail from '@/pages/product/PurchaseContractDetail';
import PurchaseContractView from '@/pages/product/PurchaseContractView';
import FixedAssetsManagement from '@/pages/financial/FixedAssetsManagement';
import OperatingAssetsManagement from '@/pages/financial/OperatingAssetsManagement';
import RentalAssetsManagement from '@/pages/financial/RentalAssetsManagement';
import RDCostManagement from '@/pages/financial/RDCostManagement';
import IncomeAssetsManagement from '@/pages/financial/IncomeAssetsManagement';
import ExpenseAssetsManagement from '@/pages/financial/ExpenseAssetsManagement';
import MemoManagement from '@/pages/memo/MemoManagement';
import MemoForm from '@/pages/memo/MemoForm';
import ProductPurchaseOrderManagement from '@/pages/purchase/ProductPurchaseOrderManagement';
import ProductPurchaseOrderKanban from '@/pages/purchase/ProductPurchaseOrderKanban';
import ProductPurchaseOrderDetail from '@/pages/purchase/ProductPurchaseOrderDetail';
import DemandOrderMerge from '@/pages/purchase/DemandOrderMerge';
import DemandPurchaseStatistics from '@/pages/statistics/DemandPurchaseStatistics';
import DemandOrderStatistics from '@/pages/statistics/DemandOrderStatistics';
import PurchaseOrderStatistics from '@/pages/statistics/PurchaseOrderStatistics';
import ProductDemandStatistics from '@/pages/statistics/ProductDemandStatistics';
import SupplierPurchaseOverview from '@/pages/supplier/SupplierPurchaseOverview';
import SupplierPurchaseRanking from '@/pages/supplier/SupplierPurchaseRanking';
import SupplierPerformanceComparison from '@/pages/supplier/SupplierPerformanceComparison';
import SupplierPurchaseDetail from '@/pages/supplier/SupplierPurchaseDetail';
import SalesDemandOrderManagement from '@/pages/sales/SalesDemandOrderManagement';
import SalesDemandOrderForm from '@/pages/sales/SalesDemandOrderForm';
import SalesDemandOrderDetail from '@/pages/sales/SalesDemandOrderDetail';
import SalesOrderManagement from '@/pages/sales/SalesOrderManagement';
import SalesOrderDetail from '@/pages/sales/SalesOrderDetail';
import SalesReturnManagement from '@/pages/sales/SalesReturnManagement';
import SalesReturnDetail from '@/pages/sales/SalesReturnDetail';
import CommissionContractsList from '@/pages/commission/CommissionContractsList';
import CommissionContractsForm from '@/pages/commission/CommissionContractsForm';
import PermissionTest from '@/pages/test/PermissionTest';
import MenuDebug from '@/pages/test/MenuDebug';
import QuickTest from '@/pages/test/QuickTest';
import PaySalaryTest from '@/pages/test/PaySalaryTest';

import Dashboard from '@/pages/dashboard/Dashboard';
import { NotFound, Forbidden, ServerError } from '@/pages/error';

// 路由配置
const routes: RouteObject[] = [
  {
    path: '/login',
    element: <NewLogin />,
  },
  {
    path: '/',
    element: (
      <AuthGuard>
        <BasicLayout />
      </AuthGuard>
    ),
    children: [
      {
        index: true,
        element: <Dashboard />,
      },
      {
        path: 'system/users',
        element: (
          <SuperAdminGuard>
            <UserManagement />
          </SuperAdminGuard>
        ),
      },
      {
        path: 'system/users/add',
        element: (
          <SuperAdminGuard>
            <UserForm />
          </SuperAdminGuard>
        ),
      },
      {
        path: 'system/users/edit/:code',
        element: (
          <SuperAdminGuard>
            <UserForm />
          </SuperAdminGuard>
        ),
      },
      {
        path: 'system/users/view/:code',
        element: (
          <SuperAdminGuard>
            <UserForm />
          </SuperAdminGuard>
        ),
      },
      {
        path: 'company/management',
        element: (
          <RoutePermissionGuard requiredRoute="/company/management">
            <CompanyManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'company/supplier',
        element: (
          <RoutePermissionGuard requiredRoute="/company/supplier">
            <SupplierManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'company/supplier-archives',
        element: (
          <RoutePermissionGuard requiredRoute="/company/supplier-archives">
            <SupplierArchives />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'company/customer',
        element: (
          <RoutePermissionGuard requiredRoute="/company/customer">
            <CustomerManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/brand',
        element: (
          <RoutePermissionGuard requiredRoute="/product/brand">
            <BrandManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/category',
        element: (
          <RoutePermissionGuard requiredRoute="/product/category">
            <CategoryManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/color',
        element: (
          <RoutePermissionGuard requiredRoute="/product/color">
            <ColorManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/auxiliary',
        element: (
          <RoutePermissionGuard requiredRoute="/product/auxiliary">
            <AccessoryManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/sku',
        element: (
          <RoutePermissionGuard requiredRoute="/product/sku">
            <SkuManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/sku/add',
        element: (
          <RoutePermissionGuard requiredRoute="/product/sku">
            <SkuForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/sku/edit/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/product/sku">
            <SkuForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/sku/view/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/product/sku">
            <SkuForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/skus-inventory',
        element: (
          <RoutePermissionGuard requiredRoute="/product/skus-inventory">
            <SkusInventoryManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/skus-inventory/add',
        element: (
          <RoutePermissionGuard requiredRoute="/product/skus-inventory">
            <SkusInventoryForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/skus-inventory/edit/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/product/skus-inventory">
            <SkusInventoryForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/skus-inventory/view/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/product/skus-inventory">
            <SkusInventoryForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'product/inventory/detail/:productCode',
        element: (
          <RoutePermissionGuard requiredRoute="/product/skus-inventory">
            <ProductInventoryDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'purchase/purchase-contracts',
        element: (
          <RoutePermissionGuard requiredRoute="/purchase/purchase-contracts">
            <PurchaseContractsManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'purchase/purchase-contracts/edit/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/purchase/purchase-contracts">
            <PurchaseContractDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'purchase/purchase-contracts/add',
        element: (
          <RoutePermissionGuard requiredRoute="/purchase/purchase-contracts">
            <PurchaseContractDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'purchase/purchase-contracts/view/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/purchase/purchase-contracts">
            <PurchaseContractView />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'purchase/product-orders',
        element: (
          <RoutePermissionGuard requiredRoute="/purchase/product-orders">
            <ProductPurchaseOrderManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'purchase/product-orders/kanban',
        element: (
          <RoutePermissionGuard requiredRoute="/purchase/product-orders">
            <ProductPurchaseOrderKanban />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'purchase/product-orders/merge',
        element: (
          <RoutePermissionGuard requiredRoute="/purchase/product-orders">
            <DemandOrderMerge />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'purchase/product-orders/view/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/purchase/product-orders">
            <ProductPurchaseOrderDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/demand-orders',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/demand-orders">
            <SalesDemandOrderManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/demand-orders/create',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/demand-orders">
            <SalesDemandOrderForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/demand-orders/edit/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/demand-orders">
            <SalesDemandOrderForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/demand-orders/view/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/demand-orders">
            <SalesDemandOrderDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/orders',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/orders">
            <SalesOrderManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/orders/add',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/orders">
            <SalesOrderDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/orders/edit/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/orders">
            <SalesOrderDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/orders/view/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/orders">
            <SalesOrderDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/returns',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/returns">
            <SalesReturnManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/returns/add',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/returns">
            <SalesReturnDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/returns/edit/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/returns">
            <SalesReturnDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'sales/returns/view/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/sales/returns">
            <SalesReturnDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'financial/fixed-assets',
        element: (
          <RoutePermissionGuard requiredRoute="/financial/fixed-assets">
            <FixedAssetsManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'financial/operating-assets',
        element: (
          <RoutePermissionGuard requiredRoute="/financial/operating-assets">
            <OperatingAssetsManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'financial/rental-assets',
        element: (
          <RoutePermissionGuard requiredRoute="/financial/rental-assets">
            <RentalAssetsManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'financial/rd-costs',
        element: (
          <RoutePermissionGuard requiredRoute="/financial/rd-costs">
            <RDCostManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'financial/income-assets',
        element: (
          <RoutePermissionGuard requiredRoute="/financial/income-assets">
            <IncomeAssetsManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'financial/expense-assets',
        element: (
          <RoutePermissionGuard requiredRoute="/financial/expense-assets">
            <ExpenseAssetsManagement />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'statistics/demand-purchase',
        element: (
          <RoutePermissionGuard requiredRoute="/statistics/demand-purchase">
            <DemandPurchaseStatistics />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'statistics/demand-orders',
        element: (
          <RoutePermissionGuard requiredRoute="/statistics/demand-purchase">
            <DemandOrderStatistics />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'statistics/purchase-orders',
        element: (
          <RoutePermissionGuard requiredRoute="/statistics/demand-purchase">
            <PurchaseOrderStatistics />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'statistics/product-demand',
        element: (
          <RoutePermissionGuard requiredRoute="/statistics/demand-purchase">
            <ProductDemandStatistics />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'supplier/purchase-overview',
        element: (
          <RoutePermissionGuard requiredRoute="/supplier/purchase-overview">
            <SupplierPurchaseOverview />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'supplier/purchase-ranking',
        element: (
          <RoutePermissionGuard requiredRoute="/supplier/purchase-overview">
            <SupplierPurchaseRanking />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'supplier/performance-comparison',
        element: (
          <RoutePermissionGuard requiredRoute="/supplier/purchase-overview">
            <SupplierPerformanceComparison />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'supplier/purchase-overview/:supplierCode',
        element: (
          <RoutePermissionGuard requiredRoute="/supplier/purchase-overview">
            <SupplierPurchaseDetail />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'admin/memos',
        element: (
          <SuperAdminGuard>
            <MemoManagement />
          </SuperAdminGuard>
        ),
      },
      {
        path: 'admin/memos/create',
        element: (
          <SuperAdminGuard>
            <MemoForm />
          </SuperAdminGuard>
        ),
      },
      {
        path: 'admin/memos/edit/:id',
        element: (
          <SuperAdminGuard>
            <MemoForm />
          </SuperAdminGuard>
        ),
      },
      {
        path: 'admin/memos/view/:id',
        element: (
          <SuperAdminGuard>
            <MemoForm />
          </SuperAdminGuard>
        ),
      },
      {
        path: 'commission/contracts',
        element: (
          <RoutePermissionGuard requiredRoute="/commission/contracts">
            <CommissionContractsList />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'commission/contracts/create',
        element: (
          <RoutePermissionGuard requiredRoute="/commission/contracts">
            <CommissionContractsForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'commission/contracts/edit/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/commission/contracts">
            <CommissionContractsForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'commission/contracts/view/:id',
        element: (
          <RoutePermissionGuard requiredRoute="/commission/contracts">
            <CommissionContractsForm />
          </RoutePermissionGuard>
        ),
      },
      {
        path: 'test/permissions',
        element: <PermissionTest />,
      },
      {
        path: 'test/menu',
        element: <MenuDebug />,
      },
      {
        path: 'test/quick',
        element: <QuickTest />,
      },
      {
        path: 'test/pay-salary',
        element: <PaySalaryTest />,
      },
    ],
  },
  {
    path: '/404',
    element: <NotFound />,
  },
  {
    path: '/403',
    element: <Forbidden />,
  },
  {
    path: '/500',
    element: <ServerError />,
  },
  {
    path: '*',
    element: <NotFound />,
  },
];

const router = createBrowserRouter(routes);

// 导出路由配置供其他模块使用
export { routes };
export default router;
