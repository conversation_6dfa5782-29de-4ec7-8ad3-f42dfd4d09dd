# VASA管理系统部署脚本 (PowerShell版本)
# 配置信息
$SERVER_USER = "root"
$SERVER_IP = "*************"
$SERVER_PATH = "/root/manager-web"
$CONTAINER_NAME = "vasa-frontend"
$DOMAIN = "vasa.work"

Write-Host "====================================" -ForegroundColor Green
Write-Host "   VASA管理系统部署脚本   " -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host "域名: http://$DOMAIN" -ForegroundColor Blue
Write-Host "API: http://${DOMAIN}:3000" -ForegroundColor Blue
Write-Host "====================================" -ForegroundColor Green

# 检查是否在正确的目录
if (!(Test-Path "package.json") -or !(Test-Path "vite.config.ts")) {
    Write-Host "请在项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

# 检查pnpm
try {
    $pnpmVersion = pnpm --version
    Write-Host "pnpm版本: $pnpmVersion" -ForegroundColor Green
} catch {
    Write-Host "pnpm 未安装，请先安装 pnpm" -ForegroundColor Red
    Write-Host "安装命令: npm install -g pnpm" -ForegroundColor Yellow
    exit 1
}

# 检查依赖
Write-Host "[1/6] 检查项目依赖..." -ForegroundColor Yellow
if (!(Test-Path "node_modules")) {
    Write-Host "安装项目依赖..." -ForegroundColor Blue
    pnpm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "依赖安装失败" -ForegroundColor Red
        exit 1
    }
}

# 本地构建
Write-Host "[2/6] 本地构建项目..." -ForegroundColor Yellow
Write-Host "使用生产环境配置构建..." -ForegroundColor Blue

# 清理旧的构建文件
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
}

# 设置环境变量并构建
$env:NODE_ENV = "production"
pnpm build

if ($LASTEXITCODE -ne 0) {
    Write-Host "本地构建失败，请检查错误信息" -ForegroundColor Red
    exit 1
}

# 检查构建结果
if (!(Test-Path "dist") -or !(Get-ChildItem "dist")) {
    Write-Host "构建产物不存在或为空" -ForegroundColor Red
    exit 1
}

Write-Host "本地构建成功!" -ForegroundColor Green
$distSize = (Get-ChildItem "dist" -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "构建文件大小: $([math]::Round($distSize, 2)) MB" -ForegroundColor Blue

# 压缩构建文件
Write-Host "[3/6] 压缩构建文件..." -ForegroundColor Yellow
if (Test-Path "dist.zip") {
    Remove-Item "dist.zip"
}
Compress-Archive -Path "dist" -DestinationPath "dist.zip" -Force

if (!(Test-Path "dist.zip")) {
    Write-Host "压缩失败" -ForegroundColor Red
    exit 1
}

$zipSize = (Get-Item "dist.zip").Length / 1MB
Write-Host "压缩成功! 压缩包大小: $([math]::Round($zipSize, 2)) MB" -ForegroundColor Green

# 上传文件到服务器
Write-Host "[4/6] 上传文件到服务器..." -ForegroundColor Yellow
Write-Host "目标服务器: ${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}" -ForegroundColor Blue

# 使用scp上传文件
try {
    scp "dist.zip" "${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/"
    scp "Dockerfile" "${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/"
    scp "nginx.conf" "${SERVER_USER}@${SERVER_IP}:${SERVER_PATH}/"

    if ($LASTEXITCODE -ne 0) {
        throw "文件上传失败"
    }
    Write-Host "文件上传成功!" -ForegroundColor Green
} catch {
    Write-Host "文件上传失败: $_" -ForegroundColor Red
    exit 1
}

# 在服务器上部署
Write-Host "[5/6] 在服务器上部署..." -ForegroundColor Yellow

$deployScript = @"
cd $SERVER_PATH

# 解压构建文件
echo 'Extracting build files...'
rm -rf dist/
mkdir -p dist
unzip -q dist.zip -d dist/
rm dist.zip

# 停止并删除旧容器
echo 'Stopping old container...'
docker stop $CONTAINER_NAME 2>/dev/null
docker rm $CONTAINER_NAME 2>/dev/null

# 删除旧镜像
docker rmi vasa-frontend:latest 2>/dev/null

# 构建新镜像
echo 'Building Docker image...'
docker build -t vasa-frontend:latest .

# 启动新容器
echo 'Starting new container...'
docker run -d --name $CONTAINER_NAME --restart always -p 80:80 vasa-frontend:latest

echo 'Deployment completed'
"@

try {
    ssh "${SERVER_USER}@${SERVER_IP}" $deployScript
    if ($LASTEXITCODE -ne 0) {
        throw "服务器部署失败"
    }
    Write-Host "服务器部署成功!" -ForegroundColor Green
} catch {
    Write-Host "服务器部署失败: $_" -ForegroundColor Red
    exit 1
}

# 验证部署
Write-Host "[6/6] 验证部署状态..." -ForegroundColor Yellow

Start-Sleep -Seconds 5  # 等待容器启动

# 检查容器状态
try {
    $containerStatus = ssh "${SERVER_USER}@${SERVER_IP}" "docker ps --filter name=$CONTAINER_NAME --format 'table {{.Status}}' | tail -n +2"

    if ($containerStatus -like "*Up*") {
        Write-Host "✅ 容器运行正常!" -ForegroundColor Green

        # 测试网站访问
        Write-Host "测试域名访问..." -ForegroundColor Blue
        try {
            $response = Invoke-WebRequest -Uri "http://$DOMAIN/" -TimeoutSec 10 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ 域名访问正常! (http://$DOMAIN)" -ForegroundColor Green
            } else {
                Write-Host "⚠️  域名返回状态码: $($response.StatusCode)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "⚠️  域名访问失败，测试IP访问..." -ForegroundColor Yellow
            try {
                $responseIP = Invoke-WebRequest -Uri "http://$SERVER_IP/" -TimeoutSec 10 -UseBasicParsing
                if ($responseIP.StatusCode -eq 200) {
                    Write-Host "✅ IP访问正常! (http://$SERVER_IP)" -ForegroundColor Green
                    Write-Host "⚠️  请检查域名DNS解析配置" -ForegroundColor Yellow
                } else {
                    Write-Host "⚠️  IP返回状态码: $($responseIP.StatusCode)" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "❌ IP访问也失败" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "❌ 容器启动失败" -ForegroundColor Red
        Write-Host "查看容器日志:" -ForegroundColor Yellow
        ssh "${SERVER_USER}@${SERVER_IP}" "docker logs $CONTAINER_NAME"
        exit 1
    }
} catch {
    Write-Host "❌ 无法检查容器状态: $_" -ForegroundColor Red
    exit 1
}

# 清理本地临时文件
Remove-Item "dist.zip"

Write-Host "====================================" -ForegroundColor Green
Write-Host "   🎉 部署完成! 🎉   " -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host "前端访问地址: http://$DOMAIN" -ForegroundColor Blue
Write-Host "后端API地址: http://${DOMAIN}:3000" -ForegroundColor Blue
Write-Host "备用IP访问: http://$SERVER_IP" -ForegroundColor Blue
Write-Host "====================================" -ForegroundColor Green
Write-Host "部署信息:" -ForegroundColor Yellow
Write-Host "  容器名称: $CONTAINER_NAME"
Write-Host "  镜像名称: vasa-frontend:latest"
Write-Host "  端口映射: 80:80"
Write-Host "  域名: $DOMAIN"
Write-Host "====================================" -ForegroundColor Green
