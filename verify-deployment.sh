#!/bin/bash

# Deployment Verification Script

SERVER_IP="*************"

# Color output functions
print_success() { echo -e "\033[32m$1\033[0m"; }
print_warning() { echo -e "\033[33m$1\033[0m"; }
print_error() { echo -e "\033[31m$1\033[0m"; }
print_info() { echo -e "\033[34m$1\033[0m"; }

print_info "Verifying deployment on $SERVER_IP..."

# Check if server is responding
print_info "1. Checking server response..."
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_IP")
if [[ $HTTP_STATUS == "200" ]]; then
    print_success "✓ Server is responding (HTTP $HTTP_STATUS)"
else
    print_error "✗ Server not responding properly (HTTP $HTTP_STATUS)"
fi

# Check if index.html exists and get its timestamp
print_info "2. Checking index.html..."
RESPONSE=$(curl -s -I "http://$SERVER_IP" | grep -E "(Last-Modified|Date)")
if [[ ! -z "$RESPONSE" ]]; then
    print_success "✓ Index.html is accessible"
    echo "$RESPONSE"
else
    print_error "✗ Index.html not accessible"
fi

# Check for specific content that should be in the new version
print_info "3. Checking for recent changes..."
CONTENT=$(curl -s "http://$SERVER_IP" | grep -o "createDate\|F5\|F8" | head -3)
if [[ ! -z "$CONTENT" ]]; then
    print_success "✓ Found recent changes in content:"
    echo "$CONTENT"
else
    print_warning "? Could not verify recent changes in content"
fi

# Check API endpoint
print_info "4. Checking API endpoint..."
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_IP/api/health" 2>/dev/null || echo "000")
if [[ $API_STATUS == "200" ]]; then
    print_success "✓ API is responding (HTTP $API_STATUS)"
elif [[ $API_STATUS == "404" ]]; then
    print_warning "? API health endpoint not found (this might be normal)"
else
    print_error "✗ API not responding (HTTP $API_STATUS)"
fi

print_info "5. Cache busting test..."
TIMESTAMP=$(date +%s)
CACHE_TEST=$(curl -s "http://$SERVER_IP?t=$TIMESTAMP" | wc -l)
if [[ $CACHE_TEST -gt 10 ]]; then
    print_success "✓ Content loaded with cache busting"
else
    print_warning "? Cache busting test inconclusive"
fi

print_info "Verification complete!"
print_warning "If you still see old content:"
print_info "• Try hard refresh: Ctrl+F5 or Cmd+Shift+R"
print_info "• Clear browser cache completely"
print_info "• Use incognito/private mode"
print_info "• Try: http://$SERVER_IP?t=$(date +%s)"
