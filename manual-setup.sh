#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   VASA管理系统手动环境设置   ${NC}"
echo -e "${GREEN}====================================${NC}"

# 1. 安装sshpass
echo -e "${YELLOW}[1/4] 安装sshpass...${NC}"
if ! command -v sshpass &> /dev/null; then
    if command -v yum &> /dev/null; then
        echo -e "${BLUE}检测到CentOS/RHEL系统，使用yum安装...${NC}"
        sudo yum install -y epel-release
        sudo yum install -y sshpass
    else
        echo -e "${RED}请手动安装sshpass${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}sshpass已安装${NC}"
fi

# 2. 检查Node.js和pnpm
echo -e "${YELLOW}[2/4] 检查Node.js和pnpm...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js未安装，请先安装Node.js 18+${NC}"
    echo -e "${BLUE}安装命令: curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -${NC}"
    echo -e "${BLUE}然后运行: sudo yum install -y nodejs${NC}"
    exit 1
fi

if ! command -v pnpm &> /dev/null; then
    echo -e "${YELLOW}pnpm未安装，正在安装...${NC}"
    npm install -g pnpm
    if [ $? -ne 0 ]; then
        echo -e "${RED}pnpm安装失败${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}Node.js版本: $(node --version)${NC}"
echo -e "${GREEN}pnpm版本: $(pnpm --version)${NC}"

# 3. 安装项目依赖
echo -e "${YELLOW}[3/4] 安装项目依赖...${NC}"
if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}正在安装依赖，这可能需要几分钟...${NC}"
    pnpm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}依赖安装失败${NC}"
        echo -e "${YELLOW}尝试清理缓存后重新安装...${NC}"
        pnpm store prune
        pnpm install
        if [ $? -ne 0 ]; then
            echo -e "${RED}依赖安装仍然失败，请检查网络连接${NC}"
            exit 1
        fi
    fi
    echo -e "${GREEN}依赖安装成功!${NC}"
else
    echo -e "${GREEN}依赖已存在${NC}"
fi

# 4. 测试构建
echo -e "${YELLOW}[4/4] 测试构建...${NC}"
echo -e "${BLUE}正在进行测试构建...${NC}"
pnpm build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}====================================${NC}"
    echo -e "${GREEN}   环境设置完成! ✅   ${NC}"
    echo -e "${GREEN}====================================${NC}"
    echo -e "${BLUE}现在您可以运行部署脚本:${NC}"
    echo -e "${YELLOW}./deploy.sh${NC}"
    echo -e "${GREEN}====================================${NC}"
else
    echo -e "${RED}测试构建失败，请检查错误信息${NC}"
    exit 1
fi
