# Quick Frontend Update Script
param(
    [string]$User = "root",
    [switch]$Help
)

$SERVER_IP = "*************"
$WEB_ROOT = "/usr/share/nginx/html"

function Write-Success { param([string]$Text) Write-Host $Text -ForegroundColor Green }
function Write-Warning { param([string]$Text) Write-Host $Text -ForegroundColor Yellow }
function Write-Error { param([string]$Text) Write-Host $Text -ForegroundColor Red }
function Write-Info { param([string]$Text) Write-Host $Text -ForegroundColor Cyan }

if ($Help) {
    Write-Host @"
Quick Frontend Update Script

Usage:
    .\deploy-quick.ps1                # Use root user (default)
    .\deploy-quick.ps1 -User husky    # Use husky user
    .\deploy-quick.ps1 -Help          # Show help

This script will:
    1. Build frontend project
    2. Upload files to server
    3. Update web files only (no nginx config changes)
    4. Reload nginx

Fast deployment for code updates!
"@
    exit 0
}

Write-Success "===================================="
Write-Success "   Quick Frontend Update   "
Write-Success "===================================="
Write-Info "Updating: $User@$SERVER_IP"
Write-Info "Web root: $WEB_ROOT"
Write-Success "===================================="

# Step 1: Build project
Write-Warning "Step 1: Building frontend project..."
if (!(Get-Command pnpm -ErrorAction SilentlyContinue)) {
    Write-Error "pnpm not found. Please install pnpm first"
    exit 1
}

pnpm build

if ($LASTEXITCODE -ne 0) {
    Write-Error "Frontend build failed"
    exit 1
}

Write-Success "Frontend build successful"

# Step 2: Compress and upload files
Write-Warning "Step 2: Uploading files to server..."
if (Test-Path "dist.tar.gz") {
    Remove-Item "dist.tar.gz" -Force
}

tar -czf dist.tar.gz -C dist .
scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null dist.tar.gz ${User}@${SERVER_IP}:/tmp/

if ($LASTEXITCODE -ne 0) {
    Write-Error "File upload failed"
    exit 1
}

Write-Success "Files uploaded successfully"

# Step 3: Update files on server
Write-Warning "Step 3: Updating files on server..."

ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "
# Create backup
BACKUP_NAME=backup_`$(date +%Y%m%d_%H%M%S)
mkdir -p $WEB_ROOT/backups
if [ -d '$WEB_ROOT' ] && [ '`$(ls -A $WEB_ROOT 2>/dev/null | grep -v backups)' ]; then
    cp -r $WEB_ROOT/* $WEB_ROOT/backups/`$BACKUP_NAME/ 2>/dev/null || true
    echo 已备份到: $WEB_ROOT/backups/`$BACKUP_NAME
fi

# Clear current files (except backups)
find $WEB_ROOT -mindepth 1 -maxdepth 1 ! -name 'backups' -exec rm -rf {} \;

# Extract new files
cd $WEB_ROOT
tar -xzf /tmp/dist.tar.gz

# Set permissions
chown -R nginx:nginx $WEB_ROOT 2>/dev/null || chown -R www-data:www-data $WEB_ROOT 2>/dev/null || true
chmod -R 755 $WEB_ROOT

# Reload nginx
nginx -s reload

# Clean up
rm /tmp/dist.tar.gz

echo Files updated successfully
"

if ($LASTEXITCODE -ne 0) {
    Write-Error "File update failed"
    exit 1
}

Write-Success "Files updated successfully"

# Step 4: Verify deployment
Write-Warning "Step 4: Verifying deployment..."
Start-Sleep -Seconds 2

$nginxStatus = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "systemctl is-active nginx"
Write-Info "Nginx status: $nginxStatus"

if ($nginxStatus -eq "active") {
    Write-Success "Nginx is running!"

    $httpTest = ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $User@$SERVER_IP "curl -s -o /dev/null -w '%{http_code}' http://localhost:80 2>/dev/null || echo 'HTTP test failed'"
    Write-Info "HTTP response code: $httpTest"

    if ($httpTest -eq "200") {
        Write-Success "HTTP test passed! Website is accessible"
    } else {
        Write-Warning "HTTP test returned: $httpTest"
    }
} else {
    Write-Error "Nginx is not running"
    exit 1
}

Remove-Item "dist.tar.gz" -Force -ErrorAction SilentlyContinue

Write-Success "===================================="
Write-Success "   🚀 Quick Update Successful! 🚀   "
Write-Success "===================================="
Write-Info "Frontend URL: http://$SERVER_IP"
Write-Info "Web Root: $WEB_ROOT"
Write-Success "===================================="
