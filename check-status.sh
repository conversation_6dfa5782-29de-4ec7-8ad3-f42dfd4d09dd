#!/bin/bash

# VASA前端服务器状态检查脚本

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置信息
SERVER_USER="root"
SERVER_IP="*************"
WEB_ROOT="/usr/share/nginx/html"

echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   VASA服务器状态检查   ${NC}"
echo -e "${GREEN}====================================${NC}"

echo -e "${YELLOW}正在连接服务器 ${SERVER_USER}@${SERVER_IP}...${NC}"

ssh ${SERVER_USER}@${SERVER_IP} << 'EOF'
echo -e "\033[0;34m=== 系统信息 ===\033[0m"
echo "服务器时间: $(date)"
echo "系统版本: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "运行时间: $(uptime -p)"

echo -e "\n\033[0;34m=== 资源使用情况 ===\033[0m"
echo "内存使用:"
free -h
echo -e "\n磁盘使用:"
df -h /usr/share/nginx/html

echo -e "\n\033[0;34m=== Nginx状态 ===\033[0m"
if systemctl is-active --quiet nginx; then
    echo -e "\033[0;32m✅ Nginx 运行正常\033[0m"
    echo "Nginx版本: $(nginx -v 2>&1)"
    echo "配置测试: $(nginx -t 2>&1)"
else
    echo -e "\033[0;31m❌ Nginx 未运行\033[0m"
fi

echo -e "\n\033[0;34m=== 端口监听状态 ===\033[0m"
netstat -tlnp | grep :80 || echo "端口80未监听"
netstat -tlnp | grep :3000 || echo "端口3000未监听"

echo -e "\n\033[0;34m=== 网站文件状态 ===\033[0m"
if [ -d "/usr/share/nginx/html" ]; then
    echo "网站目录: /usr/share/nginx/html"
    echo "文件数量: $(find /usr/share/nginx/html -type f | wc -l)"
    echo "目录大小: $(du -sh /usr/share/nginx/html | cut -f1)"
    echo -e "\n主要文件:"
    ls -la /usr/share/nginx/html/ | head -10
    
    if [ -f "/usr/share/nginx/html/index.html" ]; then
        echo -e "\n\033[0;32m✅ index.html 存在\033[0m"
        echo "文件大小: $(du -h /usr/share/nginx/html/index.html | cut -f1)"
        echo "修改时间: $(stat -c %y /usr/share/nginx/html/index.html)"
    else
        echo -e "\n\033[0;31m❌ index.html 不存在\033[0m"
    fi
    
    if [ -d "/usr/share/nginx/html/assets" ]; then
        echo -e "\033[0;32m✅ assets 目录存在\033[0m"
        echo "assets文件数: $(find /usr/share/nginx/html/assets -type f | wc -l)"
    else
        echo -e "\033[0;31m❌ assets 目录不存在\033[0m"
    fi
else
    echo -e "\033[0;31m❌ 网站目录不存在\033[0m"
fi

echo -e "\n\033[0;34m=== 备份文件 ===\033[0m"
if [ -d "/usr/share/nginx/html/backups" ]; then
    echo "备份目录存在"
    echo "备份数量: $(ls -1 /usr/share/nginx/html/backups/ | wc -l)"
    echo "最新备份:"
    ls -lt /usr/share/nginx/html/backups/ | head -3
else
    echo "无备份文件"
fi

echo -e "\n\033[0;34m=== 网站访问测试 ===\033[0m"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost)
if [ "$HTTP_CODE" = "200" ]; then
    echo -e "\033[0;32m✅ 本地访问正常 (HTTP $HTTP_CODE)\033[0m"
else
    echo -e "\033[0;31m❌ 本地访问异常 (HTTP $HTTP_CODE)\033[0m"
fi

echo -e "\n\033[0;34m=== 最近的日志 ===\033[0m"
if [ -f "/var/log/nginx/web-manager.access.log" ]; then
    echo "最近5条访问日志:"
    tail -5 /var/log/nginx/web-manager.access.log
else
    echo "访问日志文件不存在"
fi

if [ -f "/var/log/nginx/web-manager.error.log" ]; then
    echo -e "\n最近5条错误日志:"
    tail -5 /var/log/nginx/web-manager.error.log
else
    echo "错误日志文件不存在"
fi

EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}====================================${NC}"
    echo -e "${GREEN}   状态检查完成   ${NC}"
    echo -e "${GREEN}====================================${NC}"
    
    echo -e "${BLUE}外部访问测试:${NC}"
    if curl -s -o /dev/null -w "%{http_code}" http://${SERVER_IP} | grep -q "200"; then
        echo -e "${GREEN}✅ 外部访问正常${NC}"
    else
        echo -e "${YELLOW}⚠️  外部访问可能有问题${NC}"
    fi
    
    echo -e "${BLUE}访问地址:${NC}"
    echo -e "${BLUE}前端: http://${SERVER_IP}${NC}"
    echo -e "${BLUE}API:  http://${SERVER_IP}/api${NC}"
else
    echo -e "${RED}连接服务器失败${NC}"
    exit 1
fi
