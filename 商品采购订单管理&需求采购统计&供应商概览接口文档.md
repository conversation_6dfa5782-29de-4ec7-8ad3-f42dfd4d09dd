# 销售需求订单 & 商品采购订单 & 统计分析 & 供应商概览接口文档

## 目录

1. [销售需求订单管理接口](#销售需求订单管理接口)
2. [商品采购订单管理接口](#商品采购订单管理接口)
3. [需求采购统计分析接口](#需求采购统计分析接口)
4. [供应商采购概览接口](#供应商采购概览接口)

---

## 销售需求订单管理接口

### 模块概述

销售需求订单管理模块是整个采购流程的起点，负责处理销售人员根据客户需求创建的需求订单，包括订单创建、审核流程、状态管理等功能。

### 基础路径

```
/sales-demand-orders
```

### 认证要求

所有接口都需要 JWT Token 认证，请在请求头中添加：

```
Authorization: Bearer <your-jwt-token>
```

### 1. 创建销售需求订单

**接口地址：** `POST /sales-demand-orders`

**用途：** 销售人员根据客户需求创建需求订单，系统会自动计算库存缺口和预估金额

**请求参数：**

```typescript
{
  customerCode?: string,           // 客户编码（可选）
  salesPersonCode: string,         // 销售人员编码（必填）
  demandDate: string,             // 需求日期（必填，格式：YYYY-MM-DD）
  expectedDeliveryDate?: string,   // 期望交货日期（可选，格式：YYYY-MM-DD）
  priorityLevel?: string,         // 优先级（可选：urgent/high/normal/low，默认normal）
  remark?: string,                // 备注（可选）
  createdByUserCode?: string,     // 创建人编码（可选）
  details: [{                     // 需求订单明细（必填，至少一条）
    productCode: string,          // 商品编码（必填）
    colorCode: string,           // 颜色编码（必填）
    sizeCode: string,            // 尺寸编码（必填）
    demandQuantity: number,      // 需求数量（必填，大于0）
    unitPrice?: number,          // 预估单价（可选，用于计算预估金额）
    remark?: string              // 明细备注（可选）
  }]
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "需求订单创建成功",
  data: null
}
```

**业务逻辑：**

- 系统自动生成订单编号（格式：SD + 年月日时分秒）
- 自动计算每个SKU的库存缺口
- 自动计算订单总数量和预估总金额
- 订单初始状态为"草稿"

### 2. 查询销售需求订单列表

**接口地址：** `GET /sales-demand-orders`

**用途：** 分页查询销售需求订单列表，支持多种筛选和排序条件

**查询参数：**

- `page` (必填): 页码，从1开始
- `pageSize` (必填): 每页数量，1-100
- `orderNumber` (可选): 订单编号，支持模糊查询
- `customerCode` (可选): 客户编码，精确匹配
- `salesPersonCode` (可选): 销售人员编码，精确匹配
- `status` (可选): 订单状态
  - `draft`: 草稿 - 刚创建，可以编辑
  - `submitted`: 已提交 - 已提交审核，不可编辑
  - `approved`: 已审核 - 审核通过，可以合并
  - `merged`: 已合并 - 已合并到采购订单
  - `completed`: 已完成 - 采购完成
  - `cancelled`: 已取消 - 订单已取消
- `priorityLevel` (可选): 优先级（urgent/high/normal/low）
- `demandDateStart` (可选): 需求日期开始，格式：YYYY-MM-DD
- `demandDateEnd` (可选): 需求日期结束，格式：YYYY-MM-DD
- `createdAtStart` (可选): 创建时间开始，格式：YYYY-MM-DD
- `createdAtEnd` (可选): 创建时间结束，格式：YYYY-MM-DD
- `productCode` (可选): 商品编码，查询包含指定商品的订单
- `sortBy` (可选): 排序字段，默认 createdAt
- `sortOrder` (可选): 排序方向，ASC/DESC，默认 DESC

**响应格式：**

```typescript
{
  code: 200,
  message: "查询成功",
  data: {
    data: SalesDemandOrder[],       // 订单列表
    total: number,                  // 总数量
    page: number,                   // 当前页码
    pageSize: number,               // 每页数量
    totalPages: number              // 总页数
  }
}
```

**订单对象结构：**

```typescript
{
  id: string,                           // 需求订单ID
  orderNumber: string,                  // 需求订单编号
  customerCode?: string,                // 客户编码
  salesPersonCode: string,              // 销售人员编码
  demandDate: string,                   // 需求日期
  expectedDeliveryDate?: string,        // 期望交货日期
  priorityLevel: string,                // 优先级
  status: string,                       // 订单状态
  totalQuantity: number,                // 总需求数量
  estimatedTotalAmount: number,         // 预估总金额
  remark?: string,                      // 备注
  createdByUserCode?: string,           // 创建人编码
  approvedByUserCode?: string,          // 审核人编码
  approvedAt?: string,                  // 审核时间
  mergedToPurchaseOrderId?: string,     // 合并到的采购订单ID
  mergedAt?: string,                    // 合并时间
  createdAt: string,                    // 创建时间
  updatedAt: string,                    // 更新时间
  customer?: Customer,                  // 客户信息
  salesPerson?: User,                   // 销售人员信息
  createdByUser?: User,                 // 创建人信息
  approvedByUser?: User,                // 审核人信息
  details?: SalesDemandOrderDetail[]    // 订单详情
}
```

### 3. 获取可合并的需求订单

**接口地址：** `GET /sales-demand-orders/mergeable`

**用途：** 获取状态为"已审核"且可以合并为采购订单的需求订单列表，用于采购订单合并功能

**查询参数：**

- `supplierCode` (可选): 供应商编码，筛选指定供应商的商品

**响应格式：**

```typescript
{
  code: 200,
  message: "查询成功",
  data: SalesDemandOrder[]  // 可合并的需求订单列表
}
```

**业务逻辑：**

- 只返回状态为"approved"的订单
- 如果指定供应商编码，只返回包含该供应商商品的订单
- 返回的订单包含完整的明细信息

### 4. 获取销售需求订单详情

**接口地址：** `GET /sales-demand-orders/{id}`

**用途：** 获取单个销售需求订单的详细信息，包括订单明细和关联信息

**路径参数：**

- `id`: 需求订单ID

**响应格式：**

```typescript
{
  code: 200,
  message: "查询成功",
  data: {
    ...SalesDemandOrder,              // 订单基本信息
    details: [{                       // 订单明细
      id: string,                     // 明细ID
      productCode: string,            // 商品编码
      colorCode: string,              // 颜色编码
      sizeCode: string,               // 尺寸编码
      skuCode: string,                // SKU编码
      demandQuantity: number,         // 需求数量
      unitPrice?: number,             // 预估单价
      totalAmount?: number,           // 小计金额
      currentStock?: number,          // 当前库存
      shortageQuantity?: number,      // 缺货数量
      remark?: string,                // 明细备注
      product?: Product,              // 商品信息
      color?: Color                   // 颜色信息
    }]
  }
}
```

### 5. 更新销售需求订单

**接口地址：** `PATCH /sales-demand-orders/{id}`

**用途：** 更新销售需求订单信息，只有草稿状态的订单可以修改

**路径参数：**

- `id`: 需求订单ID

**请求参数：**

```typescript
{
  customerCode?: string,           // 客户编码（可选）
  demandDate?: string,            // 需求日期（可选，格式：YYYY-MM-DD）
  expectedDeliveryDate?: string,   // 期望交货日期（可选，格式：YYYY-MM-DD）
  priorityLevel?: string,         // 优先级（可选）
  remark?: string,                // 备注（可选）
  details?: [{                    // 需求订单明细（可选）
    id?: string,                  // 明细ID（更新时必填，新增时不填）
    productCode: string,          // 商品编码（必填）
    colorCode: string,           // 颜色编码（必填）
    sizeCode: string,            // 尺寸编码（必填）
    demandQuantity: number,      // 需求数量（必填，大于0）
    unitPrice?: number,          // 预估单价（可选）
    remark?: string              // 明细备注（可选）
  }]
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "需求订单更新成功",
  data: null
}
```

**业务逻辑：**

- 只有草稿状态的订单可以修改
- 系统会重新计算总数量和预估总金额
- 明细支持新增、修改、删除操作

### 6. 提交审核

**接口地址：** `POST /sales-demand-orders/{id}/submit`

**用途：** 将草稿状态的需求订单提交审核

**路径参数：**

- `id`: 需求订单ID

**请求参数：**

```typescript
{
  userCode?: string  // 提交人编码（可选）
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "需求订单提交成功",
  data: null
}
```

**业务逻辑：**

- 只有草稿状态的订单可以提交
- 提交后状态变为"已提交"，不可再编辑

### 7. 审核通过

**接口地址：** `POST /sales-demand-orders/{id}/approve`

**用途：** 审核通过需求订单，使其可以被合并为采购订单

**路径参数：**

- `id`: 需求订单ID

**请求参数：**

```typescript
{
  approvedByUserCode: string; // 审核人编码（必填）
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "需求订单审核成功",
  data: null
}
```

**业务逻辑：**

- 只有已提交状态的订单可以审核
- 审核后状态变为"已审核"，可以被合并为采购订单

### 8. 取消订单

**接口地址：** `POST /sales-demand-orders/{id}/cancel`

**用途：** 取消需求订单

**路径参数：**

- `id`: 需求订单ID

**响应格式：**

```typescript
{
  code: 200,
  message: "需求订单取消成功",
  data: null
}
```

**业务逻辑：**

- 任何状态的订单都可以取消（除了已完成）
- 取消后状态变为"已取消"

### 9. 删除订单

**接口地址：** `DELETE /sales-demand-orders/{id}`

**用途：** 删除需求订单（软删除）

**路径参数：**

- `id`: 需求订单ID

**响应格式：**

```typescript
{
  code: 200,
  message: "需求订单删除成功",
  data: null
}
```

**业务逻辑：**

- 只有草稿或已取消状态的订单可以删除
- 执行软删除，数据不会真正删除

### 10. 导出Excel

**接口地址：** `GET /sales-demand-orders/export/excel`

**用途：** 导出需求订单数据为Excel文件

**查询参数：**
支持与列表查询相同的筛选参数

**响应格式：**
返回Excel文件流

**业务逻辑：**

- 导出当前筛选条件下的所有订单
- 包含订单基本信息和明细信息

---

## 商品采购订单管理接口

### 模块概述

商品采购订单管理模块负责处理从需求订单到采购订单的完整流程，包括需求订单合并、采购订单创建、状态管理等功能。

### 基础路径

```
/product-purchase-orders
```

### 认证要求

所有接口都需要 JWT Token 认证，请在请求头中添加：

```
Authorization: Bearer <your-jwt-token>
```

### 1. 合并需求订单为采购订单

**接口地址：** `POST /product-purchase-orders/merge-demands`

**用途：** 将多个需求订单合并为一个采购订单，用于批量采购管理

**请求参数：**

```typescript
{
  demandOrderIds: string[];        // 需求订单ID列表（必填）
  supplierCode: string;           // 供应商编码（必填）
  expectedDeliveryDate?: string;  // 预计交货日期（可选，格式：YYYY-MM-DD）
  createdByUserCode: string;      // 创建人编码（必填）
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "采购订单创建成功",
  data: null
}
```

### 2. 查询采购订单列表

**接口地址：** `GET /product-purchase-orders`

**用途：** 分页查询采购订单列表，支持多种筛选条件

**查询参数：**

- `page` (必填): 页码，从1开始
- `pageSize` (必填): 每页数量，1-100
- `orderNumber` (可选): 订单编号，支持模糊查询
- `supplierCode` (可选): 供应商编码
- `status` (可选): 订单状态
  - `draft`: 草稿
  - `confirmed`: 已确认
  - `producing`: 生产中
  - `shipped`: 已发货
  - `received`: 已收货
  - `completed`: 已完成
  - `cancelled`: 已取消
- `orderDateStart` (可选): 订单日期开始，格式：YYYY-MM-DD
- `orderDateEnd` (可选): 订单日期结束，格式：YYYY-MM-DD
- `createdAtStart` (可选): 创建时间开始，格式：YYYY-MM-DD
- `createdAtEnd` (可选): 创建时间结束，格式：YYYY-MM-DD
- `productCode` (可选): 商品编码，查询包含指定商品的订单
- `sortBy` (可选): 排序字段，默认 createdAt
- `sortOrder` (可选): 排序方向，ASC/DESC，默认 DESC

**响应格式：**

```typescript
{
  code: 200,
  message: "查询成功",
  data: {
    items: ProductPurchaseOrder[],  // 订单列表
    total: number,                  // 总数量
    page: number,                   // 当前页码
    pageSize: number,               // 每页数量
    totalPages: number              // 总页数
  }
}
```

**订单对象结构：**

```typescript
{
  id: string,                           // 采购订单ID
  orderNumber: string,                  // 采购订单编号
  supplierCode: string,                 // 供应商编码
  orderDate: string,                    // 采购订单日期
  expectedDeliveryDate?: string,        // 预计交货日期
  totalAmount: number,                  // 合计金额
  totalQuantity: number,                // 合计数量
  status: string,                       // 订单状态
  createdByUserCode?: string,           // 创建人编码
  mergedDemandOrderIds?: string,        // 合并的需求订单ID列表
  confirmedByUserCode?: string,         // 确认人编码
  confirmedAt?: string,                 // 确认时间
  receivedByUserCode?: string,          // 收货人编码
  receivedAt?: string,                  // 收货时间
  createdAt: string,                    // 创建时间
  updatedAt: string,                    // 更新时间
  supplier?: Supplier,                  // 供应商信息
  createdByUser?: User,                 // 创建人信息
  details?: ProductPurchaseOrderDetail[] // 订单详情
}
```

### 3. 获取采购订单详情

**接口地址：** `GET /product-purchase-orders/{id}`

**用途：** 获取单个采购订单的详细信息，包括订单详情和需求来源

**路径参数：**

- `id`: 采购订单ID

**响应格式：**

```typescript
{
  code: 200,
  message: "查询成功",
  data: {
    ...ProductPurchaseOrder,        // 订单基本信息
    demandSources?: Array<{         // 需求来源信息
      demandOrderId: string,
      demandOrderNumber: string,
      salesPersonCode: string,
      demandDate: string,
      totalQuantity: number,
      totalAmount: number
    }>
  }
}
```

### 4. 确认采购订单

**接口地址：** `POST /product-purchase-orders/{id}/confirm`

**用途：** 确认采购订单，将状态从草稿变更为已确认

**路径参数：**

- `id`: 采购订单ID

**请求参数：**

```typescript
{
  confirmedByUserCode: string; // 确认人编码（必填）
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "订单确认成功",
  data: null
}
```

### 5. 收货确认

**接口地址：** `POST /product-purchase-orders/{id}/receive`

**用途：** 确认收货，更新库存并变更订单状态

**路径参数：**

- `id`: 采购订单ID

**请求参数：**

```typescript
{
  receivedByUserCode: string; // 收货人编码（必填）
  receiveDetails: Array<{
    // 收货详情（必填）
    detailId: string; // 订单详情ID
    receivedQuantity: number; // 实际收货数量
  }>;
}
```

**响应格式：**

```typescript
{
  code: 200,
  message: "收货确认成功",
  data: null
}
```

### 6. 取消采购订单

**接口地址：** `POST /product-purchase-orders/{id}/cancel`

**用途：** 取消采购订单，只有草稿状态的订单可以取消

**路径参数：**

- `id`: 采购订单ID

**响应格式：**

```typescript
{
  code: 200,
  message: "订单取消成功",
  data: null
}
```

---

## 需求采购统计分析接口

### 模块概述

需求采购统计分析模块提供全面的数据分析功能，包括需求订单统计、采购订单统计、商品需求分析和综合仪表板数据。

### 基础路径

```
/demand-purchase-statistics
```

### 1. 综合仪表板数据

**接口地址：** `GET /demand-purchase-statistics/dashboard`

**用途：** 获取综合仪表板数据，包含需求采购转换率、满足率等关键指标

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `salesPersonCode` (可选): 销售人员编码
- `supplierCode` (可选): 供应商编码
- `brandCode` (可选): 品牌编码
- `categoryCode` (可选): 分类编码

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    summary: {
      totalDemandOrders: number,      // 总需求订单数
      totalPurchaseOrders: number,    // 总采购订单数
      totalDemandQuantity: number,    // 总需求数量
      totalPurchaseQuantity: number,  // 总采购数量
      totalDemandAmount: number,      // 总需求金额
      totalPurchaseAmount: number,    // 总采购金额
      conversionRate: number,         // 转换率(%)
      fulfillmentRate: number         // 满足率(%)
    },
    demandOrderStats: object,         // 需求订单统计
    purchaseOrderStats: object,       // 采购订单统计
    productDemandStats: object        // 商品需求统计
  }
}
```

### 2. 需求订单统计

**接口地址：** `GET /demand-purchase-statistics/demand-orders`

**用途：** 获取需求订单的详细统计分析，包括状态分布、销售人员分布、月度趋势等

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `salesPersonCode` (可选): 销售人员编码

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    totalOrders: number,              // 总订单数
    totalQuantity: number,            // 总数量
    totalAmount: number,              // 总金额
    avgAmount: number,                // 平均金额
    statusBreakdown: {                // 状态分布
      [status: string]: number
    },
    priorityBreakdown: {              // 优先级分布
      [priority: string]: number
    },
    salesPersonBreakdown: Array<{     // 销售人员分布
      salesPersonCode: string,
      orderCount: number,
      totalQuantity: number,
      totalAmount: number
    }>,
    monthlyTrend: Array<{             // 月度趋势
      month: string,
      orderCount: number,
      totalQuantity: number,
      totalAmount: number
    }>
  }
}
```

### 3. 采购订单统计

**接口地址：** `GET /demand-purchase-statistics/purchase-orders`

**用途：** 获取采购订单的详细统计分析，包括供应商分布、月度趋势等

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `supplierCode` (可选): 供应商编码

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    totalOrders: number,              // 总订单数
    totalQuantity: number,            // 总数量
    totalAmount: number,              // 总金额
    avgAmount: number,                // 平均金额
    statusBreakdown: {                // 状态分布
      [status: string]: number
    },
    supplierBreakdown: Array<{        // 供应商分布
      supplierCode: string,
      orderCount: number,
      totalQuantity: number,
      totalAmount: number,
      avgDeliveryDays: number
    }>,
    monthlyTrend: Array<{             // 月度趋势
      month: string,
      orderCount: number,
      totalQuantity: number,
      totalAmount: number
    }>
  }
}
```

### 4. 商品需求统计

**接口地址：** `GET /demand-purchase-statistics/product-demand`

**用途：** 获取商品需求的详细分析，包括热门商品、品牌分析、供应商分析

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `brandCode` (可选): 品牌编码
- `categoryCode` (可选): 分类编码

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    topDemandProducts: Array<{        // 热门需求商品（前20名）
      productCode: string,
      totalDemand: number,
      totalShortage: number,
      demandOrderCount: number,
      avgUnitPrice: number
    }>,
    brandAnalysis: Array<{            // 品牌分析
      brandCode: string,
      totalDemand: number,
      totalShortage: number,
      productCount: number
    }>,
    supplierAnalysis: Array<{         // 供应商分析
      supplierCode: string,
      totalDemand: number,
      totalShortage: number,
      productCount: number
    }>
  }
}
```

---

## 供应商采购概览接口

### 模块概述

供应商采购概览模块提供供应商维度的采购数据分析，包括供应商概览、详细信息、排行榜和绩效对比等功能。

### 基础路径

```
/supplier-purchase-overview
```

### 1. 获取所有供应商采购概览

**接口地址：** `GET /supplier-purchase-overview`

**用途：** 获取所有有采购记录的供应商概览信息，用于供应商管理首页展示

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: Array<{
    supplierCode: string,             // 供应商编码
    supplierName: string,             // 供应商名称
    totalOrders: number,              // 总订单数
    totalAmount: number,              // 总金额
    totalQuantity: number,            // 总数量
    avgOrderAmount: number,           // 平均订单金额
    activeContracts: number,          // 有效合同数
    pendingOrders: number,            // 待处理订单数
    completedOrders: number,          // 已完成订单数
    onTimeDeliveryRate: number,       // 按时交货率(%)
    lastOrderDate: string,            // 最后订单日期
    topProducts: Array<{              // 热门商品（前5名）
      productCode: string,
      productName: string,
      totalQuantity: number,
      totalAmount: number
    }>
  }>
}
```

### 2. 获取供应商采购排行榜

**接口地址：** `GET /supplier-purchase-overview/ranking`

**用途：** 获取供应商采购排行榜，按采购金额排序

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD
- `limit` (可选): 返回数量限制，默认10

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: Array<{
    rank: number,                     // 排名
    supplierCode: string,             // 供应商编码
    supplierName: string,             // 供应商名称
    totalAmount: number,              // 总采购金额
    totalOrders: number,              // 总订单数
    totalQuantity: number,            // 总采购数量
    avgOrderAmount: number,           // 平均订单金额
    onTimeDeliveryRate: number,       // 按时交货率(%)
    growthRate: number                // 增长率(%)
  }>
}
```

### 3. 获取供应商绩效对比

**接口地址：** `GET /supplier-purchase-overview/performance-comparison`

**用途：** 对比多个供应商的绩效指标

**查询参数：**

- `supplierCodes` (必填): 供应商编码列表，逗号分隔，如：SUP001,SUP002,SUP003

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: Array<{
    supplierCode: string,             // 供应商编码
    supplierName: string,             // 供应商名称
    totalOrders: number,              // 总订单数
    totalAmount: number,              // 总金额
    totalQuantity: number,            // 总数量
    avgOrderAmount: number,           // 平均订单金额
    avgDeliveryDays: number,          // 平均交货天数
    onTimeDeliveryRate: number,       // 按时交货率(%)
    qualityScore: number,             // 质量评分
    cooperationMonths: number         // 合作月数
  }>
}
```

### 4. 获取单个供应商采购详情

**接口地址：** `GET /supplier-purchase-overview/{supplierCode}`

**用途：** 获取单个供应商的详细采购信息，包括统计数据、商品分析、需求来源等

**路径参数：**

- `supplierCode`: 供应商编码

**查询参数：**

- `startDate` (可选): 开始日期，格式：YYYY-MM-DD
- `endDate` (可选): 结束日期，格式：YYYY-MM-DD

**响应格式：**

```typescript
{
  code: 200,
  message: "获取成功",
  data: {
    supplier: {                       // 供应商基本信息
      code: string,                   // 供应商编码
      name: string,                   // 供应商名称
      address: string,                // 地址
      contactName: string,            // 联系人
      contactPhone: string,           // 联系电话
      createdAt: string,              // 创建时间
      updatedAt: string               // 更新时间
    },
    summary: {                        // 统计摘要
      totalOrders: number,            // 总订单数
      totalAmount: number,            // 总金额
      totalQuantity: number,          // 总数量
      avgOrderAmount: number,         // 平均订单金额
      avgDeliveryDays: number,        // 平均交货天数
      onTimeDeliveryRate: number      // 按时交货率(%)
    },
    ordersByStatus: {                 // 按状态分布
      [status: string]: number
    },
    monthlyTrend: Array<{             // 月度趋势
      month: string,
      orderCount: number,
      totalAmount: number,
      avgDeliveryDays: number
    }>,
    topProducts: Array<{              // 热门商品（前10名）
      productCode: string,
      productName: string,
      brandCode: string,
      categoryCode: string,
      totalQuantity: number,
      totalAmount: number,
      avgPrice: number,
      orderCount: number
    }>,
    demandSources: Array<{            // 需求来源分析
      salesPersonCode: string,
      salesPersonName: string,
      demandOrderCount: number,
      totalDemandQuantity: number,
      contributionRate: number
    }>,
    recentOrders: Array<{             // 最近订单（前10条）
      id: string,
      orderNumber: string,
      orderDate: string,
      totalAmount: number,
      totalQuantity: number,
      status: string,
      createdByUserCode: string
    }>
  }
}
```

---

## 数据字典

### 销售需求订单状态 (SalesDemandOrderStatus)

- `draft`: 草稿 - 刚创建，可以编辑和删除
- `submitted`: 已提交 - 已提交审核，不可编辑
- `approved`: 已审核 - 审核通过，可以合并为采购订单
- `merged`: 已合并 - 已合并到采购订单，不可修改
- `completed`: 已完成 - 采购完成，流程结束
- `cancelled`: 已取消 - 订单已取消

### 采购订单状态 (ProductPurchaseOrderStatus)

- `draft`: 草稿 - 订单刚创建，可以编辑
- `confirmed`: 已确认 - 订单已确认，不可编辑
- `producing`: 生产中 - 供应商正在生产
- `shipped`: 已发货 - 供应商已发货
- `received`: 已收货 - 已收到货物
- `completed`: 已完成 - 订单完成
- `cancelled`: 已取消 - 订单已取消

### 优先级 (PriorityLevel)

- `urgent`: 紧急 - 最高优先级，需要立即处理
- `high`: 高 - 高优先级，优先处理
- `normal`: 普通 - 正常优先级，默认值
- `low`: 低 - 低优先级，可延后处理

### 状态流转规则

#### 销售需求订单状态流转

```
草稿 → 已提交 → 已审核 → 已合并 → 已完成
  ↓      ↓       ↓       ↓
已取消  已取消   已取消   已取消
```

#### 采购订单状态流转

```
草稿 → 已确认 → 生产中 → 已发货 → 已收货 → 已完成
  ↓      ↓
已取消  已取消
```

### 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 业务冲突（如状态不允许操作）
- `500`: 服务器内部错误

---

## 使用建议

### 1. 完整业务流程

#### 阶段一：销售需求管理

1. **创建需求订单**: 销售人员根据客户需求创建销售需求订单
   ```
   POST /sales-demand-orders
   ```
2. **编辑需求订单**: 在草稿状态下可以修改订单信息
   ```
   PATCH /sales-demand-orders/{id}
   ```
3. **提交审核**: 销售人员完善订单后提交审核
   ```
   POST /sales-demand-orders/{id}/submit
   ```
4. **审核通过**: 销售主管审核通过需求订单
   ```
   POST /sales-demand-orders/{id}/approve
   ```

#### 阶段二：采购订单管理

5. **查看可合并订单**: 生产人员查看已审核的需求订单
   ```
   GET /sales-demand-orders/mergeable
   ```
6. **合并需求订单**: 将多个需求订单合并为采购订单
   ```
   POST /product-purchase-orders/merge-demands
   ```
7. **确认采购订单**: 确认采购订单并发送给供应商
   ```
   POST /product-purchase-orders/{id}/confirm
   ```
8. **跟踪订单状态**: 跟踪采购订单状态直到收货完成
   ```
   GET /product-purchase-orders/{id}
   POST /product-purchase-orders/{id}/receive
   ```

#### 阶段三：数据分析

9. **统计分析**: 通过统计接口分析采购效率和供应商绩效
   ```
   GET /demand-purchase-statistics/dashboard
   GET /supplier-purchase-overview
   ```

### 2. 关键业务规则

#### 需求订单业务规则

- 只有草稿状态的订单可以编辑和删除
- 只有草稿状态的订单可以提交审核
- 只有已提交状态的订单可以审核通过
- 只有已审核状态的订单可以被合并为采购订单
- 订单合并后状态自动变为"已合并"

#### 采购订单业务规则

- 只能合并相同供应商的需求订单
- 合并时会自动计算总数量和总金额
- 只有草稿状态的采购订单可以取消
- 收货时支持部分收货，可多次确认
- 收货数量不能超过采购数量

#### 库存更新规则

- 收货确认时自动更新库存
- 库存更新基于SKU维度
- 支持批量库存更新

### 3. 前端开发建议

#### 页面结构建议

```
销售管理
├── 需求订单管理
│   ├── 订单列表 (GET /sales-demand-orders)
│   ├── 创建订单 (POST /sales-demand-orders)
│   ├── 订单详情 (GET /sales-demand-orders/{id})
│   └── 编辑订单 (PATCH /sales-demand-orders/{id})
├── 采购管理
│   ├── 需求订单合并 (GET /sales-demand-orders/mergeable)
│   ├── 采购订单列表 (GET /product-purchase-orders)
│   └── 采购订单详情 (GET /product-purchase-orders/{id})
└── 数据分析
    ├── 统计仪表板 (GET /demand-purchase-statistics/dashboard)
    ├── 需求分析 (GET /demand-purchase-statistics/demand-orders)
    ├── 采购分析 (GET /demand-purchase-statistics/purchase-orders)
    └── 供应商概览 (GET /supplier-purchase-overview)
```

#### 页面布局建议

- **需求订单列表页**: 表格展示，支持状态筛选、日期范围、销售人员筛选
- **需求订单创建页**: 表单布局，支持动态添加商品明细
- **需求订单详情页**: 标签页分组显示基本信息、订单明细、操作历史
- **订单合并页**: 左侧可合并订单列表，右侧合并预览
- **采购订单列表页**: 表格展示，支持供应商筛选、状态筛选
- **统计分析页**: 卡片+图表布局，展示关键指标和趋势

#### 交互建议

- 分页查询建议每页显示20-50条记录
- 状态使用不同颜色的标签显示
- 操作按钮根据状态动态显示/隐藏
- 支持批量操作（批量审核、批量合并等）
- 详情页面使用标签页分组显示不同维度的数据

#### 状态管理

- 需求订单状态颜色：草稿(灰色)、已提交(蓝色)、已审核(绿色)、已合并(橙色)、已完成(绿色)、已取消(红色)
- 采购订单状态颜色：草稿(灰色)、已确认(蓝色)、生产中(橙色)、已发货(紫色)、已收货(绿色)、已完成(绿色)、已取消(红色)
- 优先级颜色：紧急(红色)、高(橙色)、普通(默认)、低(灰色)
- 提供状态变更的操作按钮
- 显示状态变更历史记录

#### 权限控制建议

- **销售人员**: 只能创建、查看、编辑自己的需求订单
- **销售主管**: 可以审核需求订单，查看所有销售人员的订单
- **生产人员**: 可以查看已审核的需求订单，执行合并操作，管理采购订单
- **管理员**: 拥有所有权限，可以查看统计数据

### 3. 性能优化建议

#### 查询优化

- 大数据量查询时建议使用日期范围过滤
- 统计接口建议添加缓存机制
- 列表查询建议只返回必要字段，详情查询返回完整信息

#### 数据展示优化

- 长列表使用虚拟滚动
- 图表数据使用懒加载
- 大数据量导出使用异步处理

### 4. 业务逻辑说明

#### 订单合并逻辑

- 只能合并相同供应商的需求订单
- 合并时会自动计算总数量和总金额
- 合并后的采购订单会记录来源需求订单信息

#### 库存更新逻辑

- 收货确认时会自动更新库存
- 支持部分收货，可多次确认收货
- 收货数量不能超过采购数量

#### 统计计算逻辑

- 转换率 = 采购订单数 / 需求订单数 × 100%
- 满足率 = 采购数量 / 需求数量 × 100%
- 按时交货率 = 按时交货订单数 / 已完成订单数 × 100%

### 5. 注意事项

#### 权限控制

- 所有接口都需要JWT认证
- 不同角色可能有不同的操作权限
- 建议在前端也做相应的权限控制

#### 数据一致性

- 订单状态变更需要验证前置条件
- 库存更新需要考虑并发问题
- 统计数据可能存在延迟，建议添加刷新功能

#### 错误处理

- 网络错误时提供重试机制
- 业务错误时显示具体错误信息
- 长时间操作提供进度提示

---

## 常见问题 FAQ

### Q1: 如何处理需求订单合并失败？

A1: 检查以下条件：

- 需求订单是否属于同一供应商
- 需求订单状态是否允许合并
- 用户是否有合并权限

### Q2: 统计数据不准确怎么办？

A2: 统计数据基于实时计算，可能的原因：

- 数据库事务未提交
- 缓存未更新
- 查询条件设置错误

### Q3: 供应商概览数据为空？

A3: 可能的原因：

- 该供应商没有采购记录
- 查询时间范围内没有数据
- 供应商已被删除

### Q4: 如何提高查询性能？

A4: 建议：

- 使用合适的时间范围过滤
- 避免查询过大的数据集
- 使用分页查询
- 考虑添加索引优化

### Q5: 需求订单无法提交审核？

A5: 检查以下条件：

- 订单状态是否为"草稿"
- 订单明细是否完整
- 用户是否有提交权限

### Q6: 采购订单合并失败？

A6: 可能的原因：

- 需求订单状态不是"已审核"
- 选择的需求订单不属于同一供应商
- 需求订单已被其他采购订单合并

### Q7: 库存更新不及时？

A7: 库存更新时机：

- 只有在采购订单收货确认时才更新库存
- 需求订单创建时只计算缺货数量，不更新库存
- 如有延迟，检查收货确认是否成功

---

## 📋 接口总览

### 销售需求订单管理 (10个接口)

- ✅ 创建需求订单
- ✅ 查询订单列表
- ✅ 获取可合并订单
- ✅ 获取订单详情
- ✅ 更新订单信息
- ✅ 提交审核
- ✅ 审核通过
- ✅ 取消订单
- ✅ 删除订单
- ✅ 导出Excel

### 商品采购订单管理 (6个接口)

- ✅ 合并需求订单为采购订单
- ✅ 查询采购订单列表
- ✅ 获取采购订单详情
- ✅ 确认采购订单
- ✅ 收货确认
- ✅ 取消采购订单

### 需求采购统计分析 (4个接口)

- ✅ 综合仪表板数据
- ✅ 需求订单统计
- ✅ 采购订单统计
- ✅ 商品需求统计

### 供应商采购概览 (4个接口)

- ✅ 获取所有供应商采购概览
- ✅ 获取供应商采购排行榜
- ✅ 获取供应商绩效对比
- ✅ 获取单个供应商采购详情

**总计：24个接口，覆盖完整的销售需求到采购完成的业务流程**

---

_文档版本: v2.0_
_最后更新: 2025-01-20_
_维护人员: 后端开发团队_
_包含内容: 销售需求订单管理、商品采购订单管理、统计分析、供应商概览_
